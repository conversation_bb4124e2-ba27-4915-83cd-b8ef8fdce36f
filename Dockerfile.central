FROM atl-gitlab.ao-inc.com:5050/qlink-app/ql-build-image/flutter-build-image:3.24.5 AS build-env

ENV APP_HOME=/app

COPY quantumlink-node/ $APP_HOME
WORKDIR $APP

RUN flutter clean
RUN flutter pub get
RUN dart run build_runner build --delete-conflicting-outputs
RUN flutter build web --release --web-renderer canvaskit

FROM nginx:1.25.2-alpine

COPY --from=build-env /app/build/web /usr/share/nginx/html

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
