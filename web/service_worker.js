const CACHE_NAME = 'flutter-app-cache';
const urlsToCache = [
  '/',
  '/index.html',
  '/main.dart.js',
  '/flutter.js',
  '/flutter_bootstrap.js',
  '/canvaskit/canvaskit.js',
  '/canvaskit/canvaskit.wasm',
  '/assets/NOTICES',
  '/assets/AssetManifest.json',
  '/assets/FontManifest.json',
  '/assets/fonts/roboto/Roboto-Regular.ttf',
  '/assets/fonts/roboto/Roboto-Medium.ttf',
  '/assets/fonts/roboto/Roboto-Bold.ttf',
  '/assets/fonts/opensans/OpenSans-Medium.ttf',
  '/assets/fonts/opensans/OpenSans-Regular.ttf',
  '/assets/fonts/opensans/OpenSans-SemiBold.ttf',
  '/assets/fonts/opensans/OpenSans-Bold.ttf',
  '/assets/fonts/poppins/Poppins-Regular.ttf',
  '/assets/fonts/source_sance/SourceSans3-Medium.ttf',
  'assets/fonts/notosans/NotoSans-Medium.ttf',
  'assets/fonts/notosans/NotoSans-SemiBold.ttf',
  '/assets/fonts/monrope/Manrope-Medium.ttf',
  '/assets/fonts/monrope/Manrope-SemiBold.ttf'
];

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        return cache.addAll(urlsToCache);
      })
  );
});

self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        return response || fetch(event.request);
      })
  );
});