/*! @azure/msal-browser v2.13.1 2021-03-31 */
"use strict";!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).msal={})}(this,(function(e){
/*! *****************************************************************************
    Copyright (c) Microsoft Corporation.

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */
var t=function(e,r){return(t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,r)};function r(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}var n=function(){return(n=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function o(e,t,r,n){return new(r||(r=Promise))((function(o,i){function a(e){try{c(n.next(e))}catch(e){i(e)}}function s(e){try{c(n.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}c((n=n.apply(e,t||[])).next())}))}function i(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(r)throw new TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}}function a(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a}function s(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(a(arguments[t]));return e}
/*! @azure/msal-common v4.1.1 2021-03-31 */
/*! *****************************************************************************
    Copyright (c) Microsoft Corporation.

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */var c=function(e,t){return(c=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)};function u(e,t){function r(){this.constructor=e}c(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var h=function(){return(h=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function d(e,t,r,n){return new(r||(r=Promise))((function(o,i){function a(e){try{c(n.next(e))}catch(e){i(e)}}function s(e){try{c(n.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}c((n=n.apply(e,t||[])).next())}))}function l(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(r)throw new TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}}function p(){for(var e=0,t=0,r=arguments.length;t<r;t++)e+=arguments[t].length;var n=Array(e),o=0;for(t=0;t<r;t++)for(var i=arguments[t],a=0,s=i.length;a<s;a++,o++)n[o]=i[a];return n}var f,g,y,m,v,E="msal.js.common",C="msal",T="https://login.microsoftonline.com/common/",w="adfs",S="https://login.microsoftonline.com/common/discovery/instance?api-version=1.1&authorization_endpoint=",I="|",A="offline_access",b="code",R="S256",_="application/x-www-form-urlencoded;charset=utf-8",k="authorization_pending",O="not_defined",P="",N="/",U=["openid","profile",A],q=p(U,["email"]);!function(e){e.CONTENT_TYPE="Content-Type",e.X_CLIENT_CURR_TELEM="x-client-current-telemetry",e.X_CLIENT_LAST_TELEM="x-client-last-telemetry",e.RETRY_AFTER="Retry-After",e.X_MS_LIB_CAPABILITY="x-ms-lib-capability",e.X_MS_LIB_CAPABILITY_VALUE="retry-after, h429"}(f||(f={})),function(e){e.ID_TOKEN="idtoken",e.CLIENT_INFO="client.info",e.ADAL_ID_TOKEN="adal.idtoken",e.ERROR="error",e.ERROR_DESC="error.description"}(g||(g={})),function(e){e.COMMON="common",e.ORGANIZATIONS="organizations",e.CONSUMERS="consumers"}(y||(y={})),function(e){e.CLIENT_ID="client_id",e.REDIRECT_URI="redirect_uri",e.RESPONSE_TYPE="response_type",e.RESPONSE_MODE="response_mode",e.GRANT_TYPE="grant_type",e.CLAIMS="claims",e.SCOPE="scope",e.ERROR="error",e.ERROR_DESCRIPTION="error_description",e.ACCESS_TOKEN="access_token",e.ID_TOKEN="id_token",e.REFRESH_TOKEN="refresh_token",e.EXPIRES_IN="expires_in",e.STATE="state",e.NONCE="nonce",e.PROMPT="prompt",e.SESSION_STATE="session_state",e.CLIENT_INFO="client_info",e.CODE="code",e.CODE_CHALLENGE="code_challenge",e.CODE_CHALLENGE_METHOD="code_challenge_method",e.CODE_VERIFIER="code_verifier",e.CLIENT_REQUEST_ID="client-request-id",e.X_CLIENT_SKU="x-client-SKU",e.X_CLIENT_VER="x-client-VER",e.X_CLIENT_OS="x-client-OS",e.X_CLIENT_CPU="x-client-CPU",e.POST_LOGOUT_URI="post_logout_redirect_uri",e.ID_TOKEN_HINT="id_token_hint",e.DEVICE_CODE="device_code",e.CLIENT_SECRET="client_secret",e.CLIENT_ASSERTION="client_assertion",e.CLIENT_ASSERTION_TYPE="client_assertion_type",e.TOKEN_TYPE="token_type",e.REQ_CNF="req_cnf",e.OBO_ASSERTION="assertion",e.REQUESTED_TOKEN_USE="requested_token_use",e.ON_BEHALF_OF="on_behalf_of",e.FOCI="foci"}(m||(m={})),function(e){e.ACCESS_TOKEN="access_token",e.XMS_CC="xms_cc"}(v||(v={}));var M,L={LOGIN:"login",SELECT_ACCOUNT:"select_account",CONSENT:"consent",NONE:"none"};!function(e){e.ACCOUNT="account",e.SID="sid",e.LOGIN_HINT="login_hint",e.ID_TOKEN="id_token",e.DOMAIN_HINT="domain_hint",e.ORGANIZATIONS="organizations",e.CONSUMERS="consumers",e.ACCOUNT_ID="accountIdentifier",e.HOMEACCOUNT_ID="homeAccountIdentifier"}(M||(M={}));M.SID,M.LOGIN_HINT;var D,x,H,K,F,B,G,j={PLAIN:"plain",S256:"S256"};!function(e){e.QUERY="query",e.FRAGMENT="fragment",e.FORM_POST="form_post"}(D||(D={})),function(e){e.IMPLICIT_GRANT="implicit",e.AUTHORIZATION_CODE_GRANT="authorization_code",e.CLIENT_CREDENTIALS_GRANT="client_credentials",e.RESOURCE_OWNER_PASSWORD_GRANT="password",e.REFRESH_TOKEN_GRANT="refresh_token",e.DEVICE_CODE_GRANT="device_code",e.JWT_BEARER="urn:ietf:params:oauth:grant-type:jwt-bearer"}(x||(x={})),function(e){e.MSSTS_ACCOUNT_TYPE="MSSTS",e.ADFS_ACCOUNT_TYPE="ADFS",e.MSAV1_ACCOUNT_TYPE="MSA",e.GENERIC_ACCOUNT_TYPE="Generic"}(H||(H={})),function(e){e.CACHE_KEY_SEPARATOR="-",e.CLIENT_INFO_SEPARATOR="."}(K||(K={})),function(e){e.ID_TOKEN="IdToken",e.ACCESS_TOKEN="AccessToken",e.REFRESH_TOKEN="RefreshToken"}(F||(F={})),function(e){e.ACCOUNT="Account",e.CREDENTIAL="Credential",e.ID_TOKEN="IdToken",e.ACCESS_TOKEN="AccessToken",e.REFRESH_TOKEN="RefreshToken",e.APP_METADATA="AppMetadata",e.TEMPORARY="TempCache",e.TELEMETRY="Telemetry",e.UNDEFINED="Undefined",e.THROTTLING="Throttling"}(B||(B={})),function(e){e[e.ADFS=1001]="ADFS",e[e.MSA=1002]="MSA",e[e.MSSTS=1003]="MSSTS",e[e.GENERIC=1004]="GENERIC",e[e.ACCESS_TOKEN=2001]="ACCESS_TOKEN",e[e.REFRESH_TOKEN=2002]="REFRESH_TOKEN",e[e.ID_TOKEN=2003]="ID_TOKEN",e[e.APP_METADATA=3001]="APP_METADATA",e[e.UNDEFINED=9999]="UNDEFINED"}(G||(G={}));var z,Q="appmetadata",W="1",J="authority-metadata",V=86400;!function(e){e.CONFIG="config",e.CACHE="cache",e.NETWORK="network"}(z||(z={}));var Y,X={SCHEMA_VERSION:2,MAX_HEADER_BYTES:4e3,CACHE_KEY:"server-telemetry",CATEGORY_SEPARATOR:"|",VALUE_SEPARATOR:",",OVERFLOW_TRUE:"1",OVERFLOW_FALSE:"0",UNKNOWN_ERROR:"unknown_error"};(Y=e.AuthenticationScheme||(e.AuthenticationScheme={})).POP="pop",Y.BEARER="Bearer";var Z,$=60,ee=3600,te="throttling",re="invalid_grant",ne="client_mismatch";!function(e){e.username="username",e.password="password"}(Z||(Z={}));var oe,ie={unexpectedError:{code:"unexpected_error",desc:"Unexpected error in authentication."}},ae=function(e){function t(r,n,o){var i=this,a=n?r+": "+n:r;return i=e.call(this,a)||this,Object.setPrototypeOf(i,t.prototype),i.errorCode=r||P,i.errorMessage=n||"",i.subError=o||"",i.name="AuthError",i}return u(t,e),t.createUnexpectedError=function(e){return new t(ie.unexpectedError.code,ie.unexpectedError.desc+": "+e)},t}(Error),se={createNewGuid:function(){throw ae.createUnexpectedError("Crypto interface - createNewGuid() has not been implemented")},base64Decode:function(){throw ae.createUnexpectedError("Crypto interface - base64Decode() has not been implemented")},base64Encode:function(){throw ae.createUnexpectedError("Crypto interface - base64Encode() has not been implemented")},generatePkceCodes:function(){return d(this,void 0,void 0,(function(){return l(this,(function(e){throw"Crypto interface - generatePkceCodes() has not been implemented",ae.createUnexpectedError("Crypto interface - generatePkceCodes() has not been implemented")}))}))},getPublicKeyThumbprint:function(){return d(this,void 0,void 0,(function(){return l(this,(function(e){throw"Crypto interface - getPublicKeyThumbprint() has not been implemented",ae.createUnexpectedError("Crypto interface - getPublicKeyThumbprint() has not been implemented")}))}))},signJwt:function(){return d(this,void 0,void 0,(function(){return l(this,(function(e){throw"Crypto interface - signJwt() has not been implemented",ae.createUnexpectedError("Crypto interface - signJwt() has not been implemented")}))}))}},ce={code:"client_info_decoding_error",desc:"The client info could not be parsed/decoded correctly. Please review the trace to determine the root cause."},ue={code:"client_info_empty_error",desc:"The client info was empty. Please review the trace to determine the root cause."},he={code:"token_parsing_error",desc:"Token cannot be parsed. Please review stack trace to determine root cause."},de={code:"null_or_empty_token",desc:"The token is null or empty. Please review the trace to determine the root cause."},le={code:"endpoints_resolution_error",desc:"Error: could not resolve endpoints. Please check network and try again."},pe={code:"network_error",desc:"Network request failed. Please check network trace to determine root cause."},fe={code:"openid_config_error",desc:"Could not retrieve endpoints. Check your authority and verify the .well-known/openid-configuration endpoint returns the required endpoints."},ge={code:"hash_not_deserialized",desc:"The hash parameters could not be deserialized. Please review the trace to determine the root cause."},ye={code:"invalid_state",desc:"State was not the expected format. Please check the logs to determine whether the request was sent using ProtocolUtils.setRequestState()."},me={code:"state_mismatch",desc:"State mismatch error. Please check your network. Continued requests may cause cache overflow."},ve={code:"state_not_found",desc:"State not found"},Ee={code:"nonce_mismatch",desc:"Nonce mismatch error. This may be caused by a race condition in concurrent requests."},Ce={code:"nonce_not_found",desc:"nonce not found"},Te={code:"no_tokens_found",desc:"No tokens were found for the given scopes, and no authorization code was passed to acquireToken. You must retrieve an authorization code before making a call to acquireToken()."},we={code:"multiple_matching_tokens",desc:"The cache contains multiple tokens satisfying the requirements. Call AcquireToken again providing more requirements such as authority or account."},Se={code:"multiple_matching_accounts",desc:"The cache contains multiple accounts satisfying the given parameters. Please pass more info to obtain the correct account"},Ie={code:"multiple_matching_appMetadata",desc:"The cache contains multiple appMetadata satisfying the given parameters. Please pass more info to obtain the correct appMetadata"},Ae={code:"request_cannot_be_made",desc:"Token request cannot be made without authorization code or refresh token."},be={code:"cannot_append_empty_scope",desc:"Cannot append null or empty scope to ScopeSet. Please check the stack trace for more info."},Re={code:"cannot_remove_empty_scope",desc:"Cannot remove null or empty scope from ScopeSet. Please check the stack trace for more info."},_e={code:"cannot_append_scopeset",desc:"Cannot append ScopeSet due to error."},ke={code:"empty_input_scopeset",desc:"Empty input ScopeSet cannot be processed."},Oe={code:"device_code_polling_cancelled",desc:"Caller has cancelled token endpoint polling during device code flow by setting DeviceCodeRequest.cancel = true."},Pe={code:"device_code_expired",desc:"Device code is expired."},Ne={code:"no_account_in_silent_request",desc:"Please pass an account object, silent flow is not supported without account information"},Ue={code:"invalid_cache_record",desc:"Cache record object was null or undefined."},qe={code:"invalid_cache_environment",desc:"Invalid environment when attempting to create cache entry"},Me={code:"no_account_found",desc:"No account found in cache for given key."},Le={code:"no cache plugin set on CacheManager",desc:"ICachePlugin needs to be set before using readFromStorage or writeFromStorage"},De={code:"no_crypto_object",desc:"No crypto object detected. This is required for the following operation: "},xe={code:"invalid_cache_type",desc:"Invalid cache type"},He={code:"unexpected_account_type",desc:"Unexpected account type."},Ke={code:"unexpected_credential_type",desc:"Unexpected credential type."},Fe={code:"invalid_assertion",desc:"Client assertion must meet requirements described in https://tools.ietf.org/html/rfc7515"},Be={code:"invalid_client_credential",desc:"Client credential (secret, certificate, or assertion) must not be empty when creating a confidential client. An application should at most have one credential"},Ge={code:"token_refresh_required",desc:"Cannot return token from cache because it must be refreshed. This may be due to one of the following reasons: forceRefresh parameter is set to true, claims have been requested, there is no cached access token or it is expired."},je={code:"user_timeout_reached",desc:"User defined timeout for device code polling reached"},ze={code:"token_claims_cnf_required_for_signedjwt",desc:"Cannot generate a POP jwt if the token_claims are not populated"},Qe={code:"authorization_code_missing_from_server_response",desc:"Srver response does not contain an authorization code to proceed"},We=function(e){function t(r,n){var o=e.call(this,r,n)||this;return o.name="ClientAuthError",Object.setPrototypeOf(o,t.prototype),o}return u(t,e),t.createClientInfoDecodingError=function(e){return new t(ce.code,ce.desc+" Failed with error: "+e)},t.createClientInfoEmptyError=function(){return new t(ue.code,""+ue.desc)},t.createTokenParsingError=function(e){return new t(he.code,he.desc+" Failed with error: "+e)},t.createTokenNullOrEmptyError=function(e){return new t(de.code,de.desc+" Raw Token Value: "+e)},t.createEndpointDiscoveryIncompleteError=function(e){return new t(le.code,le.desc+" Detail: "+e)},t.createNetworkError=function(e,r){return new t(pe.code,pe.desc+" | Fetch client threw: "+r+" | Attempted to reach: "+e.split("?")[0])},t.createUnableToGetOpenidConfigError=function(e){return new t(fe.code,fe.desc+" Attempted to retrieve endpoints from: "+e)},t.createHashNotDeserializedError=function(e){return new t(ge.code,ge.desc+" Given Object: "+e)},t.createInvalidStateError=function(e,r){return new t(ye.code,ye.desc+" Invalid State: "+e+", Root Err: "+r)},t.createStateMismatchError=function(){return new t(me.code,me.desc)},t.createStateNotFoundError=function(e){return new t(ve.code,ve.desc+":  "+e)},t.createNonceMismatchError=function(){return new t(Ee.code,Ee.desc)},t.createNonceNotFoundError=function(e){return new t(Ce.code,Ce.desc+":  "+e)},t.createNoTokensFoundError=function(){return new t(Te.code,Te.desc)},t.createMultipleMatchingTokensInCacheError=function(){return new t(we.code,we.desc+".")},t.createMultipleMatchingAccountsInCacheError=function(){return new t(Se.code,Se.desc)},t.createMultipleMatchingAppMetadataInCacheError=function(){return new t(Ie.code,Ie.desc)},t.createTokenRequestCannotBeMadeError=function(){return new t(Ae.code,Ae.desc)},t.createAppendEmptyScopeToSetError=function(e){return new t(be.code,be.desc+" Given Scope: "+e)},t.createRemoveEmptyScopeFromSetError=function(e){return new t(Re.code,Re.desc+" Given Scope: "+e)},t.createAppendScopeSetError=function(e){return new t(_e.code,_e.desc+" Detail Error: "+e)},t.createEmptyInputScopeSetError=function(e){return new t(ke.code,ke.desc+" Given ScopeSet: "+e)},t.createDeviceCodeCancelledError=function(){return new t(Oe.code,""+Oe.desc)},t.createDeviceCodeExpiredError=function(){return new t(Pe.code,""+Pe.desc)},t.createNoAccountInSilentRequestError=function(){return new t(Ne.code,""+Ne.desc)},t.createNullOrUndefinedCacheRecord=function(){return new t(Ue.code,Ue.desc)},t.createInvalidCacheEnvironmentError=function(){return new t(qe.code,qe.desc)},t.createNoAccountFoundError=function(){return new t(Me.code,Me.desc)},t.createCachePluginError=function(){return new t(Le.code,""+Le.desc)},t.createNoCryptoObjectError=function(e){return new t(De.code,""+De.desc+e)},t.createInvalidCacheTypeError=function(){return new t(xe.code,""+xe.desc)},t.createUnexpectedAccountTypeError=function(){return new t(He.code,""+He.desc)},t.createUnexpectedCredentialTypeError=function(){return new t(Ke.code,""+Ke.desc)},t.createInvalidAssertionError=function(){return new t(Fe.code,""+Fe.desc)},t.createInvalidCredentialError=function(){return new t(Be.code,""+Be.desc)},t.createRefreshRequiredError=function(){return new t(Ge.code,Ge.desc)},t.createUserTimeoutReachedError=function(){return new t(je.code,je.desc)},t.createTokenClaimsRequiredError=function(){return new t(ze.code,ze.desc)},t.createNoAuthCodeInServerResponseError=function(){return new t(Qe.code,Qe.desc)},t}(ae),Je=function(){function e(){}return e.decodeAuthToken=function(t){if(e.isEmpty(t))throw We.createTokenNullOrEmptyError(t);var r=/^([^\.\s]*)\.([^\.\s]+)\.([^\.\s]*)$/.exec(t);if(!r||r.length<4)throw We.createTokenParsingError("Given token is malformed: "+JSON.stringify(t));return{header:r[1],JWSPayload:r[2],JWSSig:r[3]}},e.isEmpty=function(e){return void 0===e||!e||0===e.length},e.startsWith=function(e,t){return 0===e.indexOf(t)},e.endsWith=function(e,t){return e.length>=t.length&&e.lastIndexOf(t)===e.length-t.length},e.queryStringToObject=function(e){var t,r=/\+/g,n=/([^&=]+)=([^&]*)/g,o=function(e){return decodeURIComponent(decodeURIComponent(e.replace(r," ")))},i={};for(t=n.exec(e);t;)i[o(t[1])]=o(t[2]),t=n.exec(e);return i},e.trimArrayEntries=function(e){return e.map((function(e){return e.trim()}))},e.removeEmptyStringsFromArray=function(t){return t.filter((function(t){return!e.isEmpty(t)}))},e.jsonParseHelper=function(e){try{return JSON.parse(e)}catch(e){return null}},e.matchPattern=function(e,t){return new RegExp(e.replace(/\*/g,"[^ ]*").replace(/\?/g,"\\?")).test(t)},e}();(oe=e.LogLevel||(e.LogLevel={}))[oe.Error=0]="Error",oe[oe.Warning=1]="Warning",oe[oe.Info=2]="Info",oe[oe.Verbose=3]="Verbose";var Ve,Ye=function(){function t(t,r,n){this.level=e.LogLevel.Info;this.localCallback=t.loggerCallback||function(){},this.piiLoggingEnabled=t.piiLoggingEnabled||!1,this.level=t.logLevel||e.LogLevel.Info,this.packageName=r||P,this.packageVersion=n||P}return t.prototype.clone=function(e,r){return new t({loggerCallback:this.localCallback,piiLoggingEnabled:this.piiLoggingEnabled,logLevel:this.level},e,r)},t.prototype.logMessage=function(t,r){if(!(r.logLevel>this.level||!this.piiLoggingEnabled&&r.containsPii)){var n=(new Date).toUTCString(),o=(Je.isEmpty(this.correlationId)?"["+n+"] : ":"["+n+"] : ["+this.correlationId+"]")+" : "+this.packageName+"@"+this.packageVersion+" : "+e.LogLevel[r.logLevel]+" - "+t;this.executeCallback(r.logLevel,o,r.containsPii||!1)}},t.prototype.executeCallback=function(e,t,r){this.localCallback&&this.localCallback(e,t,r)},t.prototype.error=function(t,r){this.logMessage(t,{logLevel:e.LogLevel.Error,containsPii:!1,correlationId:r||""})},t.prototype.errorPii=function(t,r){this.logMessage(t,{logLevel:e.LogLevel.Error,containsPii:!0,correlationId:r||""})},t.prototype.warning=function(t,r){this.logMessage(t,{logLevel:e.LogLevel.Warning,containsPii:!1,correlationId:r||""})},t.prototype.warningPii=function(t,r){this.logMessage(t,{logLevel:e.LogLevel.Warning,containsPii:!0,correlationId:r||""})},t.prototype.info=function(t,r){this.logMessage(t,{logLevel:e.LogLevel.Info,containsPii:!1,correlationId:r||""})},t.prototype.infoPii=function(t,r){this.logMessage(t,{logLevel:e.LogLevel.Info,containsPii:!0,correlationId:r||""})},t.prototype.verbose=function(t,r){this.logMessage(t,{logLevel:e.LogLevel.Verbose,containsPii:!1,correlationId:r||""})},t.prototype.verbosePii=function(t,r){this.logMessage(t,{logLevel:e.LogLevel.Verbose,containsPii:!0,correlationId:r||""})},t.prototype.isPiiLoggingEnabled=function(){return this.piiLoggingEnabled||!1},t}(),Xe="4.1.1",Ze=function(){function e(){}return e.prototype.generateAccountId=function(){return e.generateAccountIdForCacheKey(this.homeAccountId,this.environment)},e.prototype.generateCredentialId=function(){return e.generateCredentialIdForCacheKey(this.credentialType,this.clientId,this.realm,this.familyId)},e.prototype.generateTarget=function(){return e.generateTargetForCacheKey(this.target)},e.prototype.generateCredentialKey=function(){return e.generateCredentialCacheKey(this.homeAccountId,this.environment,this.credentialType,this.clientId,this.realm,this.target,this.familyId)},e.prototype.generateType=function(){switch(this.credentialType){case F.ID_TOKEN:return G.ID_TOKEN;case F.ACCESS_TOKEN:return G.ACCESS_TOKEN;case F.REFRESH_TOKEN:return G.REFRESH_TOKEN;default:throw We.createUnexpectedCredentialTypeError()}},e.getCredentialType=function(e){return-1!==e.indexOf(F.ACCESS_TOKEN.toLowerCase())?F.ACCESS_TOKEN:-1!==e.indexOf(F.ID_TOKEN.toLowerCase())?F.ID_TOKEN:-1!==e.indexOf(F.REFRESH_TOKEN.toLowerCase())?F.REFRESH_TOKEN:O},e.generateCredentialCacheKey=function(e,t,r,n,o,i,a){return[this.generateAccountIdForCacheKey(e,t),this.generateCredentialIdForCacheKey(r,n,o,a),this.generateTargetForCacheKey(i)].join(K.CACHE_KEY_SEPARATOR).toLowerCase()},e.generateAccountIdForCacheKey=function(e,t){return[e,t].join(K.CACHE_KEY_SEPARATOR).toLowerCase()},e.generateCredentialIdForCacheKey=function(e,t,r,n){return[e,e===F.REFRESH_TOKEN&&n||t,r||""].join(K.CACHE_KEY_SEPARATOR).toLowerCase()},e.generateTargetForCacheKey=function(e){return(e||"").toLowerCase()},e}(),$e={code:"redirect_uri_empty",desc:"A redirect URI is required for all calls, and none has been set."},et={code:"post_logout_uri_empty",desc:"A post logout redirect has not been set."},tt={code:"claims_request_parsing_error",desc:"Could not parse the given claims request object."},rt={code:"authority_uri_insecure",desc:"Authority URIs must use https.  Please see here for valid authority configuration options: https://docs.microsoft.com/en-us/azure/active-directory/develop/msal-js-initializing-client-applications#configuration-options"},nt={code:"url_parse_error",desc:"URL could not be parsed into appropriate segments."},ot={code:"empty_url_error",desc:"URL was empty or null."},it={code:"empty_input_scopes_error",desc:"Scopes cannot be passed as null, undefined or empty array because they are required to obtain an access token."},at={code:"nonarray_input_scopes_error",desc:"Scopes cannot be passed as non-array."},st={code:"clientid_input_scopes_error",desc:"Client ID can only be provided as a single scope."},ct={code:"invalid_prompt_value",desc:"Supported prompt values are 'login', 'select_account', 'consent' and 'none'.  Please see here for valid configuration options: https://docs.microsoft.com/en-us/azure/active-directory/develop/msal-js-initializing-client-applications#configuration-options"},ut={code:"invalid_claims",desc:"Given claims parameter must be a stringified JSON object."},ht={code:"token_request_empty",desc:"Token request was empty and not found in cache."},dt={code:"logout_request_empty",desc:"The logout request was null or undefined."},lt={code:"invalid_code_challenge_method",desc:'code_challenge_method passed is invalid. Valid values are "plain" and "S256".'},pt={code:"pkce_params_missing",desc:"Both params: code_challenge and code_challenge_method are to be passed if to be sent in the request"},ft={code:"invalid_cloud_discovery_metadata",desc:"Invalid cloudDiscoveryMetadata provided. Must be a JSON object containing tenant_discovery_endpoint and metadata fields"},gt={code:"invalid_authority_metadata",desc:"Invalid authorityMetadata provided. Must by a JSON object containing authorization_endpoint, token_endpoint, end_session_endpoint, issuer fields."},yt={code:"untrusted_authority",desc:"The provided authority is not a trusted authority. Please include this authority in the knownAuthorities config parameter."},mt={code:"resourceRequest_parameters_required",desc:"resourceRequestMethod and resourceRequestUri are required"},vt=function(e){function t(r,n){var o=e.call(this,r,n)||this;return o.name="ClientConfigurationError",Object.setPrototypeOf(o,t.prototype),o}return u(t,e),t.createRedirectUriEmptyError=function(){return new t($e.code,$e.desc)},t.createPostLogoutRedirectUriEmptyError=function(){return new t(et.code,et.desc)},t.createClaimsRequestParsingError=function(e){return new t(tt.code,tt.desc+" Given value: "+e)},t.createInsecureAuthorityUriError=function(e){return new t(rt.code,rt.desc+" Given URI: "+e)},t.createUrlParseError=function(e){return new t(nt.code,nt.desc+" Given Error: "+e)},t.createUrlEmptyError=function(){return new t(ot.code,ot.desc)},t.createScopesNonArrayError=function(e){return new t(at.code,at.desc+" Given Scopes: "+e)},t.createEmptyScopesArrayError=function(e){return new t(it.code,it.desc+" Given Scopes: "+e)},t.createClientIdSingleScopeError=function(e){return new t(st.code,st.desc+" Given Scopes: "+e)},t.createInvalidPromptError=function(e){return new t(ct.code,ct.desc+" Given value: "+e)},t.createInvalidClaimsRequestError=function(){return new t(ut.code,ut.desc)},t.createEmptyLogoutRequestError=function(){return new t(dt.code,dt.desc)},t.createEmptyTokenRequestError=function(){return new t(ht.code,ht.desc)},t.createInvalidCodeChallengeMethodError=function(){return new t(lt.code,lt.desc)},t.createInvalidCodeChallengeParamsError=function(){return new t(pt.code,pt.desc)},t.createInvalidCloudDiscoveryMetadataError=function(){return new t(ft.code,ft.desc)},t.createInvalidAuthorityMetadataError=function(){return new t(gt.code,gt.desc)},t.createUntrustedAuthorityError=function(){return new t(yt.code,yt.desc)},t.createResourceRequestParametersRequiredError=function(){return new t(mt.code,mt.desc)},t}(We),Et=function(){function e(e){var t=this,r=e?Je.trimArrayEntries(p(e)):[],n=r?Je.removeEmptyStringsFromArray(r):[];this.validateInputScopes(n),this.scopes=new Set,n.forEach((function(e){return t.scopes.add(e)}))}return e.fromString=function(t){return new e((t=t||"").split(" "))},e.prototype.validateInputScopes=function(e){if(!e||e.length<1)throw vt.createEmptyScopesArrayError(e)},e.prototype.containsScope=function(t){var r=new e(this.printScopesLowerCase().split(" "));return!Je.isEmpty(t)&&r.scopes.has(t.toLowerCase())},e.prototype.containsScopeSet=function(e){var t=this;return!(!e||e.scopes.size<=0)&&(this.scopes.size>=e.scopes.size&&e.asArray().every((function(e){return t.containsScope(e)})))},e.prototype.containsOnlyOIDCScopes=function(){var e=this,t=0;return q.forEach((function(r){e.containsScope(r)&&(t+=1)})),this.scopes.size===t},e.prototype.appendScope=function(e){Je.isEmpty(e)||this.scopes.add(e.trim())},e.prototype.appendScopes=function(e){var t=this;try{e.forEach((function(e){return t.appendScope(e)}))}catch(e){throw We.createAppendScopeSetError(e)}},e.prototype.removeScope=function(e){if(Je.isEmpty(e))throw We.createRemoveEmptyScopeFromSetError(e);this.scopes.delete(e.trim())},e.prototype.removeOIDCScopes=function(){var e=this;q.forEach((function(t){e.scopes.delete(t)}))},e.prototype.unionScopeSets=function(e){if(!e)throw We.createEmptyInputScopeSetError(e);var t=new Set;return e.scopes.forEach((function(e){return t.add(e.toLowerCase())})),this.scopes.forEach((function(e){return t.add(e.toLowerCase())})),t},e.prototype.intersectingScopeSets=function(e){if(!e)throw We.createEmptyInputScopeSetError(e);e.containsOnlyOIDCScopes()||e.removeOIDCScopes();var t=this.unionScopeSets(e),r=e.getScopeCount(),n=this.getScopeCount();return t.size<n+r},e.prototype.getScopeCount=function(){return this.scopes.size},e.prototype.asArray=function(){var e=[];return this.scopes.forEach((function(t){return e.push(t)})),e},e.prototype.printScopes=function(){return this.scopes?this.asArray().join(" "):""},e.prototype.printScopesLowerCase=function(){return this.printScopes().toLowerCase()},e}();function Ct(e,t){if(Je.isEmpty(e))throw We.createClientInfoEmptyError();try{var r=t.base64Decode(e);return JSON.parse(r)}catch(e){throw We.createClientInfoDecodingError(e)}}!function(e){e[e.Default=0]="Default",e[e.Adfs=1]="Adfs"}(Ve||(Ve={}));var Tt=function(){function e(){}return e.prototype.generateAccountId=function(){return[this.homeAccountId,this.environment].join(K.CACHE_KEY_SEPARATOR).toLowerCase()},e.prototype.generateAccountKey=function(){return e.generateAccountCacheKey({homeAccountId:this.homeAccountId,environment:this.environment,tenantId:this.realm,username:this.username,localAccountId:this.localAccountId})},e.prototype.generateType=function(){switch(this.authorityType){case H.ADFS_ACCOUNT_TYPE:return G.ADFS;case H.MSAV1_ACCOUNT_TYPE:return G.MSA;case H.MSSTS_ACCOUNT_TYPE:return G.MSSTS;case H.GENERIC_ACCOUNT_TYPE:return G.GENERIC;default:throw We.createUnexpectedAccountTypeError()}},e.prototype.getAccountInfo=function(){return{homeAccountId:this.homeAccountId,environment:this.environment,tenantId:this.realm,username:this.username,localAccountId:this.localAccountId,name:this.name,idTokenClaims:this.idTokenClaims}},e.generateAccountCacheKey=function(e){return[e.homeAccountId,e.environment||"",e.tenantId||""].join(K.CACHE_KEY_SEPARATOR).toLowerCase()},e.createAccount=function(t,r,n,o,i,a,s){var c,u,h,d,l,p,f=new e;f.authorityType=H.MSSTS_ACCOUNT_TYPE,f.clientInfo=t,f.homeAccountId=r;var g=n.getPreferredCache();if(Je.isEmpty(g))throw We.createInvalidCacheEnvironmentError();return f.environment=g,f.realm=(null===(c=null==o?void 0:o.claims)||void 0===c?void 0:c.tid)||"",f.oboAssertion=i,o&&(f.idTokenClaims=o.claims,f.localAccountId=(null===(u=null==o?void 0:o.claims)||void 0===u?void 0:u.oid)||(null===(h=null==o?void 0:o.claims)||void 0===h?void 0:h.sub)||"",f.username=(null===(d=null==o?void 0:o.claims)||void 0===d?void 0:d.preferred_username)||((null===(l=null==o?void 0:o.claims)||void 0===l?void 0:l.emails)?o.claims.emails[0]:""),f.name=null===(p=null==o?void 0:o.claims)||void 0===p?void 0:p.name),f.cloudGraphHostName=a,f.msGraphHost=s,f},e.createGenericAccount=function(t,r,n,o,i,a){var s,c,u,h,d=new e;d.authorityType=t.authorityType===Ve.Adfs?H.ADFS_ACCOUNT_TYPE:H.GENERIC_ACCOUNT_TYPE,d.homeAccountId=r,d.realm="",d.oboAssertion=o;var l=t.getPreferredCache();if(Je.isEmpty(l))throw We.createInvalidCacheEnvironmentError();return n&&(d.localAccountId=(null===(s=null==n?void 0:n.claims)||void 0===s?void 0:s.oid)||(null===(c=null==n?void 0:n.claims)||void 0===c?void 0:c.sub)||"",d.username=(null===(u=null==n?void 0:n.claims)||void 0===u?void 0:u.upn)||"",d.name=(null===(h=null==n?void 0:n.claims)||void 0===h?void 0:h.name)||"",d.idTokenClaims=null==n?void 0:n.claims),d.environment=l,d.cloudGraphHostName=i,d.msGraphHost=a,d},e.generateHomeAccountId=function(e,t,r,n,o){var i,a=(null===(i=null==o?void 0:o.claims)||void 0===i?void 0:i.sub)?o.claims.sub:P;if(t===Ve.Adfs)return a;if(e){var s=Ct(e,n);if(!Je.isEmpty(s.uid)&&!Je.isEmpty(s.utid))return""+s.uid+K.CLIENT_INFO_SEPARATOR+s.utid}return r.verbose("No client info in response"),a},e.isAccountEntity=function(e){return!!e&&(e.hasOwnProperty("homeAccountId")&&e.hasOwnProperty("environment")&&e.hasOwnProperty("realm")&&e.hasOwnProperty("localAccountId")&&e.hasOwnProperty("username")&&e.hasOwnProperty("authorityType"))},e.accountInfoIsEqual=function(e,t){return!(!e||!t)&&(e.homeAccountId===t.homeAccountId&&e.localAccountId===t.localAccountId&&e.username===t.username&&e.tenantId===t.tenantId&&e.environment===t.environment)},e}(),wt=function(){function e(t,r){if(Je.isEmpty(t))throw We.createTokenNullOrEmptyError(t);this.rawToken=t,this.claims=e.extractTokenClaims(t,r)}return e.extractTokenClaims=function(e,t){var r=Je.decodeAuthToken(e);try{var n=r.JWSPayload,o=t.base64Decode(n);return JSON.parse(o)}catch(e){throw We.createTokenParsingError(e)}},e}(),St=function(){function e(e,t){this.clientId=e,this.cryptoImpl=t}return e.prototype.getAllAccounts=function(){var t=this,r=this.getAccountsFilteredBy(),n=Object.keys(r).map((function(e){return r[e]}));return n.length<1?[]:n.map((function(r){var n=e.toObject(new Tt,r).getAccountInfo(),o=t.readIdTokenFromCache(t.clientId,n);return o&&!n.idTokenClaims&&(n.idTokenClaims=new wt(o.secret,t.cryptoImpl).claims),n}))},e.prototype.saveCacheRecord=function(e){if(!e)throw We.createNullOrUndefinedCacheRecord();e.account&&this.setAccount(e.account),e.idToken&&this.setIdTokenCredential(e.idToken),e.accessToken&&this.saveAccessToken(e.accessToken),e.refreshToken&&this.setRefreshTokenCredential(e.refreshToken),e.appMetadata&&this.setAppMetadata(e.appMetadata)},e.prototype.saveAccessToken=function(e){var t=this,r=this.getCredentialsFilteredBy({clientId:e.clientId,credentialType:F.ACCESS_TOKEN,environment:e.environment,homeAccountId:e.homeAccountId,realm:e.realm}),n=Et.fromString(e.target),o=Object.keys(r.accessTokens).map((function(e){return r.accessTokens[e]}));o&&o.forEach((function(e){Et.fromString(e.target).intersectingScopeSets(n)&&t.removeCredential(e)})),this.setAccessTokenCredential(e)},e.prototype.getAccountsFilteredBy=function(e){return this.getAccountsFilteredByInternal(e?e.homeAccountId:"",e?e.environment:"",e?e.realm:"")},e.prototype.getAccountsFilteredByInternal=function(e,t,r){var n=this,o=this.getKeys(),i={};return o.forEach((function(o){var a=n.getAccount(o);a&&(e&&!n.matchHomeAccountId(a,e)||t&&!n.matchEnvironment(a,t)||r&&!n.matchRealm(a,r)||(i[o]=a))})),i},e.prototype.getCredentialsFilteredBy=function(e){return this.getCredentialsFilteredByInternal(e.homeAccountId,e.environment,e.credentialType,e.clientId,e.familyId,e.realm,e.target,e.oboAssertion)},e.prototype.getCredentialsFilteredByInternal=function(e,t,r,n,o,i,a,s){var c=this,u=this.getKeys(),h={idTokens:{},accessTokens:{},refreshTokens:{}};return u.forEach((function(u){var d=Ze.getCredentialType(u);if(d!==O){var l=c.getSpecificCredential(u,d);if(l&&(!s||c.matchOboAssertion(l,s))&&(!e||c.matchHomeAccountId(l,e))&&(!t||c.matchEnvironment(l,t))&&(!i||c.matchRealm(l,i))&&(!r||c.matchCredentialType(l,r))&&(!n||c.matchClientId(l,n))&&(!o||c.matchFamilyId(l,o))&&(!a||c.matchTarget(l,a)))switch(d){case F.ID_TOKEN:h.idTokens[u]=l;break;case F.ACCESS_TOKEN:h.accessTokens[u]=l;break;case F.REFRESH_TOKEN:h.refreshTokens[u]=l}}})),h},e.prototype.getAppMetadataFilteredBy=function(e){return this.getAppMetadataFilteredByInternal(e.environment,e.clientId)},e.prototype.getAppMetadataFilteredByInternal=function(e,t){var r=this,n=this.getKeys(),o={};return n.forEach((function(n){if(r.isAppMetadata(n)){var i=r.getAppMetadata(n);i&&(e&&!r.matchEnvironment(i,e)||t&&!r.matchClientId(i,t)||(o[n]=i))}})),o},e.prototype.getAuthorityMetadataByAlias=function(e){var t=this,r=this.getAuthorityMetadataKeys(),n=null;return r.forEach((function(r){if(t.isAuthorityMetadata(r)&&-1!==r.indexOf(t.clientId)){var o=t.getAuthorityMetadata(r);o&&-1!==o.aliases.indexOf(e)&&(n=o)}})),n},e.prototype.removeAllAccounts=function(){var e=this;return this.getKeys().forEach((function(t){e.getAccount(t)&&e.removeAccount(t)})),!0},e.prototype.removeAccount=function(e){var t=this.getAccount(e);if(!t)throw We.createNoAccountFoundError();return this.removeAccountContext(t)&&this.removeItem(e,B.ACCOUNT)},e.prototype.removeAccountContext=function(e){var t=this,r=this.getKeys(),n=e.generateAccountId();return r.forEach((function(e){var r=Ze.getCredentialType(e);if(r!==O){var o=t.getSpecificCredential(e,r);o&&n===o.generateAccountId()&&t.removeCredential(o)}})),!0},e.prototype.removeCredential=function(e){var t=e.generateCredentialKey();return this.removeItem(t,B.CREDENTIAL)},e.prototype.removeAppMetadata=function(){var e=this;return this.getKeys().forEach((function(t){e.isAppMetadata(t)&&e.removeItem(t,B.APP_METADATA)})),!0},e.prototype.readCacheRecord=function(e,t,r,n){var o=this.readAccountFromCache(e),i=this.readIdTokenFromCache(t,e),a=this.readAccessTokenFromCache(t,e,r),s=this.readRefreshTokenFromCache(t,e,!1),c=this.readAppMetadataFromCache(n,t);return o&&i&&(o.idTokenClaims=new wt(i.secret,this.cryptoImpl).claims),{account:o,idToken:i,accessToken:a,refreshToken:s,appMetadata:c}},e.prototype.readAccountFromCache=function(e){var t=Tt.generateAccountCacheKey(e);return this.getAccount(t)},e.prototype.readIdTokenFromCache=function(e,t){var r={homeAccountId:t.homeAccountId,environment:t.environment,credentialType:F.ID_TOKEN,clientId:e,realm:t.tenantId},n=this.getCredentialsFilteredBy(r),o=Object.keys(n.idTokens).map((function(e){return n.idTokens[e]})),i=o.length;if(i<1)return null;if(i>1)throw We.createMultipleMatchingTokensInCacheError();return o[0]},e.prototype.readAccessTokenFromCache=function(e,t,r){var n={homeAccountId:t.homeAccountId,environment:t.environment,credentialType:F.ACCESS_TOKEN,clientId:e,realm:t.tenantId,target:r.printScopesLowerCase()},o=this.getCredentialsFilteredBy(n),i=Object.keys(o.accessTokens).map((function(e){return o.accessTokens[e]})),a=i.length;if(a<1)return null;if(a>1)throw We.createMultipleMatchingTokensInCacheError();return i[0]},e.prototype.readRefreshTokenFromCache=function(e,t,r){var n=r?W:void 0,o={homeAccountId:t.homeAccountId,environment:t.environment,credentialType:F.REFRESH_TOKEN,clientId:e,familyId:n},i=this.getCredentialsFilteredBy(o),a=Object.keys(i.refreshTokens).map((function(e){return i.refreshTokens[e]}));return a.length<1?null:a[0]},e.prototype.readAppMetadataFromCache=function(e,t){var r={environment:e,clientId:t},n=this.getAppMetadataFilteredBy(r),o=Object.keys(n).map((function(e){return n[e]})),i=o.length;if(i<1)return null;if(i>1)throw We.createMultipleMatchingAppMetadataInCacheError();return o[0]},e.prototype.isAppMetadataFOCI=function(e,t){var r=this.readAppMetadataFromCache(e,t);return!(!r||r.familyId!==W)},e.prototype.matchHomeAccountId=function(e,t){return!(!e.homeAccountId||t!==e.homeAccountId)},e.prototype.matchOboAssertion=function(e,t){return!(!e.oboAssertion||t!==e.oboAssertion)},e.prototype.matchEnvironment=function(e,t){var r=this.getAuthorityMetadataByAlias(t);return!!(r&&r.aliases.indexOf(e.environment)>-1)},e.prototype.matchCredentialType=function(e,t){return e.credentialType&&t.toLowerCase()===e.credentialType.toLowerCase()},e.prototype.matchClientId=function(e,t){return!(!e.clientId||t!==e.clientId)},e.prototype.matchFamilyId=function(e,t){return!(!e.familyId||t!==e.familyId)},e.prototype.matchRealm=function(e,t){return!(!e.realm||t!==e.realm)},e.prototype.matchTarget=function(e,t){if(e.credentialType!==F.ACCESS_TOKEN||!e.target)return!1;var r=Et.fromString(e.target),n=Et.fromString(t);return n.containsOnlyOIDCScopes()?n.removeScope(A):n.removeOIDCScopes(),r.containsScopeSet(n)},e.prototype.isAppMetadata=function(e){return-1!==e.indexOf(Q)},e.prototype.isAuthorityMetadata=function(e){return-1!==e.indexOf(J)},e.prototype.generateAuthorityMetadataCacheKey=function(e){return J+"-"+this.clientId+"-"+e},e.prototype.getSpecificCredential=function(e,t){switch(t){case F.ID_TOKEN:return this.getIdTokenCredential(e);case F.ACCESS_TOKEN:return this.getAccessTokenCredential(e);case F.REFRESH_TOKEN:return this.getRefreshTokenCredential(e);default:return null}},e.toObject=function(e,t){for(var r in t)e[r]=t[r];return e},e}(),It=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return u(t,e),t.prototype.setAccount=function(){throw ae.createUnexpectedError("Storage interface - setAccount() has not been implemented for the cacheStorage interface.")},t.prototype.getAccount=function(){throw ae.createUnexpectedError("Storage interface - getAccount() has not been implemented for the cacheStorage interface.")},t.prototype.setIdTokenCredential=function(){throw ae.createUnexpectedError("Storage interface - setIdTokenCredential() has not been implemented for the cacheStorage interface.")},t.prototype.getIdTokenCredential=function(){throw ae.createUnexpectedError("Storage interface - getIdTokenCredential() has not been implemented for the cacheStorage interface.")},t.prototype.setAccessTokenCredential=function(){throw ae.createUnexpectedError("Storage interface - setAccessTokenCredential() has not been implemented for the cacheStorage interface.")},t.prototype.getAccessTokenCredential=function(){throw ae.createUnexpectedError("Storage interface - getAccessTokenCredential() has not been implemented for the cacheStorage interface.")},t.prototype.setRefreshTokenCredential=function(){throw ae.createUnexpectedError("Storage interface - setRefreshTokenCredential() has not been implemented for the cacheStorage interface.")},t.prototype.getRefreshTokenCredential=function(){throw ae.createUnexpectedError("Storage interface - getRefreshTokenCredential() has not been implemented for the cacheStorage interface.")},t.prototype.setAppMetadata=function(){throw ae.createUnexpectedError("Storage interface - setAppMetadata() has not been implemented for the cacheStorage interface.")},t.prototype.getAppMetadata=function(){throw ae.createUnexpectedError("Storage interface - getAppMetadata() has not been implemented for the cacheStorage interface.")},t.prototype.setServerTelemetry=function(){throw ae.createUnexpectedError("Storage interface - setServerTelemetry() has not been implemented for the cacheStorage interface.")},t.prototype.getServerTelemetry=function(){throw ae.createUnexpectedError("Storage interface - getServerTelemetry() has not been implemented for the cacheStorage interface.")},t.prototype.setAuthorityMetadata=function(){throw ae.createUnexpectedError("Storage interface - setAuthorityMetadata() has not been implemented for the cacheStorage interface.")},t.prototype.getAuthorityMetadata=function(){throw ae.createUnexpectedError("Storage interface - getAuthorityMetadata() has not been implemented for the cacheStorage interface.")},t.prototype.getAuthorityMetadataKeys=function(){throw ae.createUnexpectedError("Storage interface - getAuthorityMetadataKeys() has not been implemented for the cacheStorage interface.")},t.prototype.setThrottlingCache=function(){throw ae.createUnexpectedError("Storage interface - setThrottlingCache() has not been implemented for the cacheStorage interface.")},t.prototype.getThrottlingCache=function(){throw ae.createUnexpectedError("Storage interface - getThrottlingCache() has not been implemented for the cacheStorage interface.")},t.prototype.removeItem=function(){throw ae.createUnexpectedError("Storage interface - removeItem() has not been implemented for the cacheStorage interface.")},t.prototype.containsKey=function(){throw ae.createUnexpectedError("Storage interface - containsKey() has not been implemented for the cacheStorage interface.")},t.prototype.getKeys=function(){throw ae.createUnexpectedError("Storage interface - getKeys() has not been implemented for the cacheStorage interface.")},t.prototype.clear=function(){throw ae.createUnexpectedError("Storage interface - clear() has not been implemented for the cacheStorage interface.")},t}(St),At={tokenRenewalOffsetSeconds:300},bt={loggerCallback:function(){},piiLoggingEnabled:!1,logLevel:e.LogLevel.Info},Rt={sendGetRequestAsync:function(){return d(this,void 0,void 0,(function(){return l(this,(function(e){throw"Network interface - sendGetRequestAsync() has not been implemented",ae.createUnexpectedError("Network interface - sendGetRequestAsync() has not been implemented")}))}))},sendPostRequestAsync:function(){return d(this,void 0,void 0,(function(){return l(this,(function(e){throw"Network interface - sendPostRequestAsync() has not been implemented",ae.createUnexpectedError("Network interface - sendPostRequestAsync() has not been implemented")}))}))}},_t={sku:E,version:Xe,cpu:"",os:""},kt={clientSecret:"",clientAssertion:void 0};var Ot,Pt=function(e){function t(r,n,o){var i=e.call(this,r,n,o)||this;return i.name="ServerError",Object.setPrototypeOf(i,t.prototype),i}return u(t,e),t}(ae),Nt=function(){function e(){}return e.generateThrottlingStorageKey=function(e){return te+"."+JSON.stringify(e)},e.preProcess=function(t,r){var n,o=e.generateThrottlingStorageKey(r),i=t.getThrottlingCache(o);if(i){if(i.throttleTime<Date.now())return void t.removeItem(o,B.THROTTLING);throw new Pt((null===(n=i.errorCodes)||void 0===n?void 0:n.join(" "))||P,i.errorMessage,i.subError)}},e.postProcess=function(t,r,n){if(e.checkResponseStatus(n)||e.checkResponseForRetryAfter(n)){var o={throttleTime:e.calculateThrottleTime(parseInt(n.headers[f.RETRY_AFTER])),error:n.body.error,errorCodes:n.body.error_codes,errorMessage:n.body.error_description,subError:n.body.suberror};t.setThrottlingCache(e.generateThrottlingStorageKey(r),o)}},e.checkResponseStatus=function(e){return 429===e.status||e.status>=500&&e.status<600},e.checkResponseForRetryAfter=function(e){return!!e.headers&&(e.headers.hasOwnProperty(f.RETRY_AFTER)&&(e.status<200||e.status>=300))},e.calculateThrottleTime=function(e){e<=0&&(e=0);var t=Date.now()/1e3;return Math.floor(1e3*Math.min(t+(e||$),t+ee))},e.removeThrottle=function(e,t,r,n,o){var i={clientId:t,authority:r,scopes:n,homeAccountIdentifier:o},a=this.generateThrottlingStorageKey(i);return e.removeItem(a,B.THROTTLING)},e}(),Ut=function(){function e(e,t){this.networkClient=e,this.cacheManager=t}return e.prototype.sendPostRequest=function(e,t,r){return d(this,void 0,void 0,(function(){var n,o;return l(this,(function(i){switch(i.label){case 0:Nt.preProcess(this.cacheManager,e),i.label=1;case 1:return i.trys.push([1,3,,4]),[4,this.networkClient.sendPostRequestAsync(t,r)];case 2:return n=i.sent(),[3,4];case 3:throw(o=i.sent())instanceof ae?o:We.createNetworkError(t,o);case 4:return Nt.postProcess(this.cacheManager,e,n),[2,n]}}))}))},e}(),qt=function(){function e(e){var t,r,n,o,i,a,s,c,u,d,l,p,f;this.config=(n=(t=e).authOptions,o=t.systemOptions,i=t.loggerOptions,a=t.storageInterface,s=t.networkInterface,c=t.cryptoInterface,u=t.clientCredentials,d=t.libraryInfo,l=t.serverTelemetryManager,p=t.persistencePlugin,f=t.serializableCache,{authOptions:(r=n,h({clientCapabilities:[]},r)),systemOptions:h(h({},At),o),loggerOptions:h(h({},bt),i),storageInterface:a||new It(n.clientId,se),networkInterface:s||Rt,cryptoInterface:c||se,clientCredentials:u||kt,libraryInfo:h(h({},_t),d),serverTelemetryManager:l||null,persistencePlugin:p||null,serializableCache:f||null}),this.logger=new Ye(this.config.loggerOptions,"@azure/msal-common",Xe),this.cryptoUtils=this.config.cryptoInterface,this.cacheManager=this.config.storageInterface,this.networkClient=this.config.networkInterface,this.networkManager=new Ut(this.networkClient,this.cacheManager),this.serverTelemetryManager=this.config.serverTelemetryManager,this.authority=this.config.authOptions.authority}return e.prototype.createDefaultTokenRequestHeaders=function(){var e=this.createDefaultLibraryHeaders();return e[f.CONTENT_TYPE]=_,e[f.X_MS_LIB_CAPABILITY]=f.X_MS_LIB_CAPABILITY_VALUE,this.serverTelemetryManager&&(e[f.X_CLIENT_CURR_TELEM]=this.serverTelemetryManager.generateCurrentRequestHeaderValue(),e[f.X_CLIENT_LAST_TELEM]=this.serverTelemetryManager.generateLastRequestHeaderValue()),e},e.prototype.createDefaultLibraryHeaders=function(){var e={};return e[m.X_CLIENT_SKU]=this.config.libraryInfo.sku,e[m.X_CLIENT_VER]=this.config.libraryInfo.version,e[m.X_CLIENT_OS]=this.config.libraryInfo.os,e[m.X_CLIENT_CPU]=this.config.libraryInfo.cpu,e},e.prototype.executePostToTokenEndpoint=function(e,t,r,n){return d(this,void 0,void 0,(function(){var o;return l(this,(function(i){switch(i.label){case 0:return[4,this.networkManager.sendPostRequest(n,e,{body:t,headers:r})];case 1:return o=i.sent(),this.config.serverTelemetryManager&&o.status<500&&429!==o.status&&this.config.serverTelemetryManager.clearTelemetryCache(),[2,o]}}))}))},e.prototype.updateAuthority=function(e){if(!e.discoveryComplete())throw We.createEndpointDiscoveryIncompleteError("Updated authority has not completed endpoint discovery.");this.authority=e},e}(),Mt=function(){function e(){}return e.validateRedirectUri=function(e){if(Je.isEmpty(e))throw vt.createRedirectUriEmptyError()},e.validatePrompt=function(e){if([L.LOGIN,L.SELECT_ACCOUNT,L.CONSENT,L.NONE].indexOf(e)<0)throw vt.createInvalidPromptError(e)},e.validateClaims=function(e){try{JSON.parse(e)}catch(e){throw vt.createInvalidClaimsRequestError()}},e.validateCodeChallengeParams=function(e,t){if(Je.isEmpty(e)||Je.isEmpty(t))throw vt.createInvalidCodeChallengeParamsError();this.validateCodeChallengeMethod(t)},e.validateCodeChallengeMethod=function(e){if([j.PLAIN,j.S256].indexOf(e)<0)throw vt.createInvalidCodeChallengeMethodError()},e.sanitizeEQParams=function(e,t){return e?(t.forEach((function(t,r){e[r]&&delete e[r]})),e):{}},e}(),Lt=function(){function t(){this.parameters=new Map}return t.prototype.addResponseTypeCode=function(){this.parameters.set(m.RESPONSE_TYPE,encodeURIComponent(b))},t.prototype.addResponseMode=function(e){this.parameters.set(m.RESPONSE_MODE,encodeURIComponent(e||D.QUERY))},t.prototype.addScopes=function(e,t){void 0===t&&(t=!0);var r=t?p(e||[],U):e||[],n=new Et(r);this.parameters.set(m.SCOPE,encodeURIComponent(n.printScopes()))},t.prototype.addClientId=function(e){this.parameters.set(m.CLIENT_ID,encodeURIComponent(e))},t.prototype.addRedirectUri=function(e){Mt.validateRedirectUri(e),this.parameters.set(m.REDIRECT_URI,encodeURIComponent(e))},t.prototype.addPostLogoutRedirectUri=function(e){Mt.validateRedirectUri(e),this.parameters.set(m.POST_LOGOUT_URI,encodeURIComponent(e))},t.prototype.addIdTokenHint=function(e){this.parameters.set(m.ID_TOKEN_HINT,encodeURIComponent(e))},t.prototype.addDomainHint=function(e){this.parameters.set(M.DOMAIN_HINT,encodeURIComponent(e))},t.prototype.addLoginHint=function(e){this.parameters.set(M.LOGIN_HINT,encodeURIComponent(e))},t.prototype.addSid=function(e){this.parameters.set(M.SID,encodeURIComponent(e))},t.prototype.addClaims=function(e,t){var r=this.addClientCapabilitiesToClaims(e,t);Mt.validateClaims(r),this.parameters.set(m.CLAIMS,encodeURIComponent(r))},t.prototype.addCorrelationId=function(e){this.parameters.set(m.CLIENT_REQUEST_ID,encodeURIComponent(e))},t.prototype.addLibraryInfo=function(e){this.parameters.set(m.X_CLIENT_SKU,e.sku),this.parameters.set(m.X_CLIENT_VER,e.version),this.parameters.set(m.X_CLIENT_OS,e.os),this.parameters.set(m.X_CLIENT_CPU,e.cpu)},t.prototype.addPrompt=function(e){Mt.validatePrompt(e),this.parameters.set(""+m.PROMPT,encodeURIComponent(e))},t.prototype.addState=function(e){Je.isEmpty(e)||this.parameters.set(m.STATE,encodeURIComponent(e))},t.prototype.addNonce=function(e){this.parameters.set(m.NONCE,encodeURIComponent(e))},t.prototype.addCodeChallengeParams=function(e,t){if(Mt.validateCodeChallengeParams(e,t),!e||!t)throw vt.createInvalidCodeChallengeParamsError();this.parameters.set(m.CODE_CHALLENGE,encodeURIComponent(e)),this.parameters.set(m.CODE_CHALLENGE_METHOD,encodeURIComponent(t))},t.prototype.addAuthorizationCode=function(e){this.parameters.set(m.CODE,encodeURIComponent(e))},t.prototype.addDeviceCode=function(e){this.parameters.set(m.DEVICE_CODE,encodeURIComponent(e))},t.prototype.addRefreshToken=function(e){this.parameters.set(m.REFRESH_TOKEN,encodeURIComponent(e))},t.prototype.addCodeVerifier=function(e){this.parameters.set(m.CODE_VERIFIER,encodeURIComponent(e))},t.prototype.addClientSecret=function(e){this.parameters.set(m.CLIENT_SECRET,encodeURIComponent(e))},t.prototype.addClientAssertion=function(e){this.parameters.set(m.CLIENT_ASSERTION,encodeURIComponent(e))},t.prototype.addClientAssertionType=function(e){this.parameters.set(m.CLIENT_ASSERTION_TYPE,encodeURIComponent(e))},t.prototype.addOboAssertion=function(e){this.parameters.set(m.OBO_ASSERTION,encodeURIComponent(e))},t.prototype.addRequestTokenUse=function(e){this.parameters.set(m.REQUESTED_TOKEN_USE,encodeURIComponent(e))},t.prototype.addGrantType=function(e){this.parameters.set(m.GRANT_TYPE,encodeURIComponent(e))},t.prototype.addClientInfo=function(){this.parameters.set("client_info","1")},t.prototype.addExtraQueryParameters=function(e){var t=this;Mt.sanitizeEQParams(e,this.parameters),Object.keys(e).forEach((function(r){t.parameters.set(r,e[r])}))},t.prototype.addClientCapabilitiesToClaims=function(e,t){var r;if(e)try{r=JSON.parse(e)}catch(e){throw vt.createInvalidClaimsRequestError()}else r={};return t&&t.length>0&&(r.hasOwnProperty(v.ACCESS_TOKEN)||(r[v.ACCESS_TOKEN]={}),r[v.ACCESS_TOKEN][v.XMS_CC]={values:t}),JSON.stringify(r)},t.prototype.addUsername=function(e){this.parameters.set(Z.username,e)},t.prototype.addPassword=function(e){this.parameters.set(Z.password,e)},t.prototype.addPopToken=function(t){Je.isEmpty(t)||(this.parameters.set(m.TOKEN_TYPE,e.AuthenticationScheme.POP),this.parameters.set(m.REQ_CNF,encodeURIComponent(t)))},t.prototype.createQueryString=function(){var e=new Array;return this.parameters.forEach((function(t,r){e.push(r+"="+t)})),e.join("&")},t}(),Dt=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return u(t,e),t.createIdTokenEntity=function(e,r,n,o,i,a){var s=new t;return s.credentialType=F.ID_TOKEN,s.homeAccountId=e,s.environment=r,s.clientId=o,s.secret=n,s.realm=i,s.oboAssertion=a,s},t.isIdTokenEntity=function(e){return!!e&&(e.hasOwnProperty("homeAccountId")&&e.hasOwnProperty("environment")&&e.hasOwnProperty("credentialType")&&e.hasOwnProperty("realm")&&e.hasOwnProperty("clientId")&&e.hasOwnProperty("secret")&&e.credentialType===F.ID_TOKEN)},t}(Ze),xt=function(){function e(){}return e.nowSeconds=function(){return Math.round((new Date).getTime()/1e3)},e.isTokenExpired=function(t,r){var n=Number(t)||0;return e.nowSeconds()+r>n},e}(),Ht=function(t){function r(){return null!==t&&t.apply(this,arguments)||this}return u(r,t),r.createAccessTokenEntity=function(t,n,o,i,a,s,c,u,h,d){var l=new r;l.homeAccountId=t,l.credentialType=F.ACCESS_TOKEN,l.secret=o;var p=xt.nowSeconds();return l.cachedAt=p.toString(),l.expiresOn=c.toString(),l.extendedExpiresOn=u.toString(),l.environment=n,l.clientId=i,l.realm=a,l.target=s,l.oboAssertion=d,l.tokenType=Je.isEmpty(h)?e.AuthenticationScheme.BEARER:h,l},r.isAccessTokenEntity=function(e){return!!e&&(e.hasOwnProperty("homeAccountId")&&e.hasOwnProperty("environment")&&e.hasOwnProperty("credentialType")&&e.hasOwnProperty("realm")&&e.hasOwnProperty("clientId")&&e.hasOwnProperty("secret")&&e.hasOwnProperty("target")&&e.credentialType===F.ACCESS_TOKEN)},r}(Ze),Kt=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return u(t,e),t.createRefreshTokenEntity=function(e,r,n,o,i,a){var s=new t;return s.clientId=o,s.credentialType=F.REFRESH_TOKEN,s.environment=r,s.homeAccountId=e,s.secret=n,s.oboAssertion=a,i&&(s.familyId=i),s},t.isRefreshTokenEntity=function(e){return!!e&&(e.hasOwnProperty("homeAccountId")&&e.hasOwnProperty("environment")&&e.hasOwnProperty("credentialType")&&e.hasOwnProperty("clientId")&&e.hasOwnProperty("secret")&&e.credentialType===F.REFRESH_TOKEN)},t}(Ze),Ft=["interaction_required","consent_required","login_required"],Bt=["message_only","additional_action","basic_action","user_password_expired","consent_required"],Gt=function(e){function t(r,n,o){var i=e.call(this,r,n,o)||this;return i.name="InteractionRequiredAuthError",Object.setPrototypeOf(i,t.prototype),i}return u(t,e),t.isInteractionRequiredError=function(e,t,r){var n=!!e&&Ft.indexOf(e)>-1,o=!!r&&Bt.indexOf(r)>-1,i=!!t&&Ft.some((function(e){return t.indexOf(e)>-1}));return n||i||o},t}(Pt),jt=function(e,t,r,n,o){this.account=e||null,this.idToken=t||null,this.accessToken=r||null,this.refreshToken=n||null,this.appMetadata=o||null},zt=function(){function e(){}return e.setRequestState=function(t,r,n){var o=e.generateLibraryState(t,n);return Je.isEmpty(r)?o:""+o+I+r},e.generateLibraryState=function(e,t){if(!e)throw We.createNoCryptoObjectError("generateLibraryState");var r={id:e.createNewGuid()};t&&(r.meta=t);var n=JSON.stringify(r);return e.base64Encode(n)},e.parseRequestState=function(e,t){if(!e)throw We.createNoCryptoObjectError("parseRequestState");if(Je.isEmpty(t))throw We.createInvalidStateError(t,"Null, undefined or empty state");try{var r=decodeURIComponent(t).split(I),n=r[0],o=r.length>1?r.slice(1).join(I):"",i=e.base64Decode(n),a=JSON.parse(i);return{userRequestState:Je.isEmpty(o)?"":o,libraryState:a}}catch(e){throw We.createInvalidStateError(t,e)}},e}(),Qt=function(){function e(t){if(this._urlString=t,Je.isEmpty(this._urlString))throw vt.createUrlEmptyError();Je.isEmpty(this.getHash())&&(this._urlString=e.canonicalizeUri(t))}return Object.defineProperty(e.prototype,"urlString",{get:function(){return this._urlString},enumerable:!0,configurable:!0}),e.canonicalizeUri=function(e){return e&&(e=e.toLowerCase(),Je.endsWith(e,"?")?e=e.slice(0,-1):Je.endsWith(e,"?/")&&(e=e.slice(0,-2)),Je.endsWith(e,"/")||(e+="/")),e},e.prototype.validateAsUri=function(){var e;try{e=this.getUrlComponents()}catch(e){throw vt.createUrlParseError(e)}if(!e.HostNameAndPort||!e.PathSegments)throw vt.createUrlParseError("Given url string: "+this.urlString);if(!e.Protocol||"https:"!==e.Protocol.toLowerCase())throw vt.createInsecureAuthorityUriError(this.urlString)},e.prototype.urlRemoveQueryStringParameter=function(e){var t=new RegExp("(\\&"+e+"=)[^&]+");return this._urlString=this.urlString.replace(t,""),t=new RegExp("("+e+"=)[^&]+&"),this._urlString=this.urlString.replace(t,""),t=new RegExp("("+e+"=)[^&]+"),this._urlString=this.urlString.replace(t,""),this.urlString},e.removeHashFromUrl=function(t){return e.canonicalizeUri(t.split("#")[0])},e.prototype.replaceTenantPath=function(t){var r=this.getUrlComponents(),n=r.PathSegments;return!t||0===n.length||n[0]!==y.COMMON&&n[0]!==y.ORGANIZATIONS||(n[0]=t),e.constructAuthorityUriFromObject(r)},e.prototype.getHash=function(){return e.parseHash(this.urlString)},e.prototype.getUrlComponents=function(){var e=RegExp("^(([^:/?#]+):)?(//([^/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?"),t=this.urlString.match(e);if(!t)throw vt.createUrlParseError("Given url string: "+this.urlString);var r={Protocol:t[1],HostNameAndPort:t[4],AbsolutePath:t[5],QueryString:t[7]},n=r.AbsolutePath.split("/");return n=n.filter((function(e){return e&&e.length>0})),r.PathSegments=n,!Je.isEmpty(r.QueryString)&&r.QueryString.endsWith("/")&&(r.QueryString=r.QueryString.substring(0,r.QueryString.length-1)),r},e.getDomainFromUrl=function(e){var t=RegExp("^([^:/?#]+://)?([^/?#]*)"),r=e.match(t);if(!r)throw vt.createUrlParseError("Given url string: "+e);return r[2]},e.getAbsoluteUrl=function(t,r){if(t[0]===N){var n=new e(r).getUrlComponents();return n.Protocol+"//"+n.HostNameAndPort+t}return t},e.parseHash=function(e){var t=e.indexOf("#"),r=e.indexOf("#/");return r>-1?e.substring(r+2):t>-1?e.substring(t+1):""},e.constructAuthorityUriFromObject=function(t){return new e(t.Protocol+"//"+t.HostNameAndPort+"/"+t.PathSegments.join("/"))},e.getDeserializedHash=function(t){if(Je.isEmpty(t))return{};var r=e.parseHash(t),n=Je.queryStringToObject(Je.isEmpty(r)?t:r);if(!n)throw We.createHashNotDeserializedError(JSON.stringify(n));return n},e.hashContainsKnownProperties=function(t){if(Je.isEmpty(t))return!1;var r=e.getDeserializedHash(t);return!!(r.code||r.error_description||r.error||r.state)},e}();!function(e){e.SW="sw",e.UHW="uhw"}(Ot||(Ot={}));var Wt,Jt=function(){function e(e){this.cryptoUtils=e}return e.prototype.generateCnf=function(e,t){return d(this,void 0,void 0,(function(){var r,n;return l(this,(function(o){switch(o.label){case 0:return[4,this.cryptoUtils.getPublicKeyThumbprint(e,t)];case 1:return r=o.sent(),n={kid:r,xms_ksl:Ot.SW},[2,this.cryptoUtils.base64Encode(JSON.stringify(n))]}}))}))},e.prototype.signPopToken=function(e,t,r,n){var o;return d(this,void 0,void 0,(function(){var i,a,s;return l(this,(function(c){switch(c.label){case 0:if(i=wt.extractTokenClaims(e,this.cryptoUtils),a=new Qt(r),s=a.getUrlComponents(),!(null===(o=null==i?void 0:i.cnf)||void 0===o?void 0:o.kid))throw We.createTokenClaimsRequiredError();return[4,this.cryptoUtils.signJwt({at:e,ts:""+xt.nowSeconds(),m:t.toUpperCase(),u:s.HostNameAndPort||"",nonce:this.cryptoUtils.createNewGuid(),p:s.AbsolutePath,q:[[],s.QueryString],client_claims:n||void 0},i.cnf.kid)];case 1:return[2,c.sent()]}}))}))},e}(),Vt=function(){function e(){}return e.prototype.generateAppMetadataKey=function(){return e.generateAppMetadataCacheKey(this.environment,this.clientId)},e.generateAppMetadataCacheKey=function(e,t){return[Q,e,t].join(K.CACHE_KEY_SEPARATOR).toLowerCase()},e.createAppMetadataEntity=function(t,r,n){var o=new e;return o.clientId=t,o.environment=r,n&&(o.familyId=n),o},e.isAppMetadataEntity=function(e,t){return!!t&&(0===e.indexOf(Q)&&t.hasOwnProperty("clientId")&&t.hasOwnProperty("environment"))},e}(),Yt=function(){function e(e,t){this.cache=e,this.hasChanged=t}return Object.defineProperty(e.prototype,"cacheHasChanged",{get:function(){return this.hasChanged},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"tokenCache",{get:function(){return this.cache},enumerable:!0,configurable:!0}),e}(),Xt=function(){function t(e,t,r,n,o,i){this.clientId=e,this.cacheStorage=t,this.cryptoObj=r,this.logger=n,this.serializableCache=o,this.persistencePlugin=i}return t.prototype.validateServerAuthorizationCodeResponse=function(e,t,r){if(!e.state||!t)throw e.state?We.createStateNotFoundError("Cached State"):We.createStateNotFoundError("Server State");if(decodeURIComponent(e.state)!==decodeURIComponent(t))throw We.createStateMismatchError();if(e.error||e.error_description||e.suberror){if(Gt.isInteractionRequiredError(e.error,e.error_description,e.suberror))throw new Gt(e.error||P,e.error_description,e.suberror);throw new Pt(e.error||P,e.error_description,e.suberror)}e.client_info&&Ct(e.client_info,r)},t.prototype.validateTokenResponse=function(e){if(e.error||e.error_description||e.suberror){if(Gt.isInteractionRequiredError(e.error,e.error_description,e.suberror))throw new Gt(e.error,e.error_description,e.suberror);var t=e.error_codes+" - ["+e.timestamp+"]: "+e.error_description+" - Correlation ID: "+e.correlation_id+" - Trace ID: "+e.trace_id;throw new Pt(e.error,t)}},t.prototype.handleServerTokenResponse=function(e,r,n,o,i,a,s){return d(this,void 0,void 0,(function(){var c,u,h,d,p;return l(this,(function(l){switch(l.label){case 0:if(e.id_token&&(c=new wt(e.id_token||P,this.cryptoObj),i&&!Je.isEmpty(i.nonce)&&c.claims.nonce!==i.nonce))throw We.createNonceMismatchError();this.homeAccountIdentifier=Tt.generateHomeAccountId(e.client_info||P,r.authorityType,this.logger,this.cryptoObj,c),i&&i.state&&(u=zt.parseRequestState(this.cryptoObj,i.state)),h=this.generateCacheRecord(e,r,n,c,o.scopes,a,i),l.label=1;case 1:return l.trys.push([1,,4,7]),this.persistencePlugin&&this.serializableCache?(this.logger.verbose("Persistence enabled, calling beforeCacheAccess"),d=new Yt(this.serializableCache,!0),[4,this.persistencePlugin.beforeCacheAccess(d)]):[3,3];case 2:l.sent(),l.label=3;case 3:return!s||!h.account||(p=h.account.generateAccountKey(),this.cacheStorage.getAccount(p))?(this.cacheStorage.saveCacheRecord(h),[3,7]):(this.logger.warning("Account used to refresh tokens not in persistence, refreshed tokens will not be stored in the cache"),[2,t.generateAuthenticationResult(this.cryptoObj,r,h,!1,o,c,u)]);case 4:return this.persistencePlugin&&this.serializableCache&&d?(this.logger.verbose("Persistence enabled, calling afterCacheAccess"),[4,this.persistencePlugin.afterCacheAccess(d)]):[3,6];case 5:l.sent(),l.label=6;case 6:return[7];case 7:return[2,t.generateAuthenticationResult(this.cryptoObj,r,h,!1,o,c,u)]}}))}))},t.prototype.generateCacheRecord=function(e,t,r,n,o,i,a){var s,c,u=t.getPreferredCache();if(Je.isEmpty(u))throw We.createInvalidCacheEnvironmentError();!Je.isEmpty(e.id_token)&&n&&(s=Dt.createIdTokenEntity(this.homeAccountIdentifier,u,e.id_token||P,this.clientId,n.claims.tid||P,i),c=this.generateAccountEntity(e,n,t,i,a));var h=null;if(!Je.isEmpty(e.access_token)){var d=e.scope?Et.fromString(e.scope):new Et(o||[]),l=r+(("string"==typeof e.expires_in?parseInt(e.expires_in,10):e.expires_in)||0),p=l+(("string"==typeof e.ext_expires_in?parseInt(e.ext_expires_in,10):e.ext_expires_in)||0);h=Ht.createAccessTokenEntity(this.homeAccountIdentifier,u,e.access_token||P,this.clientId,n?n.claims.tid||P:t.tenant,d.printScopes(),l,p,e.token_type,i)}var f=null;Je.isEmpty(e.refresh_token)||(f=Kt.createRefreshTokenEntity(this.homeAccountIdentifier,u,e.refresh_token||P,this.clientId,e.foci,i));var g=null;return Je.isEmpty(e.foci)||(g=Vt.createAppMetadataEntity(this.clientId,u,e.foci)),new jt(c,s,h,f,g)},t.prototype.generateAccountEntity=function(e,t,r,n,o){var i=r.authorityType,a=o?o.cloud_graph_host_name:"",s=o?o.msgraph_host:"";if(i===Ve.Adfs)return this.logger.verbose("Authority type is ADFS, creating ADFS account"),Tt.createGenericAccount(r,this.homeAccountIdentifier,t,n,a,s);if(Je.isEmpty(e.client_info)&&"AAD"===r.protocolMode)throw We.createClientInfoEmptyError();return e.client_info?Tt.createAccount(e.client_info,this.homeAccountIdentifier,r,t,n,a,s):Tt.createGenericAccount(r,this.homeAccountIdentifier,t,n,a,s)},t.generateAuthenticationResult=function(t,r,n,o,i,a,s){var c,u,h;return d(this,void 0,void 0,(function(){var d,p,f,g,y,m,v,E;return l(this,(function(l){switch(l.label){case 0:if(d="",p=[],f=null,y=P,!n.accessToken)return[3,4];if(n.accessToken.tokenType!==e.AuthenticationScheme.POP)return[3,2];if(m=new Jt(t),!i.resourceRequestMethod||!i.resourceRequestUri)throw vt.createResourceRequestParametersRequiredError();return[4,m.signPopToken(n.accessToken.secret,i.resourceRequestMethod,i.resourceRequestUri,i.shrClaims)];case 1:return d=l.sent(),[3,3];case 2:d=n.accessToken.secret,l.label=3;case 3:p=Et.fromString(n.accessToken.target).asArray(),f=new Date(1e3*Number(n.accessToken.expiresOn)),g=new Date(1e3*Number(n.accessToken.extendedExpiresOn)),l.label=4;case 4:return n.appMetadata&&(y=n.appMetadata.familyId===W?W:P),v=(null==a?void 0:a.claims.oid)||(null==a?void 0:a.claims.sub)||P,E=(null==a?void 0:a.claims.tid)||P,[2,{authority:r.canonicalAuthority,uniqueId:v,tenantId:E,scopes:p,account:n.account?n.account.getAccountInfo():null,idToken:a?a.rawToken:P,idTokenClaims:a?a.claims:{},accessToken:d,fromCache:o,expiresOn:f,extExpiresOn:g,familyId:y,tokenType:(null===(c=n.accessToken)||void 0===c?void 0:c.tokenType)||P,state:s?s.userRequestState:P,cloudGraphHostName:(null===(u=n.account)||void 0===u?void 0:u.cloudGraphHostName)||P,msGraphHost:(null===(h=n.account)||void 0===h?void 0:h.msGraphHost)||P}]}}))}))},t}(),Zt=function(t){function r(e){return t.call(this,e)||this}return u(r,t),r.prototype.getAuthCodeUrl=function(e){return d(this,void 0,void 0,(function(){var t;return l(this,(function(r){return t=this.createAuthCodeUrlQueryString(e),[2,this.authority.authorizationEndpoint+"?"+t]}))}))},r.prototype.acquireToken=function(e,t){return d(this,void 0,void 0,(function(){var r,n,o;return l(this,(function(i){switch(i.label){case 0:if(this.logger.info("in acquireToken call"),!e||Je.isEmpty(e.code))throw We.createTokenRequestCannotBeMadeError();return r=xt.nowSeconds(),[4,this.executeTokenRequest(this.authority,e)];case 1:return n=i.sent(),(o=new Xt(this.config.authOptions.clientId,this.cacheManager,this.cryptoUtils,this.logger,this.config.serializableCache,this.config.persistencePlugin)).validateTokenResponse(n.body),[4,o.handleServerTokenResponse(n.body,this.authority,r,e,t)];case 2:return[2,i.sent()]}}))}))},r.prototype.handleFragmentResponse=function(e,t){var r=new Xt(this.config.authOptions.clientId,this.cacheManager,this.cryptoUtils,this.logger,null,null),n=new Qt(e),o=Qt.getDeserializedHash(n.getHash());if(r.validateServerAuthorizationCodeResponse(o,t,this.cryptoUtils),!o.code)throw We.createNoAuthCodeInServerResponseError();return h(h({},o),{code:o.code})},r.prototype.getLogoutUri=function(e){if(!e)throw vt.createEmptyLogoutRequestError();e.account?this.cacheManager.removeAccount(Tt.generateAccountCacheKey(e.account)):this.cacheManager.clear();var t=this.createLogoutUrlQueryString(e);return Je.isEmpty(t)?this.authority.endSessionEndpoint:this.authority.endSessionEndpoint+"?"+t},r.prototype.executeTokenRequest=function(e,t){return d(this,void 0,void 0,(function(){var r,n,o,i,a;return l(this,(function(s){switch(s.label){case 0:return r={clientId:this.config.authOptions.clientId,authority:e.canonicalAuthority,scopes:t.scopes},[4,this.createTokenRequestBody(t)];case 1:return n=s.sent(),o=this.createTokenQueryParameters(t),i=this.createDefaultTokenRequestHeaders(),a=Je.isEmpty(o)?e.tokenEndpoint:e.tokenEndpoint+"?"+o,[2,this.executePostToTokenEndpoint(a,n,i,r)]}}))}))},r.prototype.createTokenQueryParameters=function(e){var t=new Lt;return e.tokenQueryParameters&&t.addExtraQueryParameters(e.tokenQueryParameters),t.createQueryString()},r.prototype.createTokenRequestBody=function(t){return d(this,void 0,void 0,(function(){var r,n,o,i;return l(this,(function(a){switch(a.label){case 0:return(r=new Lt).addClientId(this.config.authOptions.clientId),r.addRedirectUri(t.redirectUri),r.addScopes(t.scopes),r.addAuthorizationCode(t.code),t.codeVerifier&&r.addCodeVerifier(t.codeVerifier),this.config.clientCredentials.clientSecret&&r.addClientSecret(this.config.clientCredentials.clientSecret),this.config.clientCredentials.clientAssertion&&(n=this.config.clientCredentials.clientAssertion,r.addClientAssertion(n.assertion),r.addClientAssertionType(n.assertionType)),r.addGrantType(x.AUTHORIZATION_CODE_GRANT),r.addClientInfo(),t.authenticationScheme===e.AuthenticationScheme.POP&&t.resourceRequestMethod&&t.resourceRequestUri?[4,new Jt(this.cryptoUtils).generateCnf(t.resourceRequestMethod,t.resourceRequestUri)]:[3,2];case 1:o=a.sent(),r.addPopToken(o),a.label=2;case 2:return i=t.correlationId||this.config.cryptoInterface.createNewGuid(),r.addCorrelationId(i),(!Je.isEmpty(t.claims)||this.config.authOptions.clientCapabilities&&this.config.authOptions.clientCapabilities.length>0)&&r.addClaims(t.claims,this.config.authOptions.clientCapabilities),[2,r.createQueryString()]}}))}))},r.prototype.createAuthCodeUrlQueryString=function(e){var t=new Lt;t.addClientId(this.config.authOptions.clientId);var r=p(e.scopes||[],e.extraScopesToConsent||[]);t.addScopes(r),t.addRedirectUri(e.redirectUri);var n=e.correlationId||this.config.cryptoInterface.createNewGuid();if(t.addCorrelationId(n),t.addResponseMode(e.responseMode),t.addResponseTypeCode(),t.addLibraryInfo(this.config.libraryInfo),t.addClientInfo(),e.codeChallenge&&e.codeChallengeMethod&&t.addCodeChallengeParams(e.codeChallenge,e.codeChallengeMethod),e.prompt&&t.addPrompt(e.prompt),e.domainHint&&t.addDomainHint(e.domainHint),e.prompt!==L.SELECT_ACCOUNT)if(e.sid&&e.prompt===L.NONE)this.logger.verbose("createAuthCodeUrlQueryString: Prompt is none, adding sid from request"),t.addSid(e.sid);else if(e.account){var o=this.extractAccountSid(e.account);o&&e.prompt===L.NONE?(this.logger.verbose("createAuthCodeUrlQueryString: Prompt is none, adding sid from account"),t.addSid(o)):e.loginHint?(this.logger.verbose("createAuthCodeUrlQueryString: Adding login_hint from request"),t.addLoginHint(e.loginHint)):e.account.username&&(this.logger.verbose("createAuthCodeUrlQueryString: Adding login_hint from account"),t.addLoginHint(e.account.username))}else e.loginHint&&(this.logger.verbose("createAuthCodeUrlQueryString: No account, adding login_hint from request"),t.addLoginHint(e.loginHint));else this.logger.verbose("createAuthCodeUrlQueryString: Prompt is select_account, ignoring account hints");return e.nonce&&t.addNonce(e.nonce),e.state&&t.addState(e.state),(!Je.isEmpty(e.claims)||this.config.authOptions.clientCapabilities&&this.config.authOptions.clientCapabilities.length>0)&&t.addClaims(e.claims,this.config.authOptions.clientCapabilities),e.extraQueryParameters&&t.addExtraQueryParameters(e.extraQueryParameters),t.createQueryString()},r.prototype.createLogoutUrlQueryString=function(e){var t=new Lt;return e.postLogoutRedirectUri&&t.addPostLogoutRedirectUri(e.postLogoutRedirectUri),e.correlationId&&t.addCorrelationId(e.correlationId),e.idTokenHint&&t.addIdTokenHint(e.idTokenHint),t.createQueryString()},r.prototype.extractAccountSid=function(e){return e.idTokenClaims&&e.idTokenClaims.sid||null},r}(qt),$t=(function(e){function t(t){return e.call(this,t)||this}u(t,e),t.prototype.acquireToken=function(e){return d(this,void 0,void 0,(function(){var t,r,n,o;return l(this,(function(i){switch(i.label){case 0:return[4,this.getDeviceCode(e)];case 1:return t=i.sent(),e.deviceCodeCallback(t),r=xt.nowSeconds(),[4,this.acquireTokenWithDeviceCode(e,t)];case 2:return n=i.sent(),(o=new Xt(this.config.authOptions.clientId,this.cacheManager,this.cryptoUtils,this.logger,this.config.serializableCache,this.config.persistencePlugin)).validateTokenResponse(n),[4,o.handleServerTokenResponse(n,this.authority,r,e)];case 3:return[2,i.sent()]}}))}))},t.prototype.getDeviceCode=function(e){return d(this,void 0,void 0,(function(){var t,r,n;return l(this,(function(o){return t=this.createQueryString(e),r=this.createDefaultTokenRequestHeaders(),n={clientId:this.config.authOptions.clientId,authority:e.authority,scopes:e.scopes},[2,this.executePostRequestToDeviceCodeEndpoint(this.authority.deviceCodeEndpoint,t,r,n)]}))}))},t.prototype.executePostRequestToDeviceCodeEndpoint=function(e,t,r,n){return d(this,void 0,void 0,(function(){var o,i,a,s,c,u,h;return l(this,(function(d){switch(d.label){case 0:return[4,this.networkManager.sendPostRequest(n,e,{body:t,headers:r})];case 1:return o=d.sent().body,i=o.user_code,a=o.device_code,s=o.verification_uri,c=o.expires_in,u=o.interval,h=o.message,[2,{userCode:i,deviceCode:a,verificationUri:s,expiresIn:c,interval:u,message:h}]}}))}))},t.prototype.createQueryString=function(e){var t=new Lt;return t.addScopes(e.scopes),t.addClientId(this.config.authOptions.clientId),(!Je.isEmpty(e.claims)||this.config.authOptions.clientCapabilities&&this.config.authOptions.clientCapabilities.length>0)&&t.addClaims(e.claims,this.config.authOptions.clientCapabilities),t.createQueryString()},t.prototype.acquireTokenWithDeviceCode=function(e,t){return d(this,void 0,void 0,(function(){var r,n,o,i,a,s=this;return l(this,(function(c){return r=this.createTokenRequestBody(e,t),n=this.createDefaultTokenRequestHeaders(),o=e.timeout?xt.nowSeconds()+e.timeout:void 0,i=xt.nowSeconds()+t.expiresIn,a=1e3*t.interval,[2,new Promise((function(t,c){var u=setInterval((function(){return d(s,void 0,void 0,(function(){var a,s,h;return l(this,(function(d){switch(d.label){case 0:return d.trys.push([0,6,,7]),e.cancel?(this.logger.error("Token request cancelled by setting DeviceCodeRequest.cancel = true"),clearInterval(u),c(We.createDeviceCodeCancelledError()),[3,5]):[3,1];case 1:return o&&o<i&&xt.nowSeconds()>o?(this.logger.error("User defined timeout for device code polling reached. The timeout was set for "+o),clearInterval(u),c(We.createUserTimeoutReachedError()),[3,5]):[3,2];case 2:return xt.nowSeconds()>i?(o&&this.logger.verbose("User specified timeout ignored as the device code has expired before the timeout elapsed. The user specified timeout was set for "+o),this.logger.error("Device code expired. Expiration time of device code was "+i),clearInterval(u),c(We.createDeviceCodeExpiredError()),[3,5]):[3,3];case 3:return a={clientId:this.config.authOptions.clientId,authority:e.authority,scopes:e.scopes},[4,this.executePostToTokenEndpoint(this.authority.tokenEndpoint,r,n,a)];case 4:(s=d.sent()).body&&s.body.error===k?this.logger.info(s.body.error_description||"no_error_description"):(clearInterval(u),t(s.body)),d.label=5;case 5:return[3,7];case 6:return h=d.sent(),clearInterval(u),c(h),[3,7];case 7:return[2]}}))}))}),a)}))]}))}))},t.prototype.createTokenRequestBody=function(e,t){var r=new Lt;r.addScopes(e.scopes),r.addClientId(this.config.authOptions.clientId),r.addGrantType(x.DEVICE_CODE_GRANT),r.addDeviceCode(t.deviceCode);var n=e.correlationId||this.config.cryptoInterface.createNewGuid();return r.addCorrelationId(n),r.addClientInfo(),(!Je.isEmpty(e.claims)||this.config.authOptions.clientCapabilities&&this.config.authOptions.clientCapabilities.length>0)&&r.addClaims(e.claims,this.config.authOptions.clientCapabilities),r.createQueryString()}}(qt),function(t){function r(e){return t.call(this,e)||this}return u(r,t),r.prototype.acquireToken=function(e){return d(this,void 0,void 0,(function(){var t,r,n;return l(this,(function(o){switch(o.label){case 0:return t=xt.nowSeconds(),[4,this.executeTokenRequest(e,this.authority)];case 1:return r=o.sent(),(n=new Xt(this.config.authOptions.clientId,this.cacheManager,this.cryptoUtils,this.logger,this.config.serializableCache,this.config.persistencePlugin)).validateTokenResponse(r.body),[2,n.handleServerTokenResponse(r.body,this.authority,t,e,void 0,void 0,!0)]}}))}))},r.prototype.acquireTokenByRefreshToken=function(e){return d(this,void 0,void 0,(function(){var t,r;return l(this,(function(n){if(!e)throw vt.createEmptyTokenRequestError();if(!e.account)throw We.createNoAccountInSilentRequestError();if(this.cacheManager.isAppMetadataFOCI(e.account.environment,this.config.authOptions.clientId))try{return[2,this.acquireTokenWithCachedRefreshToken(e,!0)]}catch(n){if(t=n instanceof We&&n.errorCode===Te.code,r=n instanceof Pt&&n.errorCode===re&&n.subError===ne,t||r)return[2,this.acquireTokenWithCachedRefreshToken(e,!1)];throw n}return[2,this.acquireTokenWithCachedRefreshToken(e,!1)]}))}))},r.prototype.acquireTokenWithCachedRefreshToken=function(t,r){return d(this,void 0,void 0,(function(){var n,o;return l(this,(function(i){if(!(n=this.cacheManager.readRefreshTokenFromCache(this.config.authOptions.clientId,t.account,r)))throw We.createNoTokensFoundError();return o=h(h({},t),{refreshToken:n.secret,authenticationScheme:e.AuthenticationScheme.BEARER}),[2,this.acquireToken(o)]}))}))},r.prototype.executeTokenRequest=function(e,t){return d(this,void 0,void 0,(function(){var r,n,o,i,a;return l(this,(function(s){switch(s.label){case 0:return[4,this.createTokenRequestBody(e)];case 1:return r=s.sent(),n=this.createTokenQueryParameters(e),o=this.createDefaultTokenRequestHeaders(),i={clientId:this.config.authOptions.clientId,authority:t.canonicalAuthority,scopes:e.scopes},a=Je.isEmpty(n)?t.tokenEndpoint:t.tokenEndpoint+"?"+n,[2,this.executePostToTokenEndpoint(a,r,o,i)]}}))}))},r.prototype.createTokenQueryParameters=function(e){var t=new Lt;return e.tokenQueryParameters&&t.addExtraQueryParameters(e.tokenQueryParameters),t.createQueryString()},r.prototype.createTokenRequestBody=function(t){return d(this,void 0,void 0,(function(){var r,n,o,i,a,s;return l(this,(function(c){switch(c.label){case 0:if((r=new Lt).addClientId(this.config.authOptions.clientId),r.addScopes(t.scopes),r.addGrantType(x.REFRESH_TOKEN_GRANT),r.addClientInfo(),n=t.correlationId||this.config.cryptoInterface.createNewGuid(),r.addCorrelationId(n),r.addRefreshToken(t.refreshToken),this.config.clientCredentials.clientSecret&&r.addClientSecret(this.config.clientCredentials.clientSecret),this.config.clientCredentials.clientAssertion&&(o=this.config.clientCredentials.clientAssertion,r.addClientAssertion(o.assertion),r.addClientAssertionType(o.assertionType)),t.authenticationScheme!==e.AuthenticationScheme.POP)return[3,2];if(i=new Jt(this.cryptoUtils),!t.resourceRequestMethod||!t.resourceRequestUri)throw vt.createResourceRequestParametersRequiredError();return s=(a=r).addPopToken,[4,i.generateCnf(t.resourceRequestMethod,t.resourceRequestUri)];case 1:s.apply(a,[c.sent()]),c.label=2;case 2:return(!Je.isEmpty(t.claims)||this.config.authOptions.clientCapabilities&&this.config.authOptions.clientCapabilities.length>0)&&r.addClaims(t.claims,this.config.authOptions.clientCapabilities),[2,r.createQueryString()]}}))}))},r}(qt)),er=(function(e){function t(t){return e.call(this,t)||this}u(t,e),t.prototype.acquireToken=function(e){return d(this,void 0,void 0,(function(){var t;return l(this,(function(r){switch(r.label){case 0:return this.scopeSet=new Et(e.scopes||[]),e.skipCache?[4,this.executeTokenRequest(e,this.authority)]:[3,2];case 1:return[2,r.sent()];case 2:return[4,this.getCachedAuthenticationResult(e)];case 3:return(t=r.sent())?[2,t]:[3,4];case 4:return[4,this.executeTokenRequest(e,this.authority)];case 5:return[2,r.sent()]}}))}))},t.prototype.getCachedAuthenticationResult=function(e){return d(this,void 0,void 0,(function(){var t;return l(this,(function(r){switch(r.label){case 0:return!(t=this.readAccessTokenFromCache())||xt.isTokenExpired(t.expiresOn,this.config.systemOptions.tokenRenewalOffsetSeconds)?[2,null]:[4,Xt.generateAuthenticationResult(this.cryptoUtils,this.authority,{account:null,idToken:null,accessToken:t,refreshToken:null,appMetadata:null},!0,e)];case 1:return[2,r.sent()]}}))}))},t.prototype.readAccessTokenFromCache=function(){var e={homeAccountId:"",environment:this.authority.canonicalAuthorityUrlComponents.HostNameAndPort,credentialType:F.ACCESS_TOKEN,clientId:this.config.authOptions.clientId,realm:this.authority.tenant,target:this.scopeSet.printScopesLowerCase()},t=this.cacheManager.getCredentialsFilteredBy(e),r=Object.keys(t.accessTokens).map((function(e){return t.accessTokens[e]}));if(r.length<1)return null;if(r.length>1)throw We.createMultipleMatchingTokensInCacheError();return r[0]},t.prototype.executeTokenRequest=function(e,t){return d(this,void 0,void 0,(function(){var r,n,o,i,a,s;return l(this,(function(c){switch(c.label){case 0:return r=this.createTokenRequestBody(e),n=this.createDefaultTokenRequestHeaders(),o={clientId:this.config.authOptions.clientId,authority:e.authority,scopes:e.scopes},i=xt.nowSeconds(),[4,this.executePostToTokenEndpoint(t.tokenEndpoint,r,n,o)];case 1:return a=c.sent(),(s=new Xt(this.config.authOptions.clientId,this.cacheManager,this.cryptoUtils,this.logger,this.config.serializableCache,this.config.persistencePlugin)).validateTokenResponse(a.body),[4,s.handleServerTokenResponse(a.body,this.authority,i,e)];case 2:return[2,c.sent()]}}))}))},t.prototype.createTokenRequestBody=function(e){var t=new Lt;t.addClientId(this.config.authOptions.clientId),t.addScopes(e.scopes,!1),t.addGrantType(x.CLIENT_CREDENTIALS_GRANT);var r=e.correlationId||this.config.cryptoInterface.createNewGuid();if(t.addCorrelationId(r),this.config.clientCredentials.clientSecret&&t.addClientSecret(this.config.clientCredentials.clientSecret),this.config.clientCredentials.clientAssertion){var n=this.config.clientCredentials.clientAssertion;t.addClientAssertion(n.assertion),t.addClientAssertionType(n.assertionType)}return(!Je.isEmpty(e.claims)||this.config.authOptions.clientCapabilities&&this.config.authOptions.clientCapabilities.length>0)&&t.addClaims(e.claims,this.config.authOptions.clientCapabilities),t.createQueryString()}}(qt),function(e){function t(t){return e.call(this,t)||this}u(t,e),t.prototype.acquireToken=function(e){return d(this,void 0,void 0,(function(){var t;return l(this,(function(r){switch(r.label){case 0:return this.scopeSet=new Et(e.scopes||[]),e.skipCache?[4,this.executeTokenRequest(e,this.authority)]:[3,2];case 1:return[2,r.sent()];case 2:return[4,this.getCachedAuthenticationResult(e)];case 3:return(t=r.sent())?[2,t]:[3,4];case 4:return[4,this.executeTokenRequest(e,this.authority)];case 5:return[2,r.sent()]}}))}))},t.prototype.getCachedAuthenticationResult=function(e){return d(this,void 0,void 0,(function(){var t,r,n,o,i,a;return l(this,(function(s){switch(s.label){case 0:return!(t=this.readAccessTokenFromCache(e))||xt.isTokenExpired(t.expiresOn,this.config.systemOptions.tokenRenewalOffsetSeconds)?[2,null]:(r=this.readIdTokenFromCache(e),o=null,r&&(n=new wt(r.secret,this.config.cryptoInterface),i=n.claims.oid?n.claims.oid:n.claims.sub,a={homeAccountId:r.homeAccountId,environment:r.environment,tenantId:r.realm,username:P,localAccountId:i||""},o=this.readAccountFromCache(a)),[4,Xt.generateAuthenticationResult(this.cryptoUtils,this.authority,{account:o,accessToken:t,idToken:r,refreshToken:null,appMetadata:null},!0,e,n)]);case 1:return[2,s.sent()]}}))}))},t.prototype.readAccessTokenFromCache=function(e){var t={environment:this.authority.canonicalAuthorityUrlComponents.HostNameAndPort,credentialType:F.ACCESS_TOKEN,clientId:this.config.authOptions.clientId,realm:this.authority.tenant,target:this.scopeSet.printScopesLowerCase(),oboAssertion:e.oboAssertion},r=this.cacheManager.getCredentialsFilteredBy(t),n=Object.keys(r.accessTokens).map((function(e){return r.accessTokens[e]})),o=n.length;if(o<1)return null;if(o>1)throw We.createMultipleMatchingTokensInCacheError();return n[0]},t.prototype.readIdTokenFromCache=function(e){var t={environment:this.authority.canonicalAuthorityUrlComponents.HostNameAndPort,credentialType:F.ID_TOKEN,clientId:this.config.authOptions.clientId,realm:this.authority.tenant,oboAssertion:e.oboAssertion},r=this.cacheManager.getCredentialsFilteredBy(t),n=Object.keys(r.idTokens).map((function(e){return r.idTokens[e]}));return n.length<1?null:n[0]},t.prototype.readAccountFromCache=function(e){return this.cacheManager.readAccountFromCache(e)},t.prototype.executeTokenRequest=function(e,t){return d(this,void 0,void 0,(function(){var r,n,o,i,a,s;return l(this,(function(c){switch(c.label){case 0:return r=this.createTokenRequestBody(e),n=this.createDefaultTokenRequestHeaders(),o={clientId:this.config.authOptions.clientId,authority:e.authority,scopes:e.scopes},i=xt.nowSeconds(),[4,this.executePostToTokenEndpoint(t.tokenEndpoint,r,n,o)];case 1:return a=c.sent(),(s=new Xt(this.config.authOptions.clientId,this.cacheManager,this.cryptoUtils,this.logger,this.config.serializableCache,this.config.persistencePlugin)).validateTokenResponse(a.body),[4,s.handleServerTokenResponse(a.body,this.authority,i,e)];case 2:return[2,c.sent()]}}))}))},t.prototype.createTokenRequestBody=function(e){var t=new Lt;t.addClientId(this.config.authOptions.clientId),t.addScopes(e.scopes),t.addGrantType(x.JWT_BEARER),t.addClientInfo();var r=e.correlationId||this.config.cryptoInterface.createNewGuid();if(t.addCorrelationId(r),t.addRequestTokenUse(m.ON_BEHALF_OF),t.addOboAssertion(e.oboAssertion),this.config.clientCredentials.clientSecret&&t.addClientSecret(this.config.clientCredentials.clientSecret),this.config.clientCredentials.clientAssertion){var n=this.config.clientCredentials.clientAssertion;t.addClientAssertion(n.assertion),t.addClientAssertionType(n.assertionType)}return t.createQueryString()}}(qt),function(e){function t(t){return e.call(this,t)||this}return u(t,e),t.prototype.acquireToken=function(e){return d(this,void 0,void 0,(function(){var t;return l(this,(function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),[4,this.acquireCachedToken(e)];case 1:return[2,r.sent()];case 2:if((t=r.sent())instanceof We&&t.errorCode===Ge.code)return[2,new $t(this.config).acquireTokenByRefreshToken(e)];throw t;case 3:return[2]}}))}))},t.prototype.acquireCachedToken=function(e){return d(this,void 0,void 0,(function(){var t,r,n;return l(this,(function(o){switch(o.label){case 0:if(!e)throw vt.createEmptyTokenRequestError();if(!e.account)throw We.createNoAccountInSilentRequestError();if(t=new Et(e.scopes||[]),r=e.authority||this.authority.getPreferredCache(),n=this.cacheManager.readCacheRecord(e.account,this.config.authOptions.clientId,t,r),!this.isRefreshRequired(e,n.accessToken))return[3,1];throw We.createRefreshRequiredError();case 1:return this.config.serverTelemetryManager&&this.config.serverTelemetryManager.incrementCacheHits(),[4,this.generateResultFromCacheRecord(n,e)];case 2:return[2,o.sent()]}}))}))},t.prototype.generateResultFromCacheRecord=function(e,t){return d(this,void 0,void 0,(function(){var r;return l(this,(function(n){switch(n.label){case 0:return e.idToken&&(r=new wt(e.idToken.secret,this.config.cryptoInterface)),[4,Xt.generateAuthenticationResult(this.cryptoUtils,this.authority,e,!0,t,r)];case 1:return[2,n.sent()]}}))}))},t.prototype.isRefreshRequired=function(e,t){return!(!e.forceRefresh&&!e.claims)||!(t&&!xt.isTokenExpired(t.expiresOn,this.config.systemOptions.tokenRenewalOffsetSeconds))},t}(qt));!function(e){function t(t){return e.call(this,t)||this}u(t,e),t.prototype.acquireToken=function(e){return d(this,void 0,void 0,(function(){var t,r,n;return l(this,(function(o){switch(o.label){case 0:return this.logger.info("in acquireToken call"),t=xt.nowSeconds(),[4,this.executeTokenRequest(this.authority,e)];case 1:return r=o.sent(),(n=new Xt(this.config.authOptions.clientId,this.cacheManager,this.cryptoUtils,this.logger,this.config.serializableCache,this.config.persistencePlugin)).validateTokenResponse(r.body),[2,n.handleServerTokenResponse(r.body,this.authority,t,e)]}}))}))},t.prototype.executeTokenRequest=function(e,t){return d(this,void 0,void 0,(function(){var r,n,o;return l(this,(function(i){return r={clientId:this.config.authOptions.clientId,authority:e.canonicalAuthority,scopes:t.scopes},n=this.createTokenRequestBody(t),o=this.createDefaultTokenRequestHeaders(),[2,this.executePostToTokenEndpoint(e.tokenEndpoint,n,o,r)]}))}))},t.prototype.createTokenRequestBody=function(e){var t=new Lt;t.addClientId(this.config.authOptions.clientId),t.addUsername(e.username),t.addPassword(e.password),t.addScopes(e.scopes),t.addGrantType(x.RESOURCE_OWNER_PASSWORD_GRANT),t.addClientInfo();var r=e.correlationId||this.config.cryptoInterface.createNewGuid();return t.addCorrelationId(r),(!Je.isEmpty(e.claims)||this.config.authOptions.clientCapabilities&&this.config.authOptions.clientCapabilities.length>0)&&t.addClaims(e.claims,this.config.authOptions.clientCapabilities),t.createQueryString()}}(qt);function tr(e){return e.hasOwnProperty("authorization_endpoint")&&e.hasOwnProperty("token_endpoint")&&e.hasOwnProperty("end_session_endpoint")&&e.hasOwnProperty("issuer")}(Wt=e.ProtocolMode||(e.ProtocolMode={})).AAD="AAD",Wt.OIDC="OIDC";var rr=function(){function e(){this.expiresAt=xt.nowSeconds()+V}return e.prototype.updateCloudDiscoveryMetadata=function(e,t){this.aliases=e.aliases,this.preferred_cache=e.preferred_cache,this.preferred_network=e.preferred_network,this.aliasesFromNetwork=t},e.prototype.updateEndpointMetadata=function(e,t){this.authorization_endpoint=e.authorization_endpoint,this.token_endpoint=e.token_endpoint,this.end_session_endpoint=e.end_session_endpoint,this.issuer=e.issuer,this.endpointsFromNetwork=t},e.prototype.updateCanonicalAuthority=function(e){this.canonical_authority=e},e.prototype.resetExpiresAt=function(){this.expiresAt=xt.nowSeconds()+V},e.prototype.isExpired=function(){return this.expiresAt<=xt.nowSeconds()},e.isAuthorityMetadataEntity=function(e,t){return!!t&&(0===e.indexOf(J)&&t.hasOwnProperty("aliases")&&t.hasOwnProperty("preferred_cache")&&t.hasOwnProperty("preferred_network")&&t.hasOwnProperty("canonical_authority")&&t.hasOwnProperty("authorization_endpoint")&&t.hasOwnProperty("token_endpoint")&&t.hasOwnProperty("end_session_endpoint")&&t.hasOwnProperty("issuer")&&t.hasOwnProperty("aliasesFromNetwork")&&t.hasOwnProperty("endpointsFromNetwork")&&t.hasOwnProperty("expiresAt"))},e}();var nr,or,ir,ar,sr,cr,ur=function(){function t(e,t,r,n){this.canonicalAuthority=e,this._canonicalAuthority.validateAsUri(),this.networkInterface=t,this.cacheManager=r,this.authorityOptions=n}return Object.defineProperty(t.prototype,"authorityType",{get:function(){var e=this.canonicalAuthorityUrlComponents.PathSegments;return e.length&&e[0].toLowerCase()===w?Ve.Adfs:Ve.Default},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"protocolMode",{get:function(){return this.authorityOptions.protocolMode},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"options",{get:function(){return this.authorityOptions},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"canonicalAuthority",{get:function(){return this._canonicalAuthority.urlString},set:function(e){this._canonicalAuthority=new Qt(e),this._canonicalAuthority.validateAsUri(),this._canonicalAuthorityUrlComponents=null},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"canonicalAuthorityUrlComponents",{get:function(){return this._canonicalAuthorityUrlComponents||(this._canonicalAuthorityUrlComponents=this._canonicalAuthority.getUrlComponents()),this._canonicalAuthorityUrlComponents},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"hostnameAndPort",{get:function(){return this.canonicalAuthorityUrlComponents.HostNameAndPort.toLowerCase()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"tenant",{get:function(){return this.canonicalAuthorityUrlComponents.PathSegments[0]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"authorizationEndpoint",{get:function(){if(this.discoveryComplete()){var e=this.replacePath(this.metadata.authorization_endpoint);return this.replaceTenant(e)}throw We.createEndpointDiscoveryIncompleteError("Discovery incomplete.")},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"tokenEndpoint",{get:function(){if(this.discoveryComplete()){var e=this.replacePath(this.metadata.token_endpoint);return this.replaceTenant(e)}throw We.createEndpointDiscoveryIncompleteError("Discovery incomplete.")},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"deviceCodeEndpoint",{get:function(){if(this.discoveryComplete()){var e=this.replacePath(this.metadata.token_endpoint.replace("/token","/devicecode"));return this.replaceTenant(e)}throw We.createEndpointDiscoveryIncompleteError("Discovery incomplete.")},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endSessionEndpoint",{get:function(){if(this.discoveryComplete()){var e=this.replacePath(this.metadata.end_session_endpoint);return this.replaceTenant(e)}throw We.createEndpointDiscoveryIncompleteError("Discovery incomplete.")},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"selfSignedJwtAudience",{get:function(){if(this.discoveryComplete()){var e=this.replacePath(this.metadata.issuer);return this.replaceTenant(e)}throw We.createEndpointDiscoveryIncompleteError("Discovery incomplete.")},enumerable:!0,configurable:!0}),t.prototype.replaceTenant=function(e){return e.replace(/{tenant}|{tenantid}/g,this.tenant)},t.prototype.replacePath=function(e){var t=e,r=new Qt(this.metadata.canonical_authority).getUrlComponents().PathSegments;return this.canonicalAuthorityUrlComponents.PathSegments.forEach((function(e,n){var o=r[n];e!==o&&(t=t.replace("/"+o+"/","/"+e+"/"))})),t},Object.defineProperty(t.prototype,"defaultOpenIdConfigurationEndpoint",{get:function(){return this.authorityType===Ve.Adfs||this.protocolMode===e.ProtocolMode.OIDC?this.canonicalAuthority+".well-known/openid-configuration":this.canonicalAuthority+"v2.0/.well-known/openid-configuration"},enumerable:!0,configurable:!0}),t.prototype.discoveryComplete=function(){return!!this.metadata},t.prototype.resolveEndpointsAsync=function(){return d(this,void 0,void 0,(function(){var e,t,r,n;return l(this,(function(o){switch(o.label){case 0:return(e=this.cacheManager.getAuthorityMetadataByAlias(this.hostnameAndPort))||(e=new rr).updateCanonicalAuthority(this.canonicalAuthority),[4,this.updateCloudDiscoveryMetadata(e)];case 1:return t=o.sent(),this.canonicalAuthority=this.canonicalAuthority.replace(this.hostnameAndPort,e.preferred_network),[4,this.updateEndpointMetadata(e)];case 2:return r=o.sent(),t!==z.CACHE&&r!==z.CACHE&&(e.resetExpiresAt(),e.updateCanonicalAuthority(this.canonicalAuthority)),n=this.cacheManager.generateAuthorityMetadataCacheKey(e.preferred_cache),this.cacheManager.setAuthorityMetadata(n,e),this.metadata=e,[2]}}))}))},t.prototype.updateEndpointMetadata=function(e){return d(this,void 0,void 0,(function(){var t;return l(this,(function(r){switch(r.label){case 0:return(t=this.getEndpointMetadataFromConfig())?(e.updateEndpointMetadata(t,!1),[2,z.CONFIG]):this.isAuthoritySameType(e)&&e.endpointsFromNetwork&&!e.isExpired()?[2,z.CACHE]:[4,this.getEndpointMetadataFromNetwork()];case 1:if(t=r.sent())return e.updateEndpointMetadata(t,!0),[2,z.NETWORK];throw We.createUnableToGetOpenidConfigError(this.defaultOpenIdConfigurationEndpoint)}}))}))},t.prototype.isAuthoritySameType=function(e){return new Qt(e.canonical_authority).getUrlComponents().PathSegments.length===this.canonicalAuthorityUrlComponents.PathSegments.length},t.prototype.getEndpointMetadataFromConfig=function(){if(this.authorityOptions.authorityMetadata)try{return JSON.parse(this.authorityOptions.authorityMetadata)}catch(e){throw vt.createInvalidAuthorityMetadataError()}return null},t.prototype.getEndpointMetadataFromNetwork=function(){return d(this,void 0,void 0,(function(){var e;return l(this,(function(t){switch(t.label){case 0:return t.trys.push([0,2,,3]),[4,this.networkInterface.sendGetRequestAsync(this.defaultOpenIdConfigurationEndpoint)];case 1:return[2,tr((e=t.sent()).body)?e.body:null];case 2:return t.sent(),[2,null];case 3:return[2]}}))}))},t.prototype.updateCloudDiscoveryMetadata=function(e){return d(this,void 0,void 0,(function(){var t;return l(this,(function(r){switch(r.label){case 0:return(t=this.getCloudDiscoveryMetadataFromConfig())?(e.updateCloudDiscoveryMetadata(t,!1),[2,z.CONFIG]):this.isAuthoritySameType(e)&&e.aliasesFromNetwork&&!e.isExpired()?[2,z.CACHE]:[4,this.getCloudDiscoveryMetadataFromNetwork()];case 1:if(t=r.sent())return e.updateCloudDiscoveryMetadata(t,!0),[2,z.NETWORK];throw vt.createUntrustedAuthorityError()}}))}))},t.prototype.getCloudDiscoveryMetadataFromConfig=function(){if(this.authorityOptions.cloudDiscoveryMetadata)try{var e=JSON.parse(this.authorityOptions.cloudDiscoveryMetadata),r=t.getCloudDiscoveryMetadataFromNetworkResponse(e.metadata,this.hostnameAndPort);if(r)return r}catch(e){throw vt.createInvalidCloudDiscoveryMetadataError()}return this.isInKnownAuthorities()?t.createCloudDiscoveryMetadataFromHost(this.hostnameAndPort):null},t.prototype.getCloudDiscoveryMetadataFromNetwork=function(){return d(this,void 0,void 0,(function(){var e,r,n,o;return l(this,(function(i){switch(i.label){case 0:e=""+S+this.canonicalAuthority+"oauth2/v2.0/authorize",r=null,i.label=1;case 1:return i.trys.push([1,3,,4]),[4,this.networkInterface.sendGetRequestAsync(e)];case 2:return n=i.sent(),o=function(e){return e.hasOwnProperty("tenant_discovery_endpoint")&&e.hasOwnProperty("metadata")}(n.body)?n.body.metadata:[],r=t.getCloudDiscoveryMetadataFromNetworkResponse(o,this.hostnameAndPort),[3,4];case 3:return i.sent(),[2,null];case 4:return r||(r=t.createCloudDiscoveryMetadataFromHost(this.hostnameAndPort)),[2,r]}}))}))},t.prototype.isInKnownAuthorities=function(){var e=this;return this.authorityOptions.knownAuthorities.filter((function(t){return Qt.getDomainFromUrl(t).toLowerCase()===e.hostnameAndPort})).length>0},t.createCloudDiscoveryMetadataFromHost=function(e){return{preferred_network:e,preferred_cache:e,aliases:[e]}},t.getCloudDiscoveryMetadataFromNetworkResponse=function(e,t){for(var r=0;r<e.length;r++){var n=e[r];if(n.aliases.indexOf(t)>-1)return n}return null},t.prototype.getPreferredCache=function(){if(this.discoveryComplete())return this.metadata.preferred_cache;throw We.createEndpointDiscoveryIncompleteError("Discovery incomplete.")},t.prototype.isAlias=function(e){return this.metadata.aliases.indexOf(e)>-1},t}(),hr=function(){function e(){}return e.createDiscoveredInstance=function(t,r,n,o){return d(this,void 0,void 0,(function(){var i,a;return l(this,(function(s){switch(s.label){case 0:i=e.createInstance(t,r,n,o),s.label=1;case 1:return s.trys.push([1,3,,4]),[4,i.resolveEndpointsAsync()];case 2:return s.sent(),[2,i];case 3:throw a=s.sent(),We.createEndpointDiscoveryIncompleteError(a);case 4:return[2]}}))}))},e.createInstance=function(e,t,r,n){if(Je.isEmpty(e))throw vt.createUrlEmptyError();return new ur(e,t,r,n)},e}(),dr=function(){function e(){this.failedRequests=[],this.errors=[],this.cacheHits=0}return e.isServerTelemetryEntity=function(e,t){var r=0===e.indexOf(X.CACHE_KEY),n=!0;return t&&(n=t.hasOwnProperty("failedRequests")&&t.hasOwnProperty("errors")&&t.hasOwnProperty("cacheHits")),r&&n},e}(),lr=function(){function e(){}return e.isThrottlingEntity=function(e,t){var r=!1;e&&(r=0===e.indexOf(te));var n=!0;return t&&(n=t.hasOwnProperty("throttleTime")),r&&n},e}(),pr={sendGetRequestAsync:function(){return Promise.reject(ae.createUnexpectedError("Network interface - sendGetRequestAsync() has not been implemented for the Network interface."))},sendPostRequestAsync:function(){return Promise.reject(ae.createUnexpectedError("Network interface - sendPostRequestAsync() has not been implemented for the Network interface."))}},fr=function(){function e(e,t){this.cacheManager=t,this.apiId=e.apiId,this.correlationId=e.correlationId,this.forceRefresh=e.forceRefresh||!1,this.wrapperSKU=e.wrapperSKU||P,this.wrapperVer=e.wrapperVer||P,this.telemetryCacheKey=X.CACHE_KEY+K.CACHE_KEY_SEPARATOR+e.clientId}return e.prototype.generateCurrentRequestHeaderValue=function(){var e=this.forceRefresh?1:0,t=""+this.apiId+X.VALUE_SEPARATOR+e,r=[this.wrapperSKU,this.wrapperVer].join(X.VALUE_SEPARATOR);return[X.SCHEMA_VERSION,t,r].join(X.CATEGORY_SEPARATOR)},e.prototype.generateLastRequestHeaderValue=function(){var t=this.getLastRequests(),r=e.maxErrorsToSend(t),n=t.failedRequests.slice(0,2*r).join(X.VALUE_SEPARATOR),o=t.errors.slice(0,r).join(X.VALUE_SEPARATOR),i=t.errors.length,a=[i,r<i?X.OVERFLOW_TRUE:X.OVERFLOW_FALSE].join(X.VALUE_SEPARATOR);return[X.SCHEMA_VERSION,t.cacheHits,n,o,a].join(X.CATEGORY_SEPARATOR)},e.prototype.cacheFailedRequest=function(e){var t=this.getLastRequests();t.failedRequests.push(this.apiId,this.correlationId),Je.isEmpty(e.subError)?Je.isEmpty(e.errorCode)?e&&e.toString()?t.errors.push(e.toString()):t.errors.push(X.UNKNOWN_ERROR):t.errors.push(e.errorCode):t.errors.push(e.subError),this.cacheManager.setServerTelemetry(this.telemetryCacheKey,t)},e.prototype.incrementCacheHits=function(){var e=this.getLastRequests();return e.cacheHits+=1,this.cacheManager.setServerTelemetry(this.telemetryCacheKey,e),e.cacheHits},e.prototype.getLastRequests=function(){var e=new dr;return this.cacheManager.getServerTelemetry(this.telemetryCacheKey)||e},e.prototype.clearTelemetryCache=function(){var t=this.getLastRequests(),r=e.maxErrorsToSend(t);if(r===t.errors.length)this.cacheManager.removeItem(this.telemetryCacheKey);else{var n=new dr;n.failedRequests=t.failedRequests.slice(2*r),n.errors=t.errors.slice(r),this.cacheManager.setServerTelemetry(this.telemetryCacheKey,n)}},e.maxErrorsToSend=function(e){var t,r=0,n=0,o=e.errors.length;for(t=0;t<o;t++){var i=e.failedRequests[2*t]||P,a=e.failedRequests[2*t+1]||P,s=e.errors[t]||P;if(!((n+=i.toString().length+a.toString().length+s.length+3)<X.MAX_HEADER_BYTES))break;r+=1}return r},e}(),gr="interaction_in_progress",yr="invalid_grant",mr=483,vr=600,Er="msal",Cr=50,Tr="msal.js.browser";(nr=e.BrowserCacheLocation||(e.BrowserCacheLocation={})).LocalStorage="localStorage",nr.SessionStorage="sessionStorage",nr.MemoryStorage="memoryStorage",function(e){e.GET="GET",e.POST="POST"}(or||(or={})),function(e){e.AUTHORITY="authority",e.ACQUIRE_TOKEN_ACCOUNT="acquireToken.account",e.SESSION_STATE="session.state",e.REQUEST_STATE="request.state",e.NONCE_IDTOKEN="nonce.id_token",e.ORIGIN_URI="request.origin",e.RENEW_STATUS="token.renew.status",e.URL_HASH="urlHash",e.REQUEST_PARAMS="request.params",e.SCOPES="scopes",e.INTERACTION_STATUS_KEY="interaction.status"}(ir||(ir={})),(ar=e.ApiId||(e.ApiId={}))[ar.acquireTokenRedirect=861]="acquireTokenRedirect",ar[ar.acquireTokenPopup=862]="acquireTokenPopup",ar[ar.ssoSilent=863]="ssoSilent",ar[ar.acquireTokenSilent_authCode=864]="acquireTokenSilent_authCode",ar[ar.handleRedirectPromise=865]="handleRedirectPromise",ar[ar.acquireTokenSilent_silentFlow=61]="acquireTokenSilent_silentFlow",ar[ar.logout=961]="logout",ar[ar.logoutPopup=962]="logoutPopup",(sr=e.InteractionType||(e.InteractionType={})).Redirect="redirect",sr.Popup="popup",sr.Silent="silent",(cr=e.InteractionStatus||(e.InteractionStatus={})).Startup="startup",cr.Login="login",cr.Logout="logout",cr.AcquireToken="acquireToken",cr.SsoSilent="ssoSilent",cr.HandleRedirect="handleRedirect",cr.None="none";var wr,Sr={scopes:U},Ir="jwk";(wr=e.WrapperSKU||(e.WrapperSKU={})).React="@azure/msal-react",wr.Angular="@azure/msal-angular";var Ar=function(){function e(){}return e.decimalToHex=function(e){for(var t=e.toString(16);t.length<2;)t="0"+t;return t},e}(),br=function(){function e(e){this.cryptoObj=e}return e.prototype.generateGuid=function(){try{var e=new Uint8Array(16);return this.cryptoObj.getRandomValues(e),e[6]|=64,e[6]&=79,e[8]|=128,e[8]&=191,Ar.decimalToHex(e[0])+Ar.decimalToHex(e[1])+Ar.decimalToHex(e[2])+Ar.decimalToHex(e[3])+"-"+Ar.decimalToHex(e[4])+Ar.decimalToHex(e[5])+"-"+Ar.decimalToHex(e[6])+Ar.decimalToHex(e[7])+"-"+Ar.decimalToHex(e[8])+Ar.decimalToHex(e[9])+"-"+Ar.decimalToHex(e[10])+Ar.decimalToHex(e[11])+Ar.decimalToHex(e[12])+Ar.decimalToHex(e[13])+Ar.decimalToHex(e[14])+Ar.decimalToHex(e[15])}catch(e){for(var t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx",r="0123456789abcdef",n=0,o="",i=0;i<36;i++)"-"!==t[i]&&"4"!==t[i]&&(n=16*Math.random()|0),"x"===t[i]?o+=r[n]:"y"===t[i]?(n&=3,o+=r[n|=8]):o+=t[i];return o}},e.isGuid=function(e){return/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e)},e}(),Rr=function(){function e(){}return e.stringToUtf8Arr=function(e){for(var t,r=0,n=e.length,o=0;o<n;o++)r+=(t=e.charCodeAt(o))<128?1:t<2048?2:t<65536?3:t<2097152?4:t<67108864?5:6;for(var i=new Uint8Array(r),a=0,s=0;a<r;s++)(t=e.charCodeAt(s))<128?i[a++]=t:t<2048?(i[a++]=192+(t>>>6),i[a++]=128+(63&t)):t<65536?(i[a++]=224+(t>>>12),i[a++]=128+(t>>>6&63),i[a++]=128+(63&t)):t<2097152?(i[a++]=240+(t>>>18),i[a++]=128+(t>>>12&63),i[a++]=128+(t>>>6&63),i[a++]=128+(63&t)):t<67108864?(i[a++]=248+(t>>>24),i[a++]=128+(t>>>18&63),i[a++]=128+(t>>>12&63),i[a++]=128+(t>>>6&63),i[a++]=128+(63&t)):(i[a++]=252+(t>>>30),i[a++]=128+(t>>>24&63),i[a++]=128+(t>>>18&63),i[a++]=128+(t>>>12&63),i[a++]=128+(t>>>6&63),i[a++]=128+(63&t));return i},e.stringToArrayBuffer=function(e){for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),n=0;n<e.length;n++)r[n]=e.charCodeAt(n);return t},e.utf8ArrToString=function(e){for(var t="",r=void 0,n=e.length,o=0;o<n;o++)r=e[o],t+=String.fromCharCode(r>251&&r<254&&o+5<n?1073741824*(r-252)+(e[++o]-128<<24)+(e[++o]-128<<18)+(e[++o]-128<<12)+(e[++o]-128<<6)+e[++o]-128:r>247&&r<252&&o+4<n?(r-248<<24)+(e[++o]-128<<18)+(e[++o]-128<<12)+(e[++o]-128<<6)+e[++o]-128:r>239&&r<248&&o+3<n?(r-240<<18)+(e[++o]-128<<12)+(e[++o]-128<<6)+e[++o]-128:r>223&&r<240&&o+2<n?(r-224<<12)+(e[++o]-128<<6)+e[++o]-128:r>191&&r<224&&o+1<n?(r-192<<6)+e[++o]-128:r);return t},e}(),_r=function(){function e(){}return e.prototype.urlEncode=function(e){return encodeURIComponent(this.encode(e).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_"))},e.prototype.urlEncodeArr=function(e){return this.base64EncArr(e).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")},e.prototype.encode=function(e){var t=Rr.stringToUtf8Arr(e);return this.base64EncArr(t)},e.prototype.base64EncArr=function(e){for(var t=(3-e.length%3)%3,r="",n=void 0,o=e.length,i=0,a=0;a<o;a++)n=a%3,i|=e[a]<<(16>>>n&24),2!==n&&e.length-a!=1||(r+=String.fromCharCode(this.uint6ToB64(i>>>18&63),this.uint6ToB64(i>>>12&63),this.uint6ToB64(i>>>6&63),this.uint6ToB64(63&i)),i=0);return 0===t?r:r.substring(0,r.length-t)+(1===t?"=":"==")},e.prototype.uint6ToB64=function(e){return e<26?e+65:e<52?e+71:e<62?e-4:62===e?43:63===e?47:65},e}(),kr=function(){function e(){}return e.prototype.decode=function(e){var t=e.replace(/-/g,"+").replace(/_/g,"/");switch(t.length%4){case 0:break;case 2:t+="==";break;case 3:t+="=";break;default:throw new Error("Invalid base64 string")}var r=this.base64DecToArr(t);return Rr.utf8ArrToString(r)},e.prototype.base64DecToArr=function(e,t){for(var r=e.replace(/[^A-Za-z0-9\+\/]/g,""),n=r.length,o=t?Math.ceil((3*n+1>>>2)/t)*t:3*n+1>>>2,i=new Uint8Array(o),a=void 0,s=void 0,c=0,u=0,h=0;h<n;h++)if(s=3&h,c|=this.b64ToUint6(r.charCodeAt(h))<<18-6*s,3===s||n-h==1){for(a=0;a<3&&u<o;a++,u++)i[u]=c>>>(16>>>a&24)&255;c=0}return i},e.prototype.b64ToUint6=function(e){return e>64&&e<91?e-65:e>96&&e<123?e-71:e>47&&e<58?e+4:43===e?62:47===e?63:0},e}(),Or={pkceNotGenerated:{code:"pkce_not_created",desc:"The PKCE code challenge and verifier could not be generated."},cryptoDoesNotExist:{code:"crypto_nonexistent",desc:"The crypto object or function is not available."},httpMethodNotImplementedError:{code:"http_method_not_implemented",desc:"The HTTP method given has not been implemented in this library."},emptyNavigateUriError:{code:"empty_navigate_uri",desc:"Navigation URI is empty. Please check stack trace for more info."},hashEmptyError:{code:"hash_empty_error",desc:"Hash value cannot be processed because it is empty. Please verify that your redirectUri is not clearing the hash."},hashDoesNotContainStateError:{code:"no_state_in_hash",desc:"Hash does not contain state. Please verify that the request originated from msal."},hashDoesNotContainKnownPropertiesError:{code:"hash_does_not_contain_known_properties",desc:"Hash does not contain known properites. Please verify that your redirectUri is not changing the hash."},unableToParseStateError:{code:"unable_to_parse_state",desc:"Unable to parse state. Please verify that the request originated from msal."},stateInteractionTypeMismatchError:{code:"state_interaction_type_mismatch",desc:"Hash contains state but the interaction type does not match the caller."},interactionInProgress:{code:"interaction_in_progress",desc:"Interaction is currently in progress. Please ensure that this interaction has been completed before calling an interactive API.  For more visit: aka.ms/msaljs/browser-errors."},popUpWindowError:{code:"popup_window_error",desc:"Error opening popup window. This can happen if you are using IE or if popups are blocked in the browser."},emptyWindowError:{code:"empty_window_error",desc:"window.open returned null or undefined window object."},userCancelledError:{code:"user_cancelled",desc:"User cancelled the flow."},monitorPopupTimeoutError:{code:"monitor_window_timeout",desc:"Token acquisition in popup failed due to timeout."},monitorIframeTimeoutError:{code:"monitor_window_timeout",desc:"Token acquisition in iframe failed due to timeout."},redirectInIframeError:{code:"redirect_in_iframe",desc:"Code flow is not supported inside an iframe. Please ensure you are using MSAL.js in a top frame of the window if using the redirect APIs, or use the popup APIs."},blockTokenRequestsInHiddenIframeError:{code:"block_iframe_reload",desc:"Request was blocked inside an iframe because MSAL detected an authentication response. For more visit: aka.ms/msaljs/browser-errors"},blockAcquireTokenInPopupsError:{code:"block_nested_popups",desc:"Request was blocked inside a popup because MSAL detected it was running in a popup."},iframeClosedPrematurelyError:{code:"iframe_closed_prematurely",desc:"The iframe being monitored was closed prematurely."},silentSSOInsufficientInfoError:{code:"silent_sso_error",desc:"Silent SSO could not be completed - insufficient information was provided. Please provide either a loginHint or sid."},noAccountError:{code:"no_account_error",desc:"No account object provided to acquireTokenSilent and no active account has been set. Please call setActiveAccount or provide an account on the request."},silentPromptValueError:{code:"silent_prompt_value_error",desc:"The value given for the prompt value is not valid for silent requests - must be set to 'none'."},noTokenRequestCacheError:{code:"no_token_request_cache_error",desc:"No token request in found in cache."},unableToParseTokenRequestCacheError:{code:"unable_to_parse_token_request_cache_error",desc:"The cached token request could not be parsed."},noCachedAuthorityError:{code:"no_cached_authority_error",desc:"No cached authority found."},authRequestNotSet:{code:"auth_request_not_set_error",desc:"Auth Request not set. Please ensure initiateAuthRequest was called from the InteractionHandler"},invalidCacheType:{code:"invalid_cache_type",desc:"Invalid cache type"},notInBrowserEnvironment:{code:"non_browser_environment",desc:"Login and token requests are not supported in non-browser environments."},databaseNotOpen:{code:"database_not_open",desc:"Database is not open!"},noNetworkConnectivity:{code:"no_network_connectivity",desc:"No network connectivity. Check your internet connection."},postRequestFailed:{code:"post_request_failed",desc:"Network request failed: If the browser threw a CORS error, check that the redirectUri is registered in the Azure App Portal as type 'SPA'"},getRequestFailed:{code:"get_request_failed",desc:"Network request failed. Please check the network trace to determine root cause."},failedToParseNetworkResponse:{code:"failed_to_parse_response",desc:"Failed to parse network response. Check network trace."}},Pr=function(e){function t(r,n){var o=e.call(this,r,n)||this;return Object.setPrototypeOf(o,t.prototype),o.name="BrowserAuthError",o}return r(t,e),t.createPkceNotGeneratedError=function(e){return new t(Or.pkceNotGenerated.code,Or.pkceNotGenerated.desc+" Detail:"+e)},t.createCryptoNotAvailableError=function(e){return new t(Or.cryptoDoesNotExist.code,Or.cryptoDoesNotExist.desc+" Detail:"+e)},t.createHttpMethodNotImplementedError=function(e){return new t(Or.httpMethodNotImplementedError.code,Or.httpMethodNotImplementedError.desc+" Given Method: "+e)},t.createEmptyNavigationUriError=function(){return new t(Or.emptyNavigateUriError.code,Or.emptyNavigateUriError.desc)},t.createEmptyHashError=function(e){return new t(Or.hashEmptyError.code,Or.hashEmptyError.desc+" Given Url: "+e)},t.createHashDoesNotContainStateError=function(){return new t(Or.hashDoesNotContainStateError.code,Or.hashDoesNotContainStateError.desc)},t.createHashDoesNotContainKnownPropertiesError=function(){return new t(Or.hashDoesNotContainKnownPropertiesError.code,Or.hashDoesNotContainKnownPropertiesError.desc)},t.createUnableToParseStateError=function(){return new t(Or.unableToParseStateError.code,Or.unableToParseStateError.desc)},t.createStateInteractionTypeMismatchError=function(){return new t(Or.stateInteractionTypeMismatchError.code,Or.stateInteractionTypeMismatchError.desc)},t.createInteractionInProgressError=function(){return new t(Or.interactionInProgress.code,Or.interactionInProgress.desc)},t.createPopupWindowError=function(e){var r=Or.popUpWindowError.desc;return r=Je.isEmpty(e)?r:r+" Details: "+e,new t(Or.popUpWindowError.code,r)},t.createEmptyWindowCreatedError=function(){return new t(Or.emptyWindowError.code,Or.emptyWindowError.desc)},t.createUserCancelledError=function(){return new t(Or.userCancelledError.code,Or.userCancelledError.desc)},t.createMonitorPopupTimeoutError=function(){return new t(Or.monitorPopupTimeoutError.code,Or.monitorPopupTimeoutError.desc)},t.createMonitorIframeTimeoutError=function(){return new t(Or.monitorIframeTimeoutError.code,Or.monitorIframeTimeoutError.desc)},t.createRedirectInIframeError=function(e){return new t(Or.redirectInIframeError.code,Or.redirectInIframeError.desc+" (window.parent !== window) => "+e)},t.createBlockReloadInHiddenIframeError=function(){return new t(Or.blockTokenRequestsInHiddenIframeError.code,Or.blockTokenRequestsInHiddenIframeError.desc)},t.createBlockAcquireTokenInPopupsError=function(){return new t(Or.blockAcquireTokenInPopupsError.code,Or.blockAcquireTokenInPopupsError.desc)},t.createIframeClosedPrematurelyError=function(){return new t(Or.iframeClosedPrematurelyError.code,Or.iframeClosedPrematurelyError.desc)},t.createSilentSSOInsufficientInfoError=function(){return new t(Or.silentSSOInsufficientInfoError.code,Or.silentSSOInsufficientInfoError.desc)},t.createNoAccountError=function(){return new t(Or.noAccountError.code,Or.noAccountError.desc)},t.createSilentPromptValueError=function(e){return new t(Or.silentPromptValueError.code,Or.silentPromptValueError.desc+" Given value: "+e)},t.createUnableToParseTokenRequestCacheError=function(){return new t(Or.unableToParseTokenRequestCacheError.code,Or.unableToParseTokenRequestCacheError.desc)},t.createNoTokenRequestCacheError=function(){return new t(Or.noTokenRequestCacheError.code,Or.noTokenRequestCacheError.desc)},t.createAuthRequestNotSetError=function(){return new t(Or.authRequestNotSet.code,Or.authRequestNotSet.desc)},t.createNoCachedAuthorityError=function(){return new t(Or.noCachedAuthorityError.code,Or.noCachedAuthorityError.desc)},t.createInvalidCacheTypeError=function(){return new t(Or.invalidCacheType.code,""+Or.invalidCacheType.desc)},t.createNonBrowserEnvironmentError=function(){return new t(Or.notInBrowserEnvironment.code,Or.notInBrowserEnvironment.desc)},t.createDatabaseNotOpenError=function(){return new t(Or.databaseNotOpen.code,Or.databaseNotOpen.desc)},t.createNoNetworkConnectivityError=function(){return new t(Or.noNetworkConnectivity.code,Or.noNetworkConnectivity.desc)},t.createPostRequestFailedError=function(e,r){return new t(Or.postRequestFailed.code,Or.postRequestFailed.desc+" | Network client threw: "+e+" | Attempted to reach: "+r.split("?")[0])},t.createGetRequestFailedError=function(e,r){return new t(Or.getRequestFailed.code,Or.getRequestFailed.desc+" | Network client threw: "+e+" | Attempted to reach: "+r.split("?")[0])},t.createFailedToParseNetworkResponseError=function(e){return new t(Or.failedToParseNetworkResponse.code,Or.failedToParseNetworkResponse.desc+" | Attempted to reach: "+e.split("?")[0])},t}(ae),Nr=function(){function e(e){this.base64Encode=new _r,this.cryptoObj=e}return e.prototype.generateCodes=function(){return o(this,void 0,void 0,(function(){var e,t;return i(this,(function(r){switch(r.label){case 0:return e=this.generateCodeVerifier(),[4,this.generateCodeChallengeFromVerifier(e)];case 1:return t=r.sent(),[2,{verifier:e,challenge:t}]}}))}))},e.prototype.generateCodeVerifier=function(){try{var e=new Uint8Array(32);return this.cryptoObj.getRandomValues(e),this.base64Encode.urlEncodeArr(e)}catch(e){throw Pr.createPkceNotGeneratedError(e)}},e.prototype.generateCodeChallengeFromVerifier=function(e){return o(this,void 0,void 0,(function(){var t,r;return i(this,(function(n){switch(n.label){case 0:return n.trys.push([0,2,,3]),[4,this.cryptoObj.sha256Digest(e)];case 1:return t=n.sent(),[2,this.base64Encode.urlEncodeArr(new Uint8Array(t))];case 2:throw r=n.sent(),Pr.createPkceNotGeneratedError(r);case 3:return[2]}}))}))},e}(),Ur="SHA-256",qr=new Uint8Array([1,0,1]),Mr=function(){function e(){if(!this.hasCryptoAPI())throw Pr.createCryptoNotAvailableError("Browser crypto or msCrypto object not available.");this._keygenAlgorithmOptions={name:"RSASSA-PKCS1-v1_5",hash:Ur,modulusLength:2048,publicExponent:qr}}return e.prototype.sha256Digest=function(e){return o(this,void 0,void 0,(function(){var t;return i(this,(function(r){return t=Rr.stringToUtf8Arr(e),[2,this.hasIECrypto()?this.getMSCryptoDigest(Ur,t):this.getSubtleCryptoDigest(Ur,t)]}))}))},e.prototype.getRandomValues=function(e){var t=window.msCrypto||window.crypto;if(!t.getRandomValues)throw Pr.createCryptoNotAvailableError("getRandomValues does not exist.");t.getRandomValues(e)},e.prototype.generateKeyPair=function(e,t){return o(this,void 0,void 0,(function(){return i(this,(function(r){return[2,this.hasIECrypto()?this.msCryptoGenerateKey(e,t):window.crypto.subtle.generateKey(this._keygenAlgorithmOptions,e,t)]}))}))},e.prototype.exportJwk=function(e){return o(this,void 0,void 0,(function(){return i(this,(function(t){return[2,this.hasIECrypto()?this.msCryptoExportJwk(e):window.crypto.subtle.exportKey(Ir,e)]}))}))},e.prototype.importJwk=function(t,r,n){return o(this,void 0,void 0,(function(){var o,a;return i(this,(function(i){return o=e.getJwkString(t),a=Rr.stringToArrayBuffer(o),[2,this.hasIECrypto()?this.msCryptoImportKey(a,r,n):window.crypto.subtle.importKey(Ir,t,this._keygenAlgorithmOptions,r,n)]}))}))},e.prototype.sign=function(e,t){return o(this,void 0,void 0,(function(){return i(this,(function(r){return[2,this.hasIECrypto()?this.msCryptoSign(e,t):window.crypto.subtle.sign(this._keygenAlgorithmOptions,e,t)]}))}))},e.prototype.hasCryptoAPI=function(){return this.hasIECrypto()||this.hasBrowserCrypto()},e.prototype.hasIECrypto=function(){return"msCrypto"in window},e.prototype.hasBrowserCrypto=function(){return"crypto"in window},e.prototype.getSubtleCryptoDigest=function(e,t){return o(this,void 0,void 0,(function(){return i(this,(function(r){return[2,window.crypto.subtle.digest(e,t)]}))}))},e.prototype.getMSCryptoDigest=function(e,t){return o(this,void 0,void 0,(function(){return i(this,(function(r){return[2,new Promise((function(r,n){var o=window.msCrypto.subtle.digest(e,t.buffer);o.addEventListener("complete",(function(e){r(e.target.result)})),o.addEventListener("error",(function(e){n(e)}))}))]}))}))},e.prototype.msCryptoGenerateKey=function(e,t){return o(this,void 0,void 0,(function(){var r=this;return i(this,(function(n){return[2,new Promise((function(n,o){var i=window.msCrypto.subtle.generateKey(r._keygenAlgorithmOptions,e,t);i.addEventListener("complete",(function(e){n(e.target.result)})),i.addEventListener("error",(function(e){o(e)}))}))]}))}))},e.prototype.msCryptoExportJwk=function(e){return o(this,void 0,void 0,(function(){return i(this,(function(t){return[2,new Promise((function(t,r){var n=window.msCrypto.subtle.exportKey(Ir,e);n.addEventListener("complete",(function(e){var n=e.target.result,o=Rr.utf8ArrToString(new Uint8Array(n)).replace(/\r/g,"").replace(/\n/g,"").replace(/\t/g,"").split(" ").join("").replace("\0","");try{t(JSON.parse(o))}catch(e){r(e)}})),n.addEventListener("error",(function(e){r(e)}))}))]}))}))},e.prototype.msCryptoImportKey=function(e,t,r){return o(this,void 0,void 0,(function(){var n=this;return i(this,(function(o){return[2,new Promise((function(o,i){var a=window.msCrypto.subtle.importKey(Ir,e,n._keygenAlgorithmOptions,t,r);a.addEventListener("complete",(function(e){o(e.target.result)})),a.addEventListener("error",(function(e){i(e)}))}))]}))}))},e.prototype.msCryptoSign=function(e,t){return o(this,void 0,void 0,(function(){var r=this;return i(this,(function(n){return[2,new Promise((function(n,o){var i=window.msCrypto.subtle.sign(r._keygenAlgorithmOptions,e,t);i.addEventListener("complete",(function(e){n(e.target.result)})),i.addEventListener("error",(function(e){o(e)}))}))]}))}))},e.getJwkString=function(e){return JSON.stringify(e,Object.keys(e).sort())},e}(),Lr=function(){function e(e,t,r){this.dbName=e,this.tableName=t,this.version=r,this.dbOpen=!1}return e.prototype.open=function(){return o(this,void 0,void 0,(function(){var e=this;return i(this,(function(t){return[2,new Promise((function(t,r){var n=window.indexedDB.open(e.dbName,e.version);n.addEventListener("upgradeneeded",(function(t){t.target.result.createObjectStore(e.tableName)})),n.addEventListener("success",(function(r){var n=r;e.db=n.target.result,e.dbOpen=!0,t()})),n.addEventListener("error",(function(e){return r(e)}))}))]}))}))},e.prototype.get=function(e){return o(this,void 0,void 0,(function(){var t=this;return i(this,(function(r){switch(r.label){case 0:return this.dbOpen?[3,2]:[4,this.open()];case 1:r.sent(),r.label=2;case 2:return[2,new Promise((function(r,n){if(!t.db)return n(Pr.createDatabaseNotOpenError());var o=t.db.transaction([t.tableName],"readonly").objectStore(t.tableName).get(e);o.addEventListener("success",(function(e){r(e.target.result)})),o.addEventListener("error",(function(e){return n(e)}))}))]}}))}))},e.prototype.put=function(e,t){return o(this,void 0,void 0,(function(){var r=this;return i(this,(function(n){switch(n.label){case 0:return this.dbOpen?[3,2]:[4,this.open()];case 1:n.sent(),n.label=2;case 2:return[2,new Promise((function(n,o){if(!r.db)return o(Pr.createDatabaseNotOpenError());var i=r.db.transaction([r.tableName],"readwrite").objectStore(r.tableName).put(t,e);i.addEventListener("success",(function(e){n(e.target.result)})),i.addEventListener("error",(function(e){return o(e)}))}))]}}))}))},e}(),Dr=function(){function e(){this.browserCrypto=new Mr,this.b64Encode=new _r,this.b64Decode=new kr,this.guidGenerator=new br(this.browserCrypto),this.pkceGenerator=new Nr(this.browserCrypto),this.cache=new Lr(e.DB_NAME,e.TABLE_NAME,e.DB_VERSION)}return e.prototype.createNewGuid=function(){return this.guidGenerator.generateGuid()},e.prototype.base64Encode=function(e){return this.b64Encode.encode(e)},e.prototype.base64Decode=function(e){return this.b64Decode.decode(e)},e.prototype.generatePkceCodes=function(){return o(this,void 0,void 0,(function(){return i(this,(function(e){return[2,this.pkceGenerator.generateCodes()]}))}))},e.prototype.getPublicKeyThumbprint=function(t,r){return o(this,void 0,void 0,(function(){var n,o,a,s,c,u,h,d;return i(this,(function(i){switch(i.label){case 0:return[4,this.browserCrypto.generateKeyPair(e.EXTRACTABLE,e.POP_KEY_USAGES)];case 1:return n=i.sent(),[4,this.browserCrypto.exportJwk(n.publicKey)];case 2:return o=i.sent(),a={e:o.e,kty:o.kty,n:o.n},s=Mr.getJwkString(a),[4,this.browserCrypto.sha256Digest(s)];case 3:return c=i.sent(),u=this.b64Encode.urlEncodeArr(new Uint8Array(c)),[4,this.browserCrypto.exportJwk(n.privateKey)];case 4:return h=i.sent(),[4,this.browserCrypto.importJwk(h,!1,["sign"])];case 5:return d=i.sent(),this.cache.put(u,{privateKey:d,publicKey:n.publicKey,requestMethod:t,requestUri:r}),[2,u]}}))}))},e.prototype.signJwt=function(e,t){return o(this,void 0,void 0,(function(){var r,n,o,a,s,c,u,h,d,l;return i(this,(function(i){switch(i.label){case 0:return[4,this.cache.get(t)];case 1:return r=i.sent(),[4,this.browserCrypto.exportJwk(r.publicKey)];case 2:return n=i.sent(),o=Mr.getJwkString(n),a={alg:n.alg,type:Ir},s=this.b64Encode.urlEncode(JSON.stringify(a)),e.cnf={jwk:JSON.parse(o)},c=this.b64Encode.urlEncode(JSON.stringify(e)),u=s+"."+c,h=Rr.stringToArrayBuffer(u),[4,this.browserCrypto.sign(r.privateKey,h)];case 3:return d=i.sent(),l=this.b64Encode.urlEncodeArr(new Uint8Array(d)),[2,u+"."+l]}}))}))},e.POP_KEY_USAGES=["sign","verify"],e.EXTRACTABLE=!0,e.DB_VERSION=1,e.DB_NAME="msal.db",e.TABLE_NAME=e.DB_NAME+".keys",e}(),xr={redirectUriNotSet:{code:"redirect_uri_empty",desc:"A redirect URI is required for all calls, and none has been set."},postLogoutUriNotSet:{code:"post_logout_uri_empty",desc:"A post logout redirect has not been set."},storageNotSupportedError:{code:"storage_not_supported",desc:"Given storage configuration option was not supported."},noRedirectCallbacksSet:{code:"no_redirect_callbacks",desc:"No redirect callbacks have been set. Please call setRedirectCallbacks() with the appropriate function arguments before continuing. More information is available here: https://github.com/AzureAD/microsoft-authentication-library-for-js/wiki/MSAL-basics."},invalidCallbackObject:{code:"invalid_callback_object",desc:"The object passed for the callback was invalid. More information is available here: https://github.com/AzureAD/microsoft-authentication-library-for-js/wiki/MSAL-basics."},stubPcaInstanceCalled:{code:"stubbed_public_client_application_called",desc:"Stub instance of Public Client Application was called. If using msal-react, please ensure context is not used without a provider. For more visit: aka.ms/msaljs/browser-errors"},inMemRedirectUnavailable:{code:"in_mem_redirect_unavailable",desc:"Redirect cannot be supported. In-memory storage was selected and storeAuthStateInCookie=false, which would cause the library to be unable to handle the incoming hash. If you would like to use the redirect API, please use session/localStorage or set storeAuthStateInCookie=true."}},Hr=function(e){function t(r,n){var o=e.call(this,r,n)||this;return o.name="BrowserConfigurationAuthError",Object.setPrototypeOf(o,t.prototype),o}return r(t,e),t.createRedirectUriEmptyError=function(){return new t(xr.redirectUriNotSet.code,xr.redirectUriNotSet.desc)},t.createPostLogoutRedirectUriEmptyError=function(){return new t(xr.postLogoutUriNotSet.code,xr.postLogoutUriNotSet.desc)},t.createStorageNotSupportedError=function(e){return new t(xr.storageNotSupportedError.code,xr.storageNotSupportedError.desc+" Given Location: "+e)},t.createInvalidCallbackObjectError=function(e){return new t(xr.invalidCallbackObject.code,xr.invalidCallbackObject.desc+" Given value for callback function: "+e)},t.createRedirectCallbacksNotSetError=function(){return new t(xr.noRedirectCallbacksSet.code,xr.noRedirectCallbacksSet.desc)},t.createStubPcaInstanceCalledError=function(){return new t(xr.stubPcaInstanceCalled.code,xr.stubPcaInstanceCalled.desc)},t.createInMemoryRedirectUnavailableError=function(){return new t(xr.inMemRedirectUnavailable.code,xr.inMemRedirectUnavailable.desc)},t}(ae),Kr=function(){function t(e){this.validateWindowStorage(e),this.windowStorage=window[e]}return t.prototype.validateWindowStorage=function(t){if(t!==e.BrowserCacheLocation.LocalStorage&&t!==e.BrowserCacheLocation.SessionStorage)throw Hr.createStorageNotSupportedError(t);if(!!!window[t])throw Hr.createStorageNotSupportedError(t)},t.prototype.getItem=function(e){return this.windowStorage.getItem(e)},t.prototype.setItem=function(e,t){this.windowStorage.setItem(e,t)},t.prototype.removeItem=function(e){this.windowStorage.removeItem(e)},t.prototype.getKeys=function(){return Object.keys(this.windowStorage)},t.prototype.containsKey=function(e){return this.windowStorage.hasOwnProperty(e)},t}(),Fr=function(){function e(){this.cache=new Map}return e.prototype.getItem=function(e){return this.cache.get(e)||null},e.prototype.setItem=function(e,t){this.cache.set(e,t)},e.prototype.removeItem=function(e){this.cache.delete(e)},e.prototype.getKeys=function(){var e=[];return this.cache.forEach((function(t,r){e.push(r)})),e},e.prototype.containsKey=function(e){return this.cache.has(e)},e.prototype.clear=function(){this.cache.clear()},e}(),Br=function(){function e(){}return e.extractBrowserRequestState=function(e,t){if(Je.isEmpty(t))return null;try{return zt.parseRequestState(e,t).libraryState.meta}catch(e){throw We.createInvalidStateError(t,e)}},e.parseServerResponseFromHash=function(e){if(!e)return{};var t=new Qt(e);return Qt.getDeserializedHash(t.getHash())},e}(),Gr=function(t){function n(e,r,n,o){var i=t.call(this,e,n)||this;return i.COOKIE_LIFE_MULTIPLIER=864e5,i.cacheConfig=r,i.logger=o,i.internalStorage=new Fr,i.browserStorage=i.setupBrowserStorage(i.cacheConfig.cacheLocation),i.temporaryCacheStorage=i.setupTemporaryCacheStorage(i.cacheConfig.cacheLocation),i.migrateCacheEntries(),i}return r(n,t),n.prototype.setupBrowserStorage=function(t){switch(t){case e.BrowserCacheLocation.LocalStorage:case e.BrowserCacheLocation.SessionStorage:try{return new Kr(t)}catch(e){this.logger.verbose(e);break}case e.BrowserCacheLocation.MemoryStorage:}return this.cacheConfig.cacheLocation=e.BrowserCacheLocation.MemoryStorage,new Fr},n.prototype.setupTemporaryCacheStorage=function(t){switch(t){case e.BrowserCacheLocation.LocalStorage:case e.BrowserCacheLocation.SessionStorage:try{return new Kr(e.BrowserCacheLocation.SessionStorage)}catch(e){return this.logger.verbose(e),this.internalStorage}case e.BrowserCacheLocation.MemoryStorage:default:return this.internalStorage}},n.prototype.migrateCacheEntries=function(){var e=this,t=C+"."+g.ID_TOKEN,r=C+"."+g.CLIENT_INFO,n=C+"."+g.ERROR,o=C+"."+g.ERROR_DESC,i=[this.browserStorage.getItem(t),this.browserStorage.getItem(r),this.browserStorage.getItem(n),this.browserStorage.getItem(o)];[g.ID_TOKEN,g.CLIENT_INFO,g.ERROR,g.ERROR_DESC].forEach((function(t,r){return e.migrateCacheEntry(t,i[r])}))},n.prototype.migrateCacheEntry=function(e,t){t&&this.setTemporaryCache(e,t,!0)},n.prototype.validateAndParseJson=function(e){try{var t=JSON.parse(e);return t&&"object"==typeof t?t:null}catch(e){return null}},n.prototype.getItem=function(e){return this.browserStorage.getItem(e)},n.prototype.setItem=function(e,t){this.browserStorage.setItem(e,t)},n.prototype.getAccount=function(e){var t=this.getItem(e);if(!t)return null;var r=this.validateAndParseJson(t);return r&&Tt.isAccountEntity(r)?St.toObject(new Tt,r):null},n.prototype.setAccount=function(e){this.logger.verbose("BrowserCacheManager.setAccount called");var t=e.generateAccountKey();this.setItem(t,JSON.stringify(e))},n.prototype.getIdTokenCredential=function(e){this.logger.verbose("BrowserCacheManager.getIdTokenCredential called");var t=this.getItem(e);if(!t)return null;var r=this.validateAndParseJson(t);return r&&Dt.isIdTokenEntity(r)?(this.logger.verbose("BrowserCacheManager.getIdTokenCredential: cache hit"),St.toObject(new Dt,r)):null},n.prototype.setIdTokenCredential=function(e){this.logger.verbose("BrowserCacheManager.setIdTokenCredential called");var t=e.generateCredentialKey();this.setItem(t,JSON.stringify(e))},n.prototype.getAccessTokenCredential=function(e){this.logger.verbose("BrowserCacheManager.getAccessTokenCredential called");var t=this.getItem(e);if(!t)return null;var r=this.validateAndParseJson(t);return r&&Ht.isAccessTokenEntity(r)?(this.logger.verbose("BrowserCacheManager.getAccessTokenCredential: cache hit"),St.toObject(new Ht,r)):null},n.prototype.setAccessTokenCredential=function(e){this.logger.verbose("BrowserCacheManager.setAccessTokenCredential called");var t=e.generateCredentialKey();this.setItem(t,JSON.stringify(e))},n.prototype.getRefreshTokenCredential=function(e){this.logger.verbose("BrowserCacheManager.getRefreshTokenCredential called");var t=this.getItem(e);if(!t)return null;var r=this.validateAndParseJson(t);return r&&Kt.isRefreshTokenEntity(r)?(this.logger.verbose("BrowserCacheManager.getRefreshTokenCredential: cache hit"),St.toObject(new Kt,r)):null},n.prototype.setRefreshTokenCredential=function(e){this.logger.verbose("BrowserCacheManager.setRefreshTokenCredential called");var t=e.generateCredentialKey();this.setItem(t,JSON.stringify(e))},n.prototype.getAppMetadata=function(e){this.logger.verbose("BrowserCacheManager.getAppMetadata called");var t=this.getItem(e);if(!t)return null;var r=this.validateAndParseJson(t);return r&&Vt.isAppMetadataEntity(e,r)?(this.logger.verbose("BrowserCacheManager.getAppMetadata: cache hit"),St.toObject(new Vt,r)):null},n.prototype.setAppMetadata=function(e){this.logger.verbose("BrowserCacheManager.setAppMetadata called");var t=e.generateAppMetadataKey();this.setItem(t,JSON.stringify(e))},n.prototype.getServerTelemetry=function(e){this.logger.verbose("BrowserCacheManager.getServerTelemetry called");var t=this.getItem(e);if(!t)return null;var r=this.validateAndParseJson(t);return r&&dr.isServerTelemetryEntity(e,r)?(this.logger.verbose("BrowserCacheManager.getServerTelemetry: cache hit"),St.toObject(new dr,r)):null},n.prototype.setServerTelemetry=function(e,t){this.logger.verbose("BrowserCacheManager.setServerTelemetry called"),this.setItem(e,JSON.stringify(t))},n.prototype.getAuthorityMetadata=function(e){this.logger.verbose("BrowserCacheManager.getAuthorityMetadata called");var t=this.internalStorage.getItem(e);if(!t)return null;var r=this.validateAndParseJson(t);return r&&rr.isAuthorityMetadataEntity(e,r)?(this.logger.verbose("BrowserCacheManager.getAuthorityMetadata: cache hit"),St.toObject(new rr,r)):null},n.prototype.getAuthorityMetadataKeys=function(){var e=this;return this.internalStorage.getKeys().filter((function(t){return e.isAuthorityMetadata(t)}))},n.prototype.setAuthorityMetadata=function(e,t){this.logger.verbose("BrowserCacheManager.setAuthorityMetadata called"),this.internalStorage.setItem(e,JSON.stringify(t))},n.prototype.getThrottlingCache=function(e){this.logger.verbose("BrowserCacheManager.getThrottlingCache called");var t=this.getItem(e);if(!t)return null;var r=this.validateAndParseJson(t);return r&&lr.isThrottlingEntity(e,r)?(this.logger.verbose("BrowserCacheManager.getThrottlingCache: cache hit"),St.toObject(new lr,r)):null},n.prototype.setThrottlingCache=function(e,t){this.logger.verbose("BrowserCacheManager.setThrottlingCache called"),this.setItem(e,JSON.stringify(t))},n.prototype.getTemporaryCache=function(e,t){this.logger.verbose("BrowserCacheManager.getTemporaryCache called");var r=t?this.generateCacheKey(e):e;if(this.cacheConfig.storeAuthStateInCookie){this.logger.verbose("BrowserCacheManager.getTemporaryCache: storeAuthStateInCookies set to true, retrieving from cookies");var n=this.getItemCookie(r);if(n)return n}var o=this.temporaryCacheStorage.getItem(r);return o||null},n.prototype.setTemporaryCache=function(e,t,r){var n=r?this.generateCacheKey(e):e;this.temporaryCacheStorage.setItem(n,t),this.cacheConfig.storeAuthStateInCookie&&(this.logger.verbose("BrowserCacheManager.setTemporaryCache: storeAuthStateInCookie set to true, setting item cookie"),this.setItemCookie(n,t))},n.prototype.removeItem=function(e){return this.browserStorage.removeItem(e),this.temporaryCacheStorage.removeItem(e),this.cacheConfig.storeAuthStateInCookie&&(this.logger.verbose("BrowserCacheManager.removeItem: storeAuthStateInCookie is true, clearing item cookie"),this.clearItemCookie(e)),!0},n.prototype.containsKey=function(e){return this.browserStorage.containsKey(e)||this.temporaryCacheStorage.containsKey(e)},n.prototype.getKeys=function(){return s(this.browserStorage.getKeys(),this.temporaryCacheStorage.getKeys())},n.prototype.clear=function(){var e=this;this.removeAllAccounts(),this.removeAppMetadata(),this.getKeys().forEach((function(t){!e.browserStorage.containsKey(t)&&!e.temporaryCacheStorage.containsKey(t)||-1===t.indexOf(C)&&-1===t.indexOf(e.clientId)||e.removeItem(t)})),this.internalStorage.clear()},n.prototype.setItemCookie=function(e,t,r){var n=encodeURIComponent(e)+"="+encodeURIComponent(t)+";path=/;";r&&(n+="expires="+this.getCookieExpirationTime(r)+";");this.cacheConfig.secureCookies&&(n+="Secure;"),document.cookie=n},n.prototype.getItemCookie=function(e){for(var t=encodeURIComponent(e)+"=",r=document.cookie.split(";"),n=0;n<r.length;n++){for(var o=r[n];" "===o.charAt(0);)o=o.substring(1);if(0===o.indexOf(t))return decodeURIComponent(o.substring(t.length,o.length))}return""},n.prototype.clearMsalCookies=function(){var e=this,t=C+"."+this.clientId;document.cookie.split(";").forEach((function(r){for(;" "===r.charAt(0);)r=r.substring(1);if(0===r.indexOf(t)){var n=r.split("=")[0];e.clearItemCookie(n)}}))},n.prototype.clearItemCookie=function(e){this.setItemCookie(e,"",-1)},n.prototype.getCookieExpirationTime=function(e){var t=new Date;return new Date(t.getTime()+e*this.COOKIE_LIFE_MULTIPLIER).toUTCString()},n.prototype.getCache=function(){return this.browserStorage},n.prototype.setCache=function(){},n.prototype.generateCacheKey=function(e){return this.validateAndParseJson(e)?JSON.stringify(e):Je.startsWith(e,C)||Je.startsWith(e,g.ADAL_ID_TOKEN)?e:C+"."+this.clientId+"."+e},n.prototype.generateAuthorityKey=function(e){var t=zt.parseRequestState(this.cryptoImpl,e).libraryState.id;return this.generateCacheKey(ir.AUTHORITY+"."+t)},n.prototype.generateNonceKey=function(e){var t=zt.parseRequestState(this.cryptoImpl,e).libraryState.id;return this.generateCacheKey(ir.NONCE_IDTOKEN+"."+t)},n.prototype.generateStateKey=function(e){var t=zt.parseRequestState(this.cryptoImpl,e).libraryState.id;return this.generateCacheKey(ir.REQUEST_STATE+"."+t)},n.prototype.getCachedAuthority=function(e){var t=this.generateStateKey(e),r=this.getTemporaryCache(t);if(!r)return null;var n=this.generateAuthorityKey(r);return this.getTemporaryCache(n)},n.prototype.updateCacheEntries=function(e,t,r){this.logger.verbose("BrowserCacheManager.updateCacheEntries called");var n=this.generateStateKey(e);this.setTemporaryCache(n,e,!1);var o=this.generateNonceKey(e);this.setTemporaryCache(o,t,!1);var i=this.generateAuthorityKey(e);this.setTemporaryCache(i,r,!1)},n.prototype.resetRequestCache=function(e){var t=this;this.logger.verbose("BrowserCacheManager.resetRequestCache called"),Je.isEmpty(e)||this.getKeys().forEach((function(r){-1!==r.indexOf(e)&&t.removeItem(r)})),e&&(this.removeItem(this.generateStateKey(e)),this.removeItem(this.generateNonceKey(e)),this.removeItem(this.generateAuthorityKey(e))),this.removeItem(this.generateCacheKey(ir.REQUEST_PARAMS)),this.removeItem(this.generateCacheKey(ir.ORIGIN_URI)),this.removeItem(this.generateCacheKey(ir.URL_HASH)),this.removeItem(this.generateCacheKey(ir.INTERACTION_STATUS_KEY))},n.prototype.cleanRequestByState=function(e){if(this.logger.verbose("BrowserCacheManager.cleanRequestByState called"),e){var t=this.generateStateKey(e),r=this.temporaryCacheStorage.getItem(t);this.logger.info("BrowserCacheManager.cleanRequestByState: Removing temporary cache items for state: "+r),this.resetRequestCache(r||"")}this.clearMsalCookies()},n.prototype.cleanRequestByInteractionType=function(e){var t=this;this.logger.verbose("BrowserCacheManager.cleanRequestByInteractionType called"),this.getKeys().forEach((function(r){if(-1!==r.indexOf(ir.REQUEST_STATE)){var n=t.temporaryCacheStorage.getItem(r);if(n){var o=Br.extractBrowserRequestState(t.cryptoImpl,n);o&&o.interactionType===e&&(t.logger.info("BrowserCacheManager.cleanRequestByInteractionType: Removing temporary cache items for state: "+n),t.resetRequestCache(n))}}})),this.clearMsalCookies()},n.prototype.cacheCodeRequest=function(e,t){this.logger.verbose("BrowserCacheManager.cacheCodeRequest called");var r=t.base64Encode(JSON.stringify(e));this.setTemporaryCache(ir.REQUEST_PARAMS,r,!0)},n.prototype.getCachedRequest=function(e,t){this.logger.verbose("BrowserCacheManager.getCachedRequest called");var r=this.getTemporaryCache(ir.REQUEST_PARAMS,!0);if(!r)throw Pr.createNoTokenRequestCacheError();var n=this.validateAndParseJson(t.base64Decode(r));if(!n)throw Pr.createUnableToParseTokenRequestCacheError();if(this.removeItem(this.generateCacheKey(ir.REQUEST_PARAMS)),Je.isEmpty(n.authority)){var o=this.generateAuthorityKey(e),i=this.getTemporaryCache(o);if(!i)throw Pr.createNoCachedAuthorityError();n.authority=i}return n},n}(St),jr=function(){function e(){}return e.prototype.sendGetRequestAsync=function(e,t){return o(this,void 0,void 0,(function(){var r,n,o;return i(this,(function(i){switch(i.label){case 0:return i.trys.push([0,2,,3]),[4,fetch(e,{method:or.GET,headers:this.getFetchHeaders(t)})];case 1:return r=i.sent(),[3,3];case 2:throw n=i.sent(),window.navigator.onLine?Pr.createGetRequestFailedError(n,e):Pr.createNoNetworkConnectivityError();case 3:return i.trys.push([3,5,,6]),o={headers:this.getHeaderDict(r.headers)},[4,r.json()];case 4:return[2,(o.body=i.sent(),o.status=r.status,o)];case 5:throw i.sent(),Pr.createFailedToParseNetworkResponseError(e);case 6:return[2]}}))}))},e.prototype.sendPostRequestAsync=function(e,t){return o(this,void 0,void 0,(function(){var r,n,o,a;return i(this,(function(i){switch(i.label){case 0:r=t&&t.body||"",i.label=1;case 1:return i.trys.push([1,3,,4]),[4,fetch(e,{method:or.POST,headers:this.getFetchHeaders(t),body:r})];case 2:return n=i.sent(),[3,4];case 3:throw o=i.sent(),window.navigator.onLine?Pr.createPostRequestFailedError(o,e):Pr.createNoNetworkConnectivityError();case 4:return i.trys.push([4,6,,7]),a={headers:this.getHeaderDict(n.headers)},[4,n.json()];case 5:return[2,(a.body=i.sent(),a.status=n.status,a)];case 6:throw i.sent(),Pr.createFailedToParseNetworkResponseError(e);case 7:return[2]}}))}))},e.prototype.getFetchHeaders=function(e){var t=new Headers;if(!e||!e.headers)return t;var r=e.headers;return Object.keys(r).forEach((function(e){t.append(e,r[e])})),t},e.prototype.getHeaderDict=function(e){var t={};return e.forEach((function(e,r){t[r]=e})),t},e}(),zr=function(){function e(){}return e.prototype.sendGetRequestAsync=function(e,t){return o(this,void 0,void 0,(function(){return i(this,(function(r){return[2,this.sendRequestAsync(e,or.GET,t)]}))}))},e.prototype.sendPostRequestAsync=function(e,t){return o(this,void 0,void 0,(function(){return i(this,(function(r){return[2,this.sendRequestAsync(e,or.POST,t)]}))}))},e.prototype.sendRequestAsync=function(e,t,r){var n=this;return new Promise((function(o,i){var a=new XMLHttpRequest;if(a.open(t,e,!0),n.setXhrHeaders(a,r),a.onload=function(){(a.status<200||a.status>=300)&&(t===or.POST?i(Pr.createPostRequestFailedError("Failed with status "+a.status,e)):i(Pr.createGetRequestFailedError("Failed with status "+a.status,e)));try{var r=JSON.parse(a.responseText),s={headers:n.getHeaderDict(a),body:r,status:a.status};o(s)}catch(t){i(Pr.createFailedToParseNetworkResponseError(e))}},a.onerror=function(){window.navigator.onLine?t===or.POST?i(Pr.createPostRequestFailedError("Failed with status "+a.status,e)):i(Pr.createGetRequestFailedError("Failed with status "+a.status,e)):i(Pr.createNoNetworkConnectivityError())},t===or.POST&&r&&r.body)a.send(r.body);else{if(t!==or.GET)throw Pr.createHttpMethodNotImplementedError(t);a.send()}}))},e.prototype.setXhrHeaders=function(e,t){if(t&&t.headers){var r=t.headers;Object.keys(r).forEach((function(t){e.setRequestHeader(t,r[t])}))}},e.prototype.getHeaderDict=function(e){var t=e.getAllResponseHeaders().trim().split(/[\r\n]+/),r={};return t.forEach((function(e){var t=e.split(": "),n=t.shift(),o=t.join(": ");n&&o&&(r[n]=o)})),r},e}(),Qr=function(){function t(){}return t.clearHash=function(){"function"==typeof history.replaceState?history.replaceState(null,P,""+window.location.pathname+window.location.search):window.location.hash=""},t.replaceHash=function(e){var t=e.split("#");t.shift(),window.location.hash=t.length>0?t.join("#"):""},t.isInIframe=function(){return window.parent!==window},t.getCurrentUri=function(){return window.location.href.split("?")[0].split("#")[0]},t.getHomepage=function(){var e=new Qt(window.location.href).getUrlComponents();return e.Protocol+"//"+e.HostNameAndPort+"/"},t.getBrowserNetworkClient=function(){return window.fetch&&window.Headers?new jr:new zr},t.blockReloadInHiddenIframes=function(){if(Qt.hashContainsKnownProperties(window.location.hash)&&t.isInIframe())throw Pr.createBlockReloadInHiddenIframeError()},t.blockRedirectInIframe=function(r,n){var o=t.isInIframe();if(r===e.InteractionType.Redirect&&o&&!n)throw Pr.createRedirectInIframeError(o)},t.blockAcquireTokenInPopups=function(){if(window.opener&&window.opener!==window&&"string"==typeof window.name&&0===window.name.indexOf(Er+"."))throw Pr.createBlockAcquireTokenInPopupsError()},t.blockNonBrowserEnvironment=function(e){if(!e)throw Pr.createNonBrowserEnvironmentError()},t.detectIEOrEdge=function(){var e=window.navigator.userAgent,t=e.indexOf("MSIE "),r=e.indexOf("Trident/"),n=e.indexOf("Edge/");return t>0||r>0||n>0},t}(),Wr=function(){function e(){}return e.prototype.navigateInternal=function(t,r){return e.defaultNavigateWindow(t,r)},e.prototype.navigateExternal=function(t,r){return e.defaultNavigateWindow(t,r)},e.defaultNavigateWindow=function(e,t){return t.noHistory?window.location.replace(e):window.location.assign(e),new Promise((function(e){setTimeout((function(){e(!0)}),t.timeout)}))},e}(),Jr=6e3;var Vr,Yr=function(){function e(e,t,r){this.authModule=e,this.browserStorage=t,this.authCodeRequest=r}return e.prototype.handleCodeResponse=function(e,t,r,n){return o(this,void 0,void 0,(function(){var o,a,s,c,u,h;return i(this,(function(i){switch(i.label){case 0:if(Je.isEmpty(e))throw Pr.createEmptyHashError(e);if(o=this.browserStorage.generateStateKey(t),!(a=this.browserStorage.getTemporaryCache(o)))throw We.createStateNotFoundError("Cached State");return s=this.authModule.handleFragmentResponse(e,a),c=this.browserStorage.generateNonceKey(a),u=this.browserStorage.getTemporaryCache(c),this.authCodeRequest.code=s.code,s.cloud_instance_host_name?[4,this.updateTokenEndpointAuthority(s.cloud_instance_host_name,r,n)]:[3,2];case 1:i.sent(),i.label=2;case 2:return s.nonce=u||void 0,s.state=a,[4,this.authModule.acquireToken(this.authCodeRequest,s)];case 3:return h=i.sent(),this.browserStorage.cleanRequestByState(t),[2,h]}}))}))},e.prototype.updateTokenEndpointAuthority=function(e,t,r){return o(this,void 0,void 0,(function(){var n,o;return i(this,(function(i){switch(i.label){case 0:return n="https://"+e+"/"+t.tenant+"/",[4,hr.createDiscoveredInstance(n,r,this.browserStorage,t.options)];case 1:return o=i.sent(),this.authModule.updateAuthority(o),[2]}}))}))},e}(),Xr=function(t){function n(e,r,n,o){var i=t.call(this,e,r,n)||this;return i.browserCrypto=o,i}return r(n,t),n.prototype.initiateAuthRequest=function(t,r){return o(this,void 0,void 0,(function(){var n;return i(this,(function(o){switch(o.label){case 0:return this.authModule.logger.verbose("RedirectHandler.initiateAuthRequest called"),Je.isEmpty(t)?[3,7]:(r.redirectStartPage&&(this.authModule.logger.verbose("RedirectHandler.initiateAuthRequest: redirectStartPage set to true, caching start page"),this.browserStorage.setTemporaryCache(ir.ORIGIN_URI,r.redirectStartPage,!0)),this.browserStorage.setTemporaryCache(ir.INTERACTION_STATUS_KEY,gr,!0),this.browserStorage.cacheCodeRequest(this.authCodeRequest,this.browserCrypto),this.authModule.logger.infoPii("RedirectHandler.initiateAuthRequest: Navigate to:"+t),n={apiId:e.ApiId.acquireTokenRedirect,timeout:r.redirectTimeout,noHistory:!1},"function"!=typeof r.onRedirectNavigate?[3,4]:(this.authModule.logger.verbose("RedirectHandler.initiateAuthRequest: Invoking onRedirectNavigate callback"),!1===r.onRedirectNavigate(t)?[3,2]:(this.authModule.logger.verbose("RedirectHandler.initiateAuthRequest: onRedirectNavigate did not return false, navigating"),[4,r.navigationClient.navigateExternal(t,n)])));case 1:return o.sent(),[2];case 2:return this.authModule.logger.verbose("RedirectHandler.initiateAuthRequest: onRedirectNavigate returned false, stopping navigation"),[2];case 3:return[3,6];case 4:return this.authModule.logger.verbose("RedirectHandler.initiateAuthRequest: Navigating window to navigate url"),[4,r.navigationClient.navigateExternal(t,n)];case 5:return o.sent(),[2];case 6:return[3,8];case 7:throw this.authModule.logger.info("RedirectHandler.initiateAuthRequest: Navigate url is empty"),Pr.createEmptyNavigationUriError();case 8:return[2]}}))}))},n.prototype.handleCodeResponse=function(e,t,r,n,a){return o(this,void 0,void 0,(function(){var o,s,c,u,h,d;return i(this,(function(i){switch(i.label){case 0:if(this.authModule.logger.verbose("RedirectHandler.handleCodeResponse called"),Je.isEmpty(e))throw Pr.createEmptyHashError(e);if(this.browserStorage.removeItem(this.browserStorage.generateCacheKey(ir.INTERACTION_STATUS_KEY)),o=this.browserStorage.generateStateKey(t),!(s=this.browserStorage.getTemporaryCache(o)))throw We.createStateNotFoundError("Cached State");return c=this.authModule.handleFragmentResponse(e,s),u=this.browserStorage.generateNonceKey(s),h=this.browserStorage.getTemporaryCache(u),this.authCodeRequest.code=c.code,c.cloud_instance_host_name?[4,this.updateTokenEndpointAuthority(c.cloud_instance_host_name,r,n)]:[3,2];case 1:i.sent(),i.label=2;case 2:return c.nonce=h||void 0,c.state=s,a&&Nt.removeThrottle(this.browserStorage,a,this.authCodeRequest.authority,this.authCodeRequest.scopes),[4,this.authModule.acquireToken(this.authCodeRequest,c)];case 3:return d=i.sent(),this.browserStorage.cleanRequestByState(t),[2,d]}}))}))},n}(Yr),Zr=function(){function t(e,t){this.browserStorage=e,this.logger=t,this.unloadWindow=this.unloadWindow.bind(this)}return t.prototype.openPopup=function(e,r,n){try{var o=void 0;if(n?(o=n,this.logger.verbosePii("Navigating popup window to: "+e),o.location.assign(e)):void 0===n&&(this.logger.verbosePii("Opening popup window to: "+e),o=t.openSizedPopup(e,r)),!o)throw Pr.createEmptyWindowCreatedError();return o.focus&&o.focus(),this.currentWindow=o,window.addEventListener("beforeunload",this.unloadWindow),o}catch(e){throw this.logger.error("error opening popup "+e.message),this.browserStorage.removeItem(this.browserStorage.generateCacheKey(ir.INTERACTION_STATUS_KEY)),Pr.createPopupWindowError(e.toString())}},t.openSizedPopup=function(e,t){var r=window.screenLeft?window.screenLeft:window.screenX,n=window.screenTop?window.screenTop:window.screenY,o=window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth,i=window.innerHeight||document.documentElement.clientHeight||document.body.clientHeight,a=Math.max(0,o/2-mr/2+r),s=Math.max(0,i/2-vr/2+n);return window.open(e,t,"width="+mr+", height="+vr+", top="+s+", left="+a+", scrollbars=yes")},t.prototype.unloadWindow=function(t){this.browserStorage.cleanRequestByInteractionType(e.InteractionType.Popup),this.currentWindow&&this.currentWindow.close(),t.preventDefault()},t.prototype.cleanPopup=function(e){e&&e.close(),window.removeEventListener("beforeunload",this.unloadWindow),this.browserStorage.removeItem(this.browserStorage.generateCacheKey(ir.INTERACTION_STATUS_KEY))},t.prototype.monitorPopupForSameOrigin=function(e){var t=this;return new Promise((function(r,n){var o=setInterval((function(){if(e.closed)return t.cleanPopup(),clearInterval(o),void n(Pr.createUserCancelledError());var i=P;try{i=e.location.href}catch(e){}Je.isEmpty(i)||"about:blank"===i||(clearInterval(o),r())}),Cr)}))},t.generatePopupName=function(e,t){return Er+"."+e+"."+t.scopes.join("-")+"."+t.authority+"."+t.correlationId},t.generateLogoutPopupName=function(e,t){var r=t.account&&t.account.homeAccountId;return Er+"."+e+"."+r+"."+t.correlationId},t}(),$r=function(e){function t(t,r,n){var o=e.call(this,t,r,n)||this;return o.popupUtils=new Zr(r,t.logger),o}return r(t,e),t.prototype.initiateAuthRequest=function(e,t){if(Je.isEmpty(e))throw this.authModule.logger.error("Navigate url is empty"),Pr.createEmptyNavigationUriError();return this.browserStorage.setTemporaryCache(ir.INTERACTION_STATUS_KEY,gr,!0),this.authModule.logger.infoPii("Navigate to:"+e),this.popupUtils.openPopup(e,t.popupName,t.popup)},t.prototype.monitorPopupForHash=function(e){var t=this;return this.popupUtils.monitorPopupForSameOrigin(e).then((function(){var r=e.location.hash;if(t.popupUtils.cleanPopup(e),!r)throw Pr.createEmptyHashError(e.location.href);if(Qt.hashContainsKnownProperties(r))return r;throw Pr.createHashDoesNotContainKnownPropertiesError()}))},t}(Yr),en=function(e){function t(t,r,n,o){var i=e.call(this,t,r,n)||this;return i.navigateFrameWait=o,i}return r(t,e),t.prototype.initiateAuthRequest=function(e){return o(this,void 0,void 0,(function(){var t;return i(this,(function(r){switch(r.label){case 0:if(Je.isEmpty(e))throw this.authModule.logger.info("Navigate url is empty"),Pr.createEmptyNavigationUriError();return this.navigateFrameWait?[4,this.loadFrame(e)]:[3,2];case 1:return t=r.sent(),[3,3];case 2:t=this.loadFrameSync(e),r.label=3;case 3:return[2,t]}}))}))},t.prototype.monitorIframeForHash=function(e,t){var r=this;return new Promise((function(n,o){t<Jr&&r.authModule.logger.warning("system.loadFrameTimeout or system.iframeHashTimeout set to lower ("+t+"ms) than the default ("+"6000ms). This may result in timeouts.");var i=window.performance.now()+t,a=setInterval((function(){if(window.performance.now()>i)return r.removeHiddenIframe(e),clearInterval(a),void o(Pr.createMonitorIframeTimeoutError());var t=P,s=e.contentWindow;try{t=s?s.location.href:P}catch(e){}if(!Je.isEmpty(t)){var c=s?s.location.hash:P;return Qt.hashContainsKnownProperties(c)?(r.removeHiddenIframe(e),clearInterval(a),void n(c)):void 0}}),Cr)}))},t.prototype.loadFrame=function(e){var t=this;return new Promise((function(r,n){var o=t.createHiddenIframe();setTimeout((function(){o?(o.src=e,r(o)):n("Unable to load iframe")}),t.navigateFrameWait)}))},t.prototype.loadFrameSync=function(e){var t=this.createHiddenIframe();return t.src=e,t},t.prototype.createHiddenIframe=function(){var e=document.createElement("iframe");return e.style.visibility="hidden",e.style.position="absolute",e.style.width=e.style.height="0",e.style.border="0",e.setAttribute("sandbox","allow-scripts allow-same-origin allow-forms"),document.getElementsByTagName("body")[0].appendChild(e),e},t.prototype.removeHiddenIframe=function(e){document.body===e.parentNode&&document.body.removeChild(e)},t}(Yr),tn="2.13.1";(Vr=e.EventType||(e.EventType={})).LOGIN_START="msal:loginStart",Vr.LOGIN_SUCCESS="msal:loginSuccess",Vr.LOGIN_FAILURE="msal:loginFailure",Vr.ACQUIRE_TOKEN_START="msal:acquireTokenStart",Vr.ACQUIRE_TOKEN_SUCCESS="msal:acquireTokenSuccess",Vr.ACQUIRE_TOKEN_FAILURE="msal:acquireTokenFailure",Vr.ACQUIRE_TOKEN_NETWORK_START="msal:acquireTokenFromNetworkStart",Vr.SSO_SILENT_START="msal:ssoSilentStart",Vr.SSO_SILENT_SUCCESS="msal:ssoSilentSuccess",Vr.SSO_SILENT_FAILURE="msal:ssoSilentFailure",Vr.HANDLE_REDIRECT_START="msal:handleRedirectStart",Vr.HANDLE_REDIRECT_END="msal:handleRedirectEnd",Vr.POPUP_OPENED="msal:popupOpened",Vr.LOGOUT_START="msal:logoutStart",Vr.LOGOUT_SUCCESS="msal:logoutSuccess",Vr.LOGOUT_FAILURE="msal:logoutFailure",Vr.LOGOUT_END="msal:logoutEnd";var rn=function(t){function a(e){return t.call(this,e)||this}return r(a,t),a.prototype.loginRedirect=function(e){return o(this,void 0,void 0,(function(){return i(this,(function(t){return this.logger.verbose("loginRedirect called"),[2,this.acquireTokenRedirect(e||Sr)]}))}))},a.prototype.loginPopup=function(e){return this.logger.verbose("loginPopup called"),this.acquireTokenPopup(e||Sr)},a.prototype.acquireTokenSilent=function(t){return o(this,void 0,void 0,(function(){var r,o,a,s,c,u;return i(this,(function(i){switch(i.label){case 0:if(this.preflightBrowserEnvironmentCheck(e.InteractionType.Silent),this.logger.verbose("acquireTokenSilent called"),!(r=t.account||this.getActiveAccount()))throw Pr.createNoAccountError();o=n(n(n({},t),this.initializeBaseRequest(t)),{account:r,forceRefresh:t.forceRefresh||!1}),this.emitEvent(e.EventType.ACQUIRE_TOKEN_START,e.InteractionType.Silent,t),i.label=1;case 1:return i.trys.push([1,4,,9]),a=this.initializeServerTelemetryManager(e.ApiId.acquireTokenSilent_silentFlow,o.correlationId),[4,this.createSilentFlowClient(a,o.authority)];case 2:return[4,i.sent().acquireCachedToken(o)];case 3:return s=i.sent(),this.emitEvent(e.EventType.ACQUIRE_TOKEN_SUCCESS,e.InteractionType.Silent,s),[2,s];case 4:i.sent(),i.label=5;case 5:return i.trys.push([5,7,,8]),[4,this.acquireTokenByRefreshToken(o)];case 6:return c=i.sent(),this.emitEvent(e.EventType.ACQUIRE_TOKEN_SUCCESS,e.InteractionType.Silent,c),[2,c];case 7:throw u=i.sent(),this.emitEvent(e.EventType.ACQUIRE_TOKEN_FAILURE,e.InteractionType.Silent,null,u),u;case 8:return[3,9];case 9:return[2]}}))}))},a}(function(){function t(t){var r,o,i,a,s,c,u,h,d,l,p,f;if(this.isBrowserEnvironment="undefined"!=typeof window,this.config=(r=t,o=this.isBrowserEnvironment,i=r.auth,a=r.cache,s=r.system,c={clientId:"",authority:""+T,knownAuthorities:[],cloudDiscoveryMetadata:"",authorityMetadata:"",redirectUri:"",postLogoutRedirectUri:"",navigateToLoginRequestUrl:!0,clientCapabilities:[],protocolMode:e.ProtocolMode.AAD},u={cacheLocation:e.BrowserCacheLocation.SessionStorage,storeAuthStateInCookie:!1,secureCookies:!1},h={loggerCallback:function(){},logLevel:e.LogLevel.Info,piiLoggingEnabled:!1},d=n(n({},At),{loggerOptions:h,networkClient:o?Qr.getBrowserNetworkClient():pr,navigationClient:new Wr,loadFrameTimeout:0,windowHashTimeout:s&&s.loadFrameTimeout||6e4,iframeHashTimeout:s&&s.loadFrameTimeout||Jr,navigateFrameWait:o&&Qr.detectIEOrEdge()?500:0,redirectNavigationTimeout:3e4,asyncPopups:!1,allowRedirectInIframe:!1}),{auth:n(n({},c),i),cache:n(n({},u),a),system:n(n({},d),s)}),this.activeLocalAccountId=null,this.eventCallbacks=new Map,this.logger=new Ye(this.config.system.loggerOptions,"@azure/msal-browser",tn),this.networkClient=this.config.system.networkClient,this.navigationClient=this.config.system.navigationClient,this.redirectResponse=new Map,!this.isBrowserEnvironment)return this.browserStorage=(l=this.config.auth.clientId,p=this.logger,f={cacheLocation:e.BrowserCacheLocation.MemoryStorage,storeAuthStateInCookie:!1,secureCookies:!1},new Gr(l,f,se,p)),void(this.browserCrypto=se);this.browserCrypto=new Dr,this.browserStorage=new Gr(this.config.auth.clientId,this.config.cache,this.browserCrypto,this.logger)}return t.prototype.handleRedirectPromise=function(t){return o(this,void 0,void 0,(function(){var r,n,o,a=this;return i(this,(function(i){return this.emitEvent(e.EventType.HANDLE_REDIRECT_START,e.InteractionType.Redirect),this.logger.verbose("handleRedirectPromise called"),r=this.getAllAccounts(),this.isBrowserEnvironment?(n=t||P,void 0===(o=this.redirectResponse.get(n))?(this.logger.verbose("handleRedirectPromise has been called for the first time, storing the promise"),o=this.handleRedirectResponse(t).then((function(t){t&&(r.length<a.getAllAccounts().length?(a.emitEvent(e.EventType.LOGIN_SUCCESS,e.InteractionType.Redirect,t),a.logger.verbose("handleRedirectResponse returned result, login success")):(a.emitEvent(e.EventType.ACQUIRE_TOKEN_SUCCESS,e.InteractionType.Redirect,t),a.logger.verbose("handleRedirectResponse returned result, acquire token success")));return a.emitEvent(e.EventType.HANDLE_REDIRECT_END,e.InteractionType.Redirect),t})).catch((function(t){throw r.length>0?a.emitEvent(e.EventType.ACQUIRE_TOKEN_FAILURE,e.InteractionType.Redirect,null,t):a.emitEvent(e.EventType.LOGIN_FAILURE,e.InteractionType.Redirect,null,t),a.emitEvent(e.EventType.HANDLE_REDIRECT_END,e.InteractionType.Redirect),t})),this.redirectResponse.set(n,o)):this.logger.verbose("handleRedirectPromise has been called previously, returning the result from the first call"),[2,o]):(this.logger.verbose("handleRedirectPromise returns null, not browser environment"),[2,null])}))}))},t.prototype.handleRedirectResponse=function(t){return o(this,void 0,void 0,(function(){var r,n,o,a,s,c,u,h,d;return i(this,(function(i){switch(i.label){case 0:if(!this.interactionInProgress())return this.logger.info("handleRedirectPromise called but there is no interaction in progress, returning null."),[2,null];if(!(r=this.getRedirectResponseHash(t||window.location.hash)))return this.logger.info("handleRedirectPromise did not detect a response hash as a result of a redirect. Cleaning temporary cache."),this.browserStorage.cleanRequestByInteractionType(e.InteractionType.Redirect),[2,null];try{n=this.validateAndExtractStateFromHash(r,e.InteractionType.Redirect),Qr.clearHash(),this.logger.verbose("State extracted from hash")}catch(t){return this.logger.info("handleRedirectPromise was unable to extract state due to: "+t),this.browserStorage.cleanRequestByInteractionType(e.InteractionType.Redirect),[2,null]}return o=this.browserStorage.getTemporaryCache(ir.ORIGIN_URI,!0)||"",a=Qt.removeHashFromUrl(o),s=Qt.removeHashFromUrl(window.location.href),a===s&&this.config.auth.navigateToLoginRequestUrl?(this.logger.verbose("Current page is loginRequestUrl, handling hash"),[4,this.handleHash(r,n)]):[3,2];case 1:return c=i.sent(),o.indexOf("#")>-1&&Qr.replaceHash(o),[2,c];case 2:return this.config.auth.navigateToLoginRequestUrl?[3,3]:(this.logger.verbose("NavigateToLoginRequestUrl set to false, handling hash"),[2,this.handleHash(r,n)]);case 3:return Qr.isInIframe()?[3,8]:(this.browserStorage.setTemporaryCache(ir.URL_HASH,r,!0),u={apiId:e.ApiId.handleRedirectPromise,timeout:this.config.system.redirectNavigationTimeout,noHistory:!0},h=!0,o&&"null"!==o?[3,5]:(d=Qr.getHomepage(),this.browserStorage.setTemporaryCache(ir.ORIGIN_URI,d,!0),this.logger.warning("Unable to get valid login request url from cache, redirecting to home page"),[4,this.navigationClient.navigateInternal(d,u)]));case 4:return h=i.sent(),[3,7];case 5:return this.logger.verbose("Navigating to loginRequestUrl: "+o),[4,this.navigationClient.navigateInternal(o,u)];case 6:h=i.sent(),i.label=7;case 7:if(!h)return[2,this.handleHash(r,n)];i.label=8;case 8:return[2,null]}}))}))},t.prototype.getRedirectResponseHash=function(e){this.logger.verbose("getRedirectResponseHash called");var t=Qt.hashContainsKnownProperties(e),r=this.browserStorage.getTemporaryCache(ir.URL_HASH,!0);return this.browserStorage.removeItem(this.browserStorage.generateCacheKey(ir.URL_HASH)),t?(this.logger.verbose("Hash contains known properties, returning response hash"),e):(this.logger.verbose("Hash does not contain known properties, returning cached hash"),r)},t.prototype.validateAndExtractStateFromHash=function(e,t){this.logger.verbose("validateAndExtractStateFromHash called");var r=Qt.getDeserializedHash(e);if(!r.state)throw Pr.createHashDoesNotContainStateError();var n=Br.extractBrowserRequestState(this.browserCrypto,r.state);if(!n)throw Pr.createUnableToParseStateError();if(n.interactionType!==t)throw Pr.createStateInteractionTypeMismatchError();return this.logger.verbose("Returning state from hash"),r.state},t.prototype.handleHash=function(t,r){return o(this,void 0,void 0,(function(){var n,o,a,s,c;return i(this,(function(i){switch(i.label){case 0:this.logger.verbose("handleHash called"),n=this.browserStorage.getCachedRequest(r,this.browserCrypto),o=this.initializeServerTelemetryManager(e.ApiId.handleRedirectPromise,n.correlationId),i.label=1;case 1:if(i.trys.push([1,4,,5]),!(a=this.browserStorage.getCachedAuthority(r)))throw Pr.createNoCachedAuthorityError();return[4,this.createAuthCodeClient(o,a)];case 2:return s=i.sent(),[4,new Xr(s,this.browserStorage,n,this.browserCrypto).handleCodeResponse(t,r,s.authority,this.networkClient,this.config.auth.clientId)];case 3:return[2,i.sent()];case 4:throw c=i.sent(),o.cacheFailedRequest(c),this.browserStorage.cleanRequestByInteractionType(e.InteractionType.Redirect),c;case 5:return[2]}}))}))},t.prototype.acquireTokenRedirect=function(t){return o(this,void 0,void 0,(function(){var r,n,o,a,s,c,u,h,d;return i(this,(function(i){switch(i.label){case 0:this.preflightBrowserEnvironmentCheck(e.InteractionType.Redirect),this.logger.verbose("acquireTokenRedirect called"),(r=this.getAllAccounts().length>0)?this.emitEvent(e.EventType.ACQUIRE_TOKEN_START,e.InteractionType.Redirect,t):this.emitEvent(e.EventType.LOGIN_START,e.InteractionType.Redirect,t),n=this.preflightInteractiveRequest(t,e.InteractionType.Redirect),o=this.initializeServerTelemetryManager(e.ApiId.acquireTokenRedirect,n.correlationId),i.label=1;case 1:return i.trys.push([1,5,,6]),[4,this.initializeAuthorizationCodeRequest(n)];case 2:return a=i.sent(),[4,this.createAuthCodeClient(o,n.authority)];case 3:return s=i.sent(),c=new Xr(s,this.browserStorage,a,this.browserCrypto),[4,s.getAuthCodeUrl(n)];case 4:return u=i.sent(),h=this.getRedirectStartPage(t.redirectStartPage),[2,c.initiateAuthRequest(u,{navigationClient:this.navigationClient,redirectTimeout:this.config.system.redirectNavigationTimeout,redirectStartPage:h,onRedirectNavigate:t.onRedirectNavigate})];case 5:throw d=i.sent(),r?this.emitEvent(e.EventType.ACQUIRE_TOKEN_FAILURE,e.InteractionType.Redirect,null,d):this.emitEvent(e.EventType.LOGIN_FAILURE,e.InteractionType.Redirect,null,d),o.cacheFailedRequest(d),this.browserStorage.cleanRequestByState(n.state),d;case 6:return[2]}}))}))},t.prototype.acquireTokenPopup=function(t){var r;try{this.preflightBrowserEnvironmentCheck(e.InteractionType.Popup),this.logger.verbose("acquireTokenPopup called"),r=this.preflightInteractiveRequest(t,e.InteractionType.Popup)}catch(e){return Promise.reject(e)}var n=Zr.generatePopupName(this.config.auth.clientId,r);if(this.config.system.asyncPopups)return this.logger.verbose("asyncPopups set to true, acquiring token"),this.acquireTokenPopupAsync(r,n);this.logger.verbose("asyncPopup set to false, opening popup before acquiring token");var o=Zr.openSizedPopup("about:blank",n);return this.acquireTokenPopupAsync(r,n,o)},t.prototype.acquireTokenPopupAsync=function(t,r,n){return o(this,void 0,void 0,(function(){var o,a,s,c,u,h,d,l,p,f,g,y;return i(this,(function(i){switch(i.label){case 0:this.logger.verbose("acquireTokenPopupAsync called"),(o=this.getAllAccounts()).length>0?this.emitEvent(e.EventType.ACQUIRE_TOKEN_START,e.InteractionType.Popup,t):this.emitEvent(e.EventType.LOGIN_START,e.InteractionType.Popup,t),a=this.initializeServerTelemetryManager(e.ApiId.acquireTokenPopup,t.correlationId),i.label=1;case 1:return i.trys.push([1,7,,8]),[4,this.initializeAuthorizationCodeRequest(t)];case 2:return s=i.sent(),[4,this.createAuthCodeClient(a,t.authority)];case 3:return[4,(c=i.sent()).getAuthCodeUrl(t)];case 4:return u=i.sent(),h=new $r(c,this.browserStorage,s),d={popup:n,popupName:r},l=h.initiateAuthRequest(u,d),this.emitEvent(e.EventType.POPUP_OPENED,e.InteractionType.Popup,{popupWindow:l},null),[4,h.monitorPopupForHash(l)];case 5:return p=i.sent(),f=this.validateAndExtractStateFromHash(p,e.InteractionType.Popup),Nt.removeThrottle(this.browserStorage,this.config.auth.clientId,s.authority,s.scopes),[4,h.handleCodeResponse(p,f,c.authority,this.networkClient)];case 6:return g=i.sent(),o.length<this.getAllAccounts().length?this.emitEvent(e.EventType.LOGIN_SUCCESS,e.InteractionType.Popup,g):this.emitEvent(e.EventType.ACQUIRE_TOKEN_SUCCESS,e.InteractionType.Popup,g),[2,g];case 7:throw y=i.sent(),o.length>0?this.emitEvent(e.EventType.ACQUIRE_TOKEN_FAILURE,e.InteractionType.Popup,null,y):this.emitEvent(e.EventType.LOGIN_FAILURE,e.InteractionType.Popup,null,y),a.cacheFailedRequest(y),this.browserStorage.cleanRequestByState(t.state),y;case 8:return[2]}}))}))},t.prototype.ssoSilent=function(t){return o(this,void 0,void 0,(function(){var r,n;return i(this,(function(o){switch(o.label){case 0:this.preflightBrowserEnvironmentCheck(e.InteractionType.Silent),this.logger.verbose("ssoSilent called"),this.emitEvent(e.EventType.SSO_SILENT_START,e.InteractionType.Silent,t),o.label=1;case 1:return o.trys.push([1,3,,4]),[4,this.acquireTokenByIframe(t,e.ApiId.ssoSilent)];case 2:return r=o.sent(),this.emitEvent(e.EventType.SSO_SILENT_SUCCESS,e.InteractionType.Silent,r),[2,r];case 3:throw n=o.sent(),this.emitEvent(e.EventType.SSO_SILENT_FAILURE,e.InteractionType.Silent,null,n),n;case 4:return[2]}}))}))},t.prototype.acquireTokenByIframe=function(t,r){return o(this,void 0,void 0,(function(){var o,a,s,c,u,h;return i(this,(function(i){switch(i.label){case 0:if(this.logger.verbose("acquireTokenByIframe called"),Je.isEmpty(t.loginHint)&&Je.isEmpty(t.sid)&&(!t.account||Je.isEmpty(t.account.username)))throw Pr.createSilentSSOInsufficientInfoError();if(t.prompt&&t.prompt!==L.NONE)throw Pr.createSilentPromptValueError(t.prompt);o=this.initializeAuthorizationRequest(n(n({},t),{prompt:L.NONE}),e.InteractionType.Silent),a=this.initializeServerTelemetryManager(r,o.correlationId),i.label=1;case 1:return i.trys.push([1,6,,7]),[4,this.initializeAuthorizationCodeRequest(o)];case 2:return s=i.sent(),[4,this.createAuthCodeClient(a,o.authority)];case 3:return[4,(c=i.sent()).getAuthCodeUrl(o)];case 4:return u=i.sent(),[4,this.silentTokenHelper(u,s,c)];case 5:return[2,i.sent()];case 6:throw h=i.sent(),a.cacheFailedRequest(h),this.browserStorage.cleanRequestByState(o.state),h;case 7:return[2]}}))}))},t.prototype.acquireTokenByRefreshToken=function(t){return o(this,void 0,void 0,(function(){var r,o,a,s,c,u;return i(this,(function(i){switch(i.label){case 0:this.emitEvent(e.EventType.ACQUIRE_TOKEN_NETWORK_START,e.InteractionType.Silent,t),Qr.blockReloadInHiddenIframes(),r=n(n({},t),this.initializeBaseRequest(t)),o=this.initializeServerTelemetryManager(e.ApiId.acquireTokenSilent_silentFlow,r.correlationId),i.label=1;case 1:return i.trys.push([1,4,,7]),[4,this.createRefreshTokenClient(o,r.authority)];case 2:return[4,i.sent().acquireTokenByRefreshToken(r)];case 3:return[2,i.sent()];case 4:return a=i.sent(),o.cacheFailedRequest(a),s=a instanceof Pt,c=a instanceof Gt,u=a.errorCode===yr,s&&u&&!c?(this.logger.verbose("Refresh token expired or invalid, attempting acquire token by iframe"),[4,this.acquireTokenByIframe(t,e.ApiId.acquireTokenSilent_authCode)]):[3,6];case 5:return[2,i.sent()];case 6:throw a;case 7:return[2]}}))}))},t.prototype.silentTokenHelper=function(t,r,n){return o(this,void 0,void 0,(function(){var o,a,s,c;return i(this,(function(i){switch(i.label){case 0:return[4,(o=new en(n,this.browserStorage,r,this.config.system.navigateFrameWait)).initiateAuthRequest(t)];case 1:return a=i.sent(),[4,o.monitorIframeForHash(a,this.config.system.iframeHashTimeout)];case 2:return s=i.sent(),c=this.validateAndExtractStateFromHash(s,e.InteractionType.Silent),[2,o.handleCodeResponse(s,c,n.authority,this.networkClient)]}}))}))},t.prototype.logout=function(e){return o(this,void 0,void 0,(function(){return i(this,(function(t){return this.logger.warning("logout API is deprecated and will be removed in msal-browser v3.0.0. Use logoutRedirect instead."),[2,this.logoutRedirect(e)]}))}))},t.prototype.logoutRedirect=function(t){return o(this,void 0,void 0,(function(){var r,n,o,a,s,c;return i(this,(function(i){switch(i.label){case 0:this.preflightBrowserEnvironmentCheck(e.InteractionType.Redirect),this.logger.verbose("logoutRedirect called"),r=this.initializeLogoutRequest(t),n=this.initializeServerTelemetryManager(e.ApiId.logout,r.correlationId),i.label=1;case 1:return i.trys.push([1,9,,10]),this.emitEvent(e.EventType.LOGOUT_START,e.InteractionType.Redirect,t),[4,this.createAuthCodeClient(n,t&&t.authority)];case 2:return o=i.sent(),a=o.getLogoutUri(r),r.account&&!Tt.accountInfoIsEqual(r.account,this.getActiveAccount())||(this.logger.verbose("Setting active account to null"),this.setActiveAccount(null)),s={apiId:e.ApiId.logout,timeout:this.config.system.redirectNavigationTimeout,noHistory:!1},this.emitEvent(e.EventType.LOGOUT_SUCCESS,e.InteractionType.Redirect,r),t&&"function"==typeof t.onRedirectNavigate?!1===t.onRedirectNavigate(a)?[3,4]:(this.logger.verbose("Logout onRedirectNavigate did not return false, navigating"),[4,this.navigationClient.navigateExternal(a,s)]):[3,6];case 3:return i.sent(),[2];case 4:this.logger.verbose("Logout onRedirectNavigate returned false, stopping navigation"),i.label=5;case 5:return[3,8];case 6:return[4,this.navigationClient.navigateExternal(a,s)];case 7:return i.sent(),[2];case 8:return[3,10];case 9:throw c=i.sent(),n.cacheFailedRequest(c),this.emitEvent(e.EventType.LOGOUT_FAILURE,e.InteractionType.Redirect,null,c),c;case 10:return this.emitEvent(e.EventType.LOGOUT_END,e.InteractionType.Redirect),[2]}}))}))},t.prototype.logoutPopup=function(t){var r;try{this.preflightBrowserEnvironmentCheck(e.InteractionType.Popup),this.logger.verbose("logoutPopup called"),r=this.initializeLogoutRequest(t)}catch(e){return Promise.reject(e)}var n,o=Zr.generateLogoutPopupName(this.config.auth.clientId,r);this.config.system.asyncPopups?this.logger.verbose("asyncPopups set to true"):(this.logger.verbose("asyncPopup set to false, opening popup"),n=Zr.openSizedPopup("about:blank",o));var i=t&&t.authority,a=t&&t.mainWindowRedirectUri;return this.logoutPopupAsync(r,o,i,n,a)},t.prototype.logoutPopupAsync=function(t,r,n,a,s){return o(this,void 0,void 0,(function(){var o,c,u,h,d,l,p,f,g;return i(this,(function(i){switch(i.label){case 0:this.logger.verbose("logoutPopupAsync called"),this.emitEvent(e.EventType.LOGOUT_START,e.InteractionType.Popup,t),o=this.initializeServerTelemetryManager(e.ApiId.logoutPopup,t.correlationId),i.label=1;case 1:return i.trys.push([1,7,,8]),this.browserStorage.setTemporaryCache(ir.INTERACTION_STATUS_KEY,gr,!0),[4,this.createAuthCodeClient(o,n)];case 2:c=i.sent(),u=c.getLogoutUri(t),t.account&&!Tt.accountInfoIsEqual(t.account,this.getActiveAccount())||(this.logger.verbose("Setting active account to null"),this.setActiveAccount(null)),this.emitEvent(e.EventType.LOGOUT_SUCCESS,e.InteractionType.Popup,t),h=new Zr(this.browserStorage,this.logger),d=h.openPopup(u,r,a),this.emitEvent(e.EventType.POPUP_OPENED,e.InteractionType.Popup,{popupWindow:d},null),i.label=3;case 3:return i.trys.push([3,5,,6]),[4,h.monitorPopupForSameOrigin(d)];case 4:return i.sent(),this.logger.verbose("Popup successfully redirected to postLogoutRedirectUri"),[3,6];case 5:return l=i.sent(),this.logger.verbose("Error occurred while monitoring popup for same origin. Session on server may remain active. Error: "+l),[3,6];case 6:return h.cleanPopup(d),s?(p={apiId:e.ApiId.logoutPopup,timeout:this.config.system.redirectNavigationTimeout,noHistory:!1},f=Qt.getAbsoluteUrl(s,Qr.getCurrentUri()),this.logger.verbose("Redirecting main window to url specified in the request"),this.logger.verbosePii("Redirecing main window to: "+f),this.navigationClient.navigateInternal(f,p)):this.logger.verbose("No main window navigation requested"),[3,8];case 7:throw g=i.sent(),this.browserStorage.removeItem(this.browserStorage.generateCacheKey(ir.INTERACTION_STATUS_KEY)),this.emitEvent(e.EventType.LOGOUT_FAILURE,e.InteractionType.Popup,null,g),o.cacheFailedRequest(g),g;case 8:return this.emitEvent(e.EventType.LOGOUT_END,e.InteractionType.Popup),[2]}}))}))},t.prototype.getAllAccounts=function(){return this.logger.verbose("getAllAccounts called"),this.isBrowserEnvironment?this.browserStorage.getAllAccounts():[]},t.prototype.getAccountByUsername=function(e){var t=this.getAllAccounts();return!Je.isEmpty(e)&&t&&t.length?(this.logger.verbose("Account matching username found, returning"),this.logger.verbosePii("Returning signed-in accounts matching username: "+e),t.filter((function(t){return t.username.toLowerCase()===e.toLowerCase()}))[0]||null):(this.logger.verbose("getAccountByUsername: No matching account found, returning null"),null)},t.prototype.getAccountByHomeId=function(e){var t=this.getAllAccounts();return!Je.isEmpty(e)&&t&&t.length?(this.logger.verbose("Account matching homeAccountId found, returning"),this.logger.verbosePii("Returning signed-in accounts matching homeAccountId: "+e),t.filter((function(t){return t.homeAccountId===e}))[0]||null):(this.logger.verbose("getAccountByHomeId: No matching account found, returning null"),null)},t.prototype.getAccountByLocalId=function(e){var t=this.getAllAccounts();return!Je.isEmpty(e)&&t&&t.length?(this.logger.verbose("Account matching localAccountId found, returning"),this.logger.verbosePii("Returning signed-in accounts matching localAccountId: "+e),t.filter((function(t){return t.localAccountId===e}))[0]||null):(this.logger.verbose("getAccountByLocalId: No matching account found, returning null"),null)},t.prototype.setActiveAccount=function(e){e?(this.logger.verbose("setActiveAccount: Active account set"),this.activeLocalAccountId=e.localAccountId):(this.logger.verbose("setActiveAccount: No account passed, active account not set"),this.activeLocalAccountId=null)},t.prototype.getActiveAccount=function(){return this.activeLocalAccountId?this.getAccountByLocalId(this.activeLocalAccountId):(this.logger.verbose("getActiveAccount: No active account"),null)},t.prototype.getRedirectUri=function(e){this.logger.verbose("getRedirectUri called");var t=e||this.config.auth.redirectUri||Qr.getCurrentUri();return Qt.getAbsoluteUrl(t,Qr.getCurrentUri())},t.prototype.getRedirectStartPage=function(e){this.logger.verbose("getRedirectStartPage called");var t=e||window.location.href;return Qt.getAbsoluteUrl(t,Qr.getCurrentUri())},t.prototype.getDiscoveredAuthority=function(e){return o(this,void 0,void 0,(function(){var t;return i(this,(function(r){switch(r.label){case 0:return this.logger.verbose("getDiscoveredAuthority called"),t={protocolMode:this.config.auth.protocolMode,knownAuthorities:this.config.auth.knownAuthorities,cloudDiscoveryMetadata:this.config.auth.cloudDiscoveryMetadata,authorityMetadata:this.config.auth.authorityMetadata},e?(this.logger.verbose("Creating discovered authority with request authority"),[4,hr.createDiscoveredInstance(e,this.config.system.networkClient,this.browserStorage,t)]):[3,2];case 1:return[2,r.sent()];case 2:return this.logger.verbose("Creating discovered authority with configured authority"),[4,hr.createDiscoveredInstance(this.config.auth.authority,this.config.system.networkClient,this.browserStorage,t)];case 3:return[2,r.sent()]}}))}))},t.prototype.interactionInProgress=function(){return this.browserStorage.getTemporaryCache(ir.INTERACTION_STATUS_KEY,!0)===gr},t.prototype.createAuthCodeClient=function(e,t){return o(this,void 0,void 0,(function(){var r;return i(this,(function(n){switch(n.label){case 0:return this.logger.verbose("createAuthCodeClient called"),[4,this.getClientConfiguration(e,t)];case 1:return r=n.sent(),[2,new Zt(r)]}}))}))},t.prototype.createSilentFlowClient=function(e,t){return o(this,void 0,void 0,(function(){var r;return i(this,(function(n){switch(n.label){case 0:return this.logger.verbose("createSilentFlowClient called"),[4,this.getClientConfiguration(e,t)];case 1:return r=n.sent(),[2,new er(r)]}}))}))},t.prototype.createRefreshTokenClient=function(e,t){return o(this,void 0,void 0,(function(){var r;return i(this,(function(n){switch(n.label){case 0:return this.logger.verbose("createRefreshTokenClient called"),[4,this.getClientConfiguration(e,t)];case 1:return r=n.sent(),[2,new $t(r)]}}))}))},t.prototype.getClientConfiguration=function(e,t){return o(this,void 0,void 0,(function(){var r;return i(this,(function(n){switch(n.label){case 0:return this.logger.verbose("getClientConfiguration called"),[4,this.getDiscoveredAuthority(t)];case 1:return r=n.sent(),[2,{authOptions:{clientId:this.config.auth.clientId,authority:r,clientCapabilities:this.config.auth.clientCapabilities},systemOptions:{tokenRenewalOffsetSeconds:this.config.system.tokenRenewalOffsetSeconds},loggerOptions:{loggerCallback:this.config.system.loggerOptions.loggerCallback,piiLoggingEnabled:this.config.system.loggerOptions.piiLoggingEnabled},cryptoInterface:this.browserCrypto,networkInterface:this.networkClient,storageInterface:this.browserStorage,serverTelemetryManager:e,libraryInfo:{sku:Tr,version:tn,cpu:"",os:""}}]}}))}))},t.prototype.preflightInteractiveRequest=function(e,t){if(this.logger.verbose("preflightInteractiveRequest called, validating app environment"),Qr.blockReloadInHiddenIframes(),this.interactionInProgress())throw Pr.createInteractionInProgressError();return this.initializeAuthorizationRequest(e,t)},t.prototype.preflightBrowserEnvironmentCheck=function(t){if(this.logger.verbose("preflightBrowserEnvironmentCheck started"),Qr.blockNonBrowserEnvironment(this.isBrowserEnvironment),Qr.blockRedirectInIframe(t,this.config.system.allowRedirectInIframe),Qr.blockReloadInHiddenIframes(),Qr.blockAcquireTokenInPopups(),t===e.InteractionType.Redirect&&this.config.cache.cacheLocation===e.BrowserCacheLocation.MemoryStorage&&!this.config.cache.storeAuthStateInCookie)throw Hr.createInMemoryRedirectUnavailableError()},t.prototype.initializeBaseRequest=function(e){this.logger.verbose("Initializing BaseAuthRequest");var t=e.authority||this.config.auth.authority,r=s(e&&e.scopes||[]),o=e&&e.correlationId||this.browserCrypto.createNewGuid();return n(n({},e),{correlationId:o,authority:t,scopes:r})},t.prototype.initializeServerTelemetryManager=function(e,t,r){this.logger.verbose("initializeServerTelemetryManager called");var n={clientId:this.config.auth.clientId,correlationId:t,apiId:e,forceRefresh:r||!1,wrapperSKU:this.wrapperSKU,wrapperVer:this.wrapperVer};return new fr(n,this.browserStorage)},t.prototype.initializeAuthorizationRequest=function(t,r){this.logger.verbose("initializeAuthorizationRequest called");var o=this.getRedirectUri(t.redirectUri),i={interactionType:r},a=zt.setRequestState(this.browserCrypto,t&&t.state||"",i),s=t.authenticationScheme||e.AuthenticationScheme.BEARER,c=n(n({},this.initializeBaseRequest(t)),{redirectUri:o,state:a,nonce:t.nonce||this.browserCrypto.createNewGuid(),responseMode:D.FRAGMENT,authenticationScheme:s}),u=t.account||this.getActiveAccount();if(u&&(this.logger.verbose("Setting validated request account"),this.logger.verbosePii("Setting validated request account: "+u),c.account=u),Je.isEmpty(c.loginHint)){var h=this.browserStorage.getTemporaryCache(g.ADAL_ID_TOKEN);if(h){var d=new wt(h,this.browserCrypto);this.browserStorage.removeItem(g.ADAL_ID_TOKEN),d.claims&&d.claims.upn&&(this.logger.verbose("No SSO params used and ADAL token retrieved, setting ADAL upn as loginHint"),c.loginHint=d.claims.upn)}}return this.browserStorage.updateCacheEntries(c.state,c.nonce,c.authority),c},t.prototype.initializeAuthorizationCodeRequest=function(e){return o(this,void 0,void 0,(function(){var t,r;return i(this,(function(o){switch(o.label){case 0:return[4,this.browserCrypto.generatePkceCodes()];case 1:return t=o.sent(),r=n(n({},e),{redirectUri:e.redirectUri,code:"",codeVerifier:t.verifier}),e.codeChallenge=t.challenge,e.codeChallengeMethod=R,[2,r]}}))}))},t.prototype.initializeLogoutRequest=function(e){if(this.logger.verbose("initializeLogoutRequest called"),this.interactionInProgress())throw Pr.createInteractionInProgressError();var t=n({correlationId:this.browserCrypto.createNewGuid()},e);return e&&null===e.postLogoutRedirectUri?this.logger.verbose("postLogoutRedirectUri passed as null, not settibng post logout redirect uri"):e&&e.postLogoutRedirectUri?(this.logger.verbose("Setting postLogoutRedirectUri to uri set on logout request"),t.postLogoutRedirectUri=Qt.getAbsoluteUrl(e.postLogoutRedirectUri,Qr.getCurrentUri())):null===this.config.auth.postLogoutRedirectUri?this.logger.verbose("postLogoutRedirectUri configured as null and no uri set on request, not passing post logout redirect"):this.config.auth.postLogoutRedirectUri?(this.logger.verbose("Setting postLogoutRedirectUri to configured uri"),t.postLogoutRedirectUri=Qt.getAbsoluteUrl(this.config.auth.postLogoutRedirectUri,Qr.getCurrentUri())):(this.logger.verbose("Setting postLogoutRedirectUri to current page"),t.postLogoutRedirectUri=Qt.getAbsoluteUrl(Qr.getCurrentUri(),Qr.getCurrentUri())),t},t.prototype.emitEvent=function(e,t,r,n){var o=this;if(this.isBrowserEnvironment){var i={eventType:e,interactionType:t||null,payload:r||null,error:n||null,timestamp:Date.now()};this.logger.info("Emitting event: "+e),this.eventCallbacks.forEach((function(t,r){o.logger.verbose("Emitting event to callback "+r+": "+e),t.apply(null,[i])}))}},t.prototype.addEventCallback=function(e){if(this.isBrowserEnvironment){var t=this.browserCrypto.createNewGuid();return this.eventCallbacks.set(t,e),this.logger.verbose("Event callback registered with id: "+t),t}return null},t.prototype.removeEventCallback=function(e){this.eventCallbacks.delete(e),this.logger.verbose("Event callback "+e+" removed.")},t.prototype.getLogger=function(){return this.logger},t.prototype.setLogger=function(e){this.logger=e},t.prototype.initializeWrapperLibrary=function(e,t){this.wrapperSKU=e,this.wrapperVer=t},t.prototype.setNavigationClient=function(e){this.navigationClient=e},t}()),nn={acquireTokenPopup:function(){return Promise.reject(Hr.createStubPcaInstanceCalledError())},acquireTokenRedirect:function(){return Promise.reject(Hr.createStubPcaInstanceCalledError())},acquireTokenSilent:function(){return Promise.reject(Hr.createStubPcaInstanceCalledError())},getAllAccounts:function(){return[]},getAccountByHomeId:function(){return null},getAccountByUsername:function(){return null},getAccountByLocalId:function(){return null},handleRedirectPromise:function(){return Promise.reject(Hr.createStubPcaInstanceCalledError())},loginPopup:function(){return Promise.reject(Hr.createStubPcaInstanceCalledError())},loginRedirect:function(){return Promise.reject(Hr.createStubPcaInstanceCalledError())},logout:function(){return Promise.reject(Hr.createStubPcaInstanceCalledError())},logoutRedirect:function(){return Promise.reject(Hr.createStubPcaInstanceCalledError())},logoutPopup:function(){return Promise.reject(Hr.createStubPcaInstanceCalledError())},ssoSilent:function(){return Promise.reject(Hr.createStubPcaInstanceCalledError())},addEventCallback:function(){return null},removeEventCallback:function(){},getLogger:function(){throw Hr.createStubPcaInstanceCalledError()},setLogger:function(){},setActiveAccount:function(){},getActiveAccount:function(){return null},initializeWrapperLibrary:function(){},setNavigationClient:function(){}},on=function(){function t(){}return t.getInteractionStatusFromEvent=function(t){switch(t.eventType){case e.EventType.LOGIN_START:return e.InteractionStatus.Login;case e.EventType.SSO_SILENT_START:return e.InteractionStatus.SsoSilent;case e.EventType.ACQUIRE_TOKEN_START:if(t.interactionType===e.InteractionType.Redirect||t.interactionType===e.InteractionType.Popup)return e.InteractionStatus.AcquireToken;break;case e.EventType.HANDLE_REDIRECT_START:return e.InteractionStatus.HandleRedirect;case e.EventType.LOGOUT_START:return e.InteractionStatus.Logout;case e.EventType.LOGIN_SUCCESS:case e.EventType.SSO_SILENT_SUCCESS:case e.EventType.HANDLE_REDIRECT_END:case e.EventType.LOGIN_FAILURE:case e.EventType.SSO_SILENT_FAILURE:case e.EventType.LOGOUT_END:return e.InteractionStatus.None;case e.EventType.ACQUIRE_TOKEN_SUCCESS:case e.EventType.ACQUIRE_TOKEN_FAILURE:if(t.interactionType===e.InteractionType.Redirect||t.interactionType===e.InteractionType.Popup)return e.InteractionStatus.None}return null},t}();e.AccountEntity=Tt,e.AuthError=ae,e.AuthErrorMessage=ie,e.BrowserAuthError=Pr,e.BrowserAuthErrorMessage=Or,e.BrowserConfigurationAuthError=Hr,e.BrowserConfigurationAuthErrorMessage=xr,e.BrowserUtils=Qr,e.EventMessageUtils=on,e.InteractionRequiredAuthError=Gt,e.Logger=Ye,e.NavigationClient=Wr,e.PublicClientApplication=rn,e.StringUtils=Je,e.UrlString=Qt,e.stubbedPublicClientApplication=nn,Object.defineProperty(e,"__esModule",{value:!0})}));
