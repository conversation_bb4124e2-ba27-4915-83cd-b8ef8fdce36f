<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->


  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="Quantumlink">
   <base href="/" />
  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="quantumlink_node">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <!-- Preload main.dart.js -->
  <link rel="preload" href="main.dart.js" as="script" />

  <!-- Include the Flutter runtime -->
  <script src="flutter.js"></script>

  <title>Quantumlink</title>
  <link rel="manifest" href="manifest.json">
<!--  <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAEOYokqBd60JiCUxFKe8-HfFXTcrJHQdQ&loading=async"></script>-->

    <script type="text/javascript" src="modules/msal-browser.min.js"
          integrity="sha384-2Vr9MyareT7qv+wLp1zBt78ZWB4aljfCTMUrml3/cxm0W81ahmDOC6uyNmmn0Vrc"
          crossorigin="anonymous"></script>
  <script src="assets/packages/aad_oauth/assets/msalv2.js"></script>
  <script src="https://apis.google.com/js/platform.js" async defer></script>


  <style>

      #splash-container {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          min-height: 100vh;
          width: 100%;
          margin: 0;
          overflow: hidden;
      }

     #splash img {
         width: auto;
         max-width: 100%;
     }


     #loadingText {
         color: #5E4884;
         font-size: 16px;
         font-family: 'Arial', sans-serif;
         margin-top: 8px;
         width: auto;
     }
  </style>


</head>
<body>
<!--<script>
  window.flutterConfiguration = {
    canvasKitBaseUrl: "/canvaskit/"
  };
</script>-->
<div id="splash-container">
  <picture id="splash">
    <img src="splash/img/light-1x.png" alt="Splash Image">
  </picture>
    <div id="loadingText">Loading...</div> <!-- Text appears immediately below -->
</div>
 <script>
  document.body.style.overflow = "hidden";
</script>

<script>
  window.addEventListener('load', function (ev) {
    // Download main.dart.js
    _flutter.loader.loadEntrypoint({
      serviceWorker: {
        serviceWorkerVersion: {{flutter_service_worker_version}},
      },
      onEntrypointLoaded: function (engineInitializer) {
        let config = {
        canvasKitVariant: "full",
        canvasKitBaseUrl: "/canvaskit/",
        canvasKitWasmUrl: "/canvaskit/canvaskit.wasm",
      };
        engineInitializer.initializeEngine(config).then(function (appRunner) {
          appRunner.runApp();
        });
      }
    });
  });
</script>

<!-- Disable two-finger swipe left and right (back) navigation-->
<script>
  history.pushState(null, null, document.URL);
     window.addEventListener('popstate', function (event) {
         history.pushState(null, null, document.URL);
     });

     window.addEventListener("wheel", function(event) {
       if (event.deltaX < 0) { // Detect left swipe
         event.preventDefault();
       }
     }, { passive: false });
</script>
<!--<script src="main.dart.js" type="application/javascript"></script>-->
<!--<script src="flutter_bootstrap.js" async></script>-->
</body>
</html>
