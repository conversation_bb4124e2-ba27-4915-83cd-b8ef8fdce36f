name: quantumlink_node
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.2.3 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  get_it: ^7.6.7 # DI - Service Locator - Cache
  get: ^4.6.6 # State, Navigation & Dependencies Manager
  rxdart: ^0.27.7 # For Sync Real time
  sizer: ^2.0.15
  bot_toast: ^4.1.3
  shared_preferences: ^2.2.2
  getwidget: ^4.0.0
  intl:
  cached_network_image: ^3.3.1
  flutter_svg: ^2.0.10+1
  shimmer: ^3.0.0
  flutter_side_menu: ^0.4.0
  go_router: ^14.1.1
  data_table_2: 2.5.11
  dropdown_button2: ^2.3.9
  dotted_border: ^2.1.0
  csv: ^6.0.0
  file_picker: ^8.0.3
  toastification: ^2.3.0
  http: ^1.2.1
  dio: ^5.4.3
  aad_oauth: ^1.0.1
  lottie: ^3.1.0
  fl_chart: ^0.68.0
  percent_indicator: ^4.2.3
  jwt_decoder: ^2.0.1
  simple_gradient_text: ^1.3.0
  json_annotation: ^4.9.0
  flutter_map: ^6.1.0 # OpenStreetMap implementation for Flutter
  latlong2: ^0.9.0 # For handling latitude/longitude coordinates
  universal_html: ^2.2.4
  web_socket_channel: ^3.0.1
  json_view: ^0.4.2
  flutter_localizations:
    sdk: flutter
  geolocator: ^13.0.2
  graphview: ^1.2.0
  firebase_core: 3.13.0
  cloud_firestore: 5.6.7
  firebase_auth: ^5.5.0
  flutter_map_marker_cluster: ^1.3.6





dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0
  build_runner: ^2.4.9
  json_serializable: ^6.8.0
  intl_utils: ^2.8.7
  


# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  assets:
    - assets/img/
    - assets/fonts/
    - assets/ic/
    - assets/animations/
    - assets/data/
    - assets/version
    - assets/amps/

  fonts:
    - family: OpenSans
      fonts:
        - asset: assets/fonts/opensans/OpenSans-Medium.ttf
        - asset: assets/fonts/opensans/OpenSans-Regular.ttf
        - asset: assets/fonts/opensans/OpenSans-SemiBold.ttf


flutter_intl:
  main_locale: en_US
  enabled: true
  arb_dir: lib/l10n
  output_dir: lib/generated  


