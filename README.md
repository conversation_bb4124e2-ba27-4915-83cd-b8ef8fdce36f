# quantumlink-central

# NOTES : 
# dart run build_runner build --delete-conflicting-outputs 


# Flutter SDK : 3.22.2

 [State]
# GetIt [API Service] - https://www.sandromaglione.com/articles/how_to_implement_dependecy_injection_in_flutter
# Controller - State using GetX [update()-Page Load or rX Object-Soft Load]
# Get.put or GetBuilder & Get.find if already put|builder 

 [Flavor] - [https://dwirandyh.medium.com/create-build-flavor-in-flutter-application-ios-android-fb35a81a9fac]
# Download appropriate google service.json | Set Base Url in Main file
# PROD -> Checkout dev branch and flutter run -t lib/main.dart --flavor prod         
    - flutter clean and flutter build apk --release -t lib/main.dart --flavor prod    
# DEV -> Checkout main branch and flutter run -t lib/main_dev.dart --flavor dev     
    - flutter clean and flutter build apk --    release -t lib/main_dev.dart --flavor dev

# Notes [For Separation Of Logic Must have controller for each page and also reusable on other pages if required and should be defined on page view model]

 [Routing]
# https://codewithandrea.com/articles/flutter-bottom-navigation-bar-nested-routes-gorouter/#does-stateful-nested-navigation-work-on-flutter-web? Github Example 
# https://stackoverflow.com/questions/********/how-to-use-shell-route-with-goroute-in-same-hierarchy-routes
# https://stackoverflow.com/questions/********/go-router-how-to-implement-nested-navigation-inside-a-widget 
 [Data Table]
# https://github.com/maxim-saplin/data_table_2/blob/main/example/lib/screens/paginated_data_table2.dart
# https://github.com/maxim-saplin/data_table_2/blob/main/example/lib/data_sources.dart
 [Oauth]
# https://medium.com/@mdshariqueminhaz/microsoft-login-for-flutter-a-step-by-step-guide-3108a5462883
# https://stackoverflow.com/questions/********/graph-api-insufficient-privileges-to-complete-the-operation

# TO EXECUTE QL NODE UI : flutter run -t lib/main_ql_node.dart --web-port=10001

# flutter build web --release --web-renderer canvaskit [Offline]

# https://github.com/dart-lang/pub/issues/4406 -> flutter pub global activate intl_utils