import 'package:quantumlink_node/app_import.dart';



class MapTopologyRepositoryImpl implements MapTopologyRepository {
  RestConstants restConstants = RestConstants.instance;
  RestHelper helper = RestHelper.instance;

  @override
  Future<dynamic> getMapLayersData({
    required BuildContext context,
    required MapLayersRequest request,
  }) async {
    final response = await helper.postRestCallWithResponse(
      otherBaseUrl: AppConfig.shared.mapUrl,
      endpoint: restConstants.mapLayers,
      body: request.toJson(),
      context: context,
    );

    if (response != null && response.statusCode == 200) {
      final responseData = jsonDecode(response.body);
      MapLayersResponse? mapLayersResponse = MapLayersResponse.fromJson( {
        "layers": {
          "devices": {
            "type": "FeatureCollection",
            "features": [
              {
                "type": "Feature",
                "geometry": {
                  "type": "Point",
                  "coordinates": [
                    -84.143039,
                    33.31557
                  ]
                },
                "properties": {
                  "device_eui": "002926000000c9c5",
                  "status": "online",
                  "vendor_code": "AOI",
                  "device_alias": "MINI HGD 1",
                  "description": "Mini HGD 1",
                  "last_seen": 1755074498.833879,
                  "type": "Line Extender",
                  "placement": "indoor_commercial",
                  "prod_id": 304,
                  "bw_mode": 0
                }
              },
              {
                "type": "Feature",
                "geometry": {
                  "type": "Point",
                  "coordinates": [
                    -84.143039,
                    33.31557
                  ]
                },
                "properties": {
                  "device_eui": "002926000000c9c2",
                  "status": "offline",
                  "vendor_code": "AOI",
                  "device_alias": "MINI HGD 1",
                  "description": "Mini HGD 1",
                  "last_seen": 1755074498.833879,
                  "type": "Line Extender",
                  "placement": "indoor_commercial",
                  "prod_id": 304,
                  "bw_mode": 0
                }
              },
              {
                "type": "Feature",
                "geometry": {
                  "type": "Point",
                  "coordinates": [
                    -84.14338000000001,
                    33.34025
                  ]
                },
                "properties": {
                  "device_eui": "002926000002049a",
                  "status": "offline",
                  "vendor_code": "AOI",
                  "device_alias": "Mini LE #3",
                  "description": "Mini Dev  LE #3",
                  "last_seen": 1753865700.484476,
                  "type": "Line Extender",
                  "placement": "underground",
                  "prod_id": 304,
                  "bw_mode": 0
                }
              },
              {
                "type": "Feature",
                "geometry": {
                  "type": "Point",
                  "coordinates": [
                    -84.143039,
                    33.31557
                  ]
                },
                "properties": {
                  "device_eui": "002926000003cc7a",
                  "status": "offline",
                  "vendor_code": "AOI",
                  "device_alias": "Mini%20LE%20%20%233",
                  "description": "Mini%20Dev%20%20LE%20%20%233",
                  "last_seen": 1755533707.76269,
                  "type": "Line Extender",
                  "placement": "aerial",
                  "prod_id": 288,
                  "bw_mode": 1
                }
              },
              {
                "type": "Feature",
                "geometry": {
                  "type": "Point",
                  "coordinates": [
                    -84.143039,
                    33.31560
                  ]
                },
                "properties": {
                  "device_eui": "002926000000c999",
                  "status": "online",
                  "vendor_code": "AOI",
                  "device_alias": "MINI HGD 1",
                  "description": "Mini HGD 1",
                  "last_seen": 1755074498.833879,
                  "type": "Line Extender",
                  "placement": "indoor_commercial",
                  "prod_id": 304,
                  "bw_mode": 0
                }
              },
              {
                "type": "Feature",
                "geometry": {
                  "type": "Point",
                  "coordinates": [
                    -84.143039,
                    33.31560
                  ]
                },
                "properties": {
                  "device_eui": "002926000000c910",
                  "status": "online",
                  "vendor_code": "AOI",
                  "device_alias": "MINI HGD 1",
                  "description": "Mini HGD 1",
                  "last_seen": 1755074498.833879,
                  "type": "Line Extender",
                  "placement": "indoor_commercial",
                  "prod_id": 304,
                  "bw_mode": 0
                }
              },

            ]
          },
          "gateways": {
            "type": "FeatureCollection",
            "features": []
          },
          "device_links": {
            "type": "FeatureCollection",
            "features": []
          }
        },
        "bbox": [
          -84.143039,
          33.261772,
          -84.11379699999999,
          33.31557
        ],
        "zoom_level": 12
      });
      final parsedData = ParsedMapLayersData.fromMapLayersResponse(mapLayersResponse);
      return parsedData;
    }
    return ParsedMapLayersData.empty();
  }
}