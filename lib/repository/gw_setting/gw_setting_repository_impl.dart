import 'dart:io';
import 'package:quantumlink_node/app_import.dart';

class GwSettingRepositoryImpl implements GwSettingRepository {
  RestHelper restServices = RestHelper.instance;
  RestConstants restConstants = RestConstants.instance;

  @override
  Future<dynamic> gwSettingHealthCheck({required BuildContext context}) async {
    try {
      final response = await restServices.getRestCall(
          otherBaseUrl: AppConfig.shared.gwSettingUrl,
          endpoint: restConstants.healthCheck,
          context: context);
      if (response != null && response.isNotEmpty) {
        Map<String, dynamic> data = jsonDecode(response);
        return data;
      }
    } on SocketException catch (e) {
      debugLogs('catch exception in gwSettingHealthCheck ---> ${e.message}');
    }
    return null;
  }


  @override
  Future<dynamic> getStats(
      {required BuildContext context, String? gwID}) async {
    try {
      String ? addOns = gwID != null ? "?${restConstants.gwKey}=$gwID" : null;
      final response = await restServices.getRestCall(
          otherBaseUrl: AppConfig.shared.gwSettingUrl,
          endpoint: "${restConstants.nodeGwKey}/${restConstants.statsKey}",
          addOns: addOns,
          context: context);
      if (response != null) {
        Map<String, dynamic> data = jsonDecode(response);
        if (data['result'] != null && (data['result'] as Map<String, dynamic>).isNotEmpty) {
          GatewayStatsResponse statsResponse = GatewayStatsResponse.fromJson(data);
          return statsResponse;
        } else {
          return data;
        }
      }
    } on SocketException catch (e) {
      debugLogs('catch exception in getDsGain ---> ${e.message}');
    }
    return null;
  }


  @override
  Future<Map<String, dynamic>?> setDsGain(
      {required BuildContext context, required Map<String, dynamic> bodyData , String? gwID}) async {
    try {
      String ? addOns = gwID != null ? "?${restConstants.gwKey}=$gwID" : null;
      final response = await restServices.postRestCall(
          body: bodyData,
          otherBaseUrl: AppConfig.shared.gwSettingUrl,
          endpoint: "${restConstants.nodeGwKey}/${restConstants.setGainKey}",
          addOns: addOns,
          context: context);
      if (response != null && response.isNotEmpty) {
        Map<String, dynamic> data = jsonDecode(response);
        return data;
      }
    } on SocketException catch (e) {
      debugLogs('catch exception in getDsGain ---> ${e.message}');
    }
    return null;
  }
  @override
  Future<dynamic> resetStats(
      {required BuildContext context, String? gwID}) async {
    try {
      String ? addOns = gwID != null ? "?${restConstants.gwKey}=$gwID" : null;
      final response = await restServices.postRestCall(
          body: {},
          otherBaseUrl: AppConfig.shared.gwSettingUrl,
          endpoint: "${restConstants.nodeGwKey}/${restConstants.resetKey}",
          addOns: addOns ,
          context: context);
      if (response != null && response.isNotEmpty) {
        return response;
      }
    } on SocketException catch (e) {
      debugLogs('catch exception in getDsGain ---> ${e.message}');
    }
    return null;
  }

  @override
  Future<dynamic> getGWDetail(
      {required BuildContext context , String? gwID}) async {
    try {
      String ? addOns = gwID != null ? "?${restConstants.gwKey}=$gwID" : null;
      final response = await restServices.getRestCall(
          otherBaseUrl: AppConfig.shared.gwSettingUrl,
          endpoint: "${restConstants.gatewaysKey}/${restConstants.gatewayKey}",
          addOns: addOns,
          context: context);
      if (response != null && response.isNotEmpty) {
        Map<String, dynamic> data = jsonDecode(response);
        if (data.containsKey('gw_eui')) {
          final gatewayItem = GatewayItem.fromJson(data);
          return gatewayItem;
        } else {
          return data;
        }
      }
    } on SocketException catch (e) {
      debugLogs('catch exception in getDsGain ---> ${e.message}');
    }
    return null;
  }

  @override
  Future<dynamic> getNodeGateways(
      {required BuildContext context,required int pageOffset, required int perPageLimit,required String statusType}) async {
    try {
      final response = await restServices.getRestCall(
          otherBaseUrl: AppConfig.shared.gwSettingUrl,
          endpoint: "${restConstants.gatewaysKey}?${restConstants.statusKey}=$statusType&${restConstants.limitKey}=$perPageLimit&${restConstants.offsetKey}=$pageOffset",
          context: context);
      if (response != null && response.isNotEmpty) {
        Map<String, dynamic> data = jsonDecode(response);
        Gateways gateways = Gateways.fromJson(data);
        return gateways;
      }
    } on SocketException catch (e) {
      debugLogs('catch exception in getDsGain ---> ${e.message}');
    }
    return null;
  }

  @override
  Future<dynamic> getNodeGwAmpsDeviceList(
      {required BuildContext context,
        required String gwEui,
        int? pageOffset,
        int? perPageLimit}) async {
    try {
      String addOns =
          "${restConstants.gatewayEuiKey}=$gwEui&${restConstants.limitKey}=$perPageLimit&${restConstants.offsetKey}=$pageOffset&${restConstants.directionKey}=ASC";

      final response = await restServices.getRestCall(
        endpoint: restConstants.amps, addOns: addOns, context: context,);
      if (response != null && response.isNotEmpty) {
        var data =  jsonDecode(response);
        if (data['result'] != null && data['result'].isNotEmpty) {
          ProvisioningDeviceList ampsDeviceItemList = ProvisioningDeviceList.fromJson(data);
          return ampsDeviceItemList;
        } else {
          return ProvisioningDeviceList.empty();
        }
      }
    } on SocketException catch (e) {
      debugLogs('catch exception in getProvisionedDeviceList ---> ${e.message}');
    }
    return null;
  }



}
