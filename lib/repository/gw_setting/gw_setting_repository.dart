import '../../app_import.dart';

abstract class GwSettingRepository {
  Future<dynamic> gwSettingHealthCheck({required BuildContext context});
  Future<dynamic> getStats({required BuildContext context, String? gwID});
  Future<Map<String, dynamic>?> setDsGain({required BuildContext context,required Map<String, dynamic> bodyData,String ? gwID});
  Future<dynamic> resetStats({required BuildContext context, String? gwID});
  Future<dynamic> getGWDetail({required BuildContext context, String? gwID});
  Future<dynamic> getNodeGateways({required BuildContext context,required int pageOffset, required int perPageLimit, required String statusType});
  Future<dynamic> getNodeGwAmpsDeviceList({required BuildContext context,required String gwEui,int ?pageOffset, int ?perPageLimit});


}
