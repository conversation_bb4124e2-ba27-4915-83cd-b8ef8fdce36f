// lib/repository/auth/google_auth_repository_impl.dart
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:quantumlink_node/app_import.dart';


class GoogleAuthRepositoryImpl implements GoogleAuthRepository {

  @override
  Future<Map<String, dynamic>?> signInWithGoogle(email) async {
    try {

      final auth = FirebaseAuth.instance;
      // Web-only Google provider login
      final googleProvider = GoogleAuthProvider();
      googleProvider.setCustomParameters({
        'login_hint': email
      });
      final userCredential = await auth.signInWithPopup(googleProvider);
      final googleIdToken = (userCredential.credential as OAuthCredential).idToken;
      final userTokenData = {
        'idToken': googleIdToken,
      };
      return userTokenData;
    } catch (e) {
      debugLogs('Error signing in with Google: $e');
      e.toString().showMessage();
      return null;
    }
  }

  @override
  Future<Map<String, dynamic>?> getUserInfo(String accessToken) async {
    try {
      final response = await http.get(
        Uri.parse('https://www.googleapis.com/oauth2/v3/userinfo'),
        headers: {'Authorization': 'Bearer $accessToken'},
      );

      if (response.statusCode == 200) {
        final userData = json.decode(response.body);
        return {
          'id': userData['sub'],
          'email': userData['email'],
          'name': userData['name'],
          'givenName': userData['given_name'],
          'locale': userData['locale'],
          'emailVerified': userData['email_verified'],
        };
      } else {
        return null;
      }
    } catch (e) {
      debugLogs('Error getting user info: $e');
      return null;
    }
  }

  @override
  Future<void> signOut() async {
    try {
      await FirebaseAuth.instance.signOut();
      debugLogs('Successfully signed out from Google');
    } catch (e) {
      debugLogs('Error signing out from Google: $e');
      rethrow;
    }
  }



}