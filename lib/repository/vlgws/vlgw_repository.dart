
import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/serialized/map_topology/map_layers_response.dart';
abstract class VLGWRepository {

  Future<dynamic> getVLGWInfo(BuildContext context,String eui);

  Future<dynamic> updateSite(BuildContext context,VLGW vlgw);

  Future<dynamic> getVLGWFSKStats(BuildContext context,String eui);

  Future<dynamic> getConfig(BuildContext context,String eui);

  Future<dynamic> getVersionOfConfig(BuildContext context,String eui);

  Future<dynamic> resetVLGWFSKStats(BuildContext context,String eui);
  Future<dynamic> getVLGWAmpsDeviceList({required BuildContext context,required String gwEui,int ?pageOffset, int ?perPageLimit});

  Future<dynamic> getVlgwTopology({required BuildContext context, required String gwEui});

  Future<VLGWs> getGatewayList(BuildContext context, {int? pageOffset, int? perPageLimit});

}