import 'dart:io';
import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/serialized/map_topology/map_layers_response.dart';



class VLGWRepositoryImpl implements VLGWRepository {
  RestConstants restConstants = RestConstants.instance;
  RestHelper helper = RestHelper.instance;

  @override
  Future<dynamic> getVlGWs(BuildContext context) async {
    try {
      final response = await helper.getRestCall(
          endpoint: restConstants.gateways, context: context);
      if (response != null && response.isNotEmpty) {
        var data = jsonDecode(response);
        if (data['result'] != null) {
          VLGWs vlgws = VLGWs.fromJson(data);
          return vlgws;
        } else {
          return VLGWs.empty();
        }
      }
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in getVlGWs ---> ${e.message}');
    }
    return VLGWs.fromJson({
      'result': [],
      'message': '',
      'success': false,
    });
  }

  @override
  Future<dynamic> getVLGWInfo(BuildContext context,String eui) async {
    try {
      final response = await helper.getRestCall(
          endpoint: "${restConstants.vLGWs}$eui", context: context);
      if (response != null && response.isNotEmpty) {
        var data = jsonDecode(response);
        VLGWs vlgws = VLGWs.fromJson(data);
        return vlgws;
      }
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in getProvisionedDeviceList ---> ${e.message}');
    }
    return VLGW.fromJson({
      'result': VLGW.empty(),
      'message': '',
      'success': false,
    });
  }

  @override
  Future updateSite(BuildContext context, VLGW vlgw) async {
    try {
      //Below Two Line is required to prevent eui into Json Body.
      final addOns = "/${restConstants.gatewayKey}/${restConstants.site}?${restConstants.gwKey}=${vlgw.eui}";
      final response = await helper.patchRestCall(
          endpoint: restConstants.gatewaysKey,  context:context,addOns: addOns, body: vlgw.toJson());

      if (response != null && response.isNotEmpty) {
        var data = jsonDecode(response);
        VLGW vlgw = VLGW.fromJson(data);
        return vlgw;
      }
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in updateSite ---> ${e.message}');
    }
    return null;
  }

  @override
  Future<dynamic> getVLGWFSKStats(BuildContext context,String eui) async {
    try {
      final response = await helper.getRestCall(
          endpoint: "${restConstants.vLGWs}$eui",addOns:"/${restConstants.fskStats}", context: context);
      if (response != null && response.isNotEmpty) {
        var data = jsonDecode(response);
        if (data['result'] != null) {
          VLGWFSKStats vlgws = VLGWFSKStats.fromJson(data['result']);
          return vlgws;
        } else {
          return VLGWFSKStats.empty();
        }
      }
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in getProvisionedDeviceList ---> ${e.message}');
    }
    return  VLGWFSKStats.empty();
  }
  @override
  Future<dynamic> getConfig(BuildContext context, String eui) async {
    try {
      final response = await helper.getRestCall(
          endpoint: "${restConstants.vLGWs}$eui",
          addOns: "/${restConstants.configKey}",
          context: context);

      if (response != null && response.isNotEmpty) {
        var data = jsonDecode(response);
        VLGWConfig ndRConfig = VLGWConfig.fromJson(data);
        return ndRConfig;
      }
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in getNDRConfig ---> ${e.message}');
    }
    return VLGWConfig.empty();
  }

  @override
  Future<dynamic> getVersionOfConfig(BuildContext context,String eui) async {
    try {
      final response = await helper.getRestCall(
          endpoint: "${restConstants.vLGWs}$eui",
          addOns: "/${restConstants.versionKey}",
          context: context);

        if (response != null && response.isNotEmpty) {
        var data = jsonDecode(response);
        
        ConfigVersion version = ConfigVersion.fromJson(data);
        print(" data = ${jsonEncode(version)}");
        return version;
      }
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in getNDFConfig ---> ${e.message}');
    }
    return ConfigVersion.empty();
  }

@override
Future resetVLGWFSKStats(BuildContext context, String eui) async {
  try {
    final response = await helper.putRestCall( context:context,
        endpoint: "${restConstants.vLGWs}$eui/resetfskstats", body: null);
    if (response != null && response.isNotEmpty) {
      var data = jsonDecode(response);
      if (data['result'] != null) {
        VLGWFSKStats vlgw = VLGWFSKStats.fromJson(data['result']);
        return vlgw;
      } else {
        return VLGWFSKStats.empty();
      }
    }
  } on SocketException catch (e) {
    debugLogs(
        'catch exception in getProvisionedDeviceList ---> ${e.message}');

  }
  return null;
}

  @override
  Future<dynamic> getVLGWAmpsDeviceList(
      {required BuildContext context,
      required String gwEui,
      int? pageOffset,
      int? perPageLimit}) async {
    try {
      String addOns =
          "?${restConstants.gatewayEuiKey}=$gwEui&${restConstants.limitKey}=$perPageLimit&${restConstants.offsetKey}=$pageOffset&${restConstants.directionKey}=ASC";

      final response = await helper.getRestCall(
          endpoint: restConstants.amps, addOns: addOns, context: context,);
      if (response != null && response.isNotEmpty) {
        var data =  jsonDecode(response);
        if (data['result'] != null && data['result'].isNotEmpty) {
          ProvisioningDeviceList ampsDeviceItemList = ProvisioningDeviceList.fromJson(data);
          return ampsDeviceItemList;
         } else {
           return ProvisioningDeviceList.empty();
         }
      }
    } on SocketException catch (e) {
      debugLogs('catch exception in getProvisionedDeviceList ---> ${e.message}');
    }
    return null;
  }

  @override
  Future<dynamic> getVlgwTopology(
      {required BuildContext context,
        required String gwEui}) async {
    try {
      String addOns =
          "/${restConstants.gwKey}/$gwEui";

      final response = await helper.getRestCall(
        endpoint: restConstants.topology, addOns: addOns, context: context,);
      if (response != null) {
        final List<dynamic> ? rawJsonList = jsonDecode(response);
        if (rawJsonList != null && rawJsonList.isNotEmpty) {

          final List<TopologyDeviceModel> devices = rawJsonList
              .map((e) => TopologyDeviceModel.fromJson(e as Map<String, dynamic>))
              .toList();
          return devices;
        } else {
          return <TopologyDeviceModel>[];
        }
      }
    } on SocketException catch (e) {
      debugLogs('catch exception in getProvisionedDeviceList ---> ${e.message}');
    }
    return null;
  }

  @override
  Future<VLGWs> getGatewayList(BuildContext context, {int? pageOffset, int? perPageLimit}) async {
    try {
      String addOns = "";
      if (pageOffset != null && perPageLimit != null) {
        addOns = "?${restConstants.limitKey}=$perPageLimit&${restConstants.offsetKey}=$pageOffset";
      }
      
      final response = await helper.getRestCall(
          endpoint: restConstants.gateways, addOns: addOns, context: context);
      if (response != null && response.isNotEmpty) {
        var data = jsonDecode(response);
        if (data['items'] != null) {
          return VLGWs.fromJson(data);
        }
      }
    } on SocketException catch (e) {
      debugLogs('catch exception in getGatewayList ---> ${e.message}');
    }
    return VLGWs.empty();
  }

  @override
  Future<dynamic> startTopologyScan(BuildContext context, String gwEui) async {
    try {
      final body = {
        "gw_eui": gwEui,
      };
      final response = await helper.postRestCallWithResponse(
        endpoint: "${restConstants.topology}/${restConstants.scansKey}",
        body: body,
        context: context,
      );
      if (response != null) {
        return response;
      }
      return null;
    } on SocketException catch (e) {
      debugLogs('catch exception in startTopologyScan ---> ${e.message}');
      return null;
    }
  }

  @override
  Future<dynamic>getTopologyScan(BuildContext context, String gwEui) async {
    try {
      final addOns = "?${restConstants.gwEuiKey}=$gwEui&${restConstants.skipKey}=0&${restConstants.limitKey}=100";
      final response = await helper.getRestCall(
        endpoint: "${restConstants.topology}/${restConstants.scansKey}",
        addOns: addOns,
        context: context,
      );
      if (response != null) {
        final List<dynamic> data = jsonDecode(response);

        final List<TopologyScanModel> scans =
        data.map((e) => TopologyScanModel.fromJson(e)).toList();
        if (scans.isNotEmpty) {
          return scans;
        }
        return [];
      }
      return null;
    } on SocketException catch (e) {
      debugLogs('catch exception in getTopologyScan ---> ${e.message}');
      return null;
    }
  }
}