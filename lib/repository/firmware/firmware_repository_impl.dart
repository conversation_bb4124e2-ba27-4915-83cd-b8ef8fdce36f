import 'dart:io';
import 'package:quantumlink_node/app_import.dart';

class FirmwareRepositoryImpl implements FirmwareRepository {
  RestHelper restServices = RestHelper.instance;
  RestConstants restConstants = RestConstants.instance;

  @override
  Future<dynamic> uploadFiles(
      {required BuildContext context,
      required PlatformFile fileFormate}) async {
    try {
      final response = await restServices.multiPartRestCall(otherBaseUrl: AppConfig.shared.foutaServerUrl,
          endpoint: "${restConstants.fm}${restConstants.filesKey}",
          keyName: restConstants.fileKey,
          context: context,
          file: fileFormate,isShowMessage: false);
      return response;
    } on SocketException catch (e) {
      debugLogs('catch exception in deleteProvisionedDevice ---> ${e.message}');
    }
    return null;
  }

  @override
  Future<dynamic> getFirmwareFileList({required BuildContext context ,  required int pageOffset, required int perPageLimit}) async {
    try {
      final response = await restServices.getRestCall(otherBaseUrl: AppConfig.shared.foutaServerUrl,
        endpoint: "${restConstants.fm}${restConstants.filesKey}",
        addOns:
            "?${restConstants.limitKey}=$perPageLimit&${restConstants.offsetKey}=${pageOffset}&${restConstants.directionKey}=ASC",
        context: context,
      );
      if (response != null && response.isNotEmpty) {
        final Map<String, dynamic> responseData = jsonDecode(response);
        FirmwareFiles firmwareFiles = FirmwareFiles.fromJson(responseData);
        return firmwareFiles;
      }
      return FirmwareFiles.empty();
    } on SocketException catch (e) {
      debugLogs('catch exception in deleteProvisionedDevice ---> ${e.message}');
    }
    return FirmwareFiles.empty();
  }

  @override
  Future<dynamic> getFirmwareFileType({required BuildContext context}) async {
    try {
      final response = await restServices.getRestCall(otherBaseUrl: AppConfig.shared.foutaServerUrl,
        endpoint: "${restConstants.fm}${restConstants.fileTypesKey}",
        addOns:
            "?${restConstants.limitKey}=${restConstants.limitVal10}&${restConstants.offsetKey}=${restConstants.offset}&${restConstants.directionKey}=ASC",
        context: context,
      );
      if (response != null && response.isNotEmpty) {
        final Map<String, dynamic> responseData = jsonDecode(response);
        return responseData;
      }
      return {};
    } on SocketException catch (e) {
      debugLogs('catch exception in deleteProvisionedDevice ---> ${e.message}');
    }
    return {};
  }

  @override
  Future<dynamic> getDeploymentsList({required BuildContext context, required int pageOffset, required int perPageLimit}) async {
    try {
      String sinceMinutesQuery = "";
      if (AppConfig.shared.showDeploymentsSinceDays.isNotEmpty && AppConfig.shared.isOpenFromBLE) {
        int deploymentsSinceDays = int.parse(AppConfig.shared.showDeploymentsSinceDays);
        int sinceMinutes = deploymentsSinceDays * 1440;
        sinceMinutesQuery = "${restConstants.sinceMinutes}=$sinceMinutes&";
      }
      final response = await restServices.getRestCall(otherBaseUrl: AppConfig.shared.foutaServerUrl,
        endpoint: restConstants.deployments,
        addOns:
            "?$sinceMinutesQuery${restConstants.limitKey}=$perPageLimit&${restConstants.offsetKey}=$pageOffset&${restConstants.directionKey}=ASC",
        context: context,
      );
      if (response != null && response.isNotEmpty) {
        final List<dynamic> responseData = jsonDecode(response);
        DeploymentList deploymentList = DeploymentList.fromJson(responseData);
        return deploymentList;
      }
      return null;
    }  catch (e) {
      debugLogs('catch exception in getDeploymentsList ---> ${e}');
    }
    return null;
  }

  @override
  Future<dynamic> createDeployment(
      {required BuildContext context, required CreateDeploymentItem createDeployment}) async {
    try {
      final response = await restServices.postRestCall(
          otherBaseUrl: AppConfig.shared.foutaServerUrl,
          endpoint: restConstants.deployments,
          context: context,
          body: createDeployment.toJson());
      if (response != null && response.isNotEmpty) {
        Map<String, dynamic> responseData = jsonDecode(response);
        CreateDeploymentFiles createDeploymentFiles = CreateDeploymentFiles.fromJson(responseData);
        return createDeploymentFiles;
      }
      return CreateDeploymentFiles.empty();
    } on SocketException catch (e) {
      debugLogs('catch exception in updateUsConfig ---> ${e.message}');
    }
    return CreateDeploymentFiles.empty();
  }

  @override
  Future<dynamic> deleteFirmware({required String firmwareId}) async {
    try {
      final response = await restServices.deleteRestCall(otherBaseUrl: AppConfig.shared.foutaServerUrl,
          endpoint: "${restConstants.fm}${restConstants.filesKey}/", addOns: firmwareId);
      if (response != null && response.isNotEmpty) {
        return jsonDecode(response);
      }
    } on SocketException catch (e) {
      debugLogs('catch exception in deleteProvisionedDevice ---> ${e.message}');
    }
    return null;
  }

  @override
  Future<dynamic> deleteDeployment({required String deploymentId}) async {
    try {
      final response = await restServices.deleteRestCall(otherBaseUrl: AppConfig.shared.foutaServerUrl,
          endpoint: "${restConstants.deployments}/", addOns: deploymentId);
      if (response != null && response.isNotEmpty) {
        return jsonDecode(response);
      }
    } on SocketException catch (e) {
      debugLogs('catch exception in deleteProvisionedDevice ---> ${e.message}');
    }
    return null;
  }
  @override
  Future<dynamic> getDeploymentDetail(
      {required BuildContext context,required String deploymentId}) async {
    try {
      final response = await restServices.getRestCall(
        otherBaseUrl: AppConfig.shared.foutaServerUrl,
        endpoint: restConstants.deployments,
        addOns:
            "/$deploymentId",
        context: context,
      );
      if (response != null && response.isNotEmpty) {
        final Map<String, dynamic> responseData = jsonDecode(response);
        DeploymentDetail deploymentList = DeploymentDetail.fromJson(responseData);
        return deploymentList;
      }
      return DeploymentDetail.empty();
    } on SocketException catch (e) {
      debugLogs('catch exception in deleteProvisionedDevice ---> ${e.message}');
    }
    return DeploymentDetail.empty();
  }
  @override
  Future<dynamic> applyDeployment(
      {required BuildContext context,required String deploymentId}) async {
    try {
      final response = await restServices.postRestCall(
          otherBaseUrl: AppConfig.shared.foutaServerUrl,
          endpoint: restConstants.deployments,
          addOns: "/$deploymentId/${restConstants.applyKey}",
          context: context,
          body: {"id": deploymentId});
      if (response != null && response.isNotEmpty) {
        return jsonDecode(response);
      }
      return null;
    } on SocketException catch (e) {
      debugLogs('catch exception in updateUsConfig ---> ${e.message}');
    }
    return null;
  }

  @override
  Future<dynamic> upgradeDeployment({required BuildContext context, required String devEui, required String deploymentId}) async {
    try {
      final response = await restServices.postRestCall(
        otherBaseUrl: AppConfig.shared.foutaServerUrl,
        endpoint: restConstants.devices,
        addOns: "/$deploymentId/${restConstants.fragKey}/$devEui/${restConstants.devRebootTimeReq}/${restConstants.rebootTime}",
        context: context,
        body: {},
      );
      if (response != null && response.isNotEmpty) {
        return jsonDecode(response);
      }
      return null;
    } on SocketException catch (e) {
      debugLogs('catch exception in updateUsConfig ---> ${e.message}');
    }
    return null;
  }
  @override
  Future<dynamic> getDeploymentDetailDeviseList({required BuildContext context, required String deploymentId,required int pageOffset, required int perPageLimit}) async {
    try {
      final response = await restServices.getRestCall(otherBaseUrl: AppConfig.shared.foutaServerUrl,
        endpoint: "${restConstants.deployments}/$deploymentId/${restConstants.devices}",
        addOns:
        "?${restConstants.limitKey}=$perPageLimit&${restConstants.offsetKey}=$pageOffset&${restConstants.directionKey}=ASC",
        context: context,
      );
      if (response != null && response.isNotEmpty) {
        final responseData = jsonDecode(response);
        DeploymentDetailDeviseList deploymentList = DeploymentDetailDeviseList.fromJson(responseData);
        return deploymentList;
      }
      return DeploymentDetailDeviseList.empty();
    } on SocketException catch (e) {
      debugLogs('catch exception in deleteProvisionedDevice ---> ${e.message}');
    }
    return DeploymentDetailDeviseList.empty();
  }

  @override
  Future<dynamic> deleteMultipleFirmwares(
      {required BuildContext context, required List firmwareIds}) async {
    try {
      final response = await restServices.postRestCall(
          otherBaseUrl: AppConfig.shared.foutaServerUrl,
          endpoint: "${restConstants.fm}${restConstants.filesKey}/${restConstants.deleteKey}",
          context: context,
          body: {"firmware_ids": firmwareIds});
      if (response != null && response.isNotEmpty) {
        return jsonDecode(response);
      }
      return null;
    } on SocketException catch (e) {
      debugLogs('catch exception in updateUsConfig ---> ${e.message}');
    }
    return null;
  }

  @override
  Future<dynamic> deleteMultipleDeployments(
      {required BuildContext context, required List deploymentsIds}) async {
    try {
      final response = await restServices.postRestCall(
          otherBaseUrl: AppConfig.shared.foutaServerUrl,
          endpoint: "${restConstants.deployments}/${restConstants.deleteKey}",
          context: context,
          body: {"deployment_ids": deploymentsIds});
      if (response != null && response.isNotEmpty) {
        return jsonDecode(response);
      }
      return null;
    } on SocketException catch (e) {
      debugLogs('catch exception in updateUsConfig ---> ${e.message}');
    }
    return null;
  }

  @override
  Future<dynamic> getFirmwareDeviseList(
      {required BuildContext context,
      required String firmwareId,
      required int pageOffset,
      required int perPageLimit}) async {
    try {
      final response = await restServices.getRestCall(
        otherBaseUrl: AppConfig.shared.foutaServerUrl,
        endpoint: "${restConstants.devices}?${restConstants.firmwareId}=$firmwareId",
        addOns:
            "&${restConstants.limitKey}=$perPageLimit&${restConstants.offsetKey}=$pageOffset&${restConstants.directionKey}=ASC",
        context: context,
      );
      if (response != null && response.isNotEmpty) {
        final responseData = jsonDecode(response);
        FirmwareDevices firmwareDevices =
        FirmwareDevices.fromJson(responseData);
        return firmwareDevices;
      }
      return FirmwareDevices.empty();
    } on SocketException catch (e) {
      debugLogs('catch exception in deleteProvisionedDevice ---> ${e.message}');
    }
    return FirmwareDevices.empty();
  }
}
