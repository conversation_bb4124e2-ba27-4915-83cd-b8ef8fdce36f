import '../../app_import.dart';

abstract class FirmwareRepository {
  Future<dynamic> uploadFiles(
      {required BuildContext context, required PlatformFile fileFormate});

  Future<dynamic> getFirmwareFileList(
      {required BuildContext context, required int pageOffset, required int perPageLimit});

  Future<dynamic> getFirmwareFileType({required BuildContext context});

  Future<dynamic> getDeploymentsList(
      {required BuildContext context, required int pageOffset, required int perPageLimit});

  Future<dynamic> createDeployment(
      {required BuildContext context, required CreateDeploymentItem createDeployment});

  Future<dynamic> deleteFirmware({required String firmwareId});

  Future<dynamic> deleteDeployment({required String deploymentId});

  Future<dynamic> getDeploymentDetail({required BuildContext context,required String deploymentId});
  Future<dynamic> applyDeployment({required BuildContext context,required String deploymentId});
  Future<dynamic> upgradeDeployment({required BuildContext context,required String devEui,required String deploymentId});
  Future<dynamic> getDeploymentDetailDeviseList({required BuildContext context, required String deploymentId,required int pageOffset, required int perPageLimit});

  Future<dynamic> deleteMultipleFirmwares(
      {required BuildContext context, required List firmwareIds});
  Future<dynamic> deleteMultipleDeployments(
      {required BuildContext context, required List deploymentsIds});

  Future<dynamic> getFirmwareDeviseList(
      {required BuildContext context,
      required String firmwareId,
      required int pageOffset,
      required int perPageLimit});
}
