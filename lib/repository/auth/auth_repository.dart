import 'package:quantumlink_node/app_import.dart';


abstract class AuthRepository {
  // Future<Map<String, dynamic>> checkWifi(BuildContext context);

  // Future<Response?> discoverWifi(BuildContext context);

  Future signIn(msConfig, Function function);
  Future refreshSession(msConfig, Function function);
  Future logOut(msConfig);

  // Future<Response?> connectToSSID(String ssid, String password, BuildContext context);
  Future signWithPassword( BuildContext context,{required String email, required String password});

  Future<dynamic> setAuditLogUser(BuildContext context,AuditLogUserData userData);
  Future<dynamic> getPublicIP();
  Future getAllDomains();
}
