import 'dart:io';
import 'dart:html' as html;
import 'dart:convert';
import 'package:http/http.dart' as http;

import 'package:quantumlink_node/app/cache/app_shared_preference.dart';
import 'package:quantumlink_node/app_import.dart';

class AuthRepositoryImpl implements AuthRepository {
  RestHelper restServices = RestHelper.instance;
  RestConstants restConstants = RestConstants.instance;
  static const String registrationTable = "registartions";



  @override
  Future logOut(msConfig) async {
    // TODO: implement logOut
    debugLogs("logOut ->");
    final AadOAuth oauth = AadOAuth(msConfig);
    await oauth.logout(showWebPopup: false);
  }

  @override
  Future signIn(msConfig, Function function) async {
    // TODO: implement signIn
    debugLogs("signIn ->");
    final AadOAuth oauth = AadOAuth(msConfig);

    final result = await oauth.login();
    result.fold(
      (failure) => failure.toString().showMessage(),
      (token) {
        debugLogs(
            'Token -> ${token.accessToken}');
        //fetchAzureUserDetails(token.accessToken, context);
        debugLogs("signIn 2 ->");
        function.call(token.accessToken);

      },
    );



  }

  @override
  Future<void> refreshSession(msConfig, Function function) async {
    debugLogs("refreshSession ->");
    final AadOAuth oauth = AadOAuth(msConfig);

    try {
      final isLoggedIn = await oauth.hasCachedAccountInformation;
      if(isLoggedIn){
        final token = await oauth.getAccessToken();
        if(token!=null&&token.toString().isNotEmpty){
          debugLogs('Auto-logged in with token: $token');
          function.call(token);
          return;
        }
      }

    } catch (e) {
      print('Token refresh failed, re-login required');
    }
    try {
      final result = await oauth.login(refreshIfAvailable: true).timeout(
        const Duration(seconds: 8),
        onTimeout: () => throw TimeoutException('Refresh token operation timed out after 8 seconds'),
      );
      result.fold(
        (failure)  {
          debugLogs('Refresh token failed: ${failure.toString()}');
          function.call("");
        },
        (token) {
          debugLogs('Token refreshed successfully -> ${token.accessToken}');
          function.call(token.accessToken);
        },
      );
    } on TimeoutException catch (e) {
      debugLogs('Refresh token timeout: ${e.message}');
      function.call("");
    } catch (e) {
      debugLogs('Unexpected error during refresh: $e');
      function.call("");
    }
  }



  @override
  Future<Response?> signWithPassword(BuildContext context,
      {required String email, required String password}) async {
    try {
      final response = await restServices.postRestCallWithResponse(
        endpoint: "${restConstants.authKey}/${restConstants.tokenKey}",
        context: context,
        body: {"username": email, "password": password},
      );
      if (response != null && response.body.isNotEmpty) {
        return Future.value(Response(statusCode: response.statusCode, body: response.body));
      }
    }  catch (e) {
      debugLogs('signWithPassword --> ${e}');
    }
    return null;
  }


  @override
  Future<dynamic> setAuditLogUser(
      BuildContext context,AuditLogUserData userData) async {
    try {
      final response = await restServices.postRestCall(
        otherBaseUrl: AppConfig.shared.auditLogUrl,
        endpoint: restConstants.usersKey,
        body: userData.toJson(),
        context: context,
      );
      if (response != null && response.isNotEmpty) {
        var data = jsonDecode(response);
        if (data is Map && data.containsKey('error')) {
          String message = data['message'] ?? 'Unknown error';
          message.showError(context);
          throw Exception('API error: $message');
        }
        return data;
      }
     } catch (e) {
      debugPrint('catch exception in setAuditLogUser ---> ${e}');
      if(userData.operation != AppStringConstants.refreshToken){
        S.of(context).errorDeviceInfoMessage.showError(context);
      }
      return null;
    }
    if(userData.operation != AppStringConstants.refreshToken){
      S.of(context).errorDeviceInfoMessage.showError(context);
    }
    return null;
  }


  @override
  Future<String> getPublicIP() async {
    try {
      final response = await http.get(Uri.parse(restConstants.ipAddressURL));
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['ip'];
      }
      return "";
    } catch (e) {
      debugLogs('Error getting public IP: $e');
      return "";
    }
  }

  @override
  Future getAllDomains() async {
    final QuerySnapshot configSnapShot =
        await FirebaseFirestore.instance.collection(FirebaseKeyConstants.config).get();
    if (configSnapShot.docs.isNotEmpty) {
      final data = configSnapShot.docs.first.data() as Map<String, dynamic>;
      final List<String> domainList = List<String>.from(data[FirebaseKeyConstants.domain]);
      AppConfig.shared.allowedEmailDomains = domainList;
      debugLogs("Allowed Email Domains-->${AppConfig.shared.allowedEmailDomains}");
    } else {
      debugLogs("No documents found in config collection");
    }
  }
}
