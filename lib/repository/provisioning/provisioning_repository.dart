import '../../app_import.dart';

abstract class ProvisioningRepository {
  Future<dynamic> getProvisionedDeviceList(
      {required BuildContext context,
        required ProvisionedRequestParams requestParams});

  Future<dynamic> addProvisionDevice({
    required ProvisioningDeviceItem itemData,
    required BuildContext context,
  });

  Future<dynamic> deleteProvisionedDevice({required String deviceEui});

  Future<dynamic> addCsvProvisionedDevices(
      {required BuildContext context, required PlatformFile csvFile});

  Future<dynamic> addCsvJoinServer({required BuildContext context, required PlatformFile csvFile});

  Future<dynamic> updateProvisionDevice({
    required ProvisioningDeviceItem itemData,
    required BuildContext context,
    required String devEUI
  });
  Future<dynamic> updateProvisionAutoJoinDevice(
      {required bool isEnabled,
        required BuildContext context,
        required String devEUI});
}
