import 'dart:io';
import 'package:quantumlink_node/app_import.dart';

class ProvisioningRepositoryImpl implements ProvisioningRepository {
  RestHelper restServices = RestHelper.instance;
  RestConstants restConstants = RestConstants.instance;

  @override
  Future<dynamic> getProvisionedDeviceList(
      {required BuildContext context,
  required ProvisionedRequestParams requestParams}) async {
    String searchQuery = requestParams.search != null && requestParams.search!.trim().isNotEmpty
        ? '${restConstants.searchQuery}=${Uri.encodeComponent(requestParams.search!)}&'
        : '';
    String vendorCodeQuery = requestParams.vendorCodeQuery;
    String autoJoinEnabledQuery = requestParams.isEnabledQuery ;
    String statusQuery = requestParams.statusQuery ;
    try {
      String addOns =
          "?$vendorCodeQuery$searchQuery$autoJoinEnabledQuery$statusQuery${restConstants.timeoutKey}=${restConstants.getCallTimeout}&${restConstants.retriesKey}=${restConstants.getCallRetriesVal}&${restConstants.limitKey}=${requestParams.perPageLimit ?? restConstants.limitVal}&${restConstants.offsetKey}=${requestParams.pageOffset ?? restConstants.offset}&${restConstants.directionKey}=ASC";
      final response = await restServices.getRestCall(
          endpoint: restConstants.amps, addOns: addOns, context: context);
      if (response != null && response.isNotEmpty) {
        var data = jsonDecode(response);
        if (data['result'] != null) {
          ProvisioningDeviceList provisionedDeviceItemList =
              ProvisioningDeviceList.fromJson(data);
          return provisionedDeviceItemList;
        } else {
          return null;
        }
      }
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in getProvisionedDeviceList ---> ${e.message}');
    }
    return null;
  }

  @override
  Future<dynamic> addProvisionDevice(
      {required ProvisioningDeviceItem itemData,
      required BuildContext context}) async {
    try {
      final response = await restServices.postRestCall(
          endpoint: restConstants.amps,
          addOns: "",
          context: context,
          body: {
            restConstants.appKeyName: itemData.appKey,
            restConstants.deviceEuiKey: itemData.deviceEui,
            restConstants.typeKey: itemData.type
          });
      if (response != null && response.isNotEmpty) {
        return jsonDecode(response);
      }
    } on SocketException catch (e) {
      debugLogs('catch exception in addProvisionDevice ---> ${e.message}');
    }
    return null;
  }

  @override
  Future<dynamic> deleteProvisionedDevice({required String deviceEui}) async {
    try {
      final response = await restServices.deleteRestCall(
          endpoint: restConstants.amps, addOns: deviceEui);
      if (response != null && response.isNotEmpty) {
        return jsonDecode(response);
      }
    } on SocketException catch (e) {
      debugLogs('catch exception in deleteProvisionedDevice ---> ${e.message}');
    }
    return null;
  }

  @override
  Future<dynamic> addCsvProvisionedDevices(
      {required BuildContext context, required PlatformFile csvFile}) async {
    try {
      final response = await restServices.multiPartRestCall(
          endpoint: "${restConstants.amps}${restConstants.csvKey}",
          keyName: restConstants.csvFileKey,
          context: context,
          file: csvFile);
      return response;
    } on SocketException catch (e) {
      debugLogs('catch exception in deleteProvisionedDevice ---> ${e.message}');
    }
    return null;
  }

  @override
  Future<dynamic> addCsvJoinServer(
      {required BuildContext context, required PlatformFile csvFile}) async {
    try {
      final response = await restServices.multiPartRestCall(
        otherBaseUrl: AppConfig.shared.joinServerUrl,
          endpoint:
              "${restConstants.provisionKey}/${restConstants.uploadKey}?${restConstants.overwriteKey}=true",
          keyName: restConstants.fileKey,
          context: context,
          file: csvFile);
      return response;
    } on SocketException catch (e) {
      debugLogs('catch exception in deleteProvisionedDevice ---> ${e.message}');
    }
    return null;
  }

  @override
  Future<dynamic> updateProvisionDevice(
      {required ProvisioningDeviceItem itemData,
      required BuildContext context,
      required String devEUI}) async {
    try {
      if (itemData.appKey == "") {
        itemData.appKey = null;
      }
      final response = await restServices.putRestCallWithResponse(
          endpoint: restConstants.amps,
          context: context,
          addOns:
              '$devEUI?${restConstants.timeoutKey}=${restConstants.postCallTimeout}&${restConstants.retriesKey}=${restConstants.getCallRetriesVal}',
          body: itemData.toJson());
      return response;
    } on SocketException catch (e) {
      debugLogs('catch exception in updateProvisionDevice ---> ${e.message}');
    }
    return null;
  }
  @override
  Future<dynamic> updateProvisionAutoJoinDevice(
      {required bool isEnabled,
        required BuildContext context,
        required String devEUI}) async {
    try {
      final response = await restServices.putRestCallWithResponse(
          endpoint: restConstants.amps,
          context: context,
          addOns:
          '$devEUI?${restConstants.timeoutKey}=${restConstants.postCallTimeout}&${restConstants.retriesKey}=${restConstants.getCallRetriesVal}',
          body: {
            "is_enabled": isEnabled
          });
      print("response-->${response}");
      return response;
    } on SocketException catch (e) {
      debugLogs('catch exception in updateProvisionDevice ---> ${e.message}');
    }
    return null;
  }

}
