import '../../app_import.dart';
import 'package:http/src/response.dart' as http;

abstract class DashboardRepository {
  Future<http.Response?> getKeyCountsFromJoinServer(BuildContext context);
  Future<dynamic> getDashboardData(BuildContext context);
  Future<Map<String,String>> getBackendVersions(BuildContext context,{String? otherBaseUrl});
  Future<dynamic> getNodeBatteryInfo(BuildContext context);
  Future<dynamic> checkHealth(BuildContext context,{String? otherBaseUrl});
  Future<dynamic> getNodeIdInfo(BuildContext context);
}
