import 'dart:io';

import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/serialized/dashboard/dashboard.dart';
import 'package:http/src/response.dart' as http;

class DashboardRepositoryImpl implements DashboardRepository {
  RestHelper restServices = RestHelper.instance;
  RestConstants restConstants = RestConstants.instance;

  @override
  Future<dynamic> getDashboardData(BuildContext context) async {
    try {
      final response = await restServices.getRestCall(
        endpoint: restConstants.amps, addOns:   restConstants.summary,
        context: context,
      );
      if (response != null && response.isNotEmpty) {
        Map<String, dynamic> jsonData = jsonDecode(response);
        dynamic dashboardData = DashboardModel.fromJson(jsonData);
        return dashboardData;
      }
    } on SocketException catch (e) {
      debugPrint('catch exception in getDashboardData ---> ${e.message}');
    }
    return DashboardModel.empty();
  }

  Future<http.Response?> getKeyCountsFromJoinServer(BuildContext context) async {
    try {
      final response = await restServices.getRestCallWithResponse(
        endpoint: "${AppConfig.shared.joinServerUrl}/${restConstants.summaryWithoutSlash}",
        isOtherBaseUrl: true,
        context: context,
      );
      if (response != null) {
        return response;
      }
    } on SocketException catch (e) {
      debugPrint('catch exception in getKeyCountsFromJoinServer --> ${e.message}');
    }
    return null;
  }

  @override
  Future<Map<String, String>> getBackendVersions(BuildContext context, {String? otherBaseUrl}) async {
    try {
      final response = await restServices.getRestCall(
        otherBaseUrl: otherBaseUrl ,
        endpoint: restConstants.version,
        context: context,
      );
      if (response != null && response.isNotEmpty) {
        Map<String, dynamic> jsonData = jsonDecode(response);
        debugPrint("Backend version response: $jsonData");
        Map<String, String> res = {};
        jsonData.forEach((key, value) {
          res[key] = value.toString();
        });
        return res;
      }
    } on SocketException catch (e) {
      debugPrint('catch exception in getBackendVersions ---> ${e.message}');
      throw Exception(e.message);
    }
    return {};
  }
  @override
  Future<dynamic> getNodeBatteryInfo(BuildContext context) async {
    try {
      final response = await restServices.getRestCall(
        endpoint: restConstants.battery,
        context: context,
        otherBaseUrl: AppConfig.shared.nodeMgrUrl
      );
      if (response != null && response.isNotEmpty) {
        Map<String, dynamic> jsonData = jsonDecode(response);
        NodeBatteryInfoItem nodeBatteryData = NodeBatteryInfoItem.fromJson(jsonData);
        return nodeBatteryData;
      }
    } on SocketException catch (e) {
      debugPrint('catch exception in getNodeDashboardData ---> ${e.message}');
    }
    return NodeBatteryInfoItem.empty();
  }


  @override
  Future<dynamic> checkHealth(context, {String? otherBaseUrl}) async {
    dynamic checkHealth;
    try {
      final response = await restServices.getRestCall(otherBaseUrl:otherBaseUrl ,
        endpoint: restConstants.healthCheck,
        context: context,
      );
      if (response != null && response.isNotEmpty) {
        Map<String, dynamic> jsonData = jsonDecode(response);
        checkHealth = jsonData;
        AppConfig.shared.apiHealthCheckStream.sink.add(true);
        return checkHealth;
      }
    } on SocketException catch (e) {
      debugPrint('catch exception in checkHealth ---> ${e.message}');
    }
    AppConfig.shared.apiHealthCheckStream.sink.add(false);
    return checkHealth;
  }
  @override
  Future<dynamic> getNodeIdInfo(BuildContext context) async {
    try {
      final response = await restServices.getRestCall(
          endpoint: restConstants.idInfo,
          context: context,
          otherBaseUrl: AppConfig.shared.nodeMgrUrl);
      if (response != null && response.isNotEmpty) {
        Map<String, dynamic> jsonData = jsonDecode(response);
        NodeIdInfoItem nodeDashboardIdData = NodeIdInfoItem.fromJson(jsonData);
        return nodeDashboardIdData;
      }
    } on SocketException catch (e) {
      debugPrint('catch exception in getNodeIdInfo ---> ${e.message}');
    }
    return NodeIdInfoItem.empty();
  }

}
