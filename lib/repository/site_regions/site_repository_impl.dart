import 'dart:io';
import 'package:quantumlink_node/app_import.dart';

class SiteRepositoryImpl implements SiteRepository {
  RestHelper restServices = RestHelper.instance;
  RestConstants restConstants = RestConstants.instance;


  @override
  Future<List<dynamic>> getSiteList({required BuildContext context}) async {
    List<dynamic> siteList = [];
    try {
      final response = await restServices.getRestCall(
          endpoint: "${restConstants.sites}/",
          addOns: "?${restConstants.skipKey}=0",
          context: context);
      if (response != null && response.isNotEmpty) {
        List data = jsonDecode(response);
        siteList.clear();
        siteList = data.map((e) => SiteDataModel.fromJson(e)).toList();
        return siteList;
      }
    } on SocketException catch (e) {
      debugLogs('catch exception in getSiteList ---> ${e.message}');
    }
    return [];
  }
  @override
  Future<Map<String, dynamic>?> addSite(
      {required BuildContext context ,required SiteDataModel  siteData}) async {
    try {
      final response = await restServices.postRestCall(
          endpoint: "${restConstants.sites}/",
          context: context,
          body: {"name": siteData.name, "description": siteData.description});
      if (response != null && response.isNotEmpty) {
        return jsonDecode(response);
      }
    } on SocketException catch (e) {
      debugLogs('catch exception in addSite ---> ${e.message}');
    }
    return null;
  }
  @override
  Future<Map<String, dynamic>?> deleteSiteDevice({required String siteId}) async {
    try {
      final response = await restServices.deleteRestCall(endpoint: restConstants.sites, addOns: "/$siteId");
      if (response != null && response.isNotEmpty) {
        return jsonDecode(response);
      }
    } on SocketException catch (e) {
      debugLogs('catch exception in deleteSiteDevice ---> ${e.message}');
    }
    return null;
  }
  @override
  Future<Map<String, dynamic>?> updateSite(
      {required BuildContext context ,required SiteDataModel  siteData}) async {
    try {
      final response = await restServices.putRestCall(
          endpoint: "${restConstants.sites}/",  context:context,addOns: siteData.id, body: siteData.toJson());
      if (response != null && response.isNotEmpty) {
        return jsonDecode(response);
      }
    } on SocketException catch (e) {
      debugLogs('catch exception in updateSite ---> ${e.message}');
    }
    return null;
  }
}
