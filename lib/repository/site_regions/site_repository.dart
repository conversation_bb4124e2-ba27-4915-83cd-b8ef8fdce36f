import '../../app_import.dart';

abstract class SiteRepository {
  Future<List<dynamic>> getSiteList({required BuildContext context});
  Future<Map<String, dynamic>?> addSite({required BuildContext context,required SiteDataModel  siteData});
  Future<Map<String, dynamic>?> updateSite({required BuildContext context,required SiteDataModel  siteData});
  Future<Map<String,dynamic>?> deleteSiteDevice({required String siteId});
}
