import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:quantumlink_node/app/config/app_config.dart';
import 'package:quantumlink_node/repository/auditlog/auditlog_repository.dart';
import 'package:quantumlink_node/serialized/auditlogs/auditlog.dart';

import '../../app/helper/rest_helper.dart';

class AuditLogsRepositoryImpl implements AuditLogsRepository {
  RestHelper restServices = RestHelper.instance;
  RestConstants restConstants = RestConstants.instance;


  @override
  Future<dynamic> getAuditLogDevicesList(BuildContext context, int pageOffset,int perPageLimit) async {
    try {
      final response = await restServices.getRestCall(
        otherBaseUrl: AppConfig.shared.auditLogUrl,
        endpoint: restConstants.devices,
        addOns:
        "?${restConstants.offsetKey}=$pageOffset&${restConstants.limitKey}=$perPageLimit",
        context: context,
      );
      if (response != null && response.isNotEmpty) {
        var data = jsonDecode(response);
        AuditLogResponse auditLogResponse  = AuditLogResponse.fromJson(data);
        return auditLogResponse;
      }
    } on SocketException catch (e) {
      debugPrint(
          'catch exception in getAuditLogList ---> ${e.message}');
      return null;
    }
    return null;
  }

  @override
  Future<dynamic> getAuditLogUserList(
      BuildContext context, int pageOffset, int perPageLimit) async {
    try {
      final response = await restServices.getRestCall(
        otherBaseUrl: AppConfig.shared.auditLogUrl,
        endpoint: restConstants.usersKey,
        addOns: "?${restConstants.offsetKey}=$pageOffset&${restConstants.limitKey}=$perPageLimit",
        context: context,
      );
      if (response != null && response.isNotEmpty) {
        var data = jsonDecode(response);
        AuditLogUserResponse auditLogUserResponse = AuditLogUserResponse.fromJson(data);
        return auditLogUserResponse;
      }
    } on SocketException catch (e) {
      debugPrint('catch exception in getAuditLogList ---> ${e.message}');
      return null;
    }
    return null;
  }
}
