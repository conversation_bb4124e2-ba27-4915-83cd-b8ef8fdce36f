import '../../app_import.dart';

abstract class AmplifierRepository {
  Future<dynamic> getAmplifierList(BuildContext context, Function f,int pageOffset, int perPageLimit, {String? search});

  Future<dynamic> getAmplifierListOfStatusData(
      {required String deviceEui, required BuildContext context});

  Future<dynamic> getAmplifierIdentification(
      {required String deviceEui, required BuildContext context});

  Future<dynamic> ampDsConfig(
      {required String deviceEui, required BuildContext context});

  Future<dynamic> ampUsConfig(
      {required String deviceEui, required BuildContext context});

  Future<dynamic> updateAmpPlacements({required String deviceEui,
    required BuildContext context,
    required AmplifierDeviceItem2 ampIdentification});

  Future<dynamic> updateDsConfig({required String deviceEui,
    required BuildContext context,
    required AmpDownStreamItem ampDownStreamItem});

  Future<dynamic> updateUsConfig({required String deviceEui,
    required BuildContext context,
    required AmpUpStreamItem ampUpStreamItem});

  Future<dynamic> setTestPointConfig({required String deviceEui,
    required BuildContext context,
    required TestPointItem testPointItem});

  Future<dynamic> startDsAlignment(
      {required String deviceEui, required BuildContext context});

  Future<dynamic> startUsAlignment(
      {required String deviceEui, required BuildContext context});

  Future<dynamic> getSpectrum(
      {required String deviceEui, required BuildContext context});

  Future<dynamic> getTelemetry({required String deviceEui,
    required BuildContext context,
    required String formDate,
    required String toDate, required int pageOffset, required int perPageLimit});

  Future<dynamic> getTelemetryThresholds(
      {required String deviceType, required BuildContext context});

  Future<dynamic> addTelemetryThresholds(
      {required BuildContext context,required String deviceType,required TelemetryThreshold itemData});


  Future<dynamic> getAlarmsHistoryData(
      {required String deviceEui, required BuildContext context, required String fromDate,
        required String toDate});

  Future<dynamic> exportTelemetryCsvFile({required String deviceEui,
    required String formDate,
    required String toDate,
    required BuildContext context});

  Future<Map<String, dynamic>> dsAutoAlignment(
      {required String deviceEui, required BuildContext context, required bool isStatusCheck });

  Future<Map<String, dynamic>> usAutoAlignment(
      {required String deviceEui, required BuildContext context, required bool isStatusCheck});

  Future<Map<String, dynamic>> dsManualAlignment(
      {required String deviceEui, required BuildContext context});

  Future<Map<String, dynamic>> setDsManualAlignment(
      {required DsManualAlignmentItem dsManualAlignmentItem,required String deviceEui, required BuildContext context});

  Future<Map<String, dynamic>> saveRevertDsManualAlignment(
      {required DsManualAlignmentItem dsManualAlignmentItem,required String deviceEui, required BuildContext context});

  Future<Map<String, dynamic>> usManualAlignment(
      {required String deviceEui, required BuildContext context});

  Future<Map<String, dynamic>> setUsManualAlignment(
      {required DsManualAlignmentItem dsManualAlignmentItem,required String deviceEui, required BuildContext context});

  Future<Map<String, dynamic>> saveRevertUsManualAlignment(
      {required DsManualAlignmentItem dsManualAlignmentItem,required String deviceEui, required BuildContext context});

  Future<Map<String, dynamic>> getDeviceSummary(
      {required String deviceEui, required BuildContext context,bool isRefresh = false, required int bitMask});

  // Future<dynamic> getAlarms(
  //     {required String deviceEui, required BuildContext context});

  Future<Map<String, dynamic>> getSpectrumData({required String deviceEui,
    required SpectrumRequestItem spectrumDataItem,
    required BuildContext context,required bool isRefresh});

  Future<dynamic> getDownstreamAmplifiers(
      {required String deviceEui, required BuildContext context});

  Future<Map<String, dynamic>> getDeviceTransponderInfo(
      {required String deviceEui, required BuildContext context,bool isRefresh = false});
  Future<dynamic> setIngressSwitchControl({required String deviceEui,
    required BuildContext context,
    required IngressSwitchItem ingressSwitchItem,required bool isRefresh, required bool isMultiIngressSwitch});
  Future<Map<String, dynamic>> getTestPointConfiguration(
      {required String deviceEui,
        required BuildContext context,
        required bool isRefresh});
  Future<Map<String, dynamic>> getIngressSwitchControl({required String deviceEui,
    required BuildContext context,
    required IngressSwitchItem ingressSwitchItem, required bool isMultiIngress});
  Future<Map<String, dynamic>> dsAutoAlignmentSpectrumData(
      {required String deviceEui, required BuildContext context,required bool isRefresh});

  Future<Map<String, dynamic>> saveRevertDsAutoAlignment(
      {required bool isSave ,required String deviceEui, required BuildContext context});

  Future<Map<String, dynamic>> saveRevertUsAutoAlignment(
      {required bool isSave ,required String deviceEui, required BuildContext context});

  Future<Map<String, dynamic>> getAMPFirmwareImageInfo(
      {required String deviceEui, required BuildContext context, required bool isRefresh});

  Future<Map<String, dynamic>> setAMPSwitchBankAndReboot(
      {required bool isRefresh ,required String deviceEui,required int bankIndex,required BuildContext context});

  Future<Map<String, dynamic>> getTransponderFWImageInfo(
      {required String deviceEui, required BuildContext context, required bool isRefresh});

  Future<Map<String, dynamic>> setTransponderSwitchBankAndReboot(
      {required bool isRefresh ,required String deviceEui,required int bankIndex, required BuildContext context});

  Future<dynamic> getAuditLogHistory(BuildContext context, int pageOffset, int perPageLimit,String deviceEui);

  Future<dynamic> getAlignmentConfigFileInfo(
      {required String deviceEui, required BuildContext context, required bool isRefresh});
  Future<dynamic> setAlignmentConfigInfo(
      {required String deviceEui, required BuildContext context,required AlignmentConfig alignmentConfig});
  Future<dynamic> setAlscConfigData(
      {required String deviceEui, required BuildContext context});
}
