import 'dart:io';
import 'package:quantumlink_node/app_import.dart';

class AmplifierRepositoryImpl implements AmplifierRepository {
  RestHelper restServices = RestHelper.instance;
  RestConstants restConstants = RestConstants.instance;
  AmplifierDeviceList?
      amplifierDeviceList; // Don't use this object in any other page

  @override
  Future<dynamic> getAmplifierList(
      BuildContext context, Function f, int pageOffset, int perPageLimit,
      {String? search}) async {
    String searchQuery = search != null && search.trim().isNotEmpty
        ? '&${restConstants.searchQuery}=${Uri.encodeComponent(search)}'
        : '';
    try {
      if (amplifierDeviceList != null && amplifierDeviceList!.result.isNotEmpty) {
        f.call(amplifierDeviceList);
      }
      final response = await restServices.getRestCall(
        endpoint: restConstants.amps,
        addOns:
            "?${restConstants.vendorCode}=${restConstants.aoi}&${restConstants.isEnabled}=true$searchQuery&${restConstants.timeoutKey}=${restConstants.getCallTimeout}&${restConstants.retriesKey}=${restConstants.getCallRetriesVal}&${restConstants.limitKey}=$perPageLimit&${restConstants.offsetKey}=$pageOffset&${restConstants.directionKey}=ASC",
        context: context,
      );
      if (response != null && response.isNotEmpty) {
        var data = jsonDecode(response);
        if (data['result'] != null) {
          amplifierDeviceList = AmplifierDeviceList.fromJson(data);
        } else {
          amplifierDeviceList = null;
        }
      } else {
        amplifierDeviceList = null;
      }
    } on SocketException catch (e) {
      debugPrint('catch exception in getAmplifierList ---> ${e.message}');
      amplifierDeviceList = AmplifierDeviceList.empty();
    }
    f.call(amplifierDeviceList);
    return amplifierDeviceList;
  }

  @override
  Future<dynamic> getAmplifierListOfStatusData(
      {required String deviceEui, required BuildContext context}) async {
    try {
      final response = await restServices.getRestCall(
          endpoint: restConstants.amps,
          addOns:
              "$deviceEui/${restConstants.getSensorData}?${restConstants.timeoutKey}=${restConstants.getCallTimeout}&${restConstants.retriesKey}=${restConstants.getCallRetriesVal}",
          context: context);
      if (response != null && response.isNotEmpty) {
        Map<String, dynamic> jsonData = jsonDecode(response);
        if (jsonData['result'] != null) {
          dynamic amplifierSensorData = AmplifierSensorData.fromJson(jsonData);
          return amplifierSensorData;
        } else {
          String error = jsonData['detail'];
          return error;
        }
      }
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in getAmplifierListOfStatusData ---> ${e.message}');
    }
    return null;
  }

  @override
  Future<dynamic> getAmplifierIdentification(
      {required String deviceEui, required BuildContext context}) async {
    try {
      final response = await restServices.getRestCall(
          endpoint: restConstants.amps,
          addOns:
              "$deviceEui/${restConstants.getAmplifierIdentification}?${restConstants.timeoutKey}=${restConstants.getCallTimeout}&${restConstants.retriesKey}=${restConstants.getCallRetriesVal}",
          context: context);
      if (response != null && response.isNotEmpty) {
        Map<String, dynamic> jsonData = jsonDecode(response);
        if (jsonData['result'] != null) {
          dynamic amplifierIdentification =
              AmplifierIdentification.fromJson(jsonData);
          return amplifierIdentification;
        } else {
          String error = jsonData['detail'];
          return error;
        }
      }
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in getAmplifierIdentification ---> ${e.message}');
    }
    return null;
  }

  @override
  Future<dynamic> ampDsConfig(
      {required String deviceEui, required BuildContext context}) async {
    try {
      final response = await restServices.getRestCall(
          endpoint: restConstants.amps,
          addOns:
              "$deviceEui/${restConstants.cfgKey}/${restConstants.downKey}?${restConstants.timeoutKey}=${restConstants.getCallTimeout}&${restConstants.retriesKey}=${restConstants.getCallRetriesVal}",
          context: context);
      if (response != null && response.isNotEmpty) {
        Map<String, dynamic> jsonData = jsonDecode(response);
        if (jsonData['result'] != null) {
          dynamic ampDownStreamConfig = AmpDownStreamModel.fromJson(jsonData);
          return ampDownStreamConfig;
        } else {
          String error = jsonData['message'];
          return error;
        }
      }
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in ampDsConfig ---> ${e.message}');
    }
    return null;
  }

  @override
  Future<dynamic> ampUsConfig(
      {required String deviceEui, required BuildContext context}) async {
    try {
      final response = await restServices.getRestCall(
          endpoint: restConstants.amps,
          addOns:
              "$deviceEui/${restConstants.cfgKey}/${restConstants.upKey}?${restConstants.timeoutKey}=${restConstants.getCallTimeout}&${restConstants.retriesKey}=${restConstants.getCallRetriesVal}",
          context: context);
      if (response != null && response.isNotEmpty) {
        Map<String, dynamic> jsonData = jsonDecode(response);
        if (jsonData['result'] != null) {
          dynamic ampUpStreamConfig = AmpUpStreamModel.fromJson(jsonData);
          return ampUpStreamConfig;
        } else {
          String error = jsonData['message'];
          return error;
        }
      }
    } on SocketException catch (e) {
      debugLogs('catch exception in ampUsConfig ---> ${e.message}');
    }
    return null;
  }

  @override
  Future<dynamic> updateAmpPlacements(
      {required String deviceEui,
      required BuildContext context,
      required AmplifierDeviceItem2 ampIdentification}) async {
    try {
      final response = await restServices.postRestCall(
        context: context,
        endpoint:
            "${restConstants.amps}$deviceEui/device_user_info?${restConstants.timeoutKey}=${restConstants.postCallTimeout}&${restConstants.retriesKey}=${restConstants.postCallRetriesVal}&${restConstants.refreshKey}=true",
        body: ampIdentification.toJson(),
      );
      if (response != null && response.isNotEmpty) {
        Map<String, dynamic> jsonData = jsonDecode(response);
        if (jsonData['result'] != null  ) {
          dynamic result = AmplifierDeviceItem2.fromJson(jsonData['result']);
          return result;
        } else {
          String error = jsonData['detail'];
          return error;
        }
      }
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in updateAmpPlacements ---> ${e.message}');
    }
    return {} ;
  }

  @override
  Future<dynamic> updateDsConfig(
      {required String deviceEui,
      required BuildContext context,
      required AmpDownStreamItem ampDownStreamItem}) async {
    try {
      final response = await restServices.postRestCall(
          endpoint: restConstants.amps,
          addOns:
              "$deviceEui/${restConstants.cfgKey}/${restConstants.downKey}?${restConstants.timeoutKey}=${restConstants.postCallTimeout}&${restConstants.retriesKey}=${restConstants.postCallRetriesVal}",
          context: context,
          body: ampDownStreamItem.toJson());
      if (response != null && response.isNotEmpty) {
        return jsonDecode(response);
      }
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in updateDsConfig ---> ${e.message}');
    }
    return null;
  }

  @override
  Future<dynamic> updateUsConfig(
      {required String deviceEui,
      required BuildContext context,
      required AmpUpStreamItem ampUpStreamItem}) async {
    try {
      final response = await restServices.postRestCall(
          endpoint: restConstants.amps,
          addOns:
              "$deviceEui/${restConstants.cfgKey}/${restConstants.upKey}?${restConstants.timeoutKey}=${restConstants.postCallTimeout}&${restConstants.retriesKey}=${restConstants.postCallRetriesVal}",
          context: context,
          body: ampUpStreamItem.toJson());
      if (response != null && response.isNotEmpty) {
        return jsonDecode(response);
      }
    } on SocketException catch (e) {
      debugLogs('catch exception in updateUsConfig ---> ${e.message}');
    }
    return null;
  }

  @override
  Future<dynamic> setTestPointConfig(
      {required String deviceEui,
      required BuildContext context,
      required TestPointItem testPointItem}) async {
    try {
      final response = await restServices.postRestCall(
          endpoint: restConstants.amps,
          addOns:
              "$deviceEui/${restConstants.setTestPointCfg}?${restConstants.timeoutKey}=10&${restConstants.retriesKey}=3&${restConstants.refreshKey}=true",
          context: context,
          body: testPointItem.toJson());

      Map<String, dynamic> jsonData = jsonDecode(response!);
      if (jsonData['result'] != null && jsonData.isNotEmpty) {
        dynamic testPointItem = TestPointItem.fromJson(jsonData['result']);
        return testPointItem;
      } else {
        String error = jsonData['message'];
        return error;
      }
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in setTestPointConfig ---> ${e.message}');
    }
    return null;
  }

  @override
  Future<dynamic> startDsAlignment(
      {required String deviceEui, required BuildContext context}) async {
    try {
      final response = await restServices.postRestCallWithoutBody(
          endpoint: restConstants.amps,
          addOns:
              "$deviceEui/${restConstants.cmdKey}/${restConstants.alignmentKey}/${restConstants.downKey}?${restConstants.timeoutKey}=${restConstants.postCallTimeout}&${restConstants.retriesKey}=${restConstants.postCallRetriesVal}",
          context: context);
      if (response != null && response.isNotEmpty) {
        return jsonDecode(response);
      }
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in startDsAlignment ---> ${e.message}');
    }
    return null;
  }

  @override
  Future<dynamic> startUsAlignment(
      {required String deviceEui, required BuildContext context}) async {
    try {
      final response = await restServices.postRestCallWithoutBody(
          endpoint: restConstants.amps,
          addOns:
              "$deviceEui/${restConstants.cmdKey}/${restConstants.alignmentKey}/${restConstants.upKey}?${restConstants.timeoutKey}=${restConstants.postCallTimeout}&${restConstants.retriesKey}=${restConstants.postCallRetriesVal}",
          context: context);
      if (response != null && response.isNotEmpty) {
        return jsonDecode(response);
      }
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in startUsAlignment ---> ${e.message}');
    }
    return null;
  }

  @override
  Future<dynamic> getSpectrum(
      {required String deviceEui, required BuildContext context}) async {
    try {
      final response = await restServices.getRestCall(
          endpoint: restConstants.amps,
          addOns:
              "$deviceEui/${restConstants.spectrum}?${restConstants.timeoutKey}=${restConstants.getCallTimeout}&${restConstants.retriesKey}=${restConstants.getCallRetriesVal}",
          context: context);
      if (response != null && response.isNotEmpty) {
        final Map<String, dynamic> responseData = jsonDecode(response);
        if (responseData['result'] != null &&
            responseData['result'].isNotEmpty) {
          SpectrumModel spectrumModels = SpectrumModel.fromJson(responseData);
          return spectrumModels;
        } else {
          throw Exception(responseData['message']);
        }
      }
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in getSpectrum ---> ${e.message}');
    }
    throw Exception("Spectrum data not found");
  }

  @override
  Future<dynamic> getTelemetry(
      {required String deviceEui,
      required BuildContext context,
      required String formDate,
      required String toDate,required int pageOffset, required int perPageLimit}) async {
    try {
      final response = await restServices.getRestCall(
          endpoint: restConstants.amps,
          addOns:
              "$deviceEui/${restConstants.telemetry}/${restConstants.dataKey}?${restConstants.fromDateKey}=$formDate&${restConstants.toDateKey}=$toDate&${restConstants.exportAsKey}=json&${restConstants.limitKey}=$perPageLimit&${restConstants.offsetKey}=$pageOffset&${restConstants.orderByKey}=timestamp&${restConstants.directionKey}=DESC",
          context: context);
      if (response != null && response.isNotEmpty) {
        final Map<String, dynamic> responseData = jsonDecode(response);
        TelemetryData telemetry = TelemetryData.fromJson(responseData);
        if (telemetry.result != null) {
          return telemetry;
        } else {
          return TelemetryData.empty();
        }
      }
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in getTelemetry ---> ${e.message}');
    }
    return TelemetryData.empty();
  }

  @override
  Future<dynamic> getTelemetryThresholds(
      {required String deviceType,
      required BuildContext context}) async {
    try {
      final response = await restServices.getRestCall(
          endpoint: restConstants.amps,
          addOns: "${restConstants.telemetryThresholds}/$deviceType",
          context: context);
      if (response != null && response.isNotEmpty) {
        final Map<String, dynamic> responseData = jsonDecode(response);
        TelemetryThreshold telemetry = TelemetryThreshold.fromJson(responseData);
        return telemetry;
      }
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in getTelemetryThresholds ---> ${e.message}');
    }
    return TelemetryThreshold.empty();
  }


  @override
  Future<dynamic> addTelemetryThresholds(
      {required BuildContext context,
        required String deviceType,
        required TelemetryThreshold itemData}) async {
    try {

      final response = await restServices.postRestCall(
          endpoint: restConstants.amps,
          addOns:
          "${restConstants.telemetryThresholds}/$deviceType",
          context: context,
          body:itemData.toJson());
      debugLogs("addTelemetryThresholds response :$response");
      if (response != null && response.isNotEmpty) {
        final Map<String, dynamic> responseData = jsonDecode(response);
        TelemetryThreshold telemetry = TelemetryThreshold.fromJson(responseData);
        return telemetry;
      }
    } on SocketException catch (e) {
      debugLogs('catch exception in addTelemetryThresholds ---> ${e.message}');
    }
    return null;
  }



  @override
  Future<dynamic> getAlarmsHistoryData(
  {required String deviceEui, required BuildContext context,required String fromDate, required String toDate}) async {
    try {
      final response = await restServices.getRestCall(
          endpoint: restConstants.amps,
          addOns:
              "$deviceEui/${restConstants.alarmsHistory}?${RestConstants.instance.fromDateKey}=$fromDate&${RestConstants.instance.toDateKey}=$toDate&${restConstants.limitKey}=${restConstants.limitVal}&${restConstants.offsetKey}=${restConstants.offset}&${restConstants.orderByKey}=timestamp&${restConstants.directionKey}=DESC",
          context: context);
      if (response != null && response.isNotEmpty) {
        final List<dynamic> responseData = jsonDecode(response);
        List<AlarmHistoryData> alarmStatus =
            responseData.map((e) => AlarmHistoryData.fromJson(e)).toList();
        return alarmStatus;
      }
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in getAlarmsHistoryData ---> ${e.message}');
    }
    return {};
  }

  @override
  Future<dynamic> exportTelemetryCsvFile(
      {required String deviceEui,
      required String formDate,
      required String toDate,
      required BuildContext context}) async {
    try {
      final response = await restServices.getRestCall(
          endpoint: restConstants.amps,
          addOns:
              "$deviceEui/${restConstants.telemetry}/${restConstants.exportCsvKey}?${restConstants.fromDateKey}=$formDate&${restConstants.toDateKey}=$toDate",
          context: context);
      if (response != null && response.isNotEmpty) {
        return response;
      }
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in exportTelemetryCsvFile ---> ${e.message}');
    }
    return {};
  }

  @override
  Future<Map<String, dynamic>> dsAutoAlignment(
      {required String deviceEui, required BuildContext context,required bool isStatusCheck}) async {
    try {
      bool isRefresh = true;
      final response = await restServices.postRestCallWithResponse(
          endpoint: restConstants.amps,
          addOns:
              "$deviceEui/${restConstants.dsAutoAlignment}?${restConstants.timeoutKey}=${restConstants.postCallTimeout}&${restConstants.retriesKey}=${restConstants.postCallRetriesVal}&${restConstants.refreshKey}=$isRefresh",
          context: context,
          body: isStatusCheck ? {}:{"auto_alignment_op_e": 4});
      if (response != null && response.body.isNotEmpty) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
           if (responseData['result'] != null) {
          DsAutoAlignmentModel dsAutoAlignmentModel = DsAutoAlignmentModel.fromJson(responseData);
          return  {'body':dsAutoAlignmentModel,'headers':response.headers };
        } else {
          return {'body': responseData, 'headers': response.headers};
        }
      }
      return  {'body':DsAutoAlignmentModel.empty(),'headers':{"updated_at":null} };
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in dsAutoAlignment ---> ${e.message}');
    }
    return  {'body':DsAutoAlignmentModel.empty(),'headers':{"updated_at":null} } ;
  }

  @override
  Future<Map<String, dynamic>> dsManualAlignment(
      {required String deviceEui, required BuildContext context}) async {
    try {
      final response = await restServices.postRestCallWithResponse(
          endpoint: restConstants.amps,
          addOns:
              "$deviceEui/${restConstants.dsManualAlignment}?${restConstants.timeoutKey}=${restConstants.manualAlignmentCallTimeout}&${restConstants.retriesKey}=${restConstants.postCallRetriesVal}&${restConstants.refreshKey}=true",
          context: context,
          body: {});
      if (response != null && response.body.isNotEmpty) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        if (responseData['result'] != null) {
          DsManualAlignmentModel dsManualAlignmentModel =
              DsManualAlignmentModel.fromJson(responseData);
          return {'body': dsManualAlignmentModel, 'headers': response.headers};
        } else {
          return {'body': responseData, 'headers': response.headers};
        }
      }
      return  {'body':DsManualAlignmentModel.empty(),'headers':{"updated_at":null} };
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in dsAutoAlignment ---> ${e.message}');
    }
    return  {'body':DsManualAlignmentModel.empty(),'headers':{"updated_at":null} } ;
  }

  @override
  Future<Map<String, dynamic>> setDsManualAlignment(
      {required String deviceEui, required DsManualAlignmentItem dsManualAlignmentItem, required BuildContext context}) async {
    try {
      final response = await restServices.postRestCallWithResponse(
          endpoint: restConstants.amps,
          addOns:
              "$deviceEui/${restConstants.setDsManualAlignment}?${restConstants.timeoutKey}=${restConstants.postCallTimeout}&${restConstants.retriesKey}=${restConstants.postCallRetriesVal}&${restConstants.refreshKey}=true",
          context: context,
          body: dsManualAlignmentItem.toJson());
      if (response != null && response.body.isNotEmpty) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
         DsManualAlignmentModel dsManualAlignmentModel = DsManualAlignmentModel.fromJson(responseData);
        return  {'body':dsManualAlignmentModel,'headers':response.headers };
      }
      return  {'body':DsManualAlignmentModel.empty(),'headers':{"updated_at":null} };
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in dsAutoAlignment ---> ${e.message}');
    }
    return  {'body':DsManualAlignmentModel.empty(),'headers':{"updated_at":null} } ;
  }
  @override
  Future<Map<String, dynamic>> saveRevertDsManualAlignment(
      {required String deviceEui, required DsManualAlignmentItem dsManualAlignmentItem, required BuildContext context}) async {
    try {
      final response = await restServices.postRestCallWithResponse(
          endpoint: restConstants.amps,
          addOns:
              "$deviceEui/${restConstants.setDsManualAlignment}?${restConstants.timeoutKey}=${restConstants.postCallTimeout}&${restConstants.retriesKey}=${restConstants.postCallRetriesVal}&${restConstants.refreshKey}=true",
          context: context,
          body: dsManualAlignmentItem.toJsonWithControlType());
      if (response != null && response.body.isNotEmpty) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
         DsManualAlignmentModel dsManualAlignmentModel = DsManualAlignmentModel.fromJson(responseData);
        return  {'body':dsManualAlignmentModel,'headers':response.headers };
      }
      return  {'body':DsManualAlignmentModel.empty(),'headers':{"updated_at":null} };
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in dsAutoAlignment ---> ${e.message}');
    }
    return  {'body':DsManualAlignmentModel.empty(),'headers':{"updated_at":null} } ;
  }
  @override
  Future<Map<String, dynamic>> usManualAlignment(
      {required String deviceEui, required BuildContext context}) async {
    try {
      final response = await restServices.postRestCallWithResponse(
          endpoint: restConstants.amps,
          addOns:
              "$deviceEui/${restConstants.usManualAlignment}?${restConstants.timeoutKey}=${restConstants.manualAlignmentCallTimeout}&${restConstants.retriesKey}=${restConstants.postCallRetriesVal}&${restConstants.refreshKey}=true",
          context: context,
          body: {});
      if (response != null && response.body.isNotEmpty) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        if (responseData['result'] != null) {
          DsManualAlignmentModel dsManualAlignmentModel =
              DsManualAlignmentModel.fromJson(responseData);
          return {'body': dsManualAlignmentModel, 'headers': response.headers};
        } else {
          return {'body': responseData, 'headers': response.headers};
        }
      }
      return  {'body':DsManualAlignmentModel.empty(),'headers':{"updated_at":null} };
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in dsAutoAlignment ---> ${e.message}');
    }
    return  {'body':DsManualAlignmentModel.empty(),'headers':{"updated_at":null} } ;
  }

  @override
  Future<Map<String, dynamic>> setUsManualAlignment(
      {required String deviceEui, required DsManualAlignmentItem dsManualAlignmentItem, required BuildContext context}) async {
    try {
      final response = await restServices.postRestCallWithResponse(
          endpoint: restConstants.amps,
          addOns:
              "$deviceEui/${restConstants.setUsManualAlignment}?${restConstants.timeoutKey}=${restConstants.postCallTimeout}&${restConstants.retriesKey}=${restConstants.postCallRetriesVal}&${restConstants.refreshKey}=true",
          context: context,
          body: dsManualAlignmentItem.toJson());
      if (response != null && response.body.isNotEmpty) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
         DsManualAlignmentModel dsManualAlignmentModel = DsManualAlignmentModel.fromJson(responseData);
        return  {'body':dsManualAlignmentModel,'headers':response.headers };
      }
      return  {'body':DsManualAlignmentModel.empty(),'headers':{"updated_at":null} };
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in dsAutoAlignment ---> ${e.message}');
    }
    return  {'body':DsManualAlignmentModel.empty(),'headers':{"updated_at":null} } ;
  }

  @override
  Future<Map<String, dynamic>> saveRevertUsManualAlignment(
      {required String deviceEui, required DsManualAlignmentItem dsManualAlignmentItem, required BuildContext context}) async {
    try {
      final response = await restServices.postRestCallWithResponse(
          endpoint: restConstants.amps,
          addOns:
              "$deviceEui/${restConstants.setUsManualAlignment}?${restConstants.timeoutKey}=${restConstants.postCallTimeout}&${restConstants.retriesKey}=${restConstants.postCallRetriesVal}&${restConstants.refreshKey}=true",
          context: context,
          body: dsManualAlignmentItem.toJsonWithControlType());
      if (response != null && response.body.isNotEmpty) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
         DsManualAlignmentModel dsManualAlignmentModel = DsManualAlignmentModel.fromJson(responseData);
        return  {'body':dsManualAlignmentModel,'headers':response.headers };
      }
      return  {'body':DsManualAlignmentModel.empty(),'headers':{"updated_at":null} };
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in dsAutoAlignment ---> ${e.message}');
    }
    return  {'body':DsManualAlignmentModel.empty(),'headers':{"updated_at":null} } ;
  }

  @override
  Future<Map<String, dynamic>> usAutoAlignment(
      {required String deviceEui, required BuildContext context,required bool isStatusCheck}) async {
    try {
      bool isRefresh = true;
      final response = await restServices.postRestCallWithResponse(
          endpoint: restConstants.amps,
          addOns:
              "$deviceEui/${restConstants.usAutoAlignment}?${restConstants.timeoutKey}=${restConstants.postCallTimeout}&${restConstants.retriesKey}=${restConstants.postCallRetriesVal}&${restConstants.refreshKey}=$isRefresh",
          context: context,
          body: isStatusCheck? {} :{"auto_alignment_op_e": 4});
      if (response != null && response.body.isNotEmpty) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        return {'body':responseData,'headers':response.headers };
      }
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in usAutoAlignment ---> ${e.message}');
    }
    return {'body': {},'headers':{"updated_at":null} } ;
  }

  @override
  Future<Map<String, dynamic>> getDeviceSummary(
      {required String deviceEui, required BuildContext context ,bool isRefresh = false,required int bitMask}) async {
    try {
      // TODO: Need to break this into multiple calls see: https://atl-gitlab.ao-inc.com/qb18/amps_api/-/blob/main/proto/amps/identity.proto?ref_type=heads
      // get the static data with refresh=false, dynamic data with refresh=true
      //int bitmask = 0x01 | 0x02 | 0x04 | 0x10 | 0x20 ;
      Map<String,int> payload = {
        "bit_flags":bitMask
      };
      final response = await restServices.postRestCallWithResponse(
          endpoint: restConstants.amps,
          body: payload,
          addOns:
              "$deviceEui/${restConstants.deviceSummary}?${restConstants.timeoutKey}=${restConstants.getCallTimeout}&${restConstants.retriesKey}=${restConstants.getCallRetriesVal}&${restConstants.refreshKey}=$isRefresh",
          context: context);
      if (response != null && response.body.isNotEmpty) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        if (responseData['result'] != null) {
          AmpDeviceSummary ampDeviceSummary =
              AmpDeviceSummary.fromJson(responseData);
         return {'body':ampDeviceSummary,'headers':response.headers };

        } else {
          String error = responseData['detail'];
          return {'body':error,'headers':response.headers};
        }
      }
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in getDeviceSummary ---> ${e.message}');
    }
    return {'body':AmpDeviceSummary.empty(),'headers':{"updated_at":null} };

  }

  @override
  Future<dynamic> getAlarms(
      {required String deviceEui, required BuildContext context}) async {
    try {
      final response = await restServices.getRestCall(
          endpoint: restConstants.amps,
          addOns:
              "$deviceEui/${restConstants.alarms}?${restConstants.timeoutKey}=${restConstants.getCallTimeout}&${restConstants.retriesKey}=${restConstants.getCallRetriesVal}",
          context: context);
      if (response != null && response.isNotEmpty) {
        final Map<String, dynamic> responseData = jsonDecode(response);
        AlarmModel alarmModel = AlarmModel.fromJson(responseData);
        return alarmModel;
      }
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in getAlarms ---> ${e.message}');
    }
    return AlarmModel.empty();
  }

  @override
  Future<Map<String, dynamic>> getSpectrumData(
      {required String deviceEui,
      required SpectrumRequestItem spectrumDataItem,
      required BuildContext context,required bool isRefresh}) async {
    try {
      bool isCapture =spectrumDataItem.config.spectrumCaptureDataTypeE == 2;
      final response = await restServices.postRestCallWithResponse(
          endpoint: restConstants.amps,
          addOns:
              "$deviceEui/${restConstants.spectrum}?${restConstants.timeoutKey}=${isCapture ? restConstants.postCallTimeout45 : restConstants.postCallTimeout }&${restConstants.retriesKey}=${restConstants.postCallRetriesVal}&${restConstants.refreshKey}=$isRefresh",
          context: context,
          body: spectrumDataItem.toJson());
      if (response != null && response.body.isNotEmpty) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        if (responseData['result'] != null) {
          SpectrumModel spectrumModels = SpectrumModel.fromJson(responseData);
          return {'body':spectrumModels,'headers': response.headers};
        }else{

          String error = responseData['message'];
          return {'body':error,'headers': response.headers};
        }
      }
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in getSpectrumData ---> ${e.message}');
    }
    return {'body':SpectrumModel.empty(),'headers':{"updated_at":null}};
  }

  @override
  Future<dynamic> getDownstreamAmplifiers  (
      {required String deviceEui, required BuildContext context}) async{
        try {
          final response = await restServices.getRestCall(
              endpoint: restConstants.topology,
              addOns: "/${restConstants.device}/$deviceEui",
              context: context);
          if (response != null && response.isNotEmpty) {
        final Map<String, dynamic> responseData = jsonDecode(response);

        DownstreamAmplifiers downstreamAmplifiersModel =
            DownstreamAmplifiers.fromJson(responseData);
        return downstreamAmplifiersModel;
      }
    } on SocketException catch (e) {
          debugLogs(
              'catch exception in getDownstreamAmplifiers ---> ${e.message}');
        }
        throw Exception("Failed to get downstream amplifiers");
      }
  @override
  Future<Map<String, dynamic>> getDeviceTransponderInfo(
      {required String deviceEui, required BuildContext context ,bool isRefresh = false}) async {
    try {
      final response = await restServices.getRestCallWithResponse(
          endpoint: restConstants.amps,
          addOns:
          "$deviceEui/${restConstants.transponderInfo}?${restConstants.timeoutKey}=${restConstants.getCallTimeout}&${restConstants.retriesKey}=${restConstants.getCallRetriesVal}&${restConstants.refreshKey}=$isRefresh",
          context: context);
      if (response != null && response.body.isNotEmpty) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        if (responseData['result'] != null) {
          TransponderInfo transponderInfo = TransponderInfo.fromJson(responseData);
          return {'body':transponderInfo,'headers': response.headers};
        } else {
          String error = responseData['message'];
          return {'body':error,'headers': response.headers};
        }
      }
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in getDeviceTransponderInfo ---> ${e.message}');
    }

    return {'body':TransponderInfo.empty(),'headers': {"updated_at":null}};
  }
  @override
  Future<dynamic> setIngressSwitchControl(
      {required String deviceEui,
        required BuildContext context,
        required IngressSwitchItem ingressSwitchItem,required bool isRefresh,required bool isMultiIngressSwitch}) async {
    try {
      final response = await restServices.postRestCall(
          endpoint: restConstants.amps,
          addOns:
              "$deviceEui/${restConstants.setIngressSwitchControl}?${restConstants.timeoutKey}=${restConstants.postCallTimeout}&${restConstants.retriesKey}=${restConstants.postCallRetriesVal}&${restConstants.refreshKey}=$isRefresh",
          context: context,
          body: isMultiIngressSwitch ? ingressSwitchItem.toJson() : {
            "ingress_switch": ingressSwitchItem.ingressSwitch,
            "result": 30
          });
      Map<String, dynamic> jsonData = jsonDecode(response!);
      if (jsonData['result'] != null && jsonData.isNotEmpty) {
        dynamic ingressSwitch = IngressSwitchControl.fromJson(jsonData);
        return ingressSwitch;
      } else {
        String error = jsonData['detail'];
        return error;
      }
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in setIngressSwitchControl ---> ${e.message}');
    }
    return null;
  }

  @override
  Future<Map<String, dynamic>> getTestPointConfiguration(
      {required String deviceEui,
      required BuildContext context,
      required bool isRefresh}) async {
    try {
      final response = await restServices.postRestCallWithResponse(
          endpoint: restConstants.amps,
          context: context,
          addOns:
              "$deviceEui/${restConstants.getTestPointCfg}?${restConstants.timeoutKey}=${restConstants.postCallTimeout}&${restConstants.retriesKey}=${restConstants.postCallRetriesVal}&${restConstants.refreshKey}=$isRefresh",
          body: {});

      Map<String, dynamic> jsonData = jsonDecode(response!.body);
      if (jsonData['result'] != null && jsonData.isNotEmpty) {
        dynamic testPointConfiguration = TestPointModel.fromJson(jsonData);
        return {'body': testPointConfiguration, 'headers': response.headers};
      } else {
        String error = jsonData['message'];
        return {'body': error, 'headers': response.headers};
      }
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in getTestPointConfiguration ---> ${e.message}');
    }
    return {
      'body': null,
      'headers': {"update_at": TestPointModel}
    };
  }

  @override
  Future<Map<String, dynamic>> getIngressSwitchControl(
      {required String deviceEui,
        required BuildContext context,
        required IngressSwitchItem ingressSwitchItem, required bool isMultiIngress}) async {
    try {

      String ingressKey = isMultiIngress ? restConstants.getIngressSwitchControlS : restConstants.getIngressSwitchControl;
      final response = await restServices.postRestCallWithResponse(
          endpoint: restConstants.amps,
          addOns:
              "$deviceEui/$ingressKey?${restConstants.timeoutKey}=${restConstants.postCallTimeout}&${restConstants.retriesKey}=${restConstants.postCallRetriesVal}&${restConstants.refreshKey}=true",
          context: context,
          body: isMultiIngress
              ? {}
              : {"ingress_switch": ingressSwitchItem.ingressSwitch, "result": 30});
      Map<String, dynamic> jsonData = jsonDecode(response!.body);
      if (jsonData['result'] != null && jsonData.isNotEmpty) {
        IngressSwitchControl ingressSwitch = IngressSwitchControl.fromJson(jsonData);
        return {'body': ingressSwitch, 'headers': response.headers};
      } else {
        String error = jsonData['detail'];
        return {'body': error, 'headers': response.headers};
      }
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in getIngressSwitchControl ---> ${e.message}');
    }
    return {'body':null,'headers':{"updated_at":null}};
  }
  @override
  Future<Map<String, dynamic>> dsAutoAlignmentSpectrumData(
      {required String deviceEui, required BuildContext context,required bool isRefresh}) async {
    try {
      final response = await restServices.postRestCallWithResponse(
          endpoint: restConstants.amps,
          addOns:
          "$deviceEui/${restConstants.dsAutoAlignmentSpectrumData}?${restConstants.timeoutKey}=${restConstants.postCallTimeout}&${restConstants.retriesKey}=${restConstants.postCallRetriesVal}&${restConstants.refreshKey}=$isRefresh",
          context: context, body: {});
      if (response != null && response.body.isNotEmpty) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        if (responseData['result'] != null) {
          DSAutoAlignSpectrum dsAutoAlignSpectrum = DSAutoAlignSpectrum.fromJson(responseData);
          return  {'body':dsAutoAlignSpectrum,'headers':response.headers};
        } else {
          return {'body': responseData, 'headers': response.headers};
        }
      }
      return  {'body':DSAutoAlignSpectrum.empty(),'headers':{"updated_at":null} };
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in dsAutoAlignment ---> ${e.message}');
    }
    return  {'body':DsAutoAlignmentModel.empty(),'headers':{"updated_at":null} } ;
  }
  @override
  Future<Map<String, dynamic>> saveRevertDsAutoAlignment(
      {required String deviceEui, required BuildContext context,required bool isSave}) async {
    try {
      final response = await restServices.postRestCallWithResponse(
          endpoint: restConstants.amps,
          addOns:
          "$deviceEui/${restConstants.dsAutoAlignment}?${restConstants.timeoutKey}=${restConstants.postCallTimeout}&${restConstants.retriesKey}=${restConstants.postCallRetriesVal}&${restConstants.refreshKey}=true",
          context: context,
          body: isSave
              ? {"auto_alignment_op_e": 5}
              : {"auto_alignment_op_e": 6},
      );
      if (response != null && response.body.isNotEmpty) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        if (responseData['result'] != null) {
          DsAutoAlignmentModel dsAutoAlignmentModel = DsAutoAlignmentModel.fromJson(responseData);
          return  {'body':dsAutoAlignmentModel,'headers':response.headers };
        } else {
          return {'body': responseData, 'headers': response.headers};
        }
      }
      return  {'body':DsAutoAlignmentModel.empty(),'headers':{"updated_at":null} };
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in dsAutoAlignment ---> ${e.message}');
    }
    return  {'body':DsAutoAlignmentModel.empty(),'headers':{"updated_at":null} } ;
  }
  @override
  Future<Map<String, dynamic>> saveRevertUsAutoAlignment(
      {required String deviceEui, required BuildContext context, required bool isSave}) async {
    try {
      final response = await restServices.postRestCallWithResponse(
          endpoint: restConstants.amps,
          addOns:
          "$deviceEui/${restConstants.usAutoAlignment}?${restConstants.timeoutKey}=${restConstants.postCallTimeout}&${restConstants.retriesKey}=${restConstants.postCallRetriesVal}&${restConstants.refreshKey}=true",
          context: context,
          body: isSave
              ? {"auto_alignment_op_e": 5}
              : {"auto_alignment_op_e": 6},
      );
      if (response != null && response.body.isNotEmpty) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        if (responseData['result'] != null) {
          DsAutoAlignmentModel dsAutoAlignmentModel = DsAutoAlignmentModel.fromJson(responseData);
          return  {'body':dsAutoAlignmentModel,'headers':response.headers };
        } else {
          return {'body': responseData, 'headers': response.headers};
        }
      }
      return  {'body':DsAutoAlignmentModel.empty(),'headers':{"updated_at":null} };
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in dsAutoAlignment ---> ${e.message}');
    }
    return  {'body':DsAutoAlignmentModel.empty(),'headers':{"updated_at":null} } ;
  }

  @override
  Future<Map<String, dynamic>> getAMPFirmwareImageInfo(
      {required String deviceEui, required BuildContext context, required bool isRefresh}) async {
    try {
      final response = await restServices.postRestCallWithResponse(
        endpoint: restConstants.amps,
        addOns:
        "$deviceEui/${restConstants.firmwareImageInfo}?${restConstants.timeoutKey}=${restConstants.postCallTimeout}&${restConstants.retriesKey}=${restConstants.postCallRetriesVal}&${restConstants.refreshKey}=$isRefresh",
        context: context,
        body: {},
      );
      if (response != null && response.body.isNotEmpty) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        if (responseData['result'] != null) {
          FirmwareImageInfo firmwareImageInfo = FirmwareImageInfo.fromJson(responseData);
          return  {'body':firmwareImageInfo,'headers':response.headers };
        } else {
          return {'body': responseData['detail'], 'headers': response.headers};
        }
      }
      return  {'body':FirmwareImageInfo.empty(),'headers':{"updated_at":null} };
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in getFirmwareImageInfo ---> ${e.message}');
    }
    return  {'body':FirmwareImageInfo.empty(),'headers':{"updated_at":null} } ;
  }

  @override
  Future<Map<String, dynamic>> setAMPSwitchBankAndReboot(
      {required String deviceEui ,required int bankIndex, required BuildContext context, required bool isRefresh}) async {
    try {
      final response = await restServices.postRestCallWithResponse(
        endpoint: restConstants.amps,
        addOns:
            "$deviceEui/${restConstants.switchBankAndReboot}?${restConstants.timeoutKey}=${restConstants.postCallTimeout}&${restConstants.retriesKey}=${restConstants.postCallRetriesVal}&${restConstants.refreshKey}=$isRefresh",
        context: context,
        body: {
          "bank_index": bankIndex,
          "result": 0,
          "type": 0
        },
      );
      if (response != null && response.body.isNotEmpty) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        if (responseData['result'] != null) {
          return {'body': responseData, 'headers': response.headers};
        } else {
          return {'body': responseData['detail'], 'headers': response.headers};
        }
      }
      return {
        'body': {},
        'headers': {"updated_at": null}
      };
    } on SocketException catch (e) {
      debugLogs('catch exception in getFirmwareImageInfo ---> ${e.message}');
    }
    return {
      'body': {},
      'headers': {"updated_at": null}
    };
  }

  @override
  Future<Map<String, dynamic>> getTransponderFWImageInfo(
      {required String deviceEui, required BuildContext context, required bool isRefresh}) async {
    try {
      final response = await restServices.postRestCallWithResponse(
        endpoint: restConstants.amps,
        addOns:
        "$deviceEui/${restConstants.transponder}/${restConstants.firmwareImageInfo}?${restConstants.timeoutKey}=${restConstants.postCallTimeout}&${restConstants.retriesKey}=${restConstants.getCallRetriesVal}&${restConstants.refreshKey}=$isRefresh",
        context: context,
        body: {},
      );
      if (response != null && response.body.isNotEmpty) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        if (responseData['result'] != null) {
          FirmwareImageInfo firmwareImageInfo = FirmwareImageInfo.fromJson(responseData);
          return  {'body':firmwareImageInfo,'headers':response.headers };
        } else {
          return {'body': responseData['detail'], 'headers': response.headers};
        }
      }
      return  {'body':FirmwareImageInfo.empty(),'headers':{"updated_at":null} };
    } on SocketException catch (e) {
      debugLogs(
          'catch exception in getFirmwareImageInfo ---> ${e.message}');
    }
    return  {'body':FirmwareImageInfo.empty(),'headers':{"updated_at":null} } ;
  }

  @override
  Future<Map<String, dynamic>> setTransponderSwitchBankAndReboot(
      {required String deviceEui, required BuildContext context,required int bankIndex, required bool isRefresh}) async {
    try {
      final response = await restServices.postRestCallWithResponse(
        endpoint: restConstants.amps,
        addOns:
            "$deviceEui/${restConstants.transponder}/${restConstants.switchBankAndReboot}?${restConstants.timeoutKey}=${restConstants.postCallTimeout}&${restConstants.retriesKey}=${restConstants.postCallRetriesVal}&${restConstants.refreshKey}=$isRefresh",
        context: context,
        body: {
          "bank_index": bankIndex,
          "result": 0,
          "type": 0
        },
      );
      if (response != null && response.body.isNotEmpty) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        if (responseData['result'] != null) {
          return {'body': responseData, 'headers': response.headers};
        } else {
          return {'body': responseData['detail'], 'headers': response.headers};
        }
      }
      return {
        'body': {},
        'headers': {"updated_at": null}
      };
    } on SocketException catch (e) {
      debugLogs('catch exception in getFirmwareImageInfo ---> ${e.message}');
    }
    return {
      'body': {},
      'headers': {"updated_at": null}
    };
  }


  @override
  Future<dynamic> getAuditLogHistory(BuildContext context, int pageOffset,int perPageLimit,String deviceEUI) async {
    try {
      final response = await restServices.getRestCall(
        otherBaseUrl: AppConfig.shared.auditLogUrl,
        endpoint: "${restConstants.devices}?${restConstants.deviceEui}=$deviceEUI",
        addOns:
        "&${restConstants.offsetKey}=$pageOffset&${restConstants.limitKey}=$perPageLimit",
        context: context,
      );
      if (response != null && response.isNotEmpty) {
        var data = jsonDecode(response);
        AuditLogResponse auditLogResponse  = AuditLogResponse.fromJson(data);
        return auditLogResponse;
      }
    } on SocketException catch (e) {
      debugPrint(
          'catch exception in getAuditLogList ---> ${e.message}');
      return null;
    }
    return null;
  }

  @override
  Future<dynamic> getAlignmentConfigFileInfo(
      {required String deviceEui, required BuildContext context,required bool isRefresh}) async {
    try {
      final response = await restServices.postRestCallWithResponse(
        endpoint: restConstants.amps,
        addOns:
            "$deviceEui/${restConstants.getConfigFile}?${restConstants.timeoutKey}=${restConstants.postCallTimeout}&${restConstants.retriesKey}=${restConstants.postCallRetriesVal}&${restConstants.refreshKey}=$isRefresh",
        context: context,
        body: {},
      );
      if (response != null && response.body.isNotEmpty) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);

        if (responseData['result'] != null) {
          ConfigFileResponse configFileResponse = ConfigFileResponse.fromJson(responseData);
          return configFileResponse;
        } else {
          return responseData;
        }
      }
      return null;
    } on SocketException catch (e) {
      debugLogs('catch exception in getAlignmentConfigFileInfo ---> ${e.message}');
    }
    return null;
  }

  @override
  Future<dynamic> setAlignmentConfigInfo(
      {required String deviceEui, required BuildContext context, required AlignmentConfig alignmentConfig}) async {
    try {

      ConfigFileResult configFile = alignmentConfig.toConfigFileResponse();
      final response = await restServices.postRestCallWithResponse(
        endpoint: restConstants.amps,
        addOns:
        "$deviceEui/${restConstants.setConfigFile}?${restConstants.timeoutKey}=${restConstants.postCallTimeout}&${restConstants.retriesKey}=${restConstants.postCallRetriesVal}&${restConstants.refreshKey}=true",
        context: context,
        body: configFile.toJson(),
      );
      if (response != null && response.body.isNotEmpty) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        if (responseData['result'] != null) {
          ConfigFileResponse configFileResponse = ConfigFileResponse.fromJson(responseData);
          return configFileResponse;
        }  else {
          return responseData;
        }
      }
      return null;
    } on SocketException catch (e) {
      debugLogs('catch exception in setAlignmentConfigInfo ---> ${e.message}');
    }
    return null;
  }
}
