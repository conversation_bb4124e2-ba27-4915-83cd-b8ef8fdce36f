import 'dart:io';
import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/repository/amp_provider/amp_provider_repository.dart';

class AmpProviderRepositoryImpl implements AmpProviderRepository {
  RestHelper restServices = RestHelper.instance;
  RestConstants restConstants = RestConstants.instance;

  @override
  Future<dynamic> getAmpProviderList(
      {required BuildContext context,
      int? pageOffset,
      int? perPageLimit}) async {
    try {
      String addOns =
          "?${restConstants.limitKey}=${perPageLimit ?? restConstants.limitVal10}&${restConstants.offsetKey}=${pageOffset ?? restConstants.offset}&${restConstants.directionKey}=ASC";
      final response = await restServices.getRestCall(
          endpoint: restConstants.vendors, addOns: addOns, context: context);
      if (response != null && response.isNotEmpty) {
        final List<dynamic> data = jsonDecode(response);
        if (data.isNotEmpty) {
          final List<AmplifierProviderItem> ampProviderList =
              data.map((e) => AmplifierProviderItem.fromJson(e)).toList();
          return ampProviderList;
        }
        return [];
      } else {
        return null;
      }
    } on SocketException catch (e) {
      debugLogs('catch exception in getProvisionedDeviceList ---> ${e.message}');
    }
    return ProvisioningDeviceList.empty();
  }

  @override
  Future<dynamic> addNewAmpProvider(
      {required AmplifierProviderItem itemData, required BuildContext context}) async {
    try {
      final response = await restServices
          .postRestCall(endpoint: restConstants.vendors, addOns: "", context: context, body: itemData.toJson());
      if (response != null && response.isNotEmpty) {
        return jsonDecode(response);
      }
    } on SocketException catch (e) {
      debugLogs('catch exception in addProvisionDevice ---> ${e.message}');
    }
    return null;
  }

  @override
  Future<dynamic> updateAmpProvider(
      {required AmplifierProviderItem itemData,
      required BuildContext context,
      required String vendorCode}) async {
    try {
      final response = await restServices.putRestCallWithResponse(
          endpoint: restConstants.vendors,
          context: context,
          addOns: "/${restConstants.code}/$vendorCode",
          body: itemData.toJson());
      return response;
    } on SocketException catch (e) {
      debugLogs('catch exception in updateProvisionDevice ---> ${e.message}');
    }
    return null;
  }

  @override
  Future<dynamic> deleteAmpProvider({
    required BuildContext context,
    required String vendorCode
  }) async {
    try {
      final response = await restServices.deleteRestCall(
        endpoint: restConstants.vendors,
        addOns: "/${restConstants.code}/$vendorCode",
      );
      print("response-->${response}");
      if (response != null && response.isNotEmpty) {
        return jsonDecode(response);
      }
    } on SocketException catch (e) {
      debugLogs('catch exception in deleteAmpProvider ---> \\${e.message}');
    }
    return null;
  }

  @override
  Future<dynamic> getVendorKeysList(
      {required BuildContext context,
       required String vendorCode}) async {
    try {

      final response = await restServices.getRestCall(
          endpoint: restConstants.vendors, addOns: "/${restConstants.keys}?${restConstants.vendorCode}=$vendorCode", context: context);
      if (response != null && response.isNotEmpty) {
        final List<dynamic> data = jsonDecode(response);
        if (data.isNotEmpty) {
          final List<VendorKeyItem> vendorKeyList =
          data.map((e) => VendorKeyItem.fromJson(e)).toList();
          return vendorKeyList;
        }
        return <VendorKeyItem>[];
      } else {
        return null;
      }
    } on SocketException catch (e) {
      debugLogs('catch exception in getProvisionedDeviceList ---> ${e.message}');
    }
    return ProvisioningDeviceList.empty();
  }

  @override
  Future<dynamic> addNewVendorKey({
    required BuildContext context,
    required VendorKeyItem keyItem,
  }) async {
    try {
      final response = await restServices.postRestCall(
        endpoint: restConstants.vendors,
        addOns: "/${restConstants.keys}",
        context: context,
        body: keyItem.key != null ? keyItem.addVendorPayload(): {"vendor_code": keyItem.vendorCode},
      );
      if (response != null && response.isNotEmpty) {
        return jsonDecode(response);
      }
    } on SocketException catch (e) {
      debugLogs('catch exception in addNewVendorKey ---> ${e.message}');
    }
    return null;
  }

  @override
  Future<dynamic> deleteVendorKey({
    required BuildContext context,
    required String keyId,
  }) async {
    try {
      final response = await restServices.deleteRestCall(
        endpoint: restConstants.vendors,
        addOns: "/${restConstants.keys}/$keyId",
      );
      if (response != null && response.isNotEmpty) {
        return jsonDecode(response);
      }
    } on SocketException catch (e) {
      debugLogs('catch exception in deleteVendorKey ---> ${e.message}');
    }
    return null;
  }
}
