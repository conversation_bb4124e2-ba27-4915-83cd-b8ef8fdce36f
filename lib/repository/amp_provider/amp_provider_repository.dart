import '../../app_import.dart';

abstract class AmpProviderRepository {
  Future<dynamic> getAmpProviderList({required BuildContext context,int ?pageOffset, int ?perPageLimit});

  Future<dynamic> addNewAmpProvider({
    required AmplifierProviderItem itemData,
    required BuildContext context,
  });


  Future<dynamic> updateAmpProvider({
    required AmplifierProviderItem itemData,
    required BuildContext context,
    required String vendorCode
  });

  Future<dynamic> deleteAmpProvider({
    required BuildContext context,
    required String vendorCode
  });

  Future<dynamic> getVendorKeysList({required BuildContext context,required String vendorCode});
  
  Future<dynamic> addNewVendorKey({
    required BuildContext context,
    required VendorKeyItem keyItem,
  });

  Future<dynamic> deleteVendorKey({
    required BuildContext context,
    required String keyId,
  });
}
