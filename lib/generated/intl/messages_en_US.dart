// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en_US locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en_US';

  static String m0(devices, nodes) =>
      "${devices} devices and ${nodes} nodes shown";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ATTN": MessageLookupByLibrary.simpleMessage("ATTN"),
        "EQ": MessageLookupByLibrary.simpleMessage("EQ"),
        "aLSCConfig": MessageLookupByLibrary.simpleMessage("ALSC Config"),
        "aMPS": MessageLookupByLibrary.simpleMessage("AMPS"),
        "aMPS1": MessageLookupByLibrary.simpleMessage("AMPS"),
        "absent": MessageLookupByLibrary.simpleMessage("Absent"),
        "acVoltage": MessageLookupByLibrary.simpleMessage("AC Voltage"),
        "accessDenied": MessageLookupByLibrary.simpleMessage("Access Denied"),
        "accessDeniedMessage": MessageLookupByLibrary.simpleMessage(
            "Direct web access is disabled"),
        "action": MessageLookupByLibrary.simpleMessage("Action"),
        "active": MessageLookupByLibrary.simpleMessage("Active"),
        "activeAlarms": MessageLookupByLibrary.simpleMessage("Active Alarms"),
        "activeAmplifiers":
            MessageLookupByLibrary.simpleMessage("Active Amplifiers"),
        "activeVersion": MessageLookupByLibrary.simpleMessage("Active Version"),
        "addAutoBtn": MessageLookupByLibrary.simpleMessage("Generate"),
        "addBtn": MessageLookupByLibrary.simpleMessage("Add"),
        "addNewBtn": MessageLookupByLibrary.simpleMessage("Add New"),
        "addSiteFailed":
            MessageLookupByLibrary.simpleMessage("Add Site Failed."),
        "addSiteSuccess":
            MessageLookupByLibrary.simpleMessage("Add Site Successfully."),
        "addSites": MessageLookupByLibrary.simpleMessage("New Site Details"),
        "addToMulticast":
            MessageLookupByLibrary.simpleMessage("Add to Multicast"),
        "addedToMulticast":
            MessageLookupByLibrary.simpleMessage("Added to Multicast"),
        "afterVersion": MessageLookupByLibrary.simpleMessage("After Version"),
        "agcConfig": MessageLookupByLibrary.simpleMessage("ALSC Config"),
        "agcConfiguration":
            MessageLookupByLibrary.simpleMessage("ALSC Configuration"),
        "alarm": MessageLookupByLibrary.simpleMessage("Alarm"),
        "alarmCleared":
            MessageLookupByLibrary.simpleMessage("No Active Alarms"),
        "alarmError":
            MessageLookupByLibrary.simpleMessage("Error Retrieving Alarms"),
        "alarmStatus": MessageLookupByLibrary.simpleMessage("Alarms"),
        "alarms": MessageLookupByLibrary.simpleMessage("Alarms"),
        "alarmsBitmask": MessageLookupByLibrary.simpleMessage("Alarms BitMask"),
        "alarmsHistory": MessageLookupByLibrary.simpleMessage("Alarms History"),
        "alarmsNotifications": MessageLookupByLibrary.simpleMessage(
            "Alarms & Notifications History"),
        "alignmentConfiguration":
            MessageLookupByLibrary.simpleMessage("Alignment Configuration"),
        "alscEnabled": MessageLookupByLibrary.simpleMessage("ALSC Enabled"),
        "ampDetails":
            MessageLookupByLibrary.simpleMessage("Placement and Identity"),
        "ampFwVersion":
            MessageLookupByLibrary.simpleMessage("Amplifier FW Version"),
        "ampIngressUpdateFailed": MessageLookupByLibrary.simpleMessage(
            "Amplifier ingress updated failed"),
        "ampIngressUpdateSuccess": MessageLookupByLibrary.simpleMessage(
            "Amplifier ingress updated successfully"),
        "ampStatus": MessageLookupByLibrary.simpleMessage("Amplifier Status"),
        "ampTestPointConfigUpdateFailed": MessageLookupByLibrary.simpleMessage(
            "Amplifier test point config updated failed"),
        "ampTestPointConfigUpdateSuccess": MessageLookupByLibrary.simpleMessage(
            "Amplifier test point config updated successfully"),
        "ampVersion": MessageLookupByLibrary.simpleMessage("Amplifier Version"),
        "amplifier": MessageLookupByLibrary.simpleMessage("Amplifier"),
        "amplifierDownstream":
            MessageLookupByLibrary.simpleMessage("Downstream Amplifier"),
        "amplifierInfo":
            MessageLookupByLibrary.simpleMessage("Amplifier Information"),
        "amplifierIngress":
            MessageLookupByLibrary.simpleMessage("Amplifier Ingress"),
        "amplifierInitializationState": MessageLookupByLibrary.simpleMessage(
            "Amplifier Initialization State"),
        "amplifierInterstageValues":
            MessageLookupByLibrary.simpleMessage("Amplifier Interstage Values"),
        "amplifierIsOffline":
            MessageLookupByLibrary.simpleMessage("Amplifier is not online."),
        "amplifierMode": MessageLookupByLibrary.simpleMessage("Amplifier Mode"),
        "amplifiers": MessageLookupByLibrary.simpleMessage("Amplifiers"),
        "amplifiersWithAlarms":
            MessageLookupByLibrary.simpleMessage("Amplifiers with Alarms"),
        "ampsFineTuning": MessageLookupByLibrary.simpleMessage(
            "Amplifier Interstage Values Fine Tuning"),
        "apiVersion": MessageLookupByLibrary.simpleMessage("API Version"),
        "apis": MessageLookupByLibrary.simpleMessage("APIs"),
        "appName": MessageLookupByLibrary.simpleMessage("QuantumLink Central"),
        "appNameDev":
            MessageLookupByLibrary.simpleMessage("Dev QuantumLink - Central"),
        "appNameNode":
            MessageLookupByLibrary.simpleMessage("QuantumLink - Local"),
        "application": MessageLookupByLibrary.simpleMessage("Application"),
        "applicationName":
            MessageLookupByLibrary.simpleMessage("Application Name"),
        "apply": MessageLookupByLibrary.simpleMessage("Apply"),
        "archivalDate": MessageLookupByLibrary.simpleMessage("Archival Date"),
        "archive": MessageLookupByLibrary.simpleMessage("Archive"),
        "archived": MessageLookupByLibrary.simpleMessage("Archived"),
        "assetID": MessageLookupByLibrary.simpleMessage("BEID"),
        "associateDeployments":
            MessageLookupByLibrary.simpleMessage("Associate Deployments"),
        "attn": MessageLookupByLibrary.simpleMessage("ATTN-"),
        "auditLogs": MessageLookupByLibrary.simpleMessage("Audit Logs"),
        "autoAlignMarkerPilots":
            MessageLookupByLibrary.simpleMessage("Auto Align Marker Pilots"),
        "autoAlignSaveRevertMessage": MessageLookupByLibrary.simpleMessage(
            "Do you want to Save Auto Alignment Changes?"),
        "autoAlignment": MessageLookupByLibrary.simpleMessage("Auto Alignment"),
        "autoConfig": MessageLookupByLibrary.simpleMessage("Auto Config"),
        "autoJoin": MessageLookupByLibrary.simpleMessage("Auto Join"),
        "autoJoinDisabled":
            MessageLookupByLibrary.simpleMessage("Auto Join Disabled"),
        "aux": MessageLookupByLibrary.simpleMessage("Aux"),
        "back": MessageLookupByLibrary.simpleMessage("Back"),
        "backUpVersion": MessageLookupByLibrary.simpleMessage("Backup Version"),
        "backup": MessageLookupByLibrary.simpleMessage("Backup"),
        "backup1": MessageLookupByLibrary.simpleMessage("Backup 1:"),
        "backup2": MessageLookupByLibrary.simpleMessage("Backup 2:"),
        "backup3": MessageLookupByLibrary.simpleMessage("Backup 3:"),
        "badChannels": MessageLookupByLibrary.simpleMessage("Bad Channels"),
        "batteryAndCharging":
            MessageLookupByLibrary.simpleMessage("Battery/Charging Status"),
        "beforeVersion": MessageLookupByLibrary.simpleMessage("Before Version"),
        "bluetoothID": MessageLookupByLibrary.simpleMessage("Bluetooth ID"),
        "btnSignIn":
            MessageLookupByLibrary.simpleMessage("Sign In With Azure AD"),
        "build": MessageLookupByLibrary.simpleMessage("Build"),
        "buildDate": MessageLookupByLibrary.simpleMessage("Build Date"),
        "campaignStartDate":
            MessageLookupByLibrary.simpleMessage("Campaign Start Date"),
        "cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
        "capture": MessageLookupByLibrary.simpleMessage("Capture"),
        "changesAreNotSaved":
            MessageLookupByLibrary.simpleMessage("Changes are not Saved!"),
        "channel": MessageLookupByLibrary.simpleMessage("No."),
        "chargingStatus":
            MessageLookupByLibrary.simpleMessage("Charging Status"),
        "chirpIP": MessageLookupByLibrary.simpleMessage("Chirpstack IP"),
        "chirpstackAppName":
            MessageLookupByLibrary.simpleMessage("Chirpstack App Name"),
        "chooseFile": MessageLookupByLibrary.simpleMessage("Choose File"),
        "clear": MessageLookupByLibrary.simpleMessage("Clear"),
        "click": MessageLookupByLibrary.simpleMessage("click"),
        "close": MessageLookupByLibrary.simpleMessage("Close"),
        "code": MessageLookupByLibrary.simpleMessage("Code"),
        "configBitmask": MessageLookupByLibrary.simpleMessage("Config BitMask"),
        "configChannels": MessageLookupByLibrary.simpleMessage("Channel"),
        "configuration": MessageLookupByLibrary.simpleMessage("Configuration"),
        "configurationValue":
            MessageLookupByLibrary.simpleMessage("Configuration :"),
        "confirmDelete": MessageLookupByLibrary.simpleMessage("Delete?"),
        "confirmMessageConfiguration":
            MessageLookupByLibrary.simpleMessage("Do you want to continue?"),
        "confirmProvision": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to provision selected devices?"),
        "connect": MessageLookupByLibrary.simpleMessage("Connect"),
        "connectTo": MessageLookupByLibrary.simpleMessage("Connect to "),
        "connected": MessageLookupByLibrary.simpleMessage("Connected"),
        "connectedWith":
            MessageLookupByLibrary.simpleMessage("You are connected with"),
        "connectedWithEthernet": MessageLookupByLibrary.simpleMessage(
            "You are connected to network."),
        "connectionTimeOut": MessageLookupByLibrary.simpleMessage(
            "Your WiFi connection may be lost. Please reconnect to the IP Address assigned to the Node GW"),
        "contactAdministrator": MessageLookupByLibrary.simpleMessage(
            "If you believe this is a mistake, please\ncontact your administrator"),
        "contributors": MessageLookupByLibrary.simpleMessage("contributors"),
        "createDeployment":
            MessageLookupByLibrary.simpleMessage("Create Deployment"),
        "createSite": MessageLookupByLibrary.simpleMessage("Create Site"),
        "created": MessageLookupByLibrary.simpleMessage("Created"),
        "createdAt": MessageLookupByLibrary.simpleMessage("Created At"),
        "createdOn": MessageLookupByLibrary.simpleMessage("Created On"),
        "createdSuccessfully":
            MessageLookupByLibrary.simpleMessage("Created Successfully"),
        "currantUser": MessageLookupByLibrary.simpleMessage("Current User"),
        "currentFWVersion":
            MessageLookupByLibrary.simpleMessage("Current FW Version"),
        "currentVersion":
            MessageLookupByLibrary.simpleMessage("Current Version"),
        "custom": MessageLookupByLibrary.simpleMessage("Custom"),
        "dB": MessageLookupByLibrary.simpleMessage("dB"),
        "dBm": MessageLookupByLibrary.simpleMessage("dBm"),
        "dBmV": MessageLookupByLibrary.simpleMessage("dBmV"),
        "dSAttn": MessageLookupByLibrary.simpleMessage("DS ATTN"),
        "dSEQ": MessageLookupByLibrary.simpleMessage("DS EQ"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("Dashboard"),
        "dashboard": MessageLookupByLibrary.simpleMessage("Dashboard"),
        "dateTime": MessageLookupByLibrary.simpleMessage("Date/ Time"),
        "dbMV": MessageLookupByLibrary.simpleMessage("dBMV"),
        "dc8v": MessageLookupByLibrary.simpleMessage("8"),
        "deleteBtn": MessageLookupByLibrary.simpleMessage("Delete"),
        "deleteConfirmationMessage": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to delete"),
        "deleteMessage":
            MessageLookupByLibrary.simpleMessage("Do you want to delete?."),
        "deleteSelected":
            MessageLookupByLibrary.simpleMessage("Delete Selected"),
        "deleteSiteSuccess":
            MessageLookupByLibrary.simpleMessage("Site Deleted."),
        "deleteSuccessfully":
            MessageLookupByLibrary.simpleMessage("Deleted Successfully"),
        "demodBadPkts":
            MessageLookupByLibrary.simpleMessage("DeMod Bad Packets"),
        "demodDiagnostic":
            MessageLookupByLibrary.simpleMessage("DeMod Diagnostic"),
        "demodDroppedPkts":
            MessageLookupByLibrary.simpleMessage("DeMod Dropped Packets"),
        "demodGoodPkts":
            MessageLookupByLibrary.simpleMessage("DeMod Good Packets"),
        "demodInputLevel":
            MessageLookupByLibrary.simpleMessage("DeMod Input Level"),
        "demodNotFoundPkts":
            MessageLookupByLibrary.simpleMessage("DeMod Not Found Packets"),
        "deploymentFailed":
            MessageLookupByLibrary.simpleMessage("Deployment Failed"),
        "deploymentStatus":
            MessageLookupByLibrary.simpleMessage("Deployment Status"),
        "deploymentSummary":
            MessageLookupByLibrary.simpleMessage("Deployment Summary"),
        "deployments": MessageLookupByLibrary.simpleMessage("Deployments"),
        "description": MessageLookupByLibrary.simpleMessage("Description"),
        "destinationIP":
            MessageLookupByLibrary.simpleMessage("Destination IP Address"),
        "details": MessageLookupByLibrary.simpleMessage("Details"),
        "detectedAmplifierInNetWork": MessageLookupByLibrary.simpleMessage(
            "Detected Amplifier in the Network"),
        "devAddress": MessageLookupByLibrary.simpleMessage("DevAddr"),
        "devEUI": MessageLookupByLibrary.simpleMessage("DevEUI"),
        "device": MessageLookupByLibrary.simpleMessage("Device"),
        "deviceAlias": MessageLookupByLibrary.simpleMessage("Device Alias"),
        "deviceCount": MessageLookupByLibrary.simpleMessage("Device Count"),
        "deviceEUI": MessageLookupByLibrary.simpleMessage("Device EUI"),
        "deviceEUIHint": MessageLookupByLibrary.simpleMessage("Device EUI"),
        "deviceInfo": MessageLookupByLibrary.simpleMessage("Device Info"),
        "deviceLinks": MessageLookupByLibrary.simpleMessage("Device Links"),
        "deviceStatus": MessageLookupByLibrary.simpleMessage("Device Status"),
        "deviceType": MessageLookupByLibrary.simpleMessage("Device Type"),
        "deviceWasUpgraded":
            MessageLookupByLibrary.simpleMessage("device was upgraded."),
        "devices": MessageLookupByLibrary.simpleMessage("Devices"),
        "devicesAndGatewaysShown": m0,
        "devicesWereUpgraded":
            MessageLookupByLibrary.simpleMessage("devices were upgraded."),
        "diagnostics": MessageLookupByLibrary.simpleMessage("Diagnostics"),
        "diplexFilter": MessageLookupByLibrary.simpleMessage("Diplex Filter"),
        "disabled": MessageLookupByLibrary.simpleMessage("Disabled"),
        "discard": MessageLookupByLibrary.simpleMessage("Discard"),
        "discovered": MessageLookupByLibrary.simpleMessage("Discovered"),
        "discoveredButNotProvisioned": MessageLookupByLibrary.simpleMessage(
            "Discovered but not Provisioned"),
        "discoveredDevice":
            MessageLookupByLibrary.simpleMessage("Discovered Device"),
        "domain": MessageLookupByLibrary.simpleMessage("Domain"),
        "domainMisMatch":
            MessageLookupByLibrary.simpleMessage("Domain mismatch"),
        "dongleConnected":
            MessageLookupByLibrary.simpleMessage("Dongle connected"),
        "download": MessageLookupByLibrary.simpleMessage("Download"),
        "downloadCompleted":
            MessageLookupByLibrary.simpleMessage("Download Completed"),
        "downloadInProgress":
            MessageLookupByLibrary.simpleMessage("Download In Progress"),
        "downloadProgress":
            MessageLookupByLibrary.simpleMessage("Download Progress"),
        "downloadSoftware":
            MessageLookupByLibrary.simpleMessage("Open Software Download Page"),
        "downloadStatus":
            MessageLookupByLibrary.simpleMessage("Download Status"),
        "downstream": MessageLookupByLibrary.simpleMessage("Downstream"),
        "downstreamAmps":
            MessageLookupByLibrary.simpleMessage("Downstream Amps"),
        "draftMessageForConfiguration": MessageLookupByLibrary.simpleMessage(
            "Changes have not been saved. Please save or revert the draft."),
        "dsAMPS": MessageLookupByLibrary.simpleMessage("#DS Amps"),
        "dsAlignCfg": MessageLookupByLibrary.simpleMessage("DS Alignment"),
        "dsAlignment": MessageLookupByLibrary.simpleMessage("D/S Alignment"),
        "dsAlignmentCompleted": MessageLookupByLibrary.simpleMessage(
            "DS alignment completed successfully"),
        "dsAlignmentFailed":
            MessageLookupByLibrary.simpleMessage("DS alignment failed."),
        "dsGain": MessageLookupByLibrary.simpleMessage("DS Gain"),
        "dsGainAdjust": MessageLookupByLibrary.simpleMessage("DS Gain Adjust"),
        "dsSlopeAdjust":
            MessageLookupByLibrary.simpleMessage("DS Slope Adjust"),
        "duration": MessageLookupByLibrary.simpleMessage("Duration"),
        "eTag": MessageLookupByLibrary.simpleMessage("Etag"),
        "edit": MessageLookupByLibrary.simpleMessage("Edit"),
        "editItemBtn": MessageLookupByLibrary.simpleMessage("Edit"),
        "email": MessageLookupByLibrary.simpleMessage("Email"),
        "empoweringConnectivity":
            MessageLookupByLibrary.simpleMessage("Empowering Connectivity"),
        "enableManualMode":
            MessageLookupByLibrary.simpleMessage("Enable Manual Mode"),
        "enabled": MessageLookupByLibrary.simpleMessage("Enabled"),
        "enclosureStatus":
            MessageLookupByLibrary.simpleMessage("Enclosure Status"),
        "end": MessageLookupByLibrary.simpleMessage("End Freq:"),
        "endDate": MessageLookupByLibrary.simpleMessage("End Date"),
        "endTime": MessageLookupByLibrary.simpleMessage("End Time"),
        "enterEmail": MessageLookupByLibrary.simpleMessage("Enter email"),
        "enterPassword": MessageLookupByLibrary.simpleMessage("Enter Password"),
        "equ": MessageLookupByLibrary.simpleMessage("EQ-"),
        "errorDeviceInfoMessage": MessageLookupByLibrary.simpleMessage(
            "Failed to set device user info."),
        "errorRetrievingMessage":
            MessageLookupByLibrary.simpleMessage("Error Retrieving Data"),
        "export": MessageLookupByLibrary.simpleMessage("Export"),
        "exportCSVError":
            MessageLookupByLibrary.simpleMessage("Export CSV File Error"),
        "exportCSVSuccess": MessageLookupByLibrary.simpleMessage(
            "Export CSV File Successfully"),
        "exportRangeError":
            MessageLookupByLibrary.simpleMessage("Please select date range"),
        "exportTelemetry":
            MessageLookupByLibrary.simpleMessage("Export Telemetry"),
        "fPort": MessageLookupByLibrary.simpleMessage("F-Port"),
        "fWBuildDate": MessageLookupByLibrary.simpleMessage("FW Build Date"),
        "fWRev": MessageLookupByLibrary.simpleMessage("FWRev"),
        "fWVersion": MessageLookupByLibrary.simpleMessage("FW Version"),
        "failedToConnect":
            MessageLookupByLibrary.simpleMessage("Failed to Connect"),
        "fetchSpectrum":
            MessageLookupByLibrary.simpleMessage("Fetching Spectrum Levels.."),
        "fileName": MessageLookupByLibrary.simpleMessage("File Name"),
        "fileTypeDescriptor":
            MessageLookupByLibrary.simpleMessage("File Type (descriptor)"),
        "fileUpload": MessageLookupByLibrary.simpleMessage("File Upload"),
        "filters": MessageLookupByLibrary.simpleMessage("Filters"),
        "firmwareDownload":
            MessageLookupByLibrary.simpleMessage("Firmware Upgrade"),
        "firmwareDownloadConfig":
            MessageLookupByLibrary.simpleMessage("Firmware Download Config"),
        "firmwareFiles": MessageLookupByLibrary.simpleMessage("Firmware Files"),
        "firmwareVersion":
            MessageLookupByLibrary.simpleMessage("Firmware Version"),
        "fiveV": MessageLookupByLibrary.simpleMessage("5.5"),
        "fiveVDC": MessageLookupByLibrary.simpleMessage("5v DC"),
        "forty": MessageLookupByLibrary.simpleMessage("40"),
        "forward": MessageLookupByLibrary.simpleMessage("Fwd"),
        "fragmentCount": MessageLookupByLibrary.simpleMessage("Fragment Count"),
        "fragmentRate": MessageLookupByLibrary.simpleMessage("Fragment Rate"),
        "fragmentSessionIndex":
            MessageLookupByLibrary.simpleMessage("Fragment Session Index"),
        "fragmentSessionSetup":
            MessageLookupByLibrary.simpleMessage("Fragment Session"),
        "fragmentSize": MessageLookupByLibrary.simpleMessage("Fragment Size"),
        "fragmentStatusCompleted":
            MessageLookupByLibrary.simpleMessage("Fragment Status"),
        "framesDropped": MessageLookupByLibrary.simpleMessage("Frames Dropped"),
        "freq": MessageLookupByLibrary.simpleMessage("Freq"),
        "frequency": MessageLookupByLibrary.simpleMessage("Frequency (MHz)"),
        "frequencyConfig":
            MessageLookupByLibrary.simpleMessage("Frequency Config"),
        "fromDateTime": MessageLookupByLibrary.simpleMessage("From Date Time"),
        "fwdInput":
            MessageLookupByLibrary.simpleMessage("FWD Input Test Point"),
        "fwdOutput":
            MessageLookupByLibrary.simpleMessage("FWD Output Test Point"),
        "fwdnld": MessageLookupByLibrary.simpleMessage("Fwdnld"),
        "gain": MessageLookupByLibrary.simpleMessage("Gain"),
        "gatWayId": MessageLookupByLibrary.simpleMessage("Gateway ID"),
        "gatWayName": MessageLookupByLibrary.simpleMessage("Gateway Name"),
        "gatewayEUIIsRequired": MessageLookupByLibrary.simpleMessage(
            "Gateway EUI is required to start topology scan"),
        "gateways": MessageLookupByLibrary.simpleMessage("Gateways"),
        "ghZ": MessageLookupByLibrary.simpleMessage("GHz"),
        "gwDetail": MessageLookupByLibrary.simpleMessage("GW Detail"),
        "gwEUI": MessageLookupByLibrary.simpleMessage("GW EUI"),
        "gwID": MessageLookupByLibrary.simpleMessage("GW ID"),
        "gwName": MessageLookupByLibrary.simpleMessage("GW Name"),
        "gwNotFoundMessage":
            MessageLookupByLibrary.simpleMessage("GW ID not found."),
        "gwSetting": MessageLookupByLibrary.simpleMessage("Gateway Settings"),
        "hWRev": MessageLookupByLibrary.simpleMessage("HWRev"),
        "hWVersion": MessageLookupByLibrary.simpleMessage("HW Version"),
        "healthy": MessageLookupByLibrary.simpleMessage("Healthy"),
        "healthyServicesMessage": MessageLookupByLibrary.simpleMessage(
            "Please Wait - Starting Services"),
        "hereToUpdateSentence":
            MessageLookupByLibrary.simpleMessage("here to update the software"),
        "high": MessageLookupByLibrary.simpleMessage("High"),
        "highSplit": MessageLookupByLibrary.simpleMessage("High Split"),
        "home": MessageLookupByLibrary.simpleMessage("Home"),
        "homeProvisionedAmplifiers":
            MessageLookupByLibrary.simpleMessage("Provisioned Amplifiers"),
        "homeProvisioning":
            MessageLookupByLibrary.simpleMessage("Provision Amplifiers"),
        "homeSettings": MessageLookupByLibrary.simpleMessage("Settings"),
        "homeSiteRegions":
            MessageLookupByLibrary.simpleMessage("Sites / Regions"),
        "hwVersion": MessageLookupByLibrary.simpleMessage("HW Version"),
        "id": MessageLookupByLibrary.simpleMessage("ID"),
        "identification":
            MessageLookupByLibrary.simpleMessage("Identification"),
        "info": MessageLookupByLibrary.simpleMessage("Info"),
        "ingressSwitch": MessageLookupByLibrary.simpleMessage("Ingress Switch"),
        "initiatedSpectrum": MessageLookupByLibrary.simpleMessage(
            "Spectrum initiated, please wait"),
        "inputStage": MessageLookupByLibrary.simpleMessage("Input Stage"),
        "interface": MessageLookupByLibrary.simpleMessage("Interface"),
        "intermediateStage":
            MessageLookupByLibrary.simpleMessage("Intermediate Stage"),
        "invalidCodeFormat":
            MessageLookupByLibrary.simpleMessage("Invalid code format"),
        "invalidTokenMessage":
            MessageLookupByLibrary.simpleMessage("Invalid Token"),
        "inventory": MessageLookupByLibrary.simpleMessage("Inventory"),
        "ipAddress": MessageLookupByLibrary.simpleMessage("IP Address"),
        "joinServer": MessageLookupByLibrary.simpleMessage("Join Server"),
        "key": MessageLookupByLibrary.simpleMessage("Key"),
        "keysSynced": MessageLookupByLibrary.simpleMessage("Keys Synced"),
        "lE": MessageLookupByLibrary.simpleMessage("LE"),
        "lastSeen": MessageLookupByLibrary.simpleMessage("Last Seen"),
        "lastWeek":
            MessageLookupByLibrary.simpleMessage("Last Week (Sun - Sat)"),
        "layers": MessageLookupByLibrary.simpleMessage("Layers"),
        "level": MessageLookupByLibrary.simpleMessage("Level"),
        "lidIsClose": MessageLookupByLibrary.simpleMessage("Lid is close"),
        "lidIsOpen": MessageLookupByLibrary.simpleMessage("Lid is open"),
        "lidOpen": MessageLookupByLibrary.simpleMessage("Lid open"),
        "lidStatus": MessageLookupByLibrary.simpleMessage("Lid Status"),
        "list": MessageLookupByLibrary.simpleMessage("List"),
        "listView": MessageLookupByLibrary.simpleMessage("List View"),
        "loading": MessageLookupByLibrary.simpleMessage("Loading..."),
        "loadingDevices":
            MessageLookupByLibrary.simpleMessage("Loading devices..."),
        "location": MessageLookupByLibrary.simpleMessage("Location"),
        "locationFieldLessThen33": MessageLookupByLibrary.simpleMessage(
            "The field should be less than 33 characters"),
        "locationLatLong":
            MessageLookupByLibrary.simpleMessage("Location\n(Lat, Long)"),
        "locationNotSet":
            MessageLookupByLibrary.simpleMessage("Location not set"),
        "locationValidateMessage":
            MessageLookupByLibrary.simpleMessage("Enter valid location"),
        "locationValidation":
            MessageLookupByLibrary.simpleMessage("Enter valid location"),
        "login": MessageLookupByLibrary.simpleMessage("Login"),
        "logout": MessageLookupByLibrary.simpleMessage("Logout"),
        "logoutConfirmation": MessageLookupByLibrary.simpleMessage(
            "Are you sure that you want to logout?"),
        "low": MessageLookupByLibrary.simpleMessage("Low"),
        "lowSplit": MessageLookupByLibrary.simpleMessage("Low Split"),
        "main": MessageLookupByLibrary.simpleMessage("Main"),
        "manual": MessageLookupByLibrary.simpleMessage("Manual"),
        "manualAlignment":
            MessageLookupByLibrary.simpleMessage("Manual Alignment"),
        "map": MessageLookupByLibrary.simpleMessage("Map"),
        "mapView": MessageLookupByLibrary.simpleMessage("Map View"),
        "maxTabDeploymentMessage": MessageLookupByLibrary.simpleMessage(
            "You’ve reached the maximum limit of 6 deployment tabs."),
        "maxTabMessage": MessageLookupByLibrary.simpleMessage(
            "A maximum of 6 tabs can be opened at a time."),
        "message": MessageLookupByLibrary.simpleMessage("Message"),
        "messageIngressDisable": MessageLookupByLibrary.simpleMessage(
            "Are you sure, you want to change the ingress switch settings?"),
        "messageIngressEnable": MessageLookupByLibrary.simpleMessage(
            "Enabling the ingress may cause service disruption. Are you sure that you want to perform this operation?"),
        "mfgDate": MessageLookupByLibrary.simpleMessage("Mfg Date"),
        "mhZ": MessageLookupByLibrary.simpleMessage("MHz"),
        "mhz": MessageLookupByLibrary.simpleMessage("MHz"),
        "missingKey": MessageLookupByLibrary.simpleMessage("Missing Key"),
        "missingSiteInfo":
            MessageLookupByLibrary.simpleMessage("Pending site info"),
        "missingVendor": MessageLookupByLibrary.simpleMessage("Missing Vendor"),
        "modDiagnostic": MessageLookupByLibrary.simpleMessage("Mod Diagnostic"),
        "modOutputLevel":
            MessageLookupByLibrary.simpleMessage("Mod Output Level"),
        "modPkts": MessageLookupByLibrary.simpleMessage("Mod packets"),
        "model": MessageLookupByLibrary.simpleMessage("Model"),
        "modelNumber": MessageLookupByLibrary.simpleMessage("Model Numbers"),
        "msgAskConfirmationAutoAlign": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to perform auto-alignment?"),
        "msgAskConfirmationDesc": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to continue?"),
        "msgAskConfirmationTitle":
            MessageLookupByLibrary.simpleMessage("Confirm ?"),
        "msgAskResetPktFwd":
            MessageLookupByLibrary.simpleMessage("Are you sure ?"),
        "msgSwitchBankReboot": MessageLookupByLibrary.simpleMessage(
            "Are you sure want to confirm switch bank and reboot?"),
        "multicastGroupAddress":
            MessageLookupByLibrary.simpleMessage("Multicast Group Address"),
        "multicastID": MessageLookupByLibrary.simpleMessage("Multicast ID"),
        "multicastIP": MessageLookupByLibrary.simpleMessage("Multicast IP"),
        "multicastIPAddress":
            MessageLookupByLibrary.simpleMessage("Multicast IP Address"),
        "multicastOn": MessageLookupByLibrary.simpleMessage("Multicast On"),
        "multicastSession":
            MessageLookupByLibrary.simpleMessage("Multicast Session"),
        "nDRSession": MessageLookupByLibrary.simpleMessage("NDR Session"),
        "na": MessageLookupByLibrary.simpleMessage("NA"),
        "name": MessageLookupByLibrary.simpleMessage("Name"),
        "ndfDestIp": MessageLookupByLibrary.simpleMessage("NDF \nDest IP"),
        "ndfMulticastIp":
            MessageLookupByLibrary.simpleMessage("NDF \nMulticast IP"),
        "ndfSession": MessageLookupByLibrary.simpleMessage("NDF Session"),
        "ndfSessionId": MessageLookupByLibrary.simpleMessage("NDF\nSession ID"),
        "ndrSessionId":
            MessageLookupByLibrary.simpleMessage("NDR \nSession ID"),
        "networkServer":
            MessageLookupByLibrary.simpleMessage("Network \nServer"),
        "networkServerIP":
            MessageLookupByLibrary.simpleMessage("Network Server IP"),
        "networkServerPort":
            MessageLookupByLibrary.simpleMessage("Network Server Port"),
        "never": MessageLookupByLibrary.simpleMessage("Never"),
        "neverSeen": MessageLookupByLibrary.simpleMessage("Never Seen"),
        "newAmpDetails":
            MessageLookupByLibrary.simpleMessage("New Amplifier Details"),
        "newAmpProvider":
            MessageLookupByLibrary.simpleMessage("New Amplifier Provider"),
        "newKeyVendor": MessageLookupByLibrary.simpleMessage("Add Custom Key"),
        "no": MessageLookupByLibrary.simpleMessage("No"),
        "noAlarms": MessageLookupByLibrary.simpleMessage("No Alarms"),
        "noData": MessageLookupByLibrary.simpleMessage("No data"),
        "noDataFound": MessageLookupByLibrary.simpleMessage("No Data Found"),
        "noDeviceFound":
            MessageLookupByLibrary.simpleMessage("No device found"),
        "noFileChosen": MessageLookupByLibrary.simpleMessage("No file chosen"),
        "noFirmwareSelected":
            MessageLookupByLibrary.simpleMessage("Select firmware file"),
        "noWifiFound":
            MessageLookupByLibrary.simpleMessage("No wifi network found"),
        "nodeGW": MessageLookupByLibrary.simpleMessage("Node GW"),
        "nodeGWDSGain":
            MessageLookupByLibrary.simpleMessage("Node GW\nDS Gain"),
        "nodeGWDongleConnection": MessageLookupByLibrary.simpleMessage(
            "Node GW/Dashboard Connection"),
        "nodeGw": MessageLookupByLibrary.simpleMessage("Node GW"),
        "notConnectedWith": MessageLookupByLibrary.simpleMessage(
            "You are not connected to network."),
        "offline": MessageLookupByLibrary.simpleMessage("Offline"),
        "ok": MessageLookupByLibrary.simpleMessage("Ok"),
        "on": MessageLookupByLibrary.simpleMessage("On"),
        "online": MessageLookupByLibrary.simpleMessage("Online"),
        "open": MessageLookupByLibrary.simpleMessage("Open"),
        "openMode": MessageLookupByLibrary.simpleMessage("Op. Mode"),
        "openStreetMap":
            MessageLookupByLibrary.simpleMessage("© OpenStreetMap "),
        "operation": MessageLookupByLibrary.simpleMessage("Operation"),
        "outputStage": MessageLookupByLibrary.simpleMessage("Output Stage"),
        "passWord": MessageLookupByLibrary.simpleMessage("Password"),
        "password": MessageLookupByLibrary.simpleMessage("Password"),
        "pavingTheWayTitle": MessageLookupByLibrary.simpleMessage(
            "Paving the Way for Next-Generation Network Innovation"),
        "pending": MessageLookupByLibrary.simpleMessage("Pending"),
        "pilot": MessageLookupByLibrary.simpleMessage("Pilot"),
        "pilot1": MessageLookupByLibrary.simpleMessage("Pilot 1:"),
        "pilot2": MessageLookupByLibrary.simpleMessage("Pilot 2:"),
        "pilot3": MessageLookupByLibrary.simpleMessage("Pilot 3:"),
        "pilotBackup": MessageLookupByLibrary.simpleMessage("Backup"),
        "pilotMarker": MessageLookupByLibrary.simpleMessage("Marker"),
        "pilotMeasuredPwr":
            MessageLookupByLibrary.simpleMessage("Pilot - Measured Power"),
        "pilotPrimary": MessageLookupByLibrary.simpleMessage("Primary"),
        "placement": MessageLookupByLibrary.simpleMessage("Placement"),
        "please": MessageLookupByLibrary.simpleMessage("Please"),
        "pleaseSelectFileType":
            MessageLookupByLibrary.simpleMessage("Please select type"),
        "pleaseWait": MessageLookupByLibrary.simpleMessage("Please wait"),
        "pointConfig": MessageLookupByLibrary.simpleMessage("Point Config"),
        "port": MessageLookupByLibrary.simpleMessage("Port"),
        "power": MessageLookupByLibrary.simpleMessage("Power :"),
        "powerSource": MessageLookupByLibrary.simpleMessage("Power Source"),
        "powerSupply":
            MessageLookupByLibrary.simpleMessage("Power Supply Information"),
        "powerTable": MessageLookupByLibrary.simpleMessage("Power"),
        "present": MessageLookupByLibrary.simpleMessage("Present"),
        "proceed": MessageLookupByLibrary.simpleMessage("Proceed"),
        "process": MessageLookupByLibrary.simpleMessage("Progress"),
        "product": MessageLookupByLibrary.simpleMessage("Type"),
        "providerNotFound":
            MessageLookupByLibrary.simpleMessage("Provider not found"),
        "provision": MessageLookupByLibrary.simpleMessage("Provision"),
        "provisionSelected":
            MessageLookupByLibrary.simpleMessage("Provision selected devices"),
        "provisioned": MessageLookupByLibrary.simpleMessage("Provisioned"),
        "qlDashboard": MessageLookupByLibrary.simpleMessage("QL Dashboard"),
        "quantumCentral":
            MessageLookupByLibrary.simpleMessage("Quantum Central"),
        "quickSearch": MessageLookupByLibrary.simpleMessage("Quick Search"),
        "rPD": MessageLookupByLibrary.simpleMessage("RPD"),
        "rPD1": MessageLookupByLibrary.simpleMessage("RPD"),
        "readyForUpgrade":
            MessageLookupByLibrary.simpleMessage("Ready for Upgrade"),
        "redisIP": MessageLookupByLibrary.simpleMessage("Redis IP"),
        "redisPort": MessageLookupByLibrary.simpleMessage("Redis Port"),
        "redundancyPct":
            MessageLookupByLibrary.simpleMessage("Redundancy Percentage"),
        "ref": MessageLookupByLibrary.simpleMessage("Ref"),
        "refresh": MessageLookupByLibrary.simpleMessage("Refresh"),
        "refreshFailedMessage":
            MessageLookupByLibrary.simpleMessage("The refresh failed in "),
        "refreshRef":
            MessageLookupByLibrary.simpleMessage("Refresh Reference "),
        "remark": MessageLookupByLibrary.simpleMessage("Remark"),
        "removeFromMulticast":
            MessageLookupByLibrary.simpleMessage("Remove from Multicast"),
        "request_method":
            MessageLookupByLibrary.simpleMessage("Request Method"),
        "request_path": MessageLookupByLibrary.simpleMessage("Request Path"),
        "reset": MessageLookupByLibrary.simpleMessage("Reset"),
        "resetDefault": MessageLookupByLibrary.simpleMessage("Reset Default"),
        "restore": MessageLookupByLibrary.simpleMessage("Restore"),
        "restrictSigninMessage":
            MessageLookupByLibrary.simpleMessage("Invalid Grant"),
        "reverse": MessageLookupByLibrary.simpleMessage("Rev"),
        "revert": MessageLookupByLibrary.simpleMessage("Revert"),
        "revertDsAlignmentCompleted": MessageLookupByLibrary.simpleMessage(
            "DS alignment reverted successfully"),
        "revertDsAlignmentFailed":
            MessageLookupByLibrary.simpleMessage("DS alignment revert failed"),
        "revertUsAlignmentCompleted": MessageLookupByLibrary.simpleMessage(
            "US alignment reverted successfully"),
        "revertUsAlignmentFailed":
            MessageLookupByLibrary.simpleMessage("US alignment revert failed"),
        "roadmap": MessageLookupByLibrary.simpleMessage("Road Map"),
        "rpd": MessageLookupByLibrary.simpleMessage("#RPD"),
        "rpdIp": MessageLookupByLibrary.simpleMessage("RPD IP(src)"),
        "rssi": MessageLookupByLibrary.simpleMessage("RSSI"),
        "sE": MessageLookupByLibrary.simpleMessage("SE"),
        "satellite": MessageLookupByLibrary.simpleMessage("Satellite"),
        "save": MessageLookupByLibrary.simpleMessage("Save"),
        "saveDsAlignmentCompleted": MessageLookupByLibrary.simpleMessage(
            "DS alignment saved successfully"),
        "saveDsAlignmentFailed":
            MessageLookupByLibrary.simpleMessage("DS alignment save failed"),
        "saveRevertInfoText": MessageLookupByLibrary.simpleMessage(
            "Click Save to save the values permanently"),
        "saveUsAlignmentCompleted": MessageLookupByLibrary.simpleMessage(
            "US alignment saved successfully"),
        "saveUsAlignmentFailed":
            MessageLookupByLibrary.simpleMessage("US alignment save failed"),
        "scanTriggeredMessage": MessageLookupByLibrary.simpleMessage(
            "Topology scan triggered. This may take a few minutes."),
        "search": MessageLookupByLibrary.simpleMessage("Search.."),
        "searchByDeviceEUI":
            MessageLookupByLibrary.simpleMessage("Enter Device EUI to Filter"),
        "searchDevice":
            MessageLookupByLibrary.simpleMessage("Search device..."),
        "searchGateway": MessageLookupByLibrary.simpleMessage(
            "Search for Gateway by Name, EUI or Site ."),
        "searchWifi":
            MessageLookupByLibrary.simpleMessage("Search wifi network"),
        "select": MessageLookupByLibrary.simpleMessage("Select"),
        "selectAgc": MessageLookupByLibrary.simpleMessage("Select ALSC"),
        "selectDateRange":
            MessageLookupByLibrary.simpleMessage("Select Date Range"),
        "selectFile": MessageLookupByLibrary.simpleMessage("Select File"),
        "selectSite": MessageLookupByLibrary.simpleMessage("Select Site"),
        "selectType": MessageLookupByLibrary.simpleMessage("Select Type"),
        "selectTypeLE": MessageLookupByLibrary.simpleMessage("Line Extender"),
        "selectTypeSE":
            MessageLookupByLibrary.simpleMessage("System Amplifier"),
        "selectUniversal":
            MessageLookupByLibrary.simpleMessage("Select Universal"),
        "selectVendor": MessageLookupByLibrary.simpleMessage("Vendor"),
        "selectWifi":
            MessageLookupByLibrary.simpleMessage("Select a WiFi Network"),
        "serial": MessageLookupByLibrary.simpleMessage("Serial"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("Serial Number"),
        "serviceStatus": MessageLookupByLibrary.simpleMessage("Service Status"),
        "services": MessageLookupByLibrary.simpleMessage("Services"),
        "sessionID": MessageLookupByLibrary.simpleMessage("Session ID"),
        "setDsAlignmentCompleted": MessageLookupByLibrary.simpleMessage(
            "DS alignment submitted successfully"),
        "setDsAlignmentFailed":
            MessageLookupByLibrary.simpleMessage("DS alignment write failed"),
        "setUsAlignmentCompleted": MessageLookupByLibrary.simpleMessage(
            "US alignment submitted successfully"),
        "setUsAlignmentFailed":
            MessageLookupByLibrary.simpleMessage("US alignment write failed"),
        "showCompleted": MessageLookupByLibrary.simpleMessage("Show Completed"),
        "showDiscovered":
            MessageLookupByLibrary.simpleMessage("Show Discovered"),
        "signIn": MessageLookupByLibrary.simpleMessage("SignIn"),
        "signOut": MessageLookupByLibrary.simpleMessage("Sign Out"),
        "site": MessageLookupByLibrary.simpleMessage("Site"),
        "siteID": MessageLookupByLibrary.simpleMessage("Site ID"),
        "siteName": MessageLookupByLibrary.simpleMessage("Site Name"),
        "siteRegions": MessageLookupByLibrary.simpleMessage("Sites/Regions"),
        "siteUpdateSuccess":
            MessageLookupByLibrary.simpleMessage("Site Updated Successfully!!"),
        "sitesAmplifiers": MessageLookupByLibrary.simpleMessage("#Amplifiers"),
        "size": MessageLookupByLibrary.simpleMessage("Size (Bytes)"),
        "skipDevices": MessageLookupByLibrary.simpleMessage("Skip Devices"),
        "slop": MessageLookupByLibrary.simpleMessage("Slope"),
        "socketExceptionMessage":
            MessageLookupByLibrary.simpleMessage("Something went wrong"),
        "socketTimeOutMessage":
            MessageLookupByLibrary.simpleMessage("Please try later."),
        "softwareUpdate":
            MessageLookupByLibrary.simpleMessage("Software Update"),
        "somethingWentWrong":
            MessageLookupByLibrary.simpleMessage("Something went wrong!!"),
        "sourceIP": MessageLookupByLibrary.simpleMessage("Source IP Address"),
        "source_service":
            MessageLookupByLibrary.simpleMessage("Source Service"),
        "spectrum": MessageLookupByLibrary.simpleMessage("Spectrum"),
        "spectrumSuccessMessage": MessageLookupByLibrary.simpleMessage(
            "Spectrum Data Fetched Successfully"),
        "ssid": MessageLookupByLibrary.simpleMessage("SSID"),
        "start": MessageLookupByLibrary.simpleMessage("Start Freq:"),
        "startAutoAlignment":
            MessageLookupByLibrary.simpleMessage("Start Auto Alignment"),
        "startCapture": MessageLookupByLibrary.simpleMessage(" Start Capture"),
        "startDate": MessageLookupByLibrary.simpleMessage("Start Date"),
        "startDownstream":
            MessageLookupByLibrary.simpleMessage("Start Downstream Alignment"),
        "startEndDateValidation": MessageLookupByLibrary.simpleMessage(
            "End date should not be earlier than the start date."),
        "startScan": MessageLookupByLibrary.simpleMessage("Start"),
        "startTime": MessageLookupByLibrary.simpleMessage("Start Time"),
        "startUpstream":
            MessageLookupByLibrary.simpleMessage("Start Upstream Alignment"),
        "state": MessageLookupByLibrary.simpleMessage("State"),
        "stats": MessageLookupByLibrary.simpleMessage("Stats"),
        "status": MessageLookupByLibrary.simpleMessage("Status"),
        "step": MessageLookupByLibrary.simpleMessage("Step-1 Freq:"),
        "submit": MessageLookupByLibrary.simpleMessage("SUBMIT"),
        "summaryStatus": MessageLookupByLibrary.simpleMessage("Summary Status"),
        "swVersion": MessageLookupByLibrary.simpleMessage("SW Version"),
        "switchBankAndReboot":
            MessageLookupByLibrary.simpleMessage("Switch Bank and Reboot"),
        "switchFW": MessageLookupByLibrary.simpleMessage("Switch FW"),
        "switchToAuto": MessageLookupByLibrary.simpleMessage("Switch to Auto"),
        "switchToManual":
            MessageLookupByLibrary.simpleMessage("Switch to Manual"),
        "tableDeviceEUI": MessageLookupByLibrary.simpleMessage("Device EUI"),
        "tableNoData": MessageLookupByLibrary.simpleMessage("No data"),
        "tableType": MessageLookupByLibrary.simpleMessage("Type"),
        "tag": MessageLookupByLibrary.simpleMessage("Tag"),
        "tagToSite":
            MessageLookupByLibrary.simpleMessage("Not tagged to a site"),
        "tags": MessageLookupByLibrary.simpleMessage("Tags"),
        "tcp": MessageLookupByLibrary.simpleMessage("TCP"),
        "telemetry": MessageLookupByLibrary.simpleMessage("Telemetry"),
        "telemetryThreshold":
            MessageLookupByLibrary.simpleMessage("Telemetry Thresholds"),
        "temp": MessageLookupByLibrary.simpleMessage("Temp ℃"),
        "tempF": MessageLookupByLibrary.simpleMessage("Temp ℉"),
        "temperature": MessageLookupByLibrary.simpleMessage("Temperature (C)"),
        "temperatureTitle": MessageLookupByLibrary.simpleMessage("Temperature"),
        "testPointConfig":
            MessageLookupByLibrary.simpleMessage("Test Point Config"),
        "thermal": MessageLookupByLibrary.simpleMessage("Thermal"),
        "thisFieldIsRequired":
            MessageLookupByLibrary.simpleMessage("This field is required"),
        "thisWeek":
            MessageLookupByLibrary.simpleMessage("This Week (Sun - Today)"),
        "threePointThreeV": MessageLookupByLibrary.simpleMessage("3.3"),
        "threeVDc": MessageLookupByLibrary.simpleMessage("3.3v DC"),
        "thresholdSuccess": MessageLookupByLibrary.simpleMessage(
            "Threshold Value Updated Successfully!!"),
        "timestamp": MessageLookupByLibrary.simpleMessage("Timestamp"),
        "toDateTime": MessageLookupByLibrary.simpleMessage("To Date Time"),
        "today": MessageLookupByLibrary.simpleMessage("Today"),
        "topology": MessageLookupByLibrary.simpleMessage("Topology"),
        "topologyScan": MessageLookupByLibrary.simpleMessage("Topology Scan"),
        "total": MessageLookupByLibrary.simpleMessage("Total"),
        "totalDeploymentTime":
            MessageLookupByLibrary.simpleMessage("Total Deployment Time"),
        "totalFragments":
            MessageLookupByLibrary.simpleMessage("Total Fragments"),
        "totalProvisioned":
            MessageLookupByLibrary.simpleMessage("Total Provisioned"),
        "totalSize": MessageLookupByLibrary.simpleMessage("Total Size"),
        "transponder":
            MessageLookupByLibrary.simpleMessage("Transponder Information"),
        "transponderFwVersion":
            MessageLookupByLibrary.simpleMessage("Transponder FW Version"),
        "transponderVersion":
            MessageLookupByLibrary.simpleMessage("Transponder Version"),
        "twentyFourDC": MessageLookupByLibrary.simpleMessage("24v DC"),
        "twentyFourV": MessageLookupByLibrary.simpleMessage("24"),
        "twoThirtyTwo": MessageLookupByLibrary.simpleMessage("232"),
        "type": MessageLookupByLibrary.simpleMessage("Type"),
        "uSAttn": MessageLookupByLibrary.simpleMessage("US ATTN"),
        "uSEQ": MessageLookupByLibrary.simpleMessage("US EQ"),
        "unProvisioned": MessageLookupByLibrary.simpleMessage("UnProvisioned"),
        "units": MessageLookupByLibrary.simpleMessage("Units"),
        "universalPlugin":
            MessageLookupByLibrary.simpleMessage("Universal Plugin"),
        "unknown": MessageLookupByLibrary.simpleMessage("Unknown"),
        "upTime": MessageLookupByLibrary.simpleMessage("Up Time"),
        "update": MessageLookupByLibrary.simpleMessage("Update"),
        "updateAmpDetails":
            MessageLookupByLibrary.simpleMessage("Update Amplifier Details"),
        "updateAmpProvider":
            MessageLookupByLibrary.simpleMessage("Update Amplifier Provider"),
        "updateFailed": MessageLookupByLibrary.simpleMessage("Update Failed"),
        "updateSiteDetails":
            MessageLookupByLibrary.simpleMessage("Update Site Details"),
        "updateSiteFailed":
            MessageLookupByLibrary.simpleMessage("Update Site Failed."),
        "updateSiteSuccess":
            MessageLookupByLibrary.simpleMessage("Update Site Successfully."),
        "updateSiteVLGW":
            MessageLookupByLibrary.simpleMessage("Update Site to VLGW"),
        "updateSuccess":
            MessageLookupByLibrary.simpleMessage("Updated Successfully"),
        "updateThreshold":
            MessageLookupByLibrary.simpleMessage("Update Threshold"),
        "upgrade": MessageLookupByLibrary.simpleMessage("Upgrade"),
        "upgradeAutoText": MessageLookupByLibrary.simpleMessage(
            "Upgrade automatically after download."),
        "upgradeCompleted":
            MessageLookupByLibrary.simpleMessage("Upgrade Completed"),
        "upgradeConfirmationMessage":
            MessageLookupByLibrary.simpleMessage("Start deployment?"),
        "upgradeRequestSent":
            MessageLookupByLibrary.simpleMessage("Upgrade Request Sent"),
        "upgradeStatus": MessageLookupByLibrary.simpleMessage("Upgrade Status"),
        "upgradeType": MessageLookupByLibrary.simpleMessage("Upgrade Type"),
        "upload": MessageLookupByLibrary.simpleMessage("Upload"),
        "uploadAt": MessageLookupByLibrary.simpleMessage("Upload Date"),
        "uploadDate": MessageLookupByLibrary.simpleMessage("Upload Date"),
        "uploadFail": MessageLookupByLibrary.simpleMessage("File Error"),
        "uploadFirmware":
            MessageLookupByLibrary.simpleMessage("Upload Firmware"),
        "uploadJoinServer":
            MessageLookupByLibrary.simpleMessage("Upload CSV for Join Server"),
        "uploadNew": MessageLookupByLibrary.simpleMessage("Upload New"),
        "uploadSuccess":
            MessageLookupByLibrary.simpleMessage("File uploaded successfully"),
        "uploaded": MessageLookupByLibrary.simpleMessage("Uploaded"),
        "upstream": MessageLookupByLibrary.simpleMessage("Upstream"),
        "url": MessageLookupByLibrary.simpleMessage("URL"),
        "usAlignCfg": MessageLookupByLibrary.simpleMessage("US Alignment"),
        "usAlignment": MessageLookupByLibrary.simpleMessage("U/S Alignment"),
        "usAlignmentCompleted": MessageLookupByLibrary.simpleMessage(
            "US alignment completed successfully."),
        "usAlignmentFailed":
            MessageLookupByLibrary.simpleMessage("US alignment failed."),
        "usGainAdjust": MessageLookupByLibrary.simpleMessage("US Gain Adjust"),
        "usRssi": MessageLookupByLibrary.simpleMessage("US RSSI"),
        "usSlopeAdjust":
            MessageLookupByLibrary.simpleMessage("US Slope Adjust"),
        "user": MessageLookupByLibrary.simpleMessage("User"),
        "userID": MessageLookupByLibrary.simpleMessage("User ID"),
        "userInformation":
            MessageLookupByLibrary.simpleMessage("User Information"),
        "vDC": MessageLookupByLibrary.simpleMessage("Vdc"),
        "vLGW": MessageLookupByLibrary.simpleMessage("#VLGW"),
        "vLGW1": MessageLookupByLibrary.simpleMessage("VLGW"),
        "vLGWInfo": MessageLookupByLibrary.simpleMessage("VLGW Info"),
        "vac": MessageLookupByLibrary.simpleMessage("VAC"),
        "validEmail": MessageLookupByLibrary.simpleMessage("Domain not found"),
        "validEmailAddress":
            MessageLookupByLibrary.simpleMessage("Enter a valid email"),
        "vdd33v": MessageLookupByLibrary.simpleMessage("3.3 V"),
        "vendor": MessageLookupByLibrary.simpleMessage("Vendor"),
        "vendorCode": MessageLookupByLibrary.simpleMessage("Vendor Code"),
        "version": MessageLookupByLibrary.simpleMessage("Version"),
        "virtualGW": MessageLookupByLibrary.simpleMessage("Virtual GW"),
        "virtual_tilt":
            MessageLookupByLibrary.simpleMessage("Virtual Tilt (dB)"),
        "vlgwFSKStats": MessageLookupByLibrary.simpleMessage("FSKStats"),
        "vlgwHostname": MessageLookupByLibrary.simpleMessage("VLGW Hostname"),
        "vlgwIp": MessageLookupByLibrary.simpleMessage("VLGW Hostname"),
        "volt": MessageLookupByLibrary.simpleMessage("V"),
        "wifiSSID": MessageLookupByLibrary.simpleMessage("WiFi SSID"),
        "wifiStatusMessage": MessageLookupByLibrary.simpleMessage(
            "Unable to check - Something went wrong"),
        "wifiUplink": MessageLookupByLibrary.simpleMessage("WiFi UpLink"),
        "write": MessageLookupByLibrary.simpleMessage("Write"),
        "yes": MessageLookupByLibrary.simpleMessage("Yes"),
        "zoom": MessageLookupByLibrary.simpleMessage("Zoom")
      };
}
