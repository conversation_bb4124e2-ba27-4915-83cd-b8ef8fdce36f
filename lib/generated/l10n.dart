// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(_current != null,
        'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.');
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(instance != null,
        'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?');
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `ATTN`
  String get ATTN {
    return Intl.message(
      'ATTN',
      name: 'ATTN',
      desc: '',
      args: [],
    );
  }

  /// `EQ`
  String get EQ {
    return Intl.message(
      'EQ',
      name: 'EQ',
      desc: '',
      args: [],
    );
  }

  /// `ALSC Config`
  String get aLSCConfig {
    return Intl.message(
      'ALSC Config',
      name: 'aLSCConfig',
      desc: '',
      args: [],
    );
  }

  /// `AMPS`
  String get aMPS {
    return Intl.message(
      'AMPS',
      name: 'aMPS',
      desc: '',
      args: [],
    );
  }

  /// `AMPS`
  String get aMPS1 {
    return Intl.message(
      'AMPS',
      name: 'aMPS1',
      desc: '',
      args: [],
    );
  }

  /// `Absent`
  String get absent {
    return Intl.message(
      'Absent',
      name: 'absent',
      desc: '',
      args: [],
    );
  }

  /// `AC Voltage`
  String get acVoltage {
    return Intl.message(
      'AC Voltage',
      name: 'acVoltage',
      desc: '',
      args: [],
    );
  }

  /// `Action`
  String get action {
    return Intl.message(
      'Action',
      name: 'action',
      desc: '',
      args: [],
    );
  }

  /// `Active`
  String get active {
    return Intl.message(
      'Active',
      name: 'active',
      desc: '',
      args: [],
    );
  }

  /// `Active Alarms`
  String get activeAlarms {
    return Intl.message(
      'Active Alarms',
      name: 'activeAlarms',
      desc: '',
      args: [],
    );
  }

  /// `Active Amplifiers`
  String get activeAmplifiers {
    return Intl.message(
      'Active Amplifiers',
      name: 'activeAmplifiers',
      desc: '',
      args: [],
    );
  }

  /// `Add`
  String get addBtn {
    return Intl.message(
      'Add',
      name: 'addBtn',
      desc: '',
      args: [],
    );
  }

  /// `Add New`
  String get addNewBtn {
    return Intl.message(
      'Add New',
      name: 'addNewBtn',
      desc: '',
      args: [],
    );
  }

  /// `Add Site Failed.`
  String get addSiteFailed {
    return Intl.message(
      'Add Site Failed.',
      name: 'addSiteFailed',
      desc: '',
      args: [],
    );
  }

  /// `Add Site Successfully.`
  String get addSiteSuccess {
    return Intl.message(
      'Add Site Successfully.',
      name: 'addSiteSuccess',
      desc: '',
      args: [],
    );
  }

  /// `New Site Details`
  String get addSites {
    return Intl.message(
      'New Site Details',
      name: 'addSites',
      desc: '',
      args: [],
    );
  }

  /// `Add to Multicast`
  String get addToMulticast {
    return Intl.message(
      'Add to Multicast',
      name: 'addToMulticast',
      desc: '',
      args: [],
    );
  }

  /// `Added to Multicast`
  String get addedToMulticast {
    return Intl.message(
      'Added to Multicast',
      name: 'addedToMulticast',
      desc: '',
      args: [],
    );
  }

  /// `ALSC Config`
  String get agcConfig {
    return Intl.message(
      'ALSC Config',
      name: 'agcConfig',
      desc: '',
      args: [],
    );
  }

  /// `ALSC Configuration`
  String get agcConfiguration {
    return Intl.message(
      'ALSC Configuration',
      name: 'agcConfiguration',
      desc: '',
      args: [],
    );
  }

  /// `Alarm`
  String get alarm {
    return Intl.message(
      'Alarm',
      name: 'alarm',
      desc: '',
      args: [],
    );
  }

  /// `No Active Alarms`
  String get alarmCleared {
    return Intl.message(
      'No Active Alarms',
      name: 'alarmCleared',
      desc: '',
      args: [],
    );
  }

  /// `Error Retrieving Alarms`
  String get alarmError {
    return Intl.message(
      'Error Retrieving Alarms',
      name: 'alarmError',
      desc: '',
      args: [],
    );
  }

  /// `Alarms`
  String get alarmStatus {
    return Intl.message(
      'Alarms',
      name: 'alarmStatus',
      desc: '',
      args: [],
    );
  }

  /// `Alarms`
  String get alarms {
    return Intl.message(
      'Alarms',
      name: 'alarms',
      desc: '',
      args: [],
    );
  }

  /// `Alarms History`
  String get alarmsHistory {
    return Intl.message(
      'Alarms History',
      name: 'alarmsHistory',
      desc: '',
      args: [],
    );
  }

  /// `Alarms & Notifications History`
  String get alarmsNotifications {
    return Intl.message(
      'Alarms & Notifications History',
      name: 'alarmsNotifications',
      desc: '',
      args: [],
    );
  }

  /// `Alignment Configuration`
  String get alignmentConfiguration {
    return Intl.message(
      'Alignment Configuration',
      name: 'alignmentConfiguration',
      desc: '',
      args: [],
    );
  }

  /// `Placement and Identity`
  String get ampDetails {
    return Intl.message(
      'Placement and Identity',
      name: 'ampDetails',
      desc: '',
      args: [],
    );
  }

  /// `Amplifier Status`
  String get ampStatus {
    return Intl.message(
      'Amplifier Status',
      name: 'ampStatus',
      desc: '',
      args: [],
    );
  }

  /// `Amplifier`
  String get amplifier {
    return Intl.message(
      'Amplifier',
      name: 'amplifier',
      desc: '',
      args: [],
    );
  }

  /// `Amplifier Information`
  String get amplifierInfo {
    return Intl.message(
      'Amplifier Information',
      name: 'amplifierInfo',
      desc: '',
      args: [],
    );
  }

  /// `Amplifier Initialization State`
  String get amplifierInitializationState {
    return Intl.message(
      'Amplifier Initialization State',
      name: 'amplifierInitializationState',
      desc: '',
      args: [],
    );
  }

  /// `Amplifier Interstage Values`
  String get amplifierInterstageValues {
    return Intl.message(
      'Amplifier Interstage Values',
      name: 'amplifierInterstageValues',
      desc: '',
      args: [],
    );
  }

  /// `Amplifier is not online.`
  String get amplifierIsOffline {
    return Intl.message(
      'Amplifier is not online.',
      name: 'amplifierIsOffline',
      desc: '',
      args: [],
    );
  }

  /// `Amplifier Mode`
  String get amplifierMode {
    return Intl.message(
      'Amplifier Mode',
      name: 'amplifierMode',
      desc: '',
      args: [],
    );
  }

  /// `Amplifiers`
  String get amplifiers {
    return Intl.message(
      'Amplifiers',
      name: 'amplifiers',
      desc: '',
      args: [],
    );
  }

  /// `Amplifiers with Alarms`
  String get amplifiersWithAlarms {
    return Intl.message(
      'Amplifiers with Alarms',
      name: 'amplifiersWithAlarms',
      desc: '',
      args: [],
    );
  }

  /// `Amplifier Interstage Values Fine Tuning`
  String get ampsFineTuning {
    return Intl.message(
      'Amplifier Interstage Values Fine Tuning',
      name: 'ampsFineTuning',
      desc: '',
      args: [],
    );
  }

  /// `QuantumLink Central`
  String get appName {
    return Intl.message(
      'QuantumLink Central',
      name: 'appName',
      desc: '',
      args: [],
    );
  }

  /// `Dev QuantumLink - Central`
  String get appNameDev {
    return Intl.message(
      'Dev QuantumLink - Central',
      name: 'appNameDev',
      desc: '',
      args: [],
    );
  }

  /// `QuantumLink - Local`
  String get appNameNode {
    return Intl.message(
      'QuantumLink - Local',
      name: 'appNameNode',
      desc: '',
      args: [],
    );
  }

  /// `Apply`
  String get apply {
    return Intl.message(
      'Apply',
      name: 'apply',
      desc: '',
      args: [],
    );
  }

  /// `Archival Date`
  String get archivalDate {
    return Intl.message(
      'Archival Date',
      name: 'archivalDate',
      desc: '',
      args: [],
    );
  }

  /// `Archive`
  String get archive {
    return Intl.message(
      'Archive',
      name: 'archive',
      desc: '',
      args: [],
    );
  }

  /// `Archived`
  String get archived {
    return Intl.message(
      'Archived',
      name: 'archived',
      desc: '',
      args: [],
    );
  }

  /// `BEID`
  String get assetID {
    return Intl.message(
      'BEID',
      name: 'assetID',
      desc: '',
      args: [],
    );
  }

  /// `ATTN-`
  String get attn {
    return Intl.message(
      'ATTN-',
      name: 'attn',
      desc: '',
      args: [],
    );
  }

  /// `Audit Logs`
  String get auditLogs {
    return Intl.message(
      'Audit Logs',
      name: 'auditLogs',
      desc: '',
      args: [],
    );
  }

  /// `Auto Alignment`
  String get autoAlignment {
    return Intl.message(
      'Auto Alignment',
      name: 'autoAlignment',
      desc: '',
      args: [],
    );
  }

  /// `Backup`
  String get backup {
    return Intl.message(
      'Backup',
      name: 'backup',
      desc: '',
      args: [],
    );
  }

  /// `Backup 1:`
  String get backup1 {
    return Intl.message(
      'Backup 1:',
      name: 'backup1',
      desc: '',
      args: [],
    );
  }

  /// `Backup 2:`
  String get backup2 {
    return Intl.message(
      'Backup 2:',
      name: 'backup2',
      desc: '',
      args: [],
    );
  }

  /// `Backup 3:`
  String get backup3 {
    return Intl.message(
      'Backup 3:',
      name: 'backup3',
      desc: '',
      args: [],
    );
  }

  /// `Bluetooth ID`
  String get bluetoothID {
    return Intl.message(
      'Bluetooth ID',
      name: 'bluetoothID',
      desc: '',
      args: [],
    );
  }

  /// `Sign In With Azure AD`
  String get btnSignIn {
    return Intl.message(
      'Sign In With Azure AD',
      name: 'btnSignIn',
      desc: '',
      args: [],
    );
  }

  /// `Build`
  String get build {
    return Intl.message(
      'Build',
      name: 'build',
      desc: '',
      args: [],
    );
  }

  /// `Build Date`
  String get buildDate {
    return Intl.message(
      'Build Date',
      name: 'buildDate',
      desc: '',
      args: [],
    );
  }

  /// `Campaign Start Date`
  String get campaignStartDate {
    return Intl.message(
      'Campaign Start Date',
      name: 'campaignStartDate',
      desc: '',
      args: [],
    );
  }

  /// `Cancel`
  String get cancel {
    return Intl.message(
      'Cancel',
      name: 'cancel',
      desc: '',
      args: [],
    );
  }

  /// `Capture`
  String get capture {
    return Intl.message(
      'Capture',
      name: 'capture',
      desc: '',
      args: [],
    );
  }

  /// `Chirpstack IP`
  String get chirpIP {
    return Intl.message(
      'Chirpstack IP',
      name: 'chirpIP',
      desc: '',
      args: [],
    );
  }

  /// `Configuration`
  String get configuration {
    return Intl.message(
      'Configuration',
      name: 'configuration',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to provision selected devices?`
  String get confirmProvision {
    return Intl.message(
      'Are you sure you want to provision selected devices?',
      name: 'confirmProvision',
      desc: '',
      args: [],
    );
  }

  /// `Configuration :`
  String get configurationValue {
    return Intl.message(
      'Configuration :',
      name: 'configurationValue',
      desc: '',
      args: [],
    );
  }

  /// `Create Site`
  String get createSite {
    return Intl.message(
      'Create Site',
      name: 'createSite',
      desc: '',
      args: [],
    );
  }

  /// `Current FW Version`
  String get currentFWVersion {
    return Intl.message(
      'Current FW Version',
      name: 'currentFWVersion',
      desc: '',
      args: [],
    );
  }

  /// `Firmware Files`
  String get firmwareFiles {
    return Intl.message(
      'Firmware Files',
      name: 'firmwareFiles',
      desc: '',
      args: [],
    );
  }

  /// `Current Version`
  String get currentVersion {
    return Intl.message(
      'Current Version',
      name: 'currentVersion',
      desc: '',
      args: [],
    );
  }

  /// `Custom`
  String get custom {
    return Intl.message(
      'Custom',
      name: 'custom',
      desc: '',
      args: [],
    );
  }

  /// `dB`
  String get dB {
    return Intl.message(
      'dB',
      name: 'dB',
      desc: '',
      args: [],
    );
  }

  /// `8`
  String get dc8v {
    return Intl.message(
      '8',
      name: 'dc8v',
      desc: '',
      args: [],
    );
  }

  /// `DS ATTN`
  String get dSAttn {
    return Intl.message(
      'DS ATTN',
      name: 'dSAttn',
      desc: '',
      args: [],
    );
  }

  /// `Auto Config`
  String get autoConfig {
    return Intl.message(
      'Auto Config',
      name: 'autoConfig',
      desc: '',
      args: [],
    );
  }

  /// `DS EQ`
  String get dSEQ {
    return Intl.message(
      'DS EQ',
      name: 'dSEQ',
      desc: '',
      args: [],
    );
  }

  /// `DS Gain Adjust`
  String get dsGainAdjust {
    return Intl.message(
      'DS Gain Adjust',
      name: 'dsGainAdjust',
      desc: '',
      args: [],
    );
  }

  /// `DS Slope Adjust`
  String get dsSlopeAdjust {
    return Intl.message(
      'DS Slope Adjust',
      name: 'dsSlopeAdjust',
      desc: '',
      args: [],
    );
  }

  /// `Dashboard`
  String get dashBoard {
    return Intl.message(
      'Dashboard',
      name: 'dashBoard',
      desc: '',
      args: [],
    );
  }

  /// `Dashboard`
  String get dashboard {
    return Intl.message(
      'Dashboard',
      name: 'dashboard',
      desc: '',
      args: [],
    );
  }

  /// `Date/ Time`
  String get dateTime {
    return Intl.message(
      'Date/ Time',
      name: 'dateTime',
      desc: '',
      args: [],
    );
  }

  /// `dBMV`
  String get dbMV {
    return Intl.message(
      'dBMV',
      name: 'dbMV',
      desc: '',
      args: [],
    );
  }

  /// `Delete`
  String get deleteBtn {
    return Intl.message(
      'Delete',
      name: 'deleteBtn',
      desc: '',
      args: [],
    );
  }

  /// `Deployment Summary`
  String get deploymentSummary {
    return Intl.message(
      'Deployment Summary',
      name: 'deploymentSummary',
      desc: '',
      args: [],
    );
  }

  /// `Site Deleted.`
  String get deleteSiteSuccess {
    return Intl.message(
      'Site Deleted.',
      name: 'deleteSiteSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Description`
  String get description {
    return Intl.message(
      'Description',
      name: 'description',
      desc: '',
      args: [],
    );
  }

  /// `Destination IP Address`
  String get destinationIP {
    return Intl.message(
      'Destination IP Address',
      name: 'destinationIP',
      desc: '',
      args: [],
    );
  }

  /// `Details`
  String get details {
    return Intl.message(
      'Details',
      name: 'details',
      desc: '',
      args: [],
    );
  }

  /// `Detected Amplifier in the Network`
  String get detectedAmplifierInNetWork {
    return Intl.message(
      'Detected Amplifier in the Network',
      name: 'detectedAmplifierInNetWork',
      desc: '',
      args: [],
    );
  }

  /// `DevAddr`
  String get devAddress {
    return Intl.message(
      'DevAddr',
      name: 'devAddress',
      desc: '',
      args: [],
    );
  }

  /// `DevEUI`
  String get devEUI {
    return Intl.message(
      'DevEUI',
      name: 'devEUI',
      desc: '',
      args: [],
    );
  }

  /// `Device Alias`
  String get deviceAlias {
    return Intl.message(
      'Device Alias',
      name: 'deviceAlias',
      desc: '',
      args: [],
    );
  }

  /// `Device EUI`
  String get deviceEUI {
    return Intl.message(
      'Device EUI',
      name: 'deviceEUI',
      desc: '',
      args: [],
    );
  }

  /// `Device EUI`
  String get deviceEUIHint {
    return Intl.message(
      'Device EUI',
      name: 'deviceEUIHint',
      desc: '',
      args: [],
    );
  }

  /// `Device Info`
  String get deviceInfo {
    return Intl.message(
      'Device Info',
      name: 'deviceInfo',
      desc: '',
      args: [],
    );
  }

  /// `Device Status`
  String get deviceStatus {
    return Intl.message(
      'Device Status',
      name: 'deviceStatus',
      desc: '',
      args: [],
    );
  }

  /// `Device Type`
  String get deviceType {
    return Intl.message(
      'Device Type',
      name: 'deviceType',
      desc: '',
      args: [],
    );
  }

  /// `Devices`
  String get devices {
    return Intl.message(
      'Devices',
      name: 'devices',
      desc: '',
      args: [],
    );
  }

  /// `Diagnostics`
  String get diagnostics {
    return Intl.message(
      'Diagnostics',
      name: 'diagnostics',
      desc: '',
      args: [],
    );
  }

  /// `Diplex Filter`
  String get diplexFilter {
    return Intl.message(
      'Diplex Filter',
      name: 'diplexFilter',
      desc: '',
      args: [],
    );
  }

  /// `Discovered`
  String get discovered {
    return Intl.message(
      'Discovered',
      name: 'discovered',
      desc: '',
      args: [],
    );
  }

  /// `Discovered but not Provisioned`
  String get discoveredButNotProvisioned {
    return Intl.message(
      'Discovered but not Provisioned',
      name: 'discoveredButNotProvisioned',
      desc: '',
      args: [],
    );
  }

  /// `Dongle connected`
  String get dongleConnected {
    return Intl.message(
      'Dongle connected',
      name: 'dongleConnected',
      desc: '',
      args: [],
    );
  }

  /// `Download`
  String get download {
    return Intl.message(
      'Download',
      name: 'download',
      desc: '',
      args: [],
    );
  }

  /// `Download Status`
  String get downloadStatus {
    return Intl.message(
      'Download Status',
      name: 'downloadStatus',
      desc: '',
      args: [],
    );
  }

  /// `Downstream`
  String get downstream {
    return Intl.message(
      'Downstream',
      name: 'downstream',
      desc: '',
      args: [],
    );
  }

  /// `Downstream Amps`
  String get downstreamAmps {
    return Intl.message(
      'Downstream Amps',
      name: 'downstreamAmps',
      desc: '',
      args: [],
    );
  }

  /// `#DS Amps`
  String get dsAMPS {
    return Intl.message(
      '#DS Amps',
      name: 'dsAMPS',
      desc: '',
      args: [],
    );
  }

  /// `DS Alignment`
  String get dsAlignCfg {
    return Intl.message(
      'DS Alignment',
      name: 'dsAlignCfg',
      desc: '',
      args: [],
    );
  }

  /// `D/S Alignment`
  String get dsAlignment {
    return Intl.message(
      'D/S Alignment',
      name: 'dsAlignment',
      desc: '',
      args: [],
    );
  }

  /// `Downstream Amplifier`
  String get amplifierDownstream {
    return Intl.message(
      'Downstream Amplifier',
      name: 'amplifierDownstream',
      desc: '',
      args: [],
    );
  }

  /// `Amplifier Ingress`
  String get amplifierIngress {
    return Intl.message(
      'Amplifier Ingress',
      name: 'amplifierIngress',
      desc: '',
      args: [],
    );
  }

  /// `DS alignment completed successfully`
  String get dsAlignmentCompleted {
    return Intl.message(
      'DS alignment completed successfully',
      name: 'dsAlignmentCompleted',
      desc: '',
      args: [],
    );
  }

  /// `DS alignment submitted successfully`
  String get setDsAlignmentCompleted {
    return Intl.message(
      'DS alignment submitted successfully',
      name: 'setDsAlignmentCompleted',
      desc: '',
      args: [],
    );
  }

  /// `DS alignment write failed`
  String get setDsAlignmentFailed {
    return Intl.message(
      'DS alignment write failed',
      name: 'setDsAlignmentFailed',
      desc: '',
      args: [],
    );
  }

  /// `DS alignment saved successfully`
  String get saveDsAlignmentCompleted {
    return Intl.message(
      'DS alignment saved successfully',
      name: 'saveDsAlignmentCompleted',
      desc: '',
      args: [],
    );
  }

  /// `DS alignment reverted successfully`
  String get revertDsAlignmentCompleted {
    return Intl.message(
      'DS alignment reverted successfully',
      name: 'revertDsAlignmentCompleted',
      desc: '',
      args: [],
    );
  }

  /// `DS alignment save failed`
  String get saveDsAlignmentFailed {
    return Intl.message(
      'DS alignment save failed',
      name: 'saveDsAlignmentFailed',
      desc: '',
      args: [],
    );
  }

  /// `DS alignment revert failed`
  String get revertDsAlignmentFailed {
    return Intl.message(
      'DS alignment revert failed',
      name: 'revertDsAlignmentFailed',
      desc: '',
      args: [],
    );
  }

  /// `US alignment completed successfully.`
  String get usAlignmentCompleted {
    return Intl.message(
      'US alignment completed successfully.',
      name: 'usAlignmentCompleted',
      desc: '',
      args: [],
    );
  }

  /// `US alignment submitted successfully`
  String get setUsAlignmentCompleted {
    return Intl.message(
      'US alignment submitted successfully',
      name: 'setUsAlignmentCompleted',
      desc: '',
      args: [],
    );
  }

  /// `US alignment write failed`
  String get setUsAlignmentFailed {
    return Intl.message(
      'US alignment write failed',
      name: 'setUsAlignmentFailed',
      desc: '',
      args: [],
    );
  }

  /// `US alignment saved successfully`
  String get saveUsAlignmentCompleted {
    return Intl.message(
      'US alignment saved successfully',
      name: 'saveUsAlignmentCompleted',
      desc: '',
      args: [],
    );
  }

  /// `US alignment reverted successfully`
  String get revertUsAlignmentCompleted {
    return Intl.message(
      'US alignment reverted successfully',
      name: 'revertUsAlignmentCompleted',
      desc: '',
      args: [],
    );
  }

  /// `US alignment save failed`
  String get saveUsAlignmentFailed {
    return Intl.message(
      'US alignment save failed',
      name: 'saveUsAlignmentFailed',
      desc: '',
      args: [],
    );
  }

  /// `US alignment revert failed`
  String get revertUsAlignmentFailed {
    return Intl.message(
      'US alignment revert failed',
      name: 'revertUsAlignmentFailed',
      desc: '',
      args: [],
    );
  }

  /// `Amplifier ingress updated successfully`
  String get ampIngressUpdateSuccess {
    return Intl.message(
      'Amplifier ingress updated successfully',
      name: 'ampIngressUpdateSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Amplifier ingress updated failed`
  String get ampIngressUpdateFailed {
    return Intl.message(
      'Amplifier ingress updated failed',
      name: 'ampIngressUpdateFailed',
      desc: '',
      args: [],
    );
  }

  /// `Amplifier test point config updated successfully`
  String get ampTestPointConfigUpdateSuccess {
    return Intl.message(
      'Amplifier test point config updated successfully',
      name: 'ampTestPointConfigUpdateSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Amplifier test point config updated failed`
  String get ampTestPointConfigUpdateFailed {
    return Intl.message(
      'Amplifier test point config updated failed',
      name: 'ampTestPointConfigUpdateFailed',
      desc: '',
      args: [],
    );
  }

  /// `DS alignment failed.`
  String get dsAlignmentFailed {
    return Intl.message(
      'DS alignment failed.',
      name: 'dsAlignmentFailed',
      desc: '',
      args: [],
    );
  }

  /// `Edit`
  String get edit {
    return Intl.message(
      'Edit',
      name: 'edit',
      desc: '',
      args: [],
    );
  }

  /// `Edit`
  String get editItemBtn {
    return Intl.message(
      'Edit',
      name: 'editItemBtn',
      desc: '',
      args: [],
    );
  }

  /// `Email`
  String get email {
    return Intl.message(
      'Email',
      name: 'email',
      desc: '',
      args: [],
    );
  }

  /// `Password`
  String get password {
    return Intl.message(
      'Password',
      name: 'password',
      desc: '',
      args: [],
    );
  }

  /// `Empowering Connectivity`
  String get empoweringConnectivity {
    return Intl.message(
      'Empowering Connectivity',
      name: 'empoweringConnectivity',
      desc: '',
      args: [],
    );
  }

  /// `Enclosure Status`
  String get enclosureStatus {
    return Intl.message(
      'Enclosure Status',
      name: 'enclosureStatus',
      desc: '',
      args: [],
    );
  }

  /// `End Freq:`
  String get end {
    return Intl.message(
      'End Freq:',
      name: 'end',
      desc: '',
      args: [],
    );
  }

  /// `End Date`
  String get endDate {
    return Intl.message(
      'End Date',
      name: 'endDate',
      desc: '',
      args: [],
    );
  }

  /// `End Time`
  String get endTime {
    return Intl.message(
      'End Time',
      name: 'endTime',
      desc: '',
      args: [],
    );
  }

  /// `Enter email`
  String get enterEmail {
    return Intl.message(
      'Enter email',
      name: 'enterEmail',
      desc: '',
      args: [],
    );
  }

  /// `EQ-`
  String get equ {
    return Intl.message(
      'EQ-',
      name: 'equ',
      desc: '',
      args: [],
    );
  }

  /// `Export Telemetry`
  String get exportTelemetry {
    return Intl.message(
      'Export Telemetry',
      name: 'exportTelemetry',
      desc: '',
      args: [],
    );
  }

  /// `Export`
  String get export {
    return Intl.message(
      'Export',
      name: 'export',
      desc: '',
      args: [],
    );
  }

  /// `Export CSV File Error`
  String get exportCSVError {
    return Intl.message(
      'Export CSV File Error',
      name: 'exportCSVError',
      desc: '',
      args: [],
    );
  }

  /// `Export CSV File Successfully`
  String get exportCSVSuccess {
    return Intl.message(
      'Export CSV File Successfully',
      name: 'exportCSVSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Please select date range`
  String get exportRangeError {
    return Intl.message(
      'Please select date range',
      name: 'exportRangeError',
      desc: '',
      args: [],
    );
  }

  /// `FW Build Date`
  String get fWBuildDate {
    return Intl.message(
      'FW Build Date',
      name: 'fWBuildDate',
      desc: '',
      args: [],
    );
  }

  /// `FWRev`
  String get fWRev {
    return Intl.message(
      'FWRev',
      name: 'fWRev',
      desc: '',
      args: [],
    );
  }

  /// `API Version`
  String get apiVersion {
    return Intl.message(
      'API Version',
      name: 'apiVersion',
      desc: '',
      args: [],
    );
  }

  /// `FW Version`
  String get fWVersion {
    return Intl.message(
      'FW Version',
      name: 'fWVersion',
      desc: '',
      args: [],
    );
  }

  /// `HW Version`
  String get hWVersion {
    return Intl.message(
      'HW Version',
      name: 'hWVersion',
      desc: '',
      args: [],
    );
  }

  /// `Fetching Spectrum Levels..`
  String get fetchSpectrum {
    return Intl.message(
      'Fetching Spectrum Levels..',
      name: 'fetchSpectrum',
      desc: '',
      args: [],
    );
  }

  /// `File Name`
  String get fileName {
    return Intl.message(
      'File Name',
      name: 'fileName',
      desc: '',
      args: [],
    );
  }

  /// `File Type (descriptor)`
  String get fileTypeDescriptor {
    return Intl.message(
      'File Type (descriptor)',
      name: 'fileTypeDescriptor',
      desc: '',
      args: [],
    );
  }

  /// `File Upload`
  String get fileUpload {
    return Intl.message(
      'File Upload',
      name: 'fileUpload',
      desc: '',
      args: [],
    );
  }

  /// `Filters`
  String get filters {
    return Intl.message(
      'Filters',
      name: 'filters',
      desc: '',
      args: [],
    );
  }

  /// `Firmware Download Config`
  String get firmwareDownloadConfig {
    return Intl.message(
      'Firmware Download Config',
      name: 'firmwareDownloadConfig',
      desc: '',
      args: [],
    );
  }

  /// `Firmware Upgrade`
  String get firmwareDownload {
    return Intl.message(
      'Firmware Upgrade',
      name: 'firmwareDownload',
      desc: '',
      args: [],
    );
  }

  /// `Firmware Version`
  String get firmwareVersion {
    return Intl.message(
      'Firmware Version',
      name: 'firmwareVersion',
      desc: '',
      args: [],
    );
  }

  /// `5.5`
  String get fiveV {
    return Intl.message(
      '5.5',
      name: 'fiveV',
      desc: '',
      args: [],
    );
  }

  /// `5v DC`
  String get fiveVDC {
    return Intl.message(
      '5v DC',
      name: 'fiveVDC',
      desc: '',
      args: [],
    );
  }

  /// `40`
  String get forty {
    return Intl.message(
      '40',
      name: 'forty',
      desc: '',
      args: [],
    );
  }

  /// `Fwd`
  String get forward {
    return Intl.message(
      'Fwd',
      name: 'forward',
      desc: '',
      args: [],
    );
  }

  /// `Fragment Count`
  String get fragmentCount {
    return Intl.message(
      'Fragment Count',
      name: 'fragmentCount',
      desc: '',
      args: [],
    );
  }

  /// `Fragment Rate`
  String get fragmentRate {
    return Intl.message(
      'Fragment Rate',
      name: 'fragmentRate',
      desc: '',
      args: [],
    );
  }

  /// `From Date Time`
  String get fromDateTime {
    return Intl.message(
      'From Date Time',
      name: 'fromDateTime',
      desc: '',
      args: [],
    );
  }

  /// `Fragment Session Index`
  String get fragmentSessionIndex {
    return Intl.message(
      'Fragment Session Index',
      name: 'fragmentSessionIndex',
      desc: '',
      args: [],
    );
  }

  /// `Fragment Session`
  String get fragmentSessionSetup {
    return Intl.message(
      'Fragment Session',
      name: 'fragmentSessionSetup',
      desc: '',
      args: [],
    );
  }

  /// `Fragment Status`
  String get fragmentStatusCompleted {
    return Intl.message(
      'Fragment Status',
      name: 'fragmentStatusCompleted',
      desc: '',
      args: [],
    );
  }

  /// `Fragment Size`
  String get fragmentSize {
    return Intl.message(
      'Fragment Size',
      name: 'fragmentSize',
      desc: '',
      args: [],
    );
  }

  /// `Frequency (MHz)`
  String get frequency {
    return Intl.message(
      'Frequency (MHz)',
      name: 'frequency',
      desc: '',
      args: [],
    );
  }

  /// `dBmV`
  String get dBmV {
    return Intl.message(
      'dBmV',
      name: 'dBmV',
      desc: '',
      args: [],
    );
  }

  /// `FWD Input Test Point`
  String get fwdInput {
    return Intl.message(
      'FWD Input Test Point',
      name: 'fwdInput',
      desc: '',
      args: [],
    );
  }

  /// `FWD Output Test Point`
  String get fwdOutput {
    return Intl.message(
      'FWD Output Test Point',
      name: 'fwdOutput',
      desc: '',
      args: [],
    );
  }

  /// `Gain`
  String get gain {
    return Intl.message(
      'Gain',
      name: 'gain',
      desc: '',
      args: [],
    );
  }

  /// `Gateway ID`
  String get gatWayId {
    return Intl.message(
      'Gateway ID',
      name: 'gatWayId',
      desc: '',
      args: [],
    );
  }

  /// `GHz`
  String get ghZ {
    return Intl.message(
      'GHz',
      name: 'ghZ',
      desc: '',
      args: [],
    );
  }

  /// `GW EUI`
  String get gwEUI {
    return Intl.message(
      'GW EUI',
      name: 'gwEUI',
      desc: '',
      args: [],
    );
  }

  /// `High`
  String get high {
    return Intl.message(
      'High',
      name: 'high',
      desc: '',
      args: [],
    );
  }

  /// `HWRev`
  String get hWRev {
    return Intl.message(
      'HWRev',
      name: 'hWRev',
      desc: '',
      args: [],
    );
  }

  /// `High Split`
  String get highSplit {
    return Intl.message(
      'High Split',
      name: 'highSplit',
      desc: '',
      args: [],
    );
  }

  /// `Home`
  String get home {
    return Intl.message(
      'Home',
      name: 'home',
      desc: '',
      args: [],
    );
  }

  /// `Provisioned Amplifiers`
  String get homeProvisionedAmplifiers {
    return Intl.message(
      'Provisioned Amplifiers',
      name: 'homeProvisionedAmplifiers',
      desc: '',
      args: [],
    );
  }

  /// `Provision Amplifiers`
  String get homeProvisioning {
    return Intl.message(
      'Provision Amplifiers',
      name: 'homeProvisioning',
      desc: '',
      args: [],
    );
  }

  /// `Settings`
  String get homeSettings {
    return Intl.message(
      'Settings',
      name: 'homeSettings',
      desc: '',
      args: [],
    );
  }

  /// `Sites / Regions`
  String get homeSiteRegions {
    return Intl.message(
      'Sites / Regions',
      name: 'homeSiteRegions',
      desc: '',
      args: [],
    );
  }

  /// `Identification`
  String get identification {
    return Intl.message(
      'Identification',
      name: 'identification',
      desc: '',
      args: [],
    );
  }

  /// `Info`
  String get info {
    return Intl.message(
      'Info',
      name: 'info',
      desc: '',
      args: [],
    );
  }

  /// `Ingress Switch`
  String get ingressSwitch {
    return Intl.message(
      'Ingress Switch',
      name: 'ingressSwitch',
      desc: '',
      args: [],
    );
  }

  /// `Spectrum initiated, please wait`
  String get initiatedSpectrum {
    return Intl.message(
      'Spectrum initiated, please wait',
      name: 'initiatedSpectrum',
      desc: '',
      args: [],
    );
  }

  /// `Input Stage`
  String get inputStage {
    return Intl.message(
      'Input Stage',
      name: 'inputStage',
      desc: '',
      args: [],
    );
  }

  /// `Interface`
  String get interface {
    return Intl.message(
      'Interface',
      name: 'interface',
      desc: '',
      args: [],
    );
  }

  /// `Intermediate Stage`
  String get intermediateStage {
    return Intl.message(
      'Intermediate Stage',
      name: 'intermediateStage',
      desc: '',
      args: [],
    );
  }

  /// `Join Server`
  String get joinServer {
    return Intl.message(
      'Join Server',
      name: 'joinServer',
      desc: '',
      args: [],
    );
  }

  /// `Keys Synced`
  String get keysSynced {
    return Intl.message(
      'Keys Synced',
      name: 'keysSynced',
      desc: '',
      args: [],
    );
  }

  /// `LE`
  String get lE {
    return Intl.message(
      'LE',
      name: 'lE',
      desc: '',
      args: [],
    );
  }

  /// `Last Seen`
  String get lastSeen {
    return Intl.message(
      'Last Seen',
      name: 'lastSeen',
      desc: '',
      args: [],
    );
  }

  /// `Last Week (Sun - Sat)`
  String get lastWeek {
    return Intl.message(
      'Last Week (Sun - Sat)',
      name: 'lastWeek',
      desc: '',
      args: [],
    );
  }

  /// `Level`
  String get level {
    return Intl.message(
      'Level',
      name: 'level',
      desc: '',
      args: [],
    );
  }

  /// `Lid is close`
  String get lidIsClose {
    return Intl.message(
      'Lid is close',
      name: 'lidIsClose',
      desc: '',
      args: [],
    );
  }

  /// `Lid is open`
  String get lidIsOpen {
    return Intl.message(
      'Lid is open',
      name: 'lidIsOpen',
      desc: '',
      args: [],
    );
  }

  /// `Lid open`
  String get lidOpen {
    return Intl.message(
      'Lid open',
      name: 'lidOpen',
      desc: '',
      args: [],
    );
  }

  /// `Lid Status`
  String get lidStatus {
    return Intl.message(
      'Lid Status',
      name: 'lidStatus',
      desc: '',
      args: [],
    );
  }

  /// `List`
  String get list {
    return Intl.message(
      'List',
      name: 'list',
      desc: '',
      args: [],
    );
  }

  /// `List View`
  String get listView {
    return Intl.message(
      'List View',
      name: 'listView',
      desc: '',
      args: [],
    );
  }

  /// `Loading...`
  String get loading {
    return Intl.message(
      'Loading...',
      name: 'loading',
      desc: '',
      args: [],
    );
  }

  /// `Location`
  String get location {
    return Intl.message(
      'Location',
      name: 'location',
      desc: '',
      args: [],
    );
  }

  /// `Location\n(Lat, Long)`
  String get locationLatLong {
    return Intl.message(
      'Location\n(Lat, Long)',
      name: 'locationLatLong',
      desc: '',
      args: [],
    );
  }

  /// `Location not set`
  String get locationNotSet {
    return Intl.message(
      'Location not set',
      name: 'locationNotSet',
      desc: '',
      args: [],
    );
  }

  /// `Enter valid location`
  String get locationValidation {
    return Intl.message(
      'Enter valid location',
      name: 'locationValidation',
      desc: '',
      args: [],
    );
  }

  /// `Low`
  String get low {
    return Intl.message(
      'Low',
      name: 'low',
      desc: '',
      args: [],
    );
  }

  /// `Login`
  String get login {
    return Intl.message(
      'Login',
      name: 'login',
      desc: '',
      args: [],
    );
  }

  /// `Logout`
  String get logout {
    return Intl.message(
      'Logout',
      name: 'logout',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure that you want to logout?`
  String get logoutConfirmation {
    return Intl.message(
      'Are you sure that you want to logout?',
      name: 'logoutConfirmation',
      desc: '',
      args: [],
    );
  }

  /// `Low Split`
  String get lowSplit {
    return Intl.message(
      'Low Split',
      name: 'lowSplit',
      desc: '',
      args: [],
    );
  }

  /// `Manual`
  String get manual {
    return Intl.message(
      'Manual',
      name: 'manual',
      desc: '',
      args: [],
    );
  }

  /// `Manual Alignment`
  String get manualAlignment {
    return Intl.message(
      'Manual Alignment',
      name: 'manualAlignment',
      desc: '',
      args: [],
    );
  }

  /// `Map`
  String get map {
    return Intl.message(
      'Map',
      name: 'map',
      desc: '',
      args: [],
    );
  }

  /// `Map View`
  String get mapView {
    return Intl.message(
      'Map View',
      name: 'mapView',
      desc: '',
      args: [],
    );
  }

  /// `Message`
  String get message {
    return Intl.message(
      'Message',
      name: 'message',
      desc: '',
      args: [],
    );
  }

  /// `Enabling the ingress may cause service disruption. Are you sure that you want to perform this operation?`
  String get messageIngressEnable {
    return Intl.message(
      'Enabling the ingress may cause service disruption. Are you sure that you want to perform this operation?',
      name: 'messageIngressEnable',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure, you want to change the ingress switch settings?`
  String get messageIngressDisable {
    return Intl.message(
      'Are you sure, you want to change the ingress switch settings?',
      name: 'messageIngressDisable',
      desc: '',
      args: [],
    );
  }

  /// `Mfg Date`
  String get mfgDate {
    return Intl.message(
      'Mfg Date',
      name: 'mfgDate',
      desc: '',
      args: [],
    );
  }

  /// `MHz`
  String get mhZ {
    return Intl.message(
      'MHz',
      name: 'mhZ',
      desc: '',
      args: [],
    );
  }

  /// `MHz`
  String get mhz {
    return Intl.message(
      'MHz',
      name: 'mhz',
      desc: '',
      args: [],
    );
  }

  /// `Pending site info`
  String get missingSiteInfo {
    return Intl.message(
      'Pending site info',
      name: 'missingSiteInfo',
      desc: '',
      args: [],
    );
  }

  /// `Model`
  String get model {
    return Intl.message(
      'Model',
      name: 'model',
      desc: '',
      args: [],
    );
  }

  /// `Model Numbers`
  String get modelNumber {
    return Intl.message(
      'Model Numbers',
      name: 'modelNumber',
      desc: '',
      args: [],
    );
  }

  /// `Multicast Group Address`
  String get multicastGroupAddress {
    return Intl.message(
      'Multicast Group Address',
      name: 'multicastGroupAddress',
      desc: '',
      args: [],
    );
  }

  /// `Multicast ID`
  String get multicastID {
    return Intl.message(
      'Multicast ID',
      name: 'multicastID',
      desc: '',
      args: [],
    );
  }

  /// `Multicast IP`
  String get multicastIP {
    return Intl.message(
      'Multicast IP',
      name: 'multicastIP',
      desc: '',
      args: [],
    );
  }

  /// `Multicast IP Address`
  String get multicastIPAddress {
    return Intl.message(
      'Multicast IP Address',
      name: 'multicastIPAddress',
      desc: '',
      args: [],
    );
  }

  /// `Multicast On`
  String get multicastOn {
    return Intl.message(
      'Multicast On',
      name: 'multicastOn',
      desc: '',
      args: [],
    );
  }

  /// `Multicast Session`
  String get multicastSession {
    return Intl.message(
      'Multicast Session',
      name: 'multicastSession',
      desc: '',
      args: [],
    );
  }

  /// `Confirm ?`
  String get msgAskConfirmationTitle {
    return Intl.message(
      'Confirm ?',
      name: 'msgAskConfirmationTitle',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to continue?`
  String get msgAskConfirmationDesc {
    return Intl.message(
      'Are you sure you want to continue?',
      name: 'msgAskConfirmationDesc',
      desc: '',
      args: [],
    );
  }

  /// `NDR Session`
  String get nDRSession {
    return Intl.message(
      'NDR Session',
      name: 'nDRSession',
      desc: '',
      args: [],
    );
  }

  /// `Name`
  String get name {
    return Intl.message(
      'Name',
      name: 'name',
      desc: '',
      args: [],
    );
  }

  /// `NDF \nMulticast IP`
  String get ndfMulticastIp {
    return Intl.message(
      'NDF \nMulticast IP',
      name: 'ndfMulticastIp',
      desc: '',
      args: [],
    );
  }

  /// `NDF Session`
  String get ndfSession {
    return Intl.message(
      'NDF Session',
      name: 'ndfSession',
      desc: '',
      args: [],
    );
  }

  /// `NDF\nSession ID`
  String get ndfSessionId {
    return Intl.message(
      'NDF\nSession ID',
      name: 'ndfSessionId',
      desc: '',
      args: [],
    );
  }

  /// `NDR \nSession ID`
  String get ndrSessionId {
    return Intl.message(
      'NDR \nSession ID',
      name: 'ndrSessionId',
      desc: '',
      args: [],
    );
  }

  /// `Network \nServer`
  String get networkServer {
    return Intl.message(
      'Network \nServer',
      name: 'networkServer',
      desc: '',
      args: [],
    );
  }

  /// `Network Server IP`
  String get networkServerIP {
    return Intl.message(
      'Network Server IP',
      name: 'networkServerIP',
      desc: '',
      args: [],
    );
  }

  /// `Network Server Port`
  String get networkServerPort {
    return Intl.message(
      'Network Server Port',
      name: 'networkServerPort',
      desc: '',
      args: [],
    );
  }

  /// `Never`
  String get never {
    return Intl.message(
      'Never',
      name: 'never',
      desc: '',
      args: [],
    );
  }

  /// `New Amplifier Details`
  String get newAmpDetails {
    return Intl.message(
      'New Amplifier Details',
      name: 'newAmpDetails',
      desc: '',
      args: [],
    );
  }

  /// `No`
  String get no {
    return Intl.message(
      'No',
      name: 'no',
      desc: '',
      args: [],
    );
  }

  /// `No Alarms`
  String get noAlarms {
    return Intl.message(
      'No Alarms',
      name: 'noAlarms',
      desc: '',
      args: [],
    );
  }

  /// `No Data Found`
  String get noDataFound {
    return Intl.message(
      'No Data Found',
      name: 'noDataFound',
      desc: '',
      args: [],
    );
  }

  /// `Node GW`
  String get nodeGW {
    return Intl.message(
      'Node GW',
      name: 'nodeGW',
      desc: '',
      args: [],
    );
  }

  /// `Node GW/Dashboard Connection`
  String get nodeGWDongleConnection {
    return Intl.message(
      'Node GW/Dashboard Connection',
      name: 'nodeGWDongleConnection',
      desc: '',
      args: [],
    );
  }

  /// `Node GW`
  String get nodeGw {
    return Intl.message(
      'Node GW',
      name: 'nodeGw',
      desc: '',
      args: [],
    );
  }

  /// `Offline`
  String get offline {
    return Intl.message(
      'Offline',
      name: 'offline',
      desc: '',
      args: [],
    );
  }

  /// `On`
  String get on {
    return Intl.message(
      'On',
      name: 'on',
      desc: '',
      args: [],
    );
  }

  /// `Ok`
  String get ok {
    return Intl.message(
      'Ok',
      name: 'ok',
      desc: '',
      args: [],
    );
  }

  /// `Online`
  String get online {
    return Intl.message(
      'Online',
      name: 'online',
      desc: '',
      args: [],
    );
  }

  /// `Open`
  String get open {
    return Intl.message(
      'Open',
      name: 'open',
      desc: '',
      args: [],
    );
  }

  /// `Output Stage`
  String get outputStage {
    return Intl.message(
      'Output Stage',
      name: 'outputStage',
      desc: '',
      args: [],
    );
  }

  /// `Paving the Way for Next-Generation Network Innovation`
  String get pavingTheWayTitle {
    return Intl.message(
      'Paving the Way for Next-Generation Network Innovation',
      name: 'pavingTheWayTitle',
      desc: '',
      args: [],
    );
  }

  /// `Pending`
  String get pending {
    return Intl.message(
      'Pending',
      name: 'pending',
      desc: '',
      args: [],
    );
  }

  /// `Pilot`
  String get pilot {
    return Intl.message(
      'Pilot',
      name: 'pilot',
      desc: '',
      args: [],
    );
  }

  /// `Pilot 1:`
  String get pilot1 {
    return Intl.message(
      'Pilot 1:',
      name: 'pilot1',
      desc: '',
      args: [],
    );
  }

  /// `Pilot 2:`
  String get pilot2 {
    return Intl.message(
      'Pilot 2:',
      name: 'pilot2',
      desc: '',
      args: [],
    );
  }

  /// `Pilot 3:`
  String get pilot3 {
    return Intl.message(
      'Pilot 3:',
      name: 'pilot3',
      desc: '',
      args: [],
    );
  }

  /// `Placement`
  String get placement {
    return Intl.message(
      'Placement',
      name: 'placement',
      desc: '',
      args: [],
    );
  }

  /// `Point Config`
  String get pointConfig {
    return Intl.message(
      'Point Config',
      name: 'pointConfig',
      desc: '',
      args: [],
    );
  }

  /// `Power :`
  String get power {
    return Intl.message(
      'Power :',
      name: 'power',
      desc: '',
      args: [],
    );
  }

  /// `Power Supply Information`
  String get powerSupply {
    return Intl.message(
      'Power Supply Information',
      name: 'powerSupply',
      desc: '',
      args: [],
    );
  }

  /// `Power`
  String get powerTable {
    return Intl.message(
      'Power',
      name: 'powerTable',
      desc: '',
      args: [],
    );
  }

  /// `Present`
  String get present {
    return Intl.message(
      'Present',
      name: 'present',
      desc: '',
      args: [],
    );
  }

  /// `Please wait`
  String get pleaseWait {
    return Intl.message(
      'Please wait',
      name: 'pleaseWait',
      desc: '',
      args: [],
    );
  }

  /// `Provision`
  String get provision {
    return Intl.message(
      'Provision',
      name: 'provision',
      desc: '',
      args: [],
    );
  }

  /// `Provisioned`
  String get provisioned {
    return Intl.message(
      'Provisioned',
      name: 'provisioned',
      desc: '',
      args: [],
    );
  }

  /// `Provision selected devices`
  String get provisionSelected {
    return Intl.message(
      'Provision selected devices',
      name: 'provisionSelected',
      desc: '',
      args: [],
    );
  }

  /// `QL Dashboard`
  String get qlDashboard {
    return Intl.message(
      'QL Dashboard',
      name: 'qlDashboard',
      desc: '',
      args: [],
    );
  }

  /// `Quantum Central`
  String get quantumCentral {
    return Intl.message(
      'Quantum Central',
      name: 'quantumCentral',
      desc: '',
      args: [],
    );
  }

  /// `Quick Search`
  String get quickSearch {
    return Intl.message(
      'Quick Search',
      name: 'quickSearch',
      desc: '',
      args: [],
    );
  }

  /// `RPD`
  String get rPD {
    return Intl.message(
      'RPD',
      name: 'rPD',
      desc: '',
      args: [],
    );
  }

  /// `RPD`
  String get rPD1 {
    return Intl.message(
      'RPD',
      name: 'rPD1',
      desc: '',
      args: [],
    );
  }

  /// `Ref`
  String get ref {
    return Intl.message(
      'Ref',
      name: 'ref',
      desc: '',
      args: [],
    );
  }

  /// `Refresh`
  String get refresh {
    return Intl.message(
      'Refresh',
      name: 'refresh',
      desc: '',
      args: [],
    );
  }

  /// `Refresh Reference `
  String get refreshRef {
    return Intl.message(
      'Refresh Reference ',
      name: 'refreshRef',
      desc: '',
      args: [],
    );
  }

  /// `Remove from Multicast`
  String get removeFromMulticast {
    return Intl.message(
      'Remove from Multicast',
      name: 'removeFromMulticast',
      desc: '',
      args: [],
    );
  }

  /// `Restore`
  String get restore {
    return Intl.message(
      'Restore',
      name: 'restore',
      desc: '',
      args: [],
    );
  }

  /// `Rev`
  String get reverse {
    return Intl.message(
      'Rev',
      name: 'reverse',
      desc: '',
      args: [],
    );
  }

  /// `Revert`
  String get revert {
    return Intl.message(
      'Revert',
      name: 'revert',
      desc: '',
      args: [],
    );
  }

  /// `Redis IP`
  String get redisIP {
    return Intl.message(
      'Redis IP',
      name: 'redisIP',
      desc: '',
      args: [],
    );
  }

  /// `Redundancy Percentage`
  String get redundancyPct {
    return Intl.message(
      'Redundancy Percentage',
      name: 'redundancyPct',
      desc: '',
      args: [],
    );
  }

  /// `Redis Port`
  String get redisPort {
    return Intl.message(
      'Redis Port',
      name: 'redisPort',
      desc: '',
      args: [],
    );
  }

  /// `Road Map`
  String get roadmap {
    return Intl.message(
      'Road Map',
      name: 'roadmap',
      desc: '',
      args: [],
    );
  }

  /// `#RPD`
  String get rpd {
    return Intl.message(
      '#RPD',
      name: 'rpd',
      desc: '',
      args: [],
    );
  }

  /// `RPD IP(src)`
  String get rpdIp {
    return Intl.message(
      'RPD IP(src)',
      name: 'rpdIp',
      desc: '',
      args: [],
    );
  }

  /// `SE`
  String get sE {
    return Intl.message(
      'SE',
      name: 'sE',
      desc: '',
      args: [],
    );
  }

  /// `Satellite`
  String get satellite {
    return Intl.message(
      'Satellite',
      name: 'satellite',
      desc: '',
      args: [],
    );
  }

  /// `Save`
  String get save {
    return Intl.message(
      'Save',
      name: 'save',
      desc: '',
      args: [],
    );
  }

  /// `Click Save to save the values permanently`
  String get saveRevertInfoText {
    return Intl.message(
      'Click Save to save the values permanently',
      name: 'saveRevertInfoText',
      desc: '',
      args: [],
    );
  }

  /// `Search..`
  String get search {
    return Intl.message(
      'Search..',
      name: 'search',
      desc: '',
      args: [],
    );
  }

  /// `Select`
  String get select {
    return Intl.message(
      'Select',
      name: 'select',
      desc: '',
      args: [],
    );
  }

  /// `Select ALSC`
  String get selectAgc {
    return Intl.message(
      'Select ALSC',
      name: 'selectAgc',
      desc: '',
      args: [],
    );
  }

  /// `Select Date Range`
  String get selectDateRange {
    return Intl.message(
      'Select Date Range',
      name: 'selectDateRange',
      desc: '',
      args: [],
    );
  }

  /// `Select File`
  String get selectFile {
    return Intl.message(
      'Select File',
      name: 'selectFile',
      desc: '',
      args: [],
    );
  }

  /// `Select Site`
  String get selectSite {
    return Intl.message(
      'Select Site',
      name: 'selectSite',
      desc: '',
      args: [],
    );
  }

  /// `Select Type`
  String get selectType {
    return Intl.message(
      'Select Type',
      name: 'selectType',
      desc: '',
      args: [],
    );
  }

  /// `Line Extender`
  String get selectTypeLE {
    return Intl.message(
      'Line Extender',
      name: 'selectTypeLE',
      desc: '',
      args: [],
    );
  }

  /// `System Amplifier`
  String get selectTypeSE {
    return Intl.message(
      'System Amplifier',
      name: 'selectTypeSE',
      desc: '',
      args: [],
    );
  }

  /// `Select Universal`
  String get selectUniversal {
    return Intl.message(
      'Select Universal',
      name: 'selectUniversal',
      desc: '',
      args: [],
    );
  }

  /// `Serial`
  String get serial {
    return Intl.message(
      'Serial',
      name: 'serial',
      desc: '',
      args: [],
    );
  }

  /// `Serial Number`
  String get serialNumber {
    return Intl.message(
      'Serial Number',
      name: 'serialNumber',
      desc: '',
      args: [],
    );
  }

  /// `Session ID`
  String get sessionID {
    return Intl.message(
      'Session ID',
      name: 'sessionID',
      desc: '',
      args: [],
    );
  }

  /// `Show Completed`
  String get showCompleted {
    return Intl.message(
      'Show Completed',
      name: 'showCompleted',
      desc: '',
      args: [],
    );
  }

  /// `Show Discovered`
  String get showDiscovered {
    return Intl.message(
      'Show Discovered',
      name: 'showDiscovered',
      desc: '',
      args: [],
    );
  }

  /// `SignIn`
  String get signIn {
    return Intl.message(
      'SignIn',
      name: 'signIn',
      desc: '',
      args: [],
    );
  }

  /// `Sign Out`
  String get signOut {
    return Intl.message(
      'Sign Out',
      name: 'signOut',
      desc: '',
      args: [],
    );
  }

  /// `Site`
  String get site {
    return Intl.message(
      'Site',
      name: 'site',
      desc: '',
      args: [],
    );
  }

  /// `Site ID`
  String get siteID {
    return Intl.message(
      'Site ID',
      name: 'siteID',
      desc: '',
      args: [],
    );
  }

  /// `Site Name`
  String get siteName {
    return Intl.message(
      'Site Name',
      name: 'siteName',
      desc: '',
      args: [],
    );
  }

  /// `Sites/Regions`
  String get siteRegions {
    return Intl.message(
      'Sites/Regions',
      name: 'siteRegions',
      desc: '',
      args: [],
    );
  }

  /// `Site Updated Successfully!!`
  String get siteUpdateSuccess {
    return Intl.message(
      'Site Updated Successfully!!',
      name: 'siteUpdateSuccess',
      desc: '',
      args: [],
    );
  }

  /// `#Amplifiers`
  String get sitesAmplifiers {
    return Intl.message(
      '#Amplifiers',
      name: 'sitesAmplifiers',
      desc: '',
      args: [],
    );
  }

  /// `Slope`
  String get slop {
    return Intl.message(
      'Slope',
      name: 'slop',
      desc: '',
      args: [],
    );
  }

  /// `Something went wrong!!`
  String get somethingWentWrong {
    return Intl.message(
      'Something went wrong!!',
      name: 'somethingWentWrong',
      desc: '',
      args: [],
    );
  }

  /// `Source IP Address`
  String get sourceIP {
    return Intl.message(
      'Source IP Address',
      name: 'sourceIP',
      desc: '',
      args: [],
    );
  }

  /// `Spectrum`
  String get spectrum {
    return Intl.message(
      'Spectrum',
      name: 'spectrum',
      desc: '',
      args: [],
    );
  }

  /// `Spectrum Data Fetched Successfully`
  String get spectrumSuccessMessage {
    return Intl.message(
      'Spectrum Data Fetched Successfully',
      name: 'spectrumSuccessMessage',
      desc: '',
      args: [],
    );
  }

  /// `Start Freq:`
  String get start {
    return Intl.message(
      'Start Freq:',
      name: 'start',
      desc: '',
      args: [],
    );
  }

  /// `Start Auto Alignment`
  String get startAutoAlignment {
    return Intl.message(
      'Start Auto Alignment',
      name: 'startAutoAlignment',
      desc: '',
      args: [],
    );
  }

  /// ` Start Capture`
  String get startCapture {
    return Intl.message(
      ' Start Capture',
      name: 'startCapture',
      desc: '',
      args: [],
    );
  }

  /// `Start Date`
  String get startDate {
    return Intl.message(
      'Start Date',
      name: 'startDate',
      desc: '',
      args: [],
    );
  }

  /// `Start Downstream Alignment`
  String get startDownstream {
    return Intl.message(
      'Start Downstream Alignment',
      name: 'startDownstream',
      desc: '',
      args: [],
    );
  }

  /// `Start Time`
  String get startTime {
    return Intl.message(
      'Start Time',
      name: 'startTime',
      desc: '',
      args: [],
    );
  }

  /// `Start Upstream Alignment`
  String get startUpstream {
    return Intl.message(
      'Start Upstream Alignment',
      name: 'startUpstream',
      desc: '',
      args: [],
    );
  }

  /// `End date should not be earlier than the start date.`
  String get startEndDateValidation {
    return Intl.message(
      'End date should not be earlier than the start date.',
      name: 'startEndDateValidation',
      desc: '',
      args: [],
    );
  }

  /// `State`
  String get state {
    return Intl.message(
      'State',
      name: 'state',
      desc: '',
      args: [],
    );
  }

  /// `Status`
  String get status {
    return Intl.message(
      'Status',
      name: 'status',
      desc: '',
      args: [],
    );
  }

  /// `Step-1 Freq:`
  String get step {
    return Intl.message(
      'Step-1 Freq:',
      name: 'step',
      desc: '',
      args: [],
    );
  }

  /// `SUBMIT`
  String get submit {
    return Intl.message(
      'SUBMIT',
      name: 'submit',
      desc: '',
      args: [],
    );
  }

  /// `Switch FW`
  String get switchFW {
    return Intl.message(
      'Switch FW',
      name: 'switchFW',
      desc: '',
      args: [],
    );
  }

  /// `Switch to Auto`
  String get switchToAuto {
    return Intl.message(
      'Switch to Auto',
      name: 'switchToAuto',
      desc: '',
      args: [],
    );
  }

  /// `Switch to Manual`
  String get switchToManual {
    return Intl.message(
      'Switch to Manual',
      name: 'switchToManual',
      desc: '',
      args: [],
    );
  }

  /// `Device EUI`
  String get tableDeviceEUI {
    return Intl.message(
      'Device EUI',
      name: 'tableDeviceEUI',
      desc: '',
      args: [],
    );
  }

  /// `No data`
  String get tableNoData {
    return Intl.message(
      'No data',
      name: 'tableNoData',
      desc: '',
      args: [],
    );
  }

  /// `Type`
  String get tableType {
    return Intl.message(
      'Type',
      name: 'tableType',
      desc: '',
      args: [],
    );
  }

  /// `Tag`
  String get tag {
    return Intl.message(
      'Tag',
      name: 'tag',
      desc: '',
      args: [],
    );
  }

  /// `Not tagged to a site`
  String get tagToSite {
    return Intl.message(
      'Not tagged to a site',
      name: 'tagToSite',
      desc: '',
      args: [],
    );
  }

  /// `Tags`
  String get tags {
    return Intl.message(
      'Tags',
      name: 'tags',
      desc: '',
      args: [],
    );
  }

  /// `Telemetry`
  String get telemetry {
    return Intl.message(
      'Telemetry',
      name: 'telemetry',
      desc: '',
      args: [],
    );
  }

  /// `Temp ℃`
  String get temp {
    return Intl.message(
      'Temp ℃',
      name: 'temp',
      desc: '',
      args: [],
    );
  }

  /// `Temperature (C)`
  String get temperature {
    return Intl.message(
      'Temperature (C)',
      name: 'temperature',
      desc: '',
      args: [],
    );
  }

  /// `Telemetry Thresholds`
  String get telemetryThreshold {
    return Intl.message(
      'Telemetry Thresholds',
      name: 'telemetryThreshold',
      desc: '',
      args: [],
    );
  }

  /// `Test Point Config`
  String get testPointConfig {
    return Intl.message(
      'Test Point Config',
      name: 'testPointConfig',
      desc: '',
      args: [],
    );
  }

  /// `Thermal`
  String get thermal {
    return Intl.message(
      'Thermal',
      name: 'thermal',
      desc: '',
      args: [],
    );
  }

  /// `Temperature`
  String get temperatureTitle {
    return Intl.message(
      'Temperature',
      name: 'temperatureTitle',
      desc: '',
      args: [],
    );
  }

  /// `Power Source`
  String get powerSource {
    return Intl.message(
      'Power Source',
      name: 'powerSource',
      desc: '',
      args: [],
    );
  }

  /// `Battery/Charging Status`
  String get batteryAndCharging {
    return Intl.message(
      'Battery/Charging Status',
      name: 'batteryAndCharging',
      desc: '',
      args: [],
    );
  }

  /// `Charging Status`
  String get chargingStatus {
    return Intl.message(
      'Charging Status',
      name: 'chargingStatus',
      desc: '',
      args: [],
    );
  }

  /// `Threshold Value Updated Successfully!!`
  String get thresholdSuccess {
    return Intl.message(
      'Threshold Value Updated Successfully!!',
      name: 'thresholdSuccess',
      desc: '',
      args: [],
    );
  }

  /// `This field is required`
  String get thisFieldIsRequired {
    return Intl.message(
      'This field is required',
      name: 'thisFieldIsRequired',
      desc: '',
      args: [],
    );
  }

  /// `This Week (Sun - Today)`
  String get thisWeek {
    return Intl.message(
      'This Week (Sun - Today)',
      name: 'thisWeek',
      desc: '',
      args: [],
    );
  }

  /// `3.3`
  String get threePointThreeV {
    return Intl.message(
      '3.3',
      name: 'threePointThreeV',
      desc: '',
      args: [],
    );
  }

  /// `3.3v DC`
  String get threeVDc {
    return Intl.message(
      '3.3v DC',
      name: 'threeVDc',
      desc: '',
      args: [],
    );
  }

  /// `Timestamp`
  String get timestamp {
    return Intl.message(
      'Timestamp',
      name: 'timestamp',
      desc: '',
      args: [],
    );
  }

  /// `Total Fragments`
  String get totalFragments {
    return Intl.message(
      'Total Fragments',
      name: 'totalFragments',
      desc: '',
      args: [],
    );
  }

  /// `To Date Time`
  String get toDateTime {
    return Intl.message(
      'To Date Time',
      name: 'toDateTime',
      desc: '',
      args: [],
    );
  }

  /// `Today`
  String get today {
    return Intl.message(
      'Today',
      name: 'today',
      desc: '',
      args: [],
    );
  }

  /// `Topology`
  String get topology {
    return Intl.message(
      'Topology',
      name: 'topology',
      desc: '',
      args: [],
    );
  }

  /// `Total`
  String get total {
    return Intl.message(
      'Total',
      name: 'total',
      desc: '',
      args: [],
    );
  }

  /// `Total Provisioned`
  String get totalProvisioned {
    return Intl.message(
      'Total Provisioned',
      name: 'totalProvisioned',
      desc: '',
      args: [],
    );
  }

  /// `Total Size`
  String get totalSize {
    return Intl.message(
      'Total Size',
      name: 'totalSize',
      desc: '',
      args: [],
    );
  }

  /// `Transponder Information`
  String get transponder {
    return Intl.message(
      'Transponder Information',
      name: 'transponder',
      desc: '',
      args: [],
    );
  }

  /// `24v DC`
  String get twentyFourDC {
    return Intl.message(
      '24v DC',
      name: 'twentyFourDC',
      desc: '',
      args: [],
    );
  }

  /// `24`
  String get twentyFourV {
    return Intl.message(
      '24',
      name: 'twentyFourV',
      desc: '',
      args: [],
    );
  }

  /// `232`
  String get twoThirtyTwo {
    return Intl.message(
      '232',
      name: 'twoThirtyTwo',
      desc: '',
      args: [],
    );
  }

  /// `Type`
  String get type {
    return Intl.message(
      'Type',
      name: 'type',
      desc: '',
      args: [],
    );
  }

  /// `US ATTN`
  String get uSAttn {
    return Intl.message(
      'US ATTN',
      name: 'uSAttn',
      desc: '',
      args: [],
    );
  }

  /// `US EQ`
  String get uSEQ {
    return Intl.message(
      'US EQ',
      name: 'uSEQ',
      desc: '',
      args: [],
    );
  }

  /// `UnProvisioned`
  String get unProvisioned {
    return Intl.message(
      'UnProvisioned',
      name: 'unProvisioned',
      desc: '',
      args: [],
    );
  }

  /// `Units`
  String get units {
    return Intl.message(
      'Units',
      name: 'units',
      desc: '',
      args: [],
    );
  }

  /// `Universal Plugin`
  String get universalPlugin {
    return Intl.message(
      'Universal Plugin',
      name: 'universalPlugin',
      desc: '',
      args: [],
    );
  }

  /// `Unknown`
  String get unknown {
    return Intl.message(
      'Unknown',
      name: 'unknown',
      desc: '',
      args: [],
    );
  }

  /// `Up Time`
  String get upTime {
    return Intl.message(
      'Up Time',
      name: 'upTime',
      desc: '',
      args: [],
    );
  }

  /// `Update`
  String get update {
    return Intl.message(
      'Update',
      name: 'update',
      desc: '',
      args: [],
    );
  }

  /// `Update Amplifier Details`
  String get updateAmpDetails {
    return Intl.message(
      'Update Amplifier Details',
      name: 'updateAmpDetails',
      desc: '',
      args: [],
    );
  }

  /// `Update Site Details`
  String get updateSiteDetails {
    return Intl.message(
      'Update Site Details',
      name: 'updateSiteDetails',
      desc: '',
      args: [],
    );
  }

  /// `Update Site Failed.`
  String get updateSiteFailed {
    return Intl.message(
      'Update Site Failed.',
      name: 'updateSiteFailed',
      desc: '',
      args: [],
    );
  }

  /// `Update Site Successfully.`
  String get updateSiteSuccess {
    return Intl.message(
      'Update Site Successfully.',
      name: 'updateSiteSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Update Site to VLGW`
  String get updateSiteVLGW {
    return Intl.message(
      'Update Site to VLGW',
      name: 'updateSiteVLGW',
      desc: '',
      args: [],
    );
  }

  /// `Updated Successfully`
  String get updateSuccess {
    return Intl.message(
      'Updated Successfully',
      name: 'updateSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Update Failed`
  String get updateFailed {
    return Intl.message(
      'Update Failed',
      name: 'updateFailed',
      desc: '',
      args: [],
    );
  }

  /// `Update Threshold`
  String get updateThreshold {
    return Intl.message(
      'Update Threshold',
      name: 'updateThreshold',
      desc: '',
      args: [],
    );
  }

  /// `Upload`
  String get upload {
    return Intl.message(
      'Upload',
      name: 'upload',
      desc: '',
      args: [],
    );
  }

  /// `Upgrade Request Sent`
  String get upgradeRequestSent {
    return Intl.message(
      'Upgrade Request Sent',
      name: 'upgradeRequestSent',
      desc: '',
      args: [],
    );
  }

  /// `Upload Date`
  String get uploadDate {
    return Intl.message(
      'Upload Date',
      name: 'uploadDate',
      desc: '',
      args: [],
    );
  }

  /// `File Error`
  String get uploadFail {
    return Intl.message(
      'File Error',
      name: 'uploadFail',
      desc: '',
      args: [],
    );
  }

  /// `Upload Firmware`
  String get uploadFirmware {
    return Intl.message(
      'Upload Firmware',
      name: 'uploadFirmware',
      desc: '',
      args: [],
    );
  }

  /// `Upload New`
  String get uploadNew {
    return Intl.message(
      'Upload New',
      name: 'uploadNew',
      desc: '',
      args: [],
    );
  }

  /// `File uploaded successfully`
  String get uploadSuccess {
    return Intl.message(
      'File uploaded successfully',
      name: 'uploadSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Uploaded`
  String get uploaded {
    return Intl.message(
      'Uploaded',
      name: 'uploaded',
      desc: '',
      args: [],
    );
  }

  /// `Upstream`
  String get upstream {
    return Intl.message(
      'Upstream',
      name: 'upstream',
      desc: '',
      args: [],
    );
  }

  /// `URL`
  String get url {
    return Intl.message(
      'URL',
      name: 'url',
      desc: '',
      args: [],
    );
  }

  /// `US Alignment`
  String get usAlignCfg {
    return Intl.message(
      'US Alignment',
      name: 'usAlignCfg',
      desc: '',
      args: [],
    );
  }

  /// `U/S Alignment`
  String get usAlignment {
    return Intl.message(
      'U/S Alignment',
      name: 'usAlignment',
      desc: '',
      args: [],
    );
  }

  /// `US alignment failed.`
  String get usAlignmentFailed {
    return Intl.message(
      'US alignment failed.',
      name: 'usAlignmentFailed',
      desc: '',
      args: [],
    );
  }

  /// `User`
  String get user {
    return Intl.message(
      'User',
      name: 'user',
      desc: '',
      args: [],
    );
  }

  /// `User ID`
  String get userID {
    return Intl.message(
      'User ID',
      name: 'userID',
      desc: '',
      args: [],
    );
  }

  /// `User Information`
  String get userInformation {
    return Intl.message(
      'User Information',
      name: 'userInformation',
      desc: '',
      args: [],
    );
  }

  /// `US Gain Adjust`
  String get usGainAdjust {
    return Intl.message(
      'US Gain Adjust',
      name: 'usGainAdjust',
      desc: '',
      args: [],
    );
  }

  /// `US Slope Adjust`
  String get usSlopeAdjust {
    return Intl.message(
      'US Slope Adjust',
      name: 'usSlopeAdjust',
      desc: '',
      args: [],
    );
  }

  /// `3.3 V`
  String get vdd33v {
    return Intl.message(
      '3.3 V',
      name: 'vdd33v',
      desc: '',
      args: [],
    );
  }

  /// `#VLGW`
  String get vLGW {
    return Intl.message(
      '#VLGW',
      name: 'vLGW',
      desc: '',
      args: [],
    );
  }

  /// `VLGW`
  String get vLGW1 {
    return Intl.message(
      'VLGW',
      name: 'vLGW1',
      desc: '',
      args: [],
    );
  }

  /// `VLGW Info`
  String get vLGWInfo {
    return Intl.message(
      'VLGW Info',
      name: 'vLGWInfo',
      desc: '',
      args: [],
    );
  }

  /// `Domain not found`
  String get validEmail {
    return Intl.message(
      'Domain not found',
      name: 'validEmail',
      desc: '',
      args: [],
    );
  }

  /// `Vendor`
  String get vendor {
    return Intl.message(
      'Vendor',
      name: 'vendor',
      desc: '',
      args: [],
    );
  }

  /// `Version`
  String get version {
    return Intl.message(
      'Version',
      name: 'version',
      desc: '',
      args: [],
    );
  }

  /// `Virtual GW`
  String get virtualGW {
    return Intl.message(
      'Virtual GW',
      name: 'virtualGW',
      desc: '',
      args: [],
    );
  }

  /// `Virtual Tilt (dB)`
  String get virtual_tilt {
    return Intl.message(
      'Virtual Tilt (dB)',
      name: 'virtual_tilt',
      desc: '',
      args: [],
    );
  }

  /// `VLGW Hostname`
  String get vlgwIp {
    return Intl.message(
      'VLGW Hostname',
      name: 'vlgwIp',
      desc: '',
      args: [],
    );
  }

  /// `VLGW Hostname`
  String get vlgwHostname {
    return Intl.message(
      'VLGW Hostname',
      name: 'vlgwHostname',
      desc: '',
      args: [],
    );
  }

  /// `IP Address`
  String get ipAddress {
    return Intl.message(
      'IP Address',
      name: 'ipAddress',
      desc: '',
      args: [],
    );
  }

  /// `Port`
  String get port {
    return Intl.message(
      'Port',
      name: 'port',
      desc: '',
      args: [],
    );
  }

  /// `FSKStats`
  String get vlgwFSKStats {
    return Intl.message(
      'FSKStats',
      name: 'vlgwFSKStats',
      desc: '',
      args: [],
    );
  }

  /// `V`
  String get volt {
    return Intl.message(
      'V',
      name: 'volt',
      desc: '',
      args: [],
    );
  }

  /// `WiFi SSID`
  String get wifiSSID {
    return Intl.message(
      'WiFi SSID',
      name: 'wifiSSID',
      desc: '',
      args: [],
    );
  }

  /// `Write`
  String get write {
    return Intl.message(
      'Write',
      name: 'write',
      desc: '',
      args: [],
    );
  }

  /// `Yes`
  String get yes {
    return Intl.message(
      'Yes',
      name: 'yes',
      desc: '',
      args: [],
    );
  }

  /// `Something went wrong`
  String get socketExceptionMessage {
    return Intl.message(
      'Something went wrong',
      name: 'socketExceptionMessage',
      desc: '',
      args: [],
    );
  }

  /// `Error Retrieving Data`
  String get errorRetrievingMessage {
    return Intl.message(
      'Error Retrieving Data',
      name: 'errorRetrievingMessage',
      desc: '',
      args: [],
    );
  }

  /// `Failed to set device user info.`
  String get errorDeviceInfoMessage {
    return Intl.message(
      'Failed to set device user info.',
      name: 'errorDeviceInfoMessage',
      desc: '',
      args: [],
    );
  }

  /// `The refresh failed in `
  String get refreshFailedMessage {
    return Intl.message(
      'The refresh failed in ',
      name: 'refreshFailedMessage',
      desc: '',
      args: [],
    );
  }

  /// `DeMod Good Packets`
  String get demodGoodPkts {
    return Intl.message(
      'DeMod Good Packets',
      name: 'demodGoodPkts',
      desc: '',
      args: [],
    );
  }

  /// `DeMod Bad Packets`
  String get demodBadPkts {
    return Intl.message(
      'DeMod Bad Packets',
      name: 'demodBadPkts',
      desc: '',
      args: [],
    );
  }

  /// `DeMod Dropped Packets`
  String get demodDroppedPkts {
    return Intl.message(
      'DeMod Dropped Packets',
      name: 'demodDroppedPkts',
      desc: '',
      args: [],
    );
  }

  /// `DeMod Not Found Packets`
  String get demodNotFoundPkts {
    return Intl.message(
      'DeMod Not Found Packets',
      name: 'demodNotFoundPkts',
      desc: '',
      args: [],
    );
  }

  /// `DeMod Diagnostic`
  String get demodDiagnostic {
    return Intl.message(
      'DeMod Diagnostic',
      name: 'demodDiagnostic',
      desc: '',
      args: [],
    );
  }

  /// `DeMod Input Level`
  String get demodInputLevel {
    return Intl.message(
      'DeMod Input Level',
      name: 'demodInputLevel',
      desc: '',
      args: [],
    );
  }

  /// `Mod Diagnostic`
  String get modDiagnostic {
    return Intl.message(
      'Mod Diagnostic',
      name: 'modDiagnostic',
      desc: '',
      args: [],
    );
  }

  /// `Mod Output Level`
  String get modOutputLevel {
    return Intl.message(
      'Mod Output Level',
      name: 'modOutputLevel',
      desc: '',
      args: [],
    );
  }

  /// `Mod packets`
  String get modPkts {
    return Intl.message(
      'Mod packets',
      name: 'modPkts',
      desc: '',
      args: [],
    );
  }

  /// `Bad Channels`
  String get badChannels {
    return Intl.message(
      'Bad Channels',
      name: 'badChannels',
      desc: '',
      args: [],
    );
  }

  /// `Frames Dropped`
  String get framesDropped {
    return Intl.message(
      'Frames Dropped',
      name: 'framesDropped',
      desc: '',
      args: [],
    );
  }

  /// `Reset`
  String get reset {
    return Intl.message(
      'Reset',
      name: 'reset',
      desc: '',
      args: [],
    );
  }

  /// `Reset Default`
  String get resetDefault {
    return Intl.message(
      'Reset Default',
      name: 'resetDefault',
      desc: '',
      args: [],
    );
  }

  /// `Enable Manual Mode`
  String get enableManualMode {
    return Intl.message(
      'Enable Manual Mode',
      name: 'enableManualMode',
      desc: '',
      args: [],
    );
  }

  /// `Open Software Download Page`
  String get downloadSoftware {
    return Intl.message(
      'Open Software Download Page',
      name: 'downloadSoftware',
      desc: '',
      args: [],
    );
  }

  /// `Services`
  String get services {
    return Intl.message(
      'Services',
      name: 'services',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to perform auto-alignment?`
  String get msgAskConfirmationAutoAlign {
    return Intl.message(
      'Are you sure you want to perform auto-alignment?',
      name: 'msgAskConfirmationAutoAlign',
      desc: '',
      args: [],
    );
  }

  /// `Create Deployment`
  String get createDeployment {
    return Intl.message(
      'Create Deployment',
      name: 'createDeployment',
      desc: '',
      args: [],
    );
  }

  /// `Associate Deployments`
  String get associateDeployments {
    return Intl.message(
      'Associate Deployments',
      name: 'associateDeployments',
      desc: '',
      args: [],
    );
  }

  /// `Device Count`
  String get deviceCount {
    return Intl.message(
      'Device Count',
      name: 'deviceCount',
      desc: '',
      args: [],
    );
  }

  /// `Created`
  String get created {
    return Intl.message(
      'Created',
      name: 'created',
      desc: '',
      args: [],
    );
  }

  /// `ID`
  String get id {
    return Intl.message(
      'ID',
      name: 'id',
      desc: '',
      args: [],
    );
  }

  /// `Deployments`
  String get deployments {
    return Intl.message(
      'Deployments',
      name: 'deployments',
      desc: '',
      args: [],
    );
  }

  /// `Progress`
  String get process {
    return Intl.message(
      'Progress',
      name: 'process',
      desc: '',
      args: [],
    );
  }

  /// `Proceed`
  String get proceed {
    return Intl.message(
      'Proceed',
      name: 'proceed',
      desc: '',
      args: [],
    );
  }

  /// `No device found`
  String get noDeviceFound {
    return Intl.message(
      'No device found',
      name: 'noDeviceFound',
      desc: '',
      args: [],
    );
  }

  /// `Discard`
  String get discard {
    return Intl.message(
      'Discard',
      name: 'discard',
      desc: '',
      args: [],
    );
  }

  /// `Duration`
  String get duration {
    return Intl.message(
      'Duration',
      name: 'duration',
      desc: '',
      args: [],
    );
  }

  /// `Size (Bytes)`
  String get size {
    return Intl.message(
      'Size (Bytes)',
      name: 'size',
      desc: '',
      args: [],
    );
  }

  /// `Upgrade`
  String get upgrade {
    return Intl.message(
      'Upgrade',
      name: 'upgrade',
      desc: '',
      args: [],
    );
  }

  /// `Please select type`
  String get pleaseSelectFileType {
    return Intl.message(
      'Please select type',
      name: 'pleaseSelectFileType',
      desc: '',
      args: [],
    );
  }

  /// `Download Progress`
  String get downloadProgress {
    return Intl.message(
      'Download Progress',
      name: 'downloadProgress',
      desc: '',
      args: [],
    );
  }

  /// `Deployment Status`
  String get deploymentStatus {
    return Intl.message(
      'Deployment Status',
      name: 'deploymentStatus',
      desc: '',
      args: [],
    );
  }

  /// `Transponder FW Version`
  String get transponderFwVersion {
    return Intl.message(
      'Transponder FW Version',
      name: 'transponderFwVersion',
      desc: '',
      args: [],
    );
  }

  /// `Amplifier FW Version`
  String get ampFwVersion {
    return Intl.message(
      'Amplifier FW Version',
      name: 'ampFwVersion',
      desc: '',
      args: [],
    );
  }

  /// `Before Version`
  String get beforeVersion {
    return Intl.message(
      'Before Version',
      name: 'beforeVersion',
      desc: '',
      args: [],
    );
  }

  /// `After Version`
  String get afterVersion {
    return Intl.message(
      'After Version',
      name: 'afterVersion',
      desc: '',
      args: [],
    );
  }

  /// `Download In Progress`
  String get downloadInProgress {
    return Intl.message(
      'Download In Progress',
      name: 'downloadInProgress',
      desc: '',
      args: [],
    );
  }

  /// `Download Completed`
  String get downloadCompleted {
    return Intl.message(
      'Download Completed',
      name: 'downloadCompleted',
      desc: '',
      args: [],
    );
  }

  /// `Ready for Upgrade`
  String get readyForUpgrade {
    return Intl.message(
      'Ready for Upgrade',
      name: 'readyForUpgrade',
      desc: '',
      args: [],
    );
  }

  /// `Upgrade Completed`
  String get upgradeCompleted {
    return Intl.message(
      'Upgrade Completed',
      name: 'upgradeCompleted',
      desc: '',
      args: [],
    );
  }

  /// `Deployment Failed`
  String get deploymentFailed {
    return Intl.message(
      'Deployment Failed',
      name: 'deploymentFailed',
      desc: '',
      args: [],
    );
  }

  /// `Upgrade Status`
  String get upgradeStatus {
    return Intl.message(
      'Upgrade Status',
      name: 'upgradeStatus',
      desc: '',
      args: [],
    );
  }

  /// `Remark`
  String get remark {
    return Intl.message(
      'Remark',
      name: 'remark',
      desc: '',
      args: [],
    );
  }

  /// `Upgrade automatically after download.`
  String get upgradeAutoText {
    return Intl.message(
      'Upgrade automatically after download.',
      name: 'upgradeAutoText',
      desc: '',
      args: [],
    );
  }

  /// `A maximum of 6 tabs can be opened at a time.`
  String get maxTabMessage {
    return Intl.message(
      'A maximum of 6 tabs can be opened at a time.',
      name: 'maxTabMessage',
      desc: '',
      args: [],
    );
  }

  /// `Summary Status`
  String get summaryStatus {
    return Intl.message(
      'Summary Status',
      name: 'summaryStatus',
      desc: '',
      args: [],
    );
  }

  /// `Software Update`
  String get softwareUpdate {
    return Intl.message(
      'Software Update',
      name: 'softwareUpdate',
      desc: '',
      args: [],
    );
  }

  /// `Please`
  String get please {
    return Intl.message(
      'Please',
      name: 'please',
      desc: '',
      args: [],
    );
  }

  /// `click`
  String get click {
    return Intl.message(
      'click',
      name: 'click',
      desc: '',
      args: [],
    );
  }

  /// `here to update the software`
  String get hereToUpdateSentence {
    return Intl.message(
      'here to update the software',
      name: 'hereToUpdateSentence',
      desc: '',
      args: [],
    );
  }

  /// `Select a WiFi Network`
  String get selectWifi {
    return Intl.message(
      'Select a WiFi Network',
      name: 'selectWifi',
      desc: '',
      args: [],
    );
  }

  /// `Connect to `
  String get connectTo {
    return Intl.message(
      'Connect to ',
      name: 'connectTo',
      desc: '',
      args: [],
    );
  }

  /// `Password`
  String get passWord {
    return Intl.message(
      'Password',
      name: 'passWord',
      desc: '',
      args: [],
    );
  }

  /// `Back`
  String get back {
    return Intl.message(
      'Back',
      name: 'back',
      desc: '',
      args: [],
    );
  }

  /// `Connected`
  String get connected {
    return Intl.message(
      'Connected',
      name: 'connected',
      desc: '',
      args: [],
    );
  }

  /// `Failed to Connect`
  String get failedToConnect {
    return Intl.message(
      'Failed to Connect',
      name: 'failedToConnect',
      desc: '',
      args: [],
    );
  }

  /// `SSID`
  String get ssid {
    return Intl.message(
      'SSID',
      name: 'ssid',
      desc: '',
      args: [],
    );
  }

  /// `Connect`
  String get connect {
    return Intl.message(
      'Connect',
      name: 'connect',
      desc: '',
      args: [],
    );
  }

  /// `Enter Password`
  String get enterPassword {
    return Intl.message(
      'Enter Password',
      name: 'enterPassword',
      desc: '',
      args: [],
    );
  }

  /// `Service Status`
  String get serviceStatus {
    return Intl.message(
      'Service Status',
      name: 'serviceStatus',
      desc: '',
      args: [],
    );
  }

  /// `Healthy`
  String get healthy {
    return Intl.message(
      'Healthy',
      name: 'healthy',
      desc: '',
      args: [],
    );
  }

  /// `Channel`
  String get configChannels {
    return Intl.message(
      'Channel',
      name: 'configChannels',
      desc: '',
      args: [],
    );
  }

  /// `Upload CSV for Join Server`
  String get uploadJoinServer {
    return Intl.message(
      'Upload CSV for Join Server',
      name: 'uploadJoinServer',
      desc: '',
      args: [],
    );
  }

  /// `No.`
  String get channel {
    return Intl.message(
      'No.',
      name: 'channel',
      desc: '',
      args: [],
    );
  }

  /// `Enabled`
  String get enabled {
    return Intl.message(
      'Enabled',
      name: 'enabled',
      desc: '',
      args: [],
    );
  }

  /// `Disabled`
  String get disabled {
    return Intl.message(
      'Disabled',
      name: 'disabled',
      desc: '',
      args: [],
    );
  }

  /// `Start deployment?`
  String get upgradeConfirmationMessage {
    return Intl.message(
      'Start deployment?',
      name: 'upgradeConfirmationMessage',
      desc: '',
      args: [],
    );
  }

  /// `Created On`
  String get createdOn {
    return Intl.message(
      'Created On',
      name: 'createdOn',
      desc: '',
      args: [],
    );
  }

  /// `Upload Date`
  String get uploadAt {
    return Intl.message(
      'Upload Date',
      name: 'uploadAt',
      desc: '',
      args: [],
    );
  }

  /// `Upgrade Type`
  String get upgradeType {
    return Intl.message(
      'Upgrade Type',
      name: 'upgradeType',
      desc: '',
      args: [],
    );
  }

  /// `Delete Selected`
  String get deleteSelected {
    return Intl.message(
      'Delete Selected',
      name: 'deleteSelected',
      desc: '',
      args: [],
    );
  }

  /// `NDF \nDest IP`
  String get ndfDestIp {
    return Intl.message(
      'NDF \nDest IP',
      name: 'ndfDestIp',
      desc: '',
      args: [],
    );
  }

  /// `device was upgraded.`
  String get deviceWasUpgraded {
    return Intl.message(
      'device was upgraded.',
      name: 'deviceWasUpgraded',
      desc: '',
      args: [],
    );
  }

  /// `devices were upgraded.`
  String get devicesWereUpgraded {
    return Intl.message(
      'devices were upgraded.',
      name: 'devicesWereUpgraded',
      desc: '',
      args: [],
    );
  }

  /// `Choose File`
  String get chooseFile {
    return Intl.message(
      'Choose File',
      name: 'chooseFile',
      desc: '',
      args: [],
    );
  }

  /// `No file chosen`
  String get noFileChosen {
    return Intl.message(
      'No file chosen',
      name: 'noFileChosen',
      desc: '',
      args: [],
    );
  }

  /// `Skip Devices`
  String get skipDevices {
    return Intl.message(
      'Skip Devices',
      name: 'skipDevices',
      desc: '',
      args: [],
    );
  }

  /// `Close`
  String get close {
    return Intl.message(
      'Close',
      name: 'close',
      desc: '',
      args: [],
    );
  }

  /// `Clear`
  String get clear {
    return Intl.message(
      'Clear',
      name: 'clear',
      desc: '',
      args: [],
    );
  }

  /// `No wifi network found`
  String get noWifiFound {
    return Intl.message(
      'No wifi network found',
      name: 'noWifiFound',
      desc: '',
      args: [],
    );
  }

  /// `Search wifi network`
  String get searchWifi {
    return Intl.message(
      'Search wifi network',
      name: 'searchWifi',
      desc: '',
      args: [],
    );
  }

  /// `WiFi UpLink`
  String get wifiUplink {
    return Intl.message(
      'WiFi UpLink',
      name: 'wifiUplink',
      desc: '',
      args: [],
    );
  }

  /// `You are connected with`
  String get connectedWith {
    return Intl.message(
      'You are connected with',
      name: 'connectedWith',
      desc: '',
      args: [],
    );
  }

  /// `You are connected to network.`
  String get connectedWithEthernet {
    return Intl.message(
      'You are connected to network.',
      name: 'connectedWithEthernet',
      desc: '',
      args: [],
    );
  }

  /// `You are not connected to network.`
  String get notConnectedWith {
    return Intl.message(
      'You are not connected to network.',
      name: 'notConnectedWith',
      desc: '',
      args: [],
    );
  }

  /// `RSSI`
  String get rssi {
    return Intl.message(
      'RSSI',
      name: 'rssi',
      desc: '',
      args: [],
    );
  }

  /// `F-Port`
  String get fPort {
    return Intl.message(
      'F-Port',
      name: 'fPort',
      desc: '',
      args: [],
    );
  }

  /// `Please Wait - Starting Services`
  String get healthyServicesMessage {
    return Intl.message(
      'Please Wait - Starting Services',
      name: 'healthyServicesMessage',
      desc: '',
      args: [],
    );
  }

  /// `Unable to check - Something went wrong`
  String get wifiStatusMessage {
    return Intl.message(
      'Unable to check - Something went wrong',
      name: 'wifiStatusMessage',
      desc: '',
      args: [],
    );
  }

  /// `Your WiFi connection may be lost. Please reconnect to the IP Address assigned to the Node GW`
  String get connectionTimeOut {
    return Intl.message(
      'Your WiFi connection may be lost. Please reconnect to the IP Address assigned to the Node GW',
      name: 'connectionTimeOut',
      desc: '',
      args: [],
    );
  }

  /// `GW ID`
  String get gwID {
    return Intl.message(
      'GW ID',
      name: 'gwID',
      desc: '',
      args: [],
    );
  }

  /// `Gateway Settings`
  String get gwSetting {
    return Intl.message(
      'Gateway Settings',
      name: 'gwSetting',
      desc: '',
      args: [],
    );
  }

  /// `DS Gain`
  String get dsGain {
    return Intl.message(
      'DS Gain',
      name: 'dsGain',
      desc: '',
      args: [],
    );
  }

  /// `US RSSI`
  String get usRssi {
    return Intl.message(
      'US RSSI',
      name: 'usRssi',
      desc: '',
      args: [],
    );
  }

  /// `Backup Version`
  String get backUpVersion {
    return Intl.message(
      'Backup Version',
      name: 'backUpVersion',
      desc: '',
      args: [],
    );
  }

  /// `Active Version`
  String get activeVersion {
    return Intl.message(
      'Active Version',
      name: 'activeVersion',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure want to confirm switch bank and reboot?`
  String get msgSwitchBankReboot {
    return Intl.message(
      'Are you sure want to confirm switch bank and reboot?',
      name: 'msgSwitchBankReboot',
      desc: '',
      args: [],
    );
  }

  /// `Device`
  String get device {
    return Intl.message(
      'Device',
      name: 'device',
      desc: '',
      args: [],
    );
  }

  /// `Stats`
  String get stats {
    return Intl.message(
      'Stats',
      name: 'stats',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure ?`
  String get msgAskResetPktFwd {
    return Intl.message(
      'Are you sure ?',
      name: 'msgAskResetPktFwd',
      desc: '',
      args: [],
    );
  }

  /// `Please try later.`
  String get socketTimeOutMessage {
    return Intl.message(
      'Please try later.',
      name: 'socketTimeOutMessage',
      desc: '',
      args: [],
    );
  }

  /// `Changes are not Saved!`
  String get changesAreNotSaved {
    return Intl.message(
      'Changes are not Saved!',
      name: 'changesAreNotSaved',
      desc: '',
      args: [],
    );
  }

  /// `Do you want to continue?`
  String get confirmMessageConfiguration {
    return Intl.message(
      'Do you want to continue?',
      name: 'confirmMessageConfiguration',
      desc: '',
      args: [],
    );
  }

  /// `Changes have not been saved. Please save or revert the draft.`
  String get draftMessageForConfiguration {
    return Intl.message(
      'Changes have not been saved. Please save or revert the draft.',
      name: 'draftMessageForConfiguration',
      desc: '',
      args: [],
    );
  }

  /// `Do you want to delete?.`
  String get deleteMessage {
    return Intl.message(
      'Do you want to delete?.',
      name: 'deleteMessage',
      desc: '',
      args: [],
    );
  }

  /// `Config BitMask`
  String get configBitmask {
    return Intl.message(
      'Config BitMask',
      name: 'configBitmask',
      desc: '',
      args: [],
    );
  }

  /// `Etag`
  String get eTag {
    return Intl.message(
      'Etag',
      name: 'eTag',
      desc: '',
      args: [],
    );
  }

  /// `Primary`
  String get pilotPrimary {
    return Intl.message(
      'Primary',
      name: 'pilotPrimary',
      desc: '',
      args: [],
    );
  }

  /// `Backup`
  String get pilotBackup {
    return Intl.message(
      'Backup',
      name: 'pilotBackup',
      desc: '',
      args: [],
    );
  }

  /// `Pilot - Measured Power`
  String get pilotMeasuredPwr {
    return Intl.message(
      'Pilot - Measured Power',
      name: 'pilotMeasuredPwr',
      desc: '',
      args: [],
    );
  }

  /// `Marker`
  String get pilotMarker {
    return Intl.message(
      'Marker',
      name: 'pilotMarker',
      desc: '',
      args: [],
    );
  }

  /// `Alarms BitMask`
  String get alarmsBitmask {
    return Intl.message(
      'Alarms BitMask',
      name: 'alarmsBitmask',
      desc: '',
      args: [],
    );
  }

  /// `GW ID not found.`
  String get gwNotFoundMessage {
    return Intl.message(
      'GW ID not found.',
      name: 'gwNotFoundMessage',
      desc: '',
      args: [],
    );
  }

  /// `Do you want to Save Auto Alignment Changes?`
  String get autoAlignSaveRevertMessage {
    return Intl.message(
      'Do you want to Save Auto Alignment Changes?',
      name: 'autoAlignSaveRevertMessage',
      desc: '',
      args: [],
    );
  }

  /// `Gateway Name`
  String get gatWayName {
    return Intl.message(
      'Gateway Name',
      name: 'gatWayName',
      desc: '',
      args: [],
    );
  }

  /// `SW Version`
  String get swVersion {
    return Intl.message(
      'SW Version',
      name: 'swVersion',
      desc: '',
      args: [],
    );
  }

  /// `HW Version`
  String get hwVersion {
    return Intl.message(
      'HW Version',
      name: 'hwVersion',
      desc: '',
      args: [],
    );
  }

  /// `GW Name`
  String get gwName {
    return Intl.message(
      'GW Name',
      name: 'gwName',
      desc: '',
      args: [],
    );
  }

  /// `APIs`
  String get apis {
    return Intl.message(
      'APIs',
      name: 'apis',
      desc: '',
      args: [],
    );
  }

  /// `Domain`
  String get domain {
    return Intl.message(
      'Domain',
      name: 'domain',
      desc: '',
      args: [],
    );
  }

  /// `TCP`
  String get tcp {
    return Intl.message(
      'TCP',
      name: 'tcp',
      desc: '',
      args: [],
    );
  }

  /// `GW Detail`
  String get gwDetail {
    return Intl.message(
      'GW Detail',
      name: 'gwDetail',
      desc: '',
      args: [],
    );
  }

  /// `Operation`
  String get operation {
    return Intl.message(
      'Operation',
      name: 'operation',
      desc: '',
      args: [],
    );
  }

  /// `Source Service`
  String get source_service {
    return Intl.message(
      'Source Service',
      name: 'source_service',
      desc: '',
      args: [],
    );
  }

  /// `Application`
  String get application {
    return Intl.message(
      'Application',
      name: 'application',
      desc: '',
      args: [],
    );
  }

  /// `Request Path`
  String get request_path {
    return Intl.message(
      'Request Path',
      name: 'request_path',
      desc: '',
      args: [],
    );
  }

  /// `Request Method`
  String get request_method {
    return Intl.message(
      'Request Method',
      name: 'request_method',
      desc: '',
      args: [],
    );
  }

  /// `Aux`
  String get aux {
    return Intl.message(
      'Aux',
      name: 'aux',
      desc: '',
      args: [],
    );
  }

  /// `Main`
  String get main {
    return Intl.message(
      'Main',
      name: 'main',
      desc: '',
      args: [],
    );
  }

  /// `Enter a valid email`
  String get validEmailAddress {
    return Intl.message(
      'Enter a valid email',
      name: 'validEmailAddress',
      desc: '',
      args: [],
    );
  }

  /// `Current User`
  String get currantUser {
    return Intl.message(
      'Current User',
      name: 'currantUser',
      desc: '',
      args: [],
    );
  }

  /// `Never Seen`
  String get neverSeen {
    return Intl.message(
      'Never Seen',
      name: 'neverSeen',
      desc: '',
      args: [],
    );
  }

  /// `Node GW\nDS Gain`
  String get nodeGWDSGain {
    return Intl.message(
      'Node GW\nDS Gain',
      name: 'nodeGWDSGain',
      desc: '',
      args: [],
    );
  }

  /// `Total Deployment Time`
  String get totalDeploymentTime {
    return Intl.message(
      'Total Deployment Time',
      name: 'totalDeploymentTime',
      desc: '',
      args: [],
    );
  }

  /// `dBm`
  String get dBm {
    return Intl.message(
      'dBm',
      name: 'dBm',
      desc: '',
      args: [],
    );
  }

  /// `Freq`
  String get freq {
    return Intl.message(
      'Freq',
      name: 'freq',
      desc: '',
      args: [],
    );
  }

  /// `Provider not found`
  String get providerNotFound {
    return Intl.message(
      'Provider not found',
      name: 'providerNotFound',
      desc: '',
      args: [],
    );
  }

  /// `Auto Align Marker Pilots`
  String get autoAlignMarkerPilots {
    return Intl.message(
      'Auto Align Marker Pilots',
      name: 'autoAlignMarkerPilots',
      desc: '',
      args: [],
    );
  }

  /// `No data`
  String get noData {
    return Intl.message(
      'No data',
      name: 'noData',
      desc: '',
      args: [],
    );
  }

  /// `Frequency Config`
  String get frequencyConfig {
    return Intl.message(
      'Frequency Config',
      name: 'frequencyConfig',
      desc: '',
      args: [],
    );
  }

  /// `Op. Mode`
  String get openMode {
    return Intl.message(
      'Op. Mode',
      name: 'openMode',
      desc: '',
      args: [],
    );
  }

  /// `Vdc`
  String get vDC {
    return Intl.message(
      'Vdc',
      name: 'vDC',
      desc: '',
      args: [],
    );
  }

  /// `VAC`
  String get vac {
    return Intl.message(
      'VAC',
      name: 'vac',
      desc: '',
      args: [],
    );
  }

  /// `Temp ℉`
  String get tempF {
    return Intl.message(
      'Temp ℉',
      name: 'tempF',
      desc: '',
      args: [],
    );
  }

  /// `Switch Bank and Reboot`
  String get switchBankAndReboot {
    return Intl.message(
      'Switch Bank and Reboot',
      name: 'switchBankAndReboot',
      desc: '',
      args: [],
    );
  }

  /// `NA`
  String get na {
    return Intl.message(
      'NA',
      name: 'na',
      desc: '',
      args: [],
    );
  }

  /// `© OpenStreetMap `
  String get openStreetMap {
    return Intl.message(
      '© OpenStreetMap ',
      name: 'openStreetMap',
      desc: '',
      args: [],
    );
  }

  /// `contributors`
  String get contributors {
    return Intl.message(
      'contributors',
      name: 'contributors',
      desc: '',
      args: [],
    );
  }

  /// `Enter valid location`
  String get locationValidateMessage {
    return Intl.message(
      'Enter valid location',
      name: 'locationValidateMessage',
      desc: '',
      args: [],
    );
  }

  /// `The field should be less than 33 characters`
  String get locationFieldLessThen33 {
    return Intl.message(
      'The field should be less than 33 characters',
      name: 'locationFieldLessThen33',
      desc: '',
      args: [],
    );
  }

  /// `Invalid Token`
  String get invalidTokenMessage {
    return Intl.message(
      'Invalid Token',
      name: 'invalidTokenMessage',
      desc: '',
      args: [],
    );
  }

  /// `Invalid Grant`
  String get restrictSigninMessage {
    return Intl.message(
      'Invalid Grant',
      name: 'restrictSigninMessage',
      desc: '',
      args: [],
    );
  }

  /// `New Amplifier Provider`
  String get newAmpProvider {
    return Intl.message(
      'New Amplifier Provider',
      name: 'newAmpProvider',
      desc: '',
      args: [],
    );
  }

  /// `Update Amplifier Provider`
  String get updateAmpProvider {
    return Intl.message(
      'Update Amplifier Provider',
      name: 'updateAmpProvider',
      desc: '',
      args: [],
    );
  }

  /// `Application Name`
  String get applicationName {
    return Intl.message(
      'Application Name',
      name: 'applicationName',
      desc: '',
      args: [],
    );
  }

  /// `Code`
  String get code {
    return Intl.message(
      'Code',
      name: 'code',
      desc: '',
      args: [],
    );
  }

  /// `Chirpstack App Name`
  String get chirpstackAppName {
    return Intl.message(
      'Chirpstack App Name',
      name: 'chirpstackAppName',
      desc: '',
      args: [],
    );
  }

  /// `Created Successfully`
  String get createdSuccessfully {
    return Intl.message(
      'Created Successfully',
      name: 'createdSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Auto Join`
  String get autoJoin {
    return Intl.message(
      'Auto Join',
      name: 'autoJoin',
      desc: '',
      args: [],
    );
  }

  /// `Auto Join Disabled`
  String get autoJoinDisabled {
    return Intl.message(
      'Auto Join Disabled',
      name: 'autoJoinDisabled',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete`
  String get deleteConfirmationMessage {
    return Intl.message(
      'Are you sure you want to delete',
      name: 'deleteConfirmationMessage',
      desc: '',
      args: [],
    );
  }

  /// `Delete?`
  String get confirmDelete {
    return Intl.message(
      'Delete?',
      name: 'confirmDelete',
      desc: '',
      args: [],
    );
  }

  /// `Deleted Successfully`
  String get deleteSuccessfully {
    return Intl.message(
      'Deleted Successfully',
      name: 'deleteSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Key`
  String get key {
    return Intl.message(
      'Key',
      name: 'key',
      desc: '',
      args: [],
    );
  }

  /// `Vendor Code`
  String get vendorCode {
    return Intl.message(
      'Vendor Code',
      name: 'vendorCode',
      desc: '',
      args: [],
    );
  }

  /// `Created At`
  String get createdAt {
    return Intl.message(
      'Created At',
      name: 'createdAt',
      desc: '',
      args: [],
    );
  }

  /// `Vendor`
  String get selectVendor {
    return Intl.message(
      'Vendor',
      name: 'selectVendor',
      desc: '',
      args: [],
    );
  }

  /// `Transponder Version`
  String get transponderVersion {
    return Intl.message(
      'Transponder Version',
      name: 'transponderVersion',
      desc: '',
      args: [],
    );
  }

  /// `Amplifier Version`
  String get ampVersion {
    return Intl.message(
      'Amplifier Version',
      name: 'ampVersion',
      desc: '',
      args: [],
    );
  }

  /// `Missing Key`
  String get missingKey {
    return Intl.message(
      'Missing Key',
      name: 'missingKey',
      desc: '',
      args: [],
    );
  }

  /// `Missing Vendor`
  String get missingVendor {
    return Intl.message(
      'Missing Vendor',
      name: 'missingVendor',
      desc: '',
      args: [],
    );
  }

  /// `Discovered Device`
  String get discoveredDevice {
    return Intl.message(
      'Discovered Device',
      name: 'discoveredDevice',
      desc: '',
      args: [],
    );
  }

  /// `Add Custom Key`
  String get newKeyVendor {
    return Intl.message(
      'Add Custom Key',
      name: 'newKeyVendor',
      desc: '',
      args: [],
    );
  }

  /// `Generate`
  String get addAutoBtn {
    return Intl.message(
      'Generate',
      name: 'addAutoBtn',
      desc: '',
      args: [],
    );
  }

  /// `Domain mismatch`
  String get domainMisMatch {
    return Intl.message(
      'Domain mismatch',
      name: 'domainMisMatch',
      desc: '',
      args: [],
    );
  }

  /// `Invalid code format`
  String get invalidCodeFormat {
    return Intl.message(
      'Invalid code format',
      name: 'invalidCodeFormat',
      desc: '',
      args: [],
    );
  }

  /// `Select firmware file`
  String get noFirmwareSelected {
    return Intl.message(
      'Select firmware file',
      name: 'noFirmwareSelected',
      desc: '',
      args: [],
    );
  }

  /// `Enter Device EUI to Filter`
  String get searchByDeviceEUI {
    return Intl.message(
      'Enter Device EUI to Filter',
      name: 'searchByDeviceEUI',
      desc: '',
      args: [],
    );
  }

  /// `ALSC Enabled`
  String get alscEnabled {
    return Intl.message(
      'ALSC Enabled',
      name: 'alscEnabled',
      desc: '',
      args: [],
    );
  }

  /// `Access Denied`
  String get accessDenied {
    return Intl.message(
      'Access Denied',
      name: 'accessDenied',
      desc: '',
      args: [],
    );
  }

  /// `Direct web access is disabled`
  String get accessDeniedMessage {
    return Intl.message(
      'Direct web access is disabled',
      name: 'accessDeniedMessage',
      desc: '',
      args: [],
    );
  }

  /// `If you believe this is a mistake, please\ncontact your administrator`
  String get contactAdministrator {
    return Intl.message(
      'If you believe this is a mistake, please\ncontact your administrator',
      name: 'contactAdministrator',
      desc: '',
      args: [],
    );
  }

  /// `Layers`
  String get layers {
    return Intl.message(
      'Layers',
      name: 'layers',
      desc: '',
      args: [],
    );
  }

  /// `Gateways`
  String get gateways {
    return Intl.message(
      'Gateways',
      name: 'gateways',
      desc: '',
      args: [],
    );
  }

  /// `Device Links`
  String get deviceLinks {
    return Intl.message(
      'Device Links',
      name: 'deviceLinks',
      desc: '',
      args: [],
    );
  }

  /// `Loading devices...`
  String get loadingDevices {
    return Intl.message(
      'Loading devices...',
      name: 'loadingDevices',
      desc: '',
      args: [],
    );
  }

  /// `Zoom`
  String get zoom {
    return Intl.message(
      'Zoom',
      name: 'zoom',
      desc: '',
      args: [],
    );
  }

  /// `{devices} devices and {nodes} nodes shown`
  String devicesAndGatewaysShown(int devices, int nodes) {
    return Intl.message(
      '$devices devices and $nodes nodes shown',
      name: 'devicesAndGatewaysShown',
      desc: '',
      args: [devices, nodes],
    );
  }

  /// `Inventory`
  String get inventory {
    return Intl.message(
      'Inventory',
      name: 'inventory',
      desc: '',
      args: [],
    );
  }

  /// `Type`
  String get product {
    return Intl.message(
      'Type',
      name: 'product',
      desc: '',
      args: [],
    );
  }

  /// `You’ve reached the maximum limit of 6 deployment tabs.`
  String get maxTabDeploymentMessage {
    return Intl.message(
      'You’ve reached the maximum limit of 6 deployment tabs.',
      name: 'maxTabDeploymentMessage',
      desc: '',
      args: [],
    );
  }

  /// `Fwdnld`
  String get fwdnld {
    return Intl.message(
      'Fwdnld',
      name: 'fwdnld',
      desc: '',
      args: [],
    );
  }

  /// `Topology Scan`
  String get topologyScan {
    return Intl.message(
      'Topology Scan',
      name: 'topologyScan',
      desc: '',
      args: [],
    );
  }

  /// `Gateway EUI is required to start topology scan`
  String get gatewayEUIIsRequired {
    return Intl.message(
      'Gateway EUI is required to start topology scan',
      name: 'gatewayEUIIsRequired',
      desc: '',
      args: [],
    );
  }

  /// `Start`
  String get startScan {
    return Intl.message(
      'Start',
      name: 'startScan',
      desc: '',
      args: [],
    );
  }

  /// `Topology scan triggered. This may take a few minutes.`
  String get scanTriggeredMessage {
    return Intl.message(
      'Topology scan triggered. This may take a few minutes.',
      name: 'scanTriggeredMessage',
      desc: '',
      args: [],
    );
  }

  /// `Search device...`
  String get searchDevice {
    return Intl.message(
      'Search device...',
      name: 'searchDevice',
      desc: '',
      args: [],
    );
  }

  /// `Search for Gateway by Name, EUI or Site .`
  String get searchGateway {
    return Intl.message(
      'Search for Gateway by Name, EUI or Site .',
      name: 'searchGateway',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en', countryCode: 'US'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
