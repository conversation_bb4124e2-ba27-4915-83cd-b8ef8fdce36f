class SpectrumRequestItem {
  SpectrumConfig config;
  List<SpectrumRange> ranges;

  SpectrumRequestItem({
    required this.config,
    required this.ranges,
  });

  factory SpectrumRequestItem.fromJson(Map<String, dynamic> json) {
    return SpectrumRequestItem(
      config: SpectrumConfig.fromJson(json['config']),
      ranges: (json['ranges'] as List)
          .map((rangeJson) => SpectrumRange.fromJson(rangeJson))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'config': config.toJson(),
      'ranges': ranges.map((range) => range.toJson()).toList(),
    };
  }
}

class SpectrumConfig {
  num spectrumCaptureModeE;
  num spectrumCaptureDataTypeE;
  num scanMode;

  SpectrumConfig({
    required this.spectrumCaptureModeE,
    required this.spectrumCaptureDataTypeE,
    required this.scanMode,
  });

  factory SpectrumConfig.fromJson(Map<String, dynamic> json) {
    return SpectrumConfig(
      spectrumCaptureModeE: json['spectrum_capture_mode_e'],
      spectrumCaptureDataTypeE: json['spectrum_capture_data_type_e'],
      scanMode: json['scan_mode'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'spectrum_capture_mode_e': spectrumCaptureModeE,
      'spectrum_capture_data_type_e': spectrumCaptureDataTypeE,
      'scan_mode': scanMode,
    };
  }
}

class SpectrumRange {
  num startFreq;
  num stepSize;
  num endFreq;

  SpectrumRange({
    required this.startFreq,
    required this.stepSize,
    required this.endFreq,
  });

  factory SpectrumRange.fromJson(Map<String, dynamic> json) {
    return SpectrumRange(
      startFreq: json['start_freq'],
      stepSize: json['step_size'],
      endFreq: json['end_freq'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'start_freq': startFreq,
      'step_size': stepSize,
      'end_freq': endFreq,
    };
  }
}
