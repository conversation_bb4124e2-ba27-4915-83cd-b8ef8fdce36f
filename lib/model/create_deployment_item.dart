class CreateDeploymentItem {
  List<String> deviceEuis;
  String firmwareId;
  String fileType;
  String updateMode;

  CreateDeploymentItem({
    required this.deviceEuis,
    required this.firmwareId,
    required this.fileType,
    required this.updateMode
  });



  Map<String, dynamic> toJson() => {
    "device_euis": List<dynamic>.from(deviceEuis.map((x) => x)),
    "firmware_id": firmwareId,
    "file_type": fileType,
    "update_mode": updateMode,
  };
}