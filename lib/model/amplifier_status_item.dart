enum DetectedStatusType {
  online,
  offline,
  pending,
  fwDownload,
  missingKey,
  missingVendor
}

DetectedStatusType? getDetectedStatusType(String? status) {
  switch (status) {
    case 'online':
      return DetectedStatusType.online;
    case 'offline':
      return DetectedStatusType.offline;
    case 'pending':
      return DetectedStatusType.pending;
    case 'missing_key':
      return DetectedStatusType.missingKey;
    case 'fwdnld':
      return DetectedStatusType.fwDownload;
    case 'missing_vendor':
      return DetectedStatusType.missingVendor;
    default:
      return DetectedStatusType.offline; // or handle the default case accordingly
  }
}
extension DetectedStatusTypeExtension on DetectedStatusType {
  String get displayName {
    switch (this) {
      case DetectedStatusType.online:
        return "Online";
      case DetectedStatusType.offline:
        return "Offline";
      case DetectedStatusType.pending:
        return "Pending";
      case DetectedStatusType.fwDownload:
        return "FW Download";
      case DetectedStatusType.missingKey:
        return "Missing Key";
      case DetectedStatusType.missingVendor:
        return "Missing Vendor";
    }
  }
}