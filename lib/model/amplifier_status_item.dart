enum DetectedStatusType {
  online,
  offline,
  pending,
  missingKey,
  missingVendor
}

DetectedStatusType? getDetectedStatusType(String? status) {
  switch (status) {
    case 'online':
      return DetectedStatusType.online;
    case 'offline':
      return DetectedStatusType.offline;
    case 'pending':
      return DetectedStatusType.pending;
    case 'missing_key':
      return DetectedStatusType.missingKey;
    case 'missing_vendor':
      return DetectedStatusType.missingVendor;
    default:
      return DetectedStatusType.offline; // or handle the default case accordingly
  }
}
