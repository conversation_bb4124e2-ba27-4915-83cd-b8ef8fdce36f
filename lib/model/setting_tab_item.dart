import 'package:quantumlink_node/app_import.dart';

class SettingTabItem {
  String title;
  bool isCurrentOpen;
  String? icon;

  SettingTabItem({
    required this.title,
    required this.isCurrentOpen,
    this.icon,
  });

  BoxDecoration getDeco(bool hovered) {
    return BoxDecoration(
        color: hovered && !isCurrentOpen
            ? AppColorConstants.colorLightBlue1.withOpacity(0.5)
            : isCurrentOpen
                ? AppColorConstants.colorLightBlue1
                : AppColorConstants.colorWhite,
        border: Border.all(
            color: isCurrentOpen ? AppColorConstants.colorLightBlue : AppColorConstants.colorH2),
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(getSize(8)), topRight: Radius.circular(getSize(8))));
  }
}
