import 'package:quantumlink_node/app/config/app_config.dart';

class UserInformationModel {




  dynamic name;
  dynamic uniqueName; // email on aad


  UserInformationModel({
    this.name,
    this.uniqueName,
  });

  UserInformationModel.fromJson(Map<String, dynamic> json) {
    if(json.containsKey("email")){
      AppConfig.shared.isGoogleSignIn = true;
    }
    name = json['name'];
    uniqueName = AppConfig.shared.isGoogleSignIn ? json['email'] : json['unique_name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    data['unique_name'] = uniqueName;
    return data;
  }
  UserInformationModel.empty();
}