// ignore_for_file: deprecated_member_use
class DownStreamAmpsModel {
  dynamic deviceEui;
  dynamic type;
  dynamic lastSeen;
  dynamic status;


  DownStreamAmpsModel({
    this.deviceEui,
    this.type,
    this.lastSeen,
    this.status,
  });

  factory DownStreamAmpsModel.fromJson(Map<String, dynamic> json) {
    return DownStreamAmpsModel(
      deviceEui: json['deviceEui'],
      type: json['type'],
      lastSeen: json['lastSeen'],
      status: json['status'],
    );
  }
}



