import 'package:quantumlink_node/app_import.dart';

class FirmTabItem {
  String title;
  bool isCurrentOpen;
  Deployment? deployment;

  FirmTabItem({
    required this.title,
    required this.isCurrentOpen,
    this.deployment,
  });

  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'isCurrentOpen': isCurrentOpen,
    };
  }

  BoxDecoration getDeco(bool hovered) {
    return BoxDecoration(
        color: hovered && !isCurrentOpen ? AppColorConstants.colorLightBlue1.withOpacity(0.5) : isCurrentOpen ? AppColorConstants.colorLightBlue1 : AppColorConstants.colorWhite,
        border: Border.all(
            color: isCurrentOpen ? AppColorConstants.colorLightBlue : AppColorConstants.colorH2),
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(getSize(8)), topRight: Radius.circular(getSize(8))));
  }
}
enum SummaryStatusType {
  success,
  inProgress,
  partialSuccess,
  failed,
}

SummaryStatusType? getSummaryStatusType(String? status) {
  switch (status) {
    case 'Success':
      return SummaryStatusType.success;
    case 'Fail':
      return SummaryStatusType.failed;
    case 'Partial Success':
      return SummaryStatusType.partialSuccess;
    default:
      return SummaryStatusType.inProgress; // or handle the default case accordingly
  }
}