import 'package:quantumlink_node/app_import.dart';

class ProvisionedRequestParams {
  RestConstants restConstants = RestConstants.instance;
  final int? pageOffset;
  final int? perPageLimit;
  final String? search;
  final String? vendorCode;
  final String? isEnabled;
  final String? status;

  ProvisionedRequestParams({
    this.pageOffset,
    this.perPageLimit,
    this.search,
    this.vendorCode,
    this.isEnabled,
    this.status,
  });

  String get vendorCodeQuery =>
      vendorCode != null && vendorCode!.trim().isNotEmpty
          ? "${restConstants.vendorCode}=$vendorCode&"
          : "";

  String get isEnabledQuery => isEnabled == null
      ? ""
      : "${restConstants.isEnabled}=${isEnabled == AppStringConstants.enable ? true : false}&";

  String get statusQuery {
    if (status == null) return "";
    String statusValue = switch (status) {
      AppStringConstants.online => "online",
      AppStringConstants.offline => "offline",
      AppStringConstants.pending => "pending",
      AppStringConstants.missingKey => "missing_key",
      AppStringConstants.missingVendor => "missing_vendor",
      _ => ""
    };
    return "${restConstants.status}=$statusValue&";
  }
}
