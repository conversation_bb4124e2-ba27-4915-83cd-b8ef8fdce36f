import 'package:quantumlink_node/app_import.dart';

class VGWTabItem {
  String title;
  bool isCurrentOpen;
  String? icon;

  VGWTabItem({
    required this.title,
    required this.isCurrentOpen,
    this.icon,
  });
  VGWTabItem.fromMap(Map<String, dynamic> map)
      : title = map['title'],
        isCurrentOpen = map['isCurrentOpen'],
        icon = map['icon'];


  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'isCurrentOpen': isCurrentOpen,
      'icon': icon,
    };
  }

  getColorByFlag(f){
    if(f){
      return AppColorConstants.colorWhite;
    }
    return AppColorConstants.colorH3;
  }

  BoxDecoration getDeco(bool hovered) {
    return BoxDecoration(
        color: hovered && !isCurrentOpen
            ? AppColorConstants.colorLightBlue1.withOpacity(0.5)
            : isCurrentOpen
                ? AppColorConstants.colorLightBlue1
                : AppColorConstants.colorWhite,
        border: Border.all(
            color: isCurrentOpen ? AppColorConstants.colorLightBlue : AppColorConstants.colorH2),
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(getSize(8)), topRight: Radius.circular(getSize(8))));
  }

}
