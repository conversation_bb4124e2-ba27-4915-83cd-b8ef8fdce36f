import 'package:quantumlink_node/app_import.dart';

class NodeGWTabItem {
  String title;
  bool isCurrentOpen;
  String? icon;
  GatewayItem ?gatewayItem ;


  NodeGWTabItem({
    required this.title,
    required this.isCurrentOpen,
    this.icon,
    this.gatewayItem,
  });


  BoxDecoration getDeco(bool hovered) {
    return BoxDecoration(
        color: hovered && !isCurrentOpen
            ? AppColorConstants.colorLightBlue1.withOpacity(0.5)
            : isCurrentOpen
                ? AppColorConstants.colorLightBlue1
                : AppColorConstants.colorWhite,
        border: Border.all(
            color: isCurrentOpen ? AppColorConstants.colorLightBlue : AppColorConstants.colorH2),
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(getSize(8)), topRight: Radius.circular(getSize(8))));
  }

}

enum GWStatusType {
  online,
  offline,
  neverSeen
}

GWStatusType? getGWStatusType(String? status) {
  switch (status) {
    case 'online':
      return GWStatusType.online;
    case 'offline':
      return GWStatusType.offline;
    case 'never_seen':
      return GWStatusType.neverSeen;
    default:
      return GWStatusType.offline; // or handle the default case accordingly
  }
}
