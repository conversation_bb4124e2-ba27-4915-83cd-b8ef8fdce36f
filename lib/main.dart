// ignore_for_file: deprecated_member_use

import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:quantumlink_node/app_import.dart';
import 'package:flutter_web_plugins/url_strategy.dart';





final getIt = GetIt.instance;

init() {
  getIt.registerSingleton<AuthRepository>(AuthRepositoryImpl());
  getIt.registerSingleton<ProvisioningRepository>(ProvisioningRepositoryImpl());
  getIt.registerSingleton<AuditLogsRepository>(AuditLogsRepositoryImpl());
  getIt.registerSingleton<AmplifierRepository>(AmplifierRepositoryImpl());
  getIt.registerSingleton<SiteRepository>(SiteRepositoryImpl());
  getIt.registerSingleton<VLGWRepository>(VLGWRepositoryImpl());
  getIt.registerSingleton<DashboardRepository>(DashboardRepositoryImpl());
  getIt.registerSingleton<FirmwareRepository>(FirmwareRepositoryImpl());
  getIt.registerSingleton<GoogleAuthRepository>(GoogleAuthRepositoryImpl());
  getIt.registerSingleton<AmpProviderRepository>(AmpProviderRepositoryImpl());
  getIt.registerSingleton<MapTopologyRepository>(MapTopologyRepositoryImpl());
}

Future<void> main() async {

  WidgetsFlutterBinding.ensureInitialized();



  await runZonedGuarded(() async {
    AppConfig.create(
      appName: AppStringConstants.appName,
      flavor: Flavor.ql_central,
    );
    readPathFromBrowser();
    await AppConfig.shared.loadJsonAsset();
    if(AppConfig.shared.isLicensingService){
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
    }
    init();
    await AppConfig.shared.fetchConfigFromFirebase(null);
     setUrlStrategy(PathUrlStrategy());
    runApp(const MyApp());
  }, (error, stack) {
    debugLogs(error);
  });
}



class MyApp extends StatefulWidget {
  const MyApp({super.key});

  static MyAppState? of(BuildContext context) =>
      context.findAncestorStateOfType<MyAppState>();

  @override
  State<MyApp> createState() => MyAppState();
}

class MyAppState extends State<MyApp> {
  Locale? locale;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final botToastBuilder = BotToastInit();
    return Sizer(
      builder: (BuildContext context, Orientation orientation, screenType) {
        return ToastificationWrapper(
            config: ToastificationConfig(
              marginBuilder: (alignment,index) => const EdgeInsets.fromLTRB(0, 16, 0, 110),
              alignment: Alignment.center,
              itemWidth: 440,
              animationDuration: const Duration(milliseconds: 500),
            ),
            child: GestureDetector(
              onTap: () => FocusManager.instance.primaryFocus!.unfocus(),
              child: GetMaterialApp.router(
                routerDelegate: router.routerDelegate,
                backButtonDispatcher: router.backButtonDispatcher,
                routeInformationParser: router.routeInformationParser,
                routeInformationProvider: router.routeInformationProvider,
                title: AppConfig.shared.appName,
                debugShowCheckedModeBanner: false,
                defaultTransition: Transition.rightToLeft,
                transitionDuration: const Duration(microseconds: 800),
                scrollBehavior: ScrollHelper(),
                navigatorObservers: <NavigatorObserver>[
                  BotToastNavigatorObserver(),
                ],
                localizationsDelegates:  const [
                  S.delegate,
                  GlobalCupertinoLocalizations.delegate,
                  GlobalMaterialLocalizations.delegate,
                  GlobalWidgetsLocalizations.delegate,
                ],
                supportedLocales: S.delegate.supportedLocales,
                locale: locale,
                theme: buildAppTheme(isFormBLEApp: AppConfig.shared.isOpenFromBLE),
                builder: (context, child) {
                  return MediaQuery(
                    data: MediaQuery.of(context).copyWith(textScaleFactor: 1.0),
                    child: child = botToastBuilder(context, child),
                  );
                },
              ),
            ));
      },
    );
  }
}