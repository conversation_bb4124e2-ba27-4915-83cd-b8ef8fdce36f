{"@@locale": "en_US", "ATTN": "ATTN", "EQ": "EQ", "aLSCConfig": "ALSC Config", "aMPS": "AMPS", "aMPS1": "AMPS", "absent": "Absent", "acVoltage": "AC Voltage", "action": "Action", "active": "Active", "activeAlarms": "Active Alarms", "activeAmplifiers": "Active Amplifiers", "addBtn": "Add", "addNewBtn": "Add New", "addSiteFailed": "Add Site Failed.", "addSiteSuccess": "Add Site Successfully.", "addSites": "New Site Details", "addToMulticast": "Add to Multicast", "addedToMulticast": "Added to Multicast", "agcConfig": "ALSC Config", "agcConfiguration": "ALSC Configuration", "alarm": "Alarm", "alarmCleared": "No Active Alarms", "alarmError": "Error Retrieving Alarms", "alarmStatus": "Alarms", "alarms": "Alarms", "alarmsHistory": "Alarms History", "alarmsNotifications": "Alarms & Notifications History", "alignmentConfiguration": "Alignment Configuration", "ampDetails": "Placement and Identity", "ampStatus": "Amplifier Status", "amplifier": "Amplifier", "amplifierInfo": "Amplifier Information", "amplifierInitializationState": "Amplifier Initialization State", "amplifierInterstageValues": "Amplifier Interstage Values", "amplifierIsOffline": "Amplifier is not online.", "amplifierMode": "Amplifier Mode", "amplifiers": "Amplifiers", "amplifiersWithAlarms": "Amplifiers with Alarms", "ampsFineTuning": "Amplifier Interstage Values Fine Tuning", "appName": "QuantumLink Central", "appNameDev": "Dev QuantumLink - Central", "appNameNode": "QuantumLink - Local", "apply": "Apply", "archivalDate": "Archival Date", "archive": "Archive", "archived": "Archived", "assetID": "BEID", "attn": "ATTN-", "auditLogs": "<PERSON><PERSON>", "autoAlignment": "Auto Alignment", "backup": "Backup", "backup1": "Backup 1:", "backup2": "Backup 2:", "backup3": "Backup 3:", "bluetoothID": "Bluetooth ID", "btnSignIn": "Sign In With Azure AD", "build": "Build", "buildDate": "Build Date", "campaignStartDate": "Campaign Start Date", "cancel": "Cancel", "capture": "Capture", "chirpIP": "Chirpstack IP", "configuration": "Configuration", "confirmProvision": "Are you sure you want to provision selected devices?", "configurationValue": "Configuration :", "createSite": "Create Site", "currentFWVersion": "Current FW Version", "firmwareFiles": "Firmware Files", "currentVersion": "Current Version", "custom": "Custom", "dB": "dB", "dc8v": "8", "dSAttn": "DS ATTN", "autoConfig": "Auto Config", "dSEQ": "DS EQ", "dsGainAdjust": "<PERSON> <PERSON><PERSON>", "dsSlopeAdjust": "DS <PERSON><PERSON>e Adjust", "dashBoard": "Dashboard", "dashboard": "Dashboard", "dateTime": "Date/ Time", "dbMV": "dBMV", "deleteBtn": "Delete", "deploymentSummary": "Deployment Summary", "deleteSiteSuccess": "Site Deleted.", "description": "Description", "destinationIP": "Destination IP Address", "details": "Details", "detectedAmplifierInNetWork": "Detected Amplifier in the Network", "devAddress": "DevAddr", "devEUI": "DevEUI", "deviceAlias": "<PERSON><PERSON>", "deviceEUI": "Device EUI", "deviceEUIHint": "Device EUI", "deviceInfo": "Device Info", "deviceStatus": "Device Status", "deviceType": "Device Type", "devices": "Devices", "diagnostics": "Diagnostics", "diplexFilter": "Diplex Filter", "discovered": "Discovered", "discoveredButNotProvisioned": "Discovered but not Provisioned", "dongleConnected": "Dongle connected", "download": "Download", "downloadStatus": "Download Status", "downstream": "Downstream", "downstreamAmps": "Downstream Amps", "dsAMPS": "#DS Amps", "dsAlignCfg": "DS Alignment", "dsAlignment": "D/S Alignment", "amplifierDownstream": "Downstream Amplifier", "amplifierIngress": "Amplifier Ingress", "dsAlignmentCompleted": "DS alignment completed successfully", "setDsAlignmentCompleted": "DS alignment submitted successfully", "setDsAlignmentFailed": "DS alignment write failed", "saveDsAlignmentCompleted": "DS alignment saved successfully", "revertDsAlignmentCompleted": "DS alignment reverted successfully", "saveDsAlignmentFailed": "DS alignment save failed", "revertDsAlignmentFailed": "DS alignment revert failed", "usAlignmentCompleted": "US alignment completed successfully.", "setUsAlignmentCompleted": "US alignment submitted successfully", "setUsAlignmentFailed": "US alignment write failed", "saveUsAlignmentCompleted": "US alignment saved successfully", "revertUsAlignmentCompleted": "US alignment reverted successfully", "saveUsAlignmentFailed": "US alignment save failed", "revertUsAlignmentFailed": "US alignment revert failed", "ampIngressUpdateSuccess": "Amplifier ingress updated successfully", "ampIngressUpdateFailed": "Amplifier ingress updated failed", "ampTestPointConfigUpdateSuccess": "Amplifier test point config updated successfully", "ampTestPointConfigUpdateFailed": "Amplifier test point config updated failed", "dsAlignmentFailed": "DS alignment failed.", "edit": "Edit", "editItemBtn": "Edit", "email": "Email", "password": "Password", "empoweringConnectivity": "Empowering Connectivity", "enclosureStatus": "Enclosure Status", "end": "End Freq:", "endDate": "End Date", "endTime": "End Time", "enterEmail": "Enter email", "equ": "EQ-", "exportTelemetry": "Export Telemetry", "export": "Export", "exportCSVError": "Export CSV File Error", "exportCSVSuccess": "Export CSV File Successfully", "exportRangeError": "Please select date range", "fWBuildDate": "FW Build Date", "fWRev": "<PERSON><PERSON><PERSON>", "apiVersion": "API Version", "fWVersion": "FW Version", "hWVersion": "HW Version", "fetchSpectrum": "Fetching Spectrum Levels..", "fileName": "File Name", "fileTypeDescriptor": "File Type (descriptor)", "fileUpload": "File Upload", "filters": "Filters", "firmwareDownloadConfig": "Firmware Download Config", "firmwareDownload": "Firmware Upgrade", "firmwareVersion": "Firmware Version", "fiveV": "5.5", "fiveVDC": "5v DC", "forty": "40", "forward": "Fwd", "fragmentCount": "Fragment Count", "fragmentRate": "Fragment Rate", "fromDateTime": "From Date Time", "fragmentSessionIndex": "Fragment Session Index", "fragmentSessionSetup": "Fragment Session", "fragmentStatusCompleted": "Fragment Status", "fragmentSize": "Fragment Size", "frequency": "Frequency (MHz)", "dBmV": "dBmV", "fwdInput": "FWD Input Test Point", "fwdOutput": "FWD Output Test Point", "gain": "<PERSON><PERSON>", "gatWayId": "Gateway ID", "ghZ": "GHz", "gwEUI": "GW EUI", "high": "High", "hWRev": "<PERSON><PERSON><PERSON>", "highSplit": "High Split", "home": "Home", "homeProvisionedAmplifiers": "Provisioned Amplifiers", "homeProvisioning": "Provision Amplifiers", "homeSettings": "Settings", "homeSiteRegions": "Sites / Regions", "identification": "Identification", "info": "Info", "ingressSwitch": "Ingress Switch", "initiatedSpectrum": "Initiated Spectrum Capture..., Please wait", "inputStage": "Input Stage", "interface": "Interface", "intermediateStage": "Intermediate Stage", "joinServer": "Join <PERSON>", "keysSynced": "<PERSON> Synced", "lE": "LE", "lastSeen": "Last Seen", "lastWeek": "Last Week (Sun - Sat)", "level": "Level", "lidIsClose": "Li<PERSON> is close", "lidIsOpen": "Lid is open", "lidOpen": "Lid open", "lidStatus": "Lid Status", "list": "List", "listView": "List View", "loading": "Loading...", "location": "Location", "locationLatLong": "Location\n(Lat, Long)", "locationNotSet": "Location not set", "locationValidation": "Enter valid location", "low": "Low", "login": "<PERSON><PERSON>", "logout": "Logout", "logoutConfirmation": "Are you sure that you want to logout?", "lowSplit": "Low Split", "manual": "Manual", "manualAlignment": "Manual Alignment", "map": "Map", "mapView": "Map View", "message": "Message", "messageIngressEnable": "Enabling the ingress may cause service disruption. Are you sure that you want to perform this operation?", "messageIngressDisable": "Are you sure, you want to change the ingress switch settings?", "mfgDate": "Mfg Date", "mhZ": "MHz", "mhz": "MHz", "missingSiteInfo": "Pending site info", "model": "Model", "modelNumber": "Model Numbers", "multicastGroupAddress": "Multicast Group Address", "multicastID": "Multicast ID", "multicastIP": "Multicast IP", "multicastIPAddress": "Multicast IP Address", "multicastOn": "Multicast On", "multicastSession": "Multicast Session", "msgAskConfirmationTitle": "Confirm ?", "msgAskConfirmationDesc": "Are you sure you want to continue?", "nDRSession": "NDR Session", "name": "Name", "ndfMulticastIp": "NDF \nMulticast IP", "ndfSession": "NDF Session", "ndfSessionId": "NDF\nSession ID", "ndrSessionId": "NDR \nSession ID", "networkServer": "Network \nServer", "networkServerIP": "Network Server IP", "networkServerPort": "Network Server Port", "never": "Never", "newAmpDetails": "New Amplifier Details", "no": "No", "noAlarms": "No Alarms", "noDataFound": "No Data Found", "nodeGW": "Node GW", "nodeGWDongleConnection": "Node GW/Dashboard Connection", "nodeGw": "Node GW", "offline": "Offline", "on": "On", "ok": "Ok", "online": "Online", "open": "Open", "outputStage": "Output Stage", "pavingTheWayTitle": "Paving the Way for Next-Generation Network Innovation", "pending": "Pending", "pilot": "Pilot", "pilot1": "Pilot 1:", "pilot2": "Pilot 2:", "pilot3": "Pilot 3:", "placement": "Placement", "pointConfig": "Point Config", "power": "Power :", "powerSupply": "Power Supply Information", "powerTable": "Power", "present": "Present", "pleaseWait": "Please wait", "provision": "Provision", "provisioned": "Provisioned", "provisionSelected": "Provision selected devices", "qlDashboard": "QL Dashboard", "quantumCentral": "Quantum Central", "quickSearch": "Quick Search", "rPD": "RPD", "rPD1": "RPD", "ref": "Ref", "refresh": "Refresh", "refreshRef": "Refresh Reference ", "removeFromMulticast": "Remove from Multicast", "restore": "Rest<PERSON>", "reverse": "Rev", "revert": "<PERSON><PERSON>", "redisIP": "Redis IP", "redundancyPct": "Redundancy Percentage", "redisPort": "Redis Port", "roadmap": "Road Map", "rpd": "#RPD", "rpdIp": "RPD IP(src)", "sE": "SE", "satellite": "Satellite", "save": "Save", "saveRevertInfoText": "Click Save to save the values permanently", "search": "Search..", "select": "Select", "selectAgc": "Select ALSC", "selectDateRange": "Select Date Range", "selectFile": "Select File", "selectSite": "Select Site", "selectType": "Select Type", "selectTypeLE": "Line Extender", "selectTypeSE": "System Amplifier", "selectUniversal": "Select Universal", "serial": "Serial", "serialNumber": "Serial Number", "sessionID": "Session ID", "showCompleted": "Show Completed", "showDiscovered": "Show Discovered", "signIn": "SignIn", "signOut": "Sign Out", "site": "Site", "siteID": "Site ID", "siteName": "Site Name", "siteRegions": "Sites/Regions", "siteUpdateSuccess": "Site Updated Successfully!!", "sitesAmplifiers": "#Amplifiers", "slop": "Slope", "somethingWentWrong": "Something went wrong!!", "sourceIP": "Source IP Address", "spectrum": "Spectrum", "spectrumSuccessMessage": "Spectrum Data Fetched Successfully", "start": "Start Freq:", "startAutoAlignment": "Start Auto Alignment", "startCapture": " Start Capture", "startDate": "Start Date", "startDownstream": "Start Downstream Alignment", "startTime": "Start Time", "startUpstream": "Start Upstream Alignment", "startEndDateValidation": "End date should not be earlier than the start date.", "state": "State", "status": "Status", "step": "Step-1 Freq:", "submit": "SUBMIT", "switchFW": "Switch FW", "switchToAuto": "Switch to Auto", "switchToManual": "Switch to Manual", "tableDeviceEUI": "Device EUI", "tableNoData": "No data", "tableType": "Type", "tag": "Tag", "tagToSite": "Not tagged to a site", "tags": "Tags", "telemetry": "Telemetry", "temp": "Temp ℃", "temperature": "Temperature (C)", "telemetryThreshold": "Telemetry Thresholds", "testPointConfig": "Test Point Config", "thermal": "Thermal", "temperatureTitle": "Temperature", "powerSource": "Power Source", "batteryAndCharging": "Battery/Charging Status", "chargingStatus": "Charging Status", "thresholdSuccess": "Threshold Value Updated Successfully!!", "thisFieldIsRequired": "This field is required", "thisWeek": "This Week (Sun - Today)", "threePointThreeV": "3.3", "threeVDc": "3.3v DC", "timestamp": "Timestamp", "totalFragments": "Total Fragments", "toDateTime": "To Date Time", "today": "Today", "topology": "Topology", "total": "Total", "totalProvisioned": "Total Provisioned", "totalSize": "Total Size", "transponder": "Transponder Information", "twentyFourDC": "24v DC", "twentyFourV": "24", "twoThirtyTwo": "232", "type": "Type", "uSAttn": "US ATTN", "uSEQ": "US EQ", "unProvisioned": "UnProvisioned", "units": "Units", "universalPlugin": "Universal Plugin", "unknown": "Unknown", "upTime": "Up Time", "update": "Update", "updateAmpDetails": "Update Amplifier Details", "updateSiteDetails": "Update Site Details", "updateSiteFailed": "Update Site Failed.", "updateSiteSuccess": "Update Site Successfully.", "updateSiteVLGW": "Update Site to VLGW", "updateSuccess": "Updated Successfully", "updateFailed": "Update Failed", "updateThreshold": "Update Threshold", "upload": "Upload", "upgradeRequestSent": "Upgrade Request Sent", "uploadDate": "Upload Date", "uploadFail": "File Error", "uploadFirmware": "Upload Firmware", "uploadNew": "Upload New", "uploadSuccess": "File uploaded successfully", "uploaded": "Uploaded", "upstream": "Upstream", "url": "URL", "usAlignCfg": "US Alignment", "usAlignment": "U/S Alignment", "usAlignmentFailed": "US alignment failed.", "user": "User", "userID": "User ID", "userInformation": "User Information", "usGainAdjust": "US Gain Adjust", "usSlopeAdjust": "US Slope Adjust", "vdd33v": "3.3 V", "vLGW": "#VLGW", "vLGW1": "VLGW", "vLGWInfo": "VLGW Info", "validEmail": "Domain not found", "vendor": "<PERSON><PERSON><PERSON>", "version": "Version", "virtualGW": "Virtual GW", "virtual_tilt": "Virtual Tilt (dB)", "vlgwIp": "VLGW Hostname", "vlgwHostname": "VLGW Hostname", "ipAddress": "IP Address", "port": "Port", "vlgwFSKStats": "FSKStats", "volt": "V", "wifiSSID": "WiFi SSID", "write": "Write", "yes": "Yes", "socketExceptionMessage": "Something went wrong", "errorRetrievingMessage": "Error Retrieving Data", "errorDeviceInfoMessage": "Failed to set device user info.", "refreshFailedMessage": "The refresh failed in ", "demodGoodPkts": "DeMod Good Packets", "demodBadPkts": "DeMod Bad Packets", "demodDroppedPkts": "DeMod Dropped Packets", "demodNotFoundPkts": "DeMod Not Found Packets", "demodDiagnostic": "<PERSON><PERSON><PERSON>", "demoInputLevel": "Demo Input Level", "modDiagnostic": "<PERSON><PERSON>", "modOutputLevel": "Mod Output Level", "modPkts": "Mod packets", "badChannels": "Bad Channels", "framesDropped": "Frames Dropped", "reset": "Reset", "resetDefault": "<PERSON><PERSON>", "enableManualMode": "Enable Manual Mode", "downloadSoftware": "Open Software Download Page", "services": "Services", "msgAskConfirmationAutoAlign": "Are you sure you want to perform auto-alignment?", "createDeployment": "Create Deployment", "associateDeployments": "Associate Deployments", "deviceCount": "<PERSON><PERSON>", "created": "Created", "id": "ID", "deployments": "Deployments", "process": "Progress", "proceed": "Proceed", "noDeviceFound": "No device found", "discard": "Discard", "duration": "Duration", "size": "Size (Bytes)", "upgrade": "Upgrade", "pleaseSelectFileType": "Please select type", "downloadProgress": "Download Progress", "deploymentStatus": "Deployment Status", "transponderFwVersion": "Transponder FW Version", "ampFwVersion": "Amplifier FW Version", "beforeVersion": "Before Version", "afterVersion": "After Version", "downloadInProgress": "Download In Progress", "downloadCompleted": "Download Completed", "readyForUpgrade": "Ready for Upgrade", "upgradeCompleted": "Upgrade Completed", "deploymentFailed": "Deployment Failed", "upgradeStatus": "Upgrade Status", "remark": "Remark", "upgradeAutoText": "Upgrade automatically after download.", "maxTabMessage": "A maximum of 6 tabs can be opened at a time.", "summaryStatus": "Summary Status", "softwareUpdate": "Software Update", "please": "Please", "click": "click", "hereToUpdateSentence": "here to update the software", "selectWifi": "Select a WiFi Network", "connectTo": "Connect to ", "passWord": "Password", "back": "Back", "connected": "Connected", "failedToConnect": "Failed to Connect", "ssid": "SSID", "connect": "Connect", "enterPassword": "Enter Password", "serviceStatus": "Service Status", "healthy": "Healthy", "configChannels": "Channel", "uploadJoinServer": "Upload CSV for Join Server", "channel": "No.", "enabled": "Enabled", "disabled": "Disabled", "upgradeConfirmationMessage": "Start deployment?", "createdOn": "Created On", "uploadAt": "Upload Date", "upgradeType": "Upgrade Type", "deleteSelected": "Delete Selected", "ndfDestIp": "NDF \nDest IP", "deviceWasUpgraded": "device was upgraded.", "devicesWereUpgraded": "devices were upgraded.", "chooseFile": "Choose <PERSON>", "noFileChosen": "No file chosen", "skipDevices": "<PERSON><PERSON>ces", "close": "Close", "clear": "Clear", "noWifiFound": "No wifi network found", "searchWifi": "Search wifi network", "wifiUplink": "WiFi UpLink", "connectedWith": "You are connected with", "connectedWithEthernet": "You are connected to network.", "notConnectedWith": "You are not connected to network.", "rssi": "RSSI", "fPort": "F-Port", "healthyServicesMessage": "Please Wait - Starting Services", "wifiStatusMessage": "Unable to check - Something went wrong", "connectionTimeOut": "Your WiFi connection may be lost. Please reconnect to the IP Address assigned to the Node GW", "gwID": "GW ID", "gwSetting": "Gateway Settings", "dsGain": "<PERSON>", "usRssi": "US RSSI", "backUpVersion": "Backup Version", "activeVersion": "Active Version", "msgSwitchBankReboot": "Are you sure want to confirm switch bank and reboot?", "device": "<PERSON><PERSON>", "stats": "Stats", "msgAskResetPktFwd": "Are you sure ?", "socketTimeOutMessage": "Please try later.", "changesAreNotSaved": "Changes are not Saved!", "confirmMessageConfiguration": "Do you want to continue?", "draftMessageForConfiguration": "Changes have not been saved. Please save or revert the draft.", "deleteMessage": "Do you want to delete?.", "configBitmask": "Config BitMask", "eTag": "Etag", "pilotPrimary": "Primary", "pilotBackup": "Backup", "pilotMeasuredPwr": "Pilot - Measured Power", "pilotMarker": "<PERSON><PERSON>", "alarmsBitmask": "Alarms BitMask", "gwNotFoundMessage": "GW ID not found.", "autoAlignSaveRevertMessage": "Do you want to Save Auto Alignment Changes?", "gatWayName": "Gateway Name", "swVersion": "SW Version", "hwVersion": "HW Version", "gwName": "GW Name", "apis": "APIs", "domain": "Domain", "tcp": "TCP", "gwDetail": "GW Detail", "operation": "Operation", "source_service": "Source Service", "application": "Application", "request_path": "Request Path", "request_method": "Request Method", "aux": "<PERSON><PERSON>", "main": "Main", "validEmailAddress": "Enter a valid email", "currantUser": "Current User", "neverSeen": "Never Seen", "nodeGWDSGain": "Node GW\nDS Gain", "totalDeploymentTime": "Total Deployment Time", "dBm": "dBm", "freq": "Freq", "providerNotFound": "Provider not found", "autoAlignMarkerPilots": "Auto Align Marker Pilots", "noData": "No data", "frequencyConfig": "Frequency Config", "openMode": "Op. Mode", "vDC": "Vdc", "vac": "VAC", "tempF": "Temp ℉", "switchBankAndReboot": "Switch Bank and Reboot", "na": "NA", "openStreetMap": "© OpenStreetMap ", "contributors": "contributors", "locationValidateMessage": "Enter valid location", "locationFieldLessThen33": "The field should be less than 33 characters", "invalidTokenMessage": "Invalid <PERSON>", "restrictSigninMessage": "<PERSON><PERSON><PERSON>", "newAmpProvider": "New Amplifier Provider", "updateAmpProvider": "Update Amplifier Provider", "applicationName": "Application Name", "code": "Code", "chirpstackAppName": "Chirpstack App Name", "createdSuccessfully": "Created Successfully", "autoJoin": "Auto Join", "autoJoinDisabled": "Auto Join Disabled", "deleteConfirmationMessage": "Are you sure you want to delete", "confirmDelete": "Delete?", "deleteSuccessfully": "Deleted Successfully", "key": "Key", "vendorCode": "Vendor Code", "createdAt": "Created At", "selectVendor": "<PERSON><PERSON><PERSON>", "transponderVersion": "Transponder Version", "ampVersion": "Amplifier Version", "missingKey": "Missing Key", "missingVendor": "Missing <PERSON><PERSON><PERSON>", "discoveredDevice": "Discovered Device", "newKeyVendor": "Add Custom Key", "addAutoBtn": "Generate", "domainMisMatch": "Domain mismatch", "invalidCodeFormat": "Invalid code format", "noFirmwareSelected": "Select firmware file", "searchByDeviceEUI": "Enter Device EUI to Filter", "alscEnabled": "ALSC Enabled", "accessDenied": "Access Denied", "accessDeniedMessage": "Direct web access is disabled", "contactAdministrator": "If you believe this is a mistake, please\ncontact your administrator", "layers": "Layers", "gateways": "Gateways", "deviceLinks": "<PERSON>ce <PERSON>s", "loadingDevices": "Loading devices...", "zoom": "Zoom", "devicesAndGatewaysShown": "{devices} devices and {gateways} gateways shown", "@devicesAndGatewaysShown": {"placeholders": {"devices": {"type": "int"}, "gateways": {"type": "int"}}}, "inventory": "Inventory", "product": "Type"}