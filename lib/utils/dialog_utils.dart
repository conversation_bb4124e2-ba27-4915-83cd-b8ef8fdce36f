import 'package:intl/intl.dart';
import 'package:lottie/lottie.dart';
import 'package:quantumlink_node/app_import.dart';

class DialogUtils {
  regularDialog(
    context,
    String title,
    String? subTitle,
    Widget middleWidget,
    VoidCallback? onSubmitPressed,
  ) {
    return showDialog(
      context: context,
      builder: (contextDialog) {
        return AlertDialog(
          surfaceTintColor: AppColorConstants.colorWhite,
          backgroundColor: AppColorConstants.colorWhite,
          insetPadding: EdgeInsets.zero,
          contentPadding: EdgeInsets.zero,
          titlePadding: EdgeInsets.zero,
          actionsPadding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
          title: Padding(
            padding:
                const EdgeInsets.only(left: 24, top: 24, right: 20, bottom: 5),
            child: Row(
              children: [
                SizedBox(
                  width: getSize(2),
                  height: getSize(8),
                ),
                CircleAvatar(
                  maxRadius: 18,
                  backgroundColor: AppColorConstants.colorLightBlue,
                  child: Icon(
                    size: 18,
                    Icons.check,
                    color: AppColorConstants.colorWhite,
                  ),
                ),
                SizedBox(
                  width: getSize(18),
                ),
                Expanded(
                  flex: 5,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      AppText(
                        title,
                        maxLines: 1,
                        style: TextStyle(
                            color: AppColorConstants.colorAppbar,
                            fontWeight: FontWeight.w700,
                            fontSize: getSize(25),
                            fontFamily: AppAssetsConstants.openSans),
                      ),
                      if (subTitle != null) ...[
                        AppText(
                          subTitle,
                          maxLines: 1,
                          style: TextStyle(
                              overflow: TextOverflow.clip,
                              color: AppColorConstants.colorAppbar,
                              fontWeight: FontWeight.w700,
                              fontSize: getSize(16),
                              fontFamily: AppAssetsConstants.openSans),
                        ),
                      ],
                    ],
                  ),
                ),
                const Spacer(),
                IconButton(
                    onPressed: () {
                      goBack();
                    },
                    icon: const Icon(Icons.close)),
              ],
            ),
          ),
          content: middleWidget,
          actions: [
            Container(
              color: AppColorConstants.colorWhiteShade,
              child: Row(
                children: [
                  const Spacer(),
                  Container(
                    padding:
                        const EdgeInsets.only(left: 16, bottom: 16, top: 10),
                    child: AppButton(
                      fontSize: 14,
                      buttonWidth: 50,
                      buttonHeight: 35,
                      fontFamily: AppAssetsConstants.openSans,
                      buttonName: S.of(contextDialog).cancel,
                      fontColor: AppColorConstants.colorBlackBlue,
                      onPressed: () {
                        goBack();
                      },
                      borderColor: AppColorConstants.colorBackgroundDark,
                      buttonColor: AppColorConstants.colorWhite1,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.only(
                        left: 16, right: 16, bottom: 16, top: 8),
                    child: AppButton(
                      fontSize: 14,
                      buttonWidth: 50,
                      buttonHeight: 35,
                      fontFamily: AppAssetsConstants.openSans,
                      buttonName: S.of(contextDialog).update,
                      onPressed: onSubmitPressed,
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  confirmationDialog(
      context,
      String title,
      String description,
      String btnSubmitTitle,
      String btnCancelTitle,
      VoidCallback? onSubmitPressed,
      VoidCallback? onCancelPressed,
      ) {
    return showDialog(
      context: context,
      builder: (contextDialog) {
        return AlertDialog(
          surfaceTintColor: AppColorConstants.colorWhite,
          backgroundColor: AppColorConstants.colorWhite,
          insetPadding: EdgeInsets.symmetric(horizontal: getSize(10)),
          contentPadding: EdgeInsets.zero,
          titlePadding: EdgeInsets.zero,
          actionsPadding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
          title: Padding(
            padding:
            const EdgeInsets.only(left: 24, top: 20, right: 24, bottom: 5),
            child: Row(
              children: [
                Icon(
                  size: 24,
                  Icons.info,
                  color: AppColorConstants.colorAppbar,
                ),
                SizedBox(
                  width: getSize(18),
                ),
                Expanded(
                  flex: 5,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      AppText(
                        title,
                        maxLines: 1,
                        style: TextStyle(
                            color: AppColorConstants.colorAppbar,
                            fontWeight: FontWeight.w700,
                            fontSize: getSize(20),
                            fontFamily: AppAssetsConstants.openSans),
                      ),
                    ],
                  ),
                ),
                const Spacer(),
                IconButton(
                    onPressed: () {
                      goBack();
                    },
                    icon: const Icon(Icons.close)),
              ],
            ),
          ),
          content: Padding(
            padding: const EdgeInsets.only(left: 24, top: 8, right: 24, bottom: 14),
            child: Container(
              width: MediaQuery.of(context).size.width * 0.3,
              child: AppText(
                description,
                style: TextStyle(
                    color: AppColorConstants.colorAppbar,
                    fontSize: getSize(14),
                    fontFamily: AppAssetsConstants.openSans),
              ),
            ),
          ),
          actions: [
            Container(
              color: AppColorConstants.colorWhiteShade,
              child: Row(
                children: [
                  const Spacer(),
                  Container(
                    padding:
                    const EdgeInsets.only(left: 16, bottom: 16, top: 10),
                    child: AppButton(
                      fontSize: 14,
                      buttonWidth: 45,
                      buttonHeight: 30,
                      fontFamily: AppAssetsConstants.openSans,
                      buttonName: btnCancelTitle,
                      fontColor: AppColorConstants.colorBlackBlue,
                      onPressed: (onCancelPressed != null)
                                     ? onCancelPressed
                                     : () { goBack(); },
                      borderColor: AppColorConstants.colorBackgroundDark,
                      buttonColor: AppColorConstants.colorWhite1,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.only(
                        left: 16, right: 16, bottom: 16, top: 8),
                    child: AppButton(
                      fontSize: 14,
                      buttonWidth: 45,
                      buttonHeight: 30,
                      fontFamily: AppAssetsConstants.openSans,
                      buttonName: btnSubmitTitle,
                      onPressed: onSubmitPressed,
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  telemetryThresholdDialog(
      context, String title, String? subTitle, Widget middleWidget,
      {VoidCallback? onSubmitPressed, VoidCallback? onResetDefaultPressed}) {
    return showDialog(
      context: context,
      builder: (contextDialog) {
        return AlertDialog(
          surfaceTintColor: AppColorConstants.colorWhite,
          backgroundColor: AppColorConstants.colorWhite,
          insetPadding: EdgeInsets.zero,
          contentPadding: EdgeInsets.zero,
          titlePadding: EdgeInsets.zero,
          actionsPadding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
          title: Padding(
            padding:
                const EdgeInsets.only(left: 24, top: 24, right: 20, bottom: 5),
            child: Row(
              children: [
                SizedBox(
                  width: getSize(2),
                  height: getSize(8),
                ),
                CircleAvatar(
                  maxRadius: 18,
                  backgroundColor: AppColorConstants.colorLightBlue,
                  child: Icon(
                    size: 18,
                    Icons.check,
                    color: AppColorConstants.colorWhite,
                  ),
                ),
                SizedBox(
                  width: getSize(18),
                ),
                Expanded(
                  flex: 5,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      AppText(
                        title,
                        maxLines: 1,
                        style: TextStyle(
                            color: AppColorConstants.colorAppbar,
                            fontWeight: FontWeight.w700,
                            fontSize: getSize(25),
                            fontFamily: AppAssetsConstants.openSans),
                      ),
                      if (subTitle != null) ...[
                        AppText(
                          subTitle,
                          maxLines: 1,
                          style: TextStyle(
                              overflow: TextOverflow.clip,
                              color: AppColorConstants.colorAppbar,
                              fontWeight: FontWeight.w700,
                              fontSize: getSize(16),
                              fontFamily: AppAssetsConstants.openSans),
                        ),
                      ],
                    ],
                  ),
                ),
                const Spacer(),
                IconButton(
                    onPressed: () {
                      goBack();
                    },
                    icon: const Icon(Icons.close)),
              ],
            ),
          ),
          content: middleWidget,
          actions: [
            Container(
              width: double.infinity,
              color: AppColorConstants.colorWhiteShade,
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.only(
                        left: 16, right: 16, bottom: 16, top: 8),
                    child: AppButton(
                      fontSize: 14,
                      buttonWidth: 100,
                      buttonHeight: 35,
                      fontFamily: AppAssetsConstants.openSans,
                      buttonName: S.of(contextDialog).resetDefault,
                      onPressed: onResetDefaultPressed,
                    ),
                  ),
                  const Spacer(),
                  Container(
                    padding:
                        const EdgeInsets.only(left: 16, bottom: 16, top: 10),
                    child: AppButton(
                      fontSize: 14,
                      buttonWidth: 50,
                      buttonHeight: 35,
                      fontFamily: AppAssetsConstants.openSans,
                      buttonName: S.of(contextDialog).cancel,
                      fontColor: AppColorConstants.colorBlackBlue,
                      onPressed: () {
                        goBack();
                      },
                      borderColor: AppColorConstants.colorBackgroundDark,
                      buttonColor: AppColorConstants.colorWhite1,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.only(
                        left: 16, right: 16, bottom: 16, top: 8),
                    child: AppButton(
                      fontSize: 14,
                      buttonWidth: 50,
                      buttonHeight: 35,
                      fontFamily: AppAssetsConstants.openSans,
                      buttonName: S.of(contextDialog).update,
                      onPressed: onSubmitPressed,
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}

class dialogBoxEdit extends StatelessWidget {
  const dialogBoxEdit({
    super.key,
    required this.controller,
    required this.title,
    required this.hintText,
    this.validator,
    this.isReadOnly = false,
    this.amplifierPageHelper = null,
    this.isMapIconDisplay = false,
    this.locationoOnPressed
  });

  final AmplifierPageHelper? amplifierPageHelper;
  final String title, hintText;
  final TextEditingController controller;
  final bool isReadOnly;
  final bool isMapIconDisplay;
  final FormFieldValidator? validator;
  final VoidCallback? locationoOnPressed;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: AppText(title,
              style: TextStyle(
                  fontFamily: AppAssetsConstants.openSans,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppColorConstants.colorBlack)),
        ),
        const SizedBox(
          width: 20,
        ),
        Expanded(
          flex: 5,
          child: Row(
            children: [
              Expanded(
                flex:3,
                child: AppTextFormField(
                  focusedBorderColor: AppColorConstants.colorPrimary,
                  enabledBorderColor: AppColorConstants.colorH2.withOpacity(0.5),
                  fontFamily: AppAssetsConstants.openSans,
                  contentPadding: const EdgeInsets.fromLTRB(10, 13, 10, 13),
                  hintText: hintText,
                  hintTextColor: AppColorConstants.colorH2.withOpacity(0.5),
                  hintFontSize: 14,
                  readOnly: isReadOnly,
                  controller: controller,
                  textStyle: (isReadOnly)
                      ? const TextStyle(
                          color: Colors.black45,
                          fontSize: 15,
                          fontWeight: FontWeight.w500,
                        )
                      : styleOfTextField,
                  maxLines: 1,
                  textInputType: TextInputType.text,
                  validator: validator ??
                      (value) {
                        if (value!.isEmpty && !isReadOnly) {
                          return S.of(context).thisFieldIsRequired;
                        } else {
                          return null;
                        }
                      },
                ),
              ),
              (!isMapIconDisplay)
                  ? Container()
                  : IconButton(
                      icon: Icon(Icons.my_location_outlined,color: AppColorConstants.colorLightBlue,),
                      onPressed: locationoOnPressed,
                    )
            ],
          ),
        )
      ],
    );
  }
}

class dialogBoxThresholdEdit extends StatelessWidget {
  const dialogBoxThresholdEdit({
    super.key,
    required this.defaultLow,
    required this.defaultHigh,
    required this.controller1,
    required this.controller2,
    required this.title,
    required this.hintText,
    this.validator,
    this.isReadOnly = false,
  });

  final String title, hintText;
  final TextEditingController controller1;
  final TextEditingController controller2;
  final dynamic defaultLow, defaultHigh;
  final bool isReadOnly;
  final FormFieldValidator? validator;
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: AppText(title,
              style: TextStyle(
                  fontFamily: AppAssetsConstants.openSans,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppColorConstants.colorBlack)),
        ),
        const SizedBox(
          width: 20,
        ),
        Expanded(
          flex: 5,
          child: Row(
            children: [
              Expanded(
                flex: 3,
                child: AppTextFormField(
                  focusedBorderColor: AppColorConstants.colorPrimary,
                  enabledBorderColor:
                      AppColorConstants.colorH2.withOpacity(0.5),
                  fontFamily: AppAssetsConstants.openSans,
                  contentPadding: const EdgeInsets.fromLTRB(10, 13, 10, 13),
                  hintText: hintText,
                  hintTextColor: AppColorConstants.colorH2.withOpacity(0.5),
                  hintFontSize: 14,
                  readOnly: isReadOnly,
                  controller: controller1,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'[0-9-.]')),
                  ],
                  textStyle: (isReadOnly)
                      ? const TextStyle(
                          color: Colors.black45,
                          fontSize: 15,
                          fontWeight: FontWeight.w400,
                        )
                      : styleOfTextField,
                  maxLines: 1,
                  textInputType: TextInputType.text,
                  validator: validator ??
                      (value) {
                        if (value!.isEmpty && !isReadOnly) {
                          return S.of(context).thisFieldIsRequired;
                        } else if (double.parse(value) >
                            double.parse(controller2.text)) {
                          return "Lower than high";
                        } else {
                          return null;
                        }
                      },
                ),
              ),
              SizedBox(
                width: 4,
              ),
              Expanded(
                flex: 3,
                child: AppTextFormField(
                  focusedBorderColor: AppColorConstants.colorPrimary,
                  enabledBorderColor:
                      AppColorConstants.colorH2.withOpacity(0.5),
                  fontFamily: AppAssetsConstants.openSans,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'[0-9-.]')),
                  ],
                  contentPadding: const EdgeInsets.fromLTRB(10, 13, 10, 13),
                  hintText: hintText,
                  hintTextColor: AppColorConstants.colorH2.withOpacity(0.5),
                  hintFontSize: 14,
                  readOnly: isReadOnly,
                  controller: controller2,
                  textStyle: (isReadOnly)
                      ? const TextStyle(
                          color: Colors.black45,
                          fontSize: 15,
                          fontWeight: FontWeight.w400,
                        )
                      : styleOfTextField,
                  maxLines: 1,
                  textInputType: TextInputType.text,
                  validator: validator ??
                      (value) {
                        if (value!.isEmpty && !isReadOnly) {
                          return S.of(context).thisFieldIsRequired;
                        } else if (double.parse(controller1.text) >
                            double.parse(value)) {
                          return "Greater than low";
                        } else {
                          return null;
                        }
                      },
                ),
              ),
              Flexible(
                flex: 1,
                child: IconButton(
                    onPressed: () {
                      controller1.text = divide1000(defaultLow);
                      controller2.text = divide1000(defaultHigh);
                    },
                    icon: AppImageAsset(
                      image: AppAssetsConstants.icReset,
                      color: AppColorConstants.colorPrimary,
                    )),
              )
            ],
          ),
        )
      ],
    );
  }
}

class dialogBoxThresholdHighLowTitle extends StatelessWidget {
  const dialogBoxThresholdHighLowTitle({
    super.key,
    required this.title,
    required this.title2,
    this.isReadOnly = false,
  });

  final String title, title2;
  final bool isReadOnly;
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: Container(),
        ),
        const SizedBox(
          width: 20,
        ),
        Expanded(
          flex: 5,
          child: Row(
            children: [
              Expanded(
                flex: 3,
                child: AppText(title,
                    style: TextStyle(
                        fontFamily: AppAssetsConstants.openSans,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: AppColorConstants.colorBlack)),
              ),
              SizedBox(
                width: 4,
              ),
              Expanded(
                  flex: 3,
                  child: AppText(title2,
                      style: TextStyle(
                          fontFamily: AppAssetsConstants.openSans,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: AppColorConstants.colorBlack))),
              Flexible(flex: 1, child: Container())
            ],
          ),
        )
      ],
    );
  }
}

class dialogBoxTelemetryExport extends StatelessWidget {
  const dialogBoxTelemetryExport({
    super.key,
      required this.controller,
      required this.title,
      required this.hintText,
      required this.dateHelper,
      this.validator,
      this.isReadOnly = false,
      this.onChanged});

  final ValueChanged<String>? onChanged;
  final DateRangeFilterHelper dateHelper;
  final String title, hintText;
  final TextEditingController controller;
  final bool isReadOnly;
  final FormFieldValidator? validator;
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: AppText(title,
              style: TextStyle(
                  fontFamily: AppAssetsConstants.openSans,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppColorConstants.colorBlack)),
        ),
        const SizedBox(
          width: 20,
        ),
        Expanded(
          flex: 5,
          child: AppTextFormField(
            focusedBorderColor: AppColorConstants.colorPrimary,
            enabledBorderColor: AppColorConstants.colorH2.withOpacity(0.5),
            fontFamily: AppAssetsConstants.openSans,
            contentPadding: const EdgeInsets.fromLTRB(10, 13, 10, 13),
            hintText: hintText,
            hintTextColor: AppColorConstants.colorH2.withOpacity(0.5),
            hintFontSize: 14,
            readOnly: isReadOnly,
            controller: controller,
            onChanged: onChanged,
            onTap: () async {
              DateTime? startDate = await dateHelper.selectDate(context);
              if (startDate != null) {
                TimeOfDay? timeOfDay = await dateHelper.selectTime(context);
                if (timeOfDay != null) {
                  var date = DateFormat(AppStringConstants.FORMATDATETIME1)
                      .format(startDate);
                  String time = timeOfDay.format(context);
                  controller.text = "$date $time";
                }
              }
            },
            textStyle: (isReadOnly)
                ? const TextStyle(
                    color: Colors.black45,
                    fontSize: 15,
                    fontWeight: FontWeight.w400,
                  )
                : styleOfTextField,
            maxLines: 1,
            textInputType: TextInputType.text,
            validator: validator ??
                (value) {
                  if (value!.isEmpty && !isReadOnly) {
                    return S.of(context).thisFieldIsRequired;
                  } else {
                    return null;
                  }
                },
          ),
        )
      ],
    );
  }
}

Stream<BuildContext?> progressDialog(BuildContext context) {
  StreamController<BuildContext>? controller;
  makeProgress() {
    Future.delayed(Duration.zero, () {
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            controller?.add(context);
            return WillPopScope(
              onWillPop: () async {
                return false;
              },
              child: Dialog(
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                child: Container(
                  height: MediaQuery.of(context).size.height * 0.4,
                  width: MediaQuery.of(context).size.width * 0.3,
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          alignment: Alignment.center,
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(getSize(3)),
                            child: AppImageAsset(
                              image: AppAssetsConstants.appLogo,
                              fit: BoxFit.contain,
                              width: getSize(170),
                              height: getSize(170),
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 10,
                        ),
                        Lottie.asset(
                          height: 50,
                          AppAssetsConstants.splashLoaderAnimation,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          });
    });
  }

  controller = StreamController<BuildContext>(onListen: makeProgress());
  return controller.stream;
}

Future<bool?> showConfirmationDialog(BuildContext context, String title, String desc) async {
  return showDialog<bool>(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        surfaceTintColor: AppColorConstants.colorWhite,
        backgroundColor: AppColorConstants.colorWhite,
        insetPadding: EdgeInsets.zero,
        contentPadding: EdgeInsets.zero,
        titlePadding: EdgeInsets.zero,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
        title: getCustomAppBarWithClose(title),
        content: StatefulBuilder(builder: (context, snapshot) {
          return Container(
              width: MediaQuery.of(context).size.width * 0.25, // Adjust the width as needed
              height: MediaQuery.of(context).size.height * 0.1,
              padding: const EdgeInsets.all(12),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AppText(
                    desc,
                    style: TextStyle(
                        fontWeight: getMediumFontWeight(),
                        fontSize: getSize(16),
                        fontFamily: AppAssetsConstants.openSans),
                  ),
                  SizedBox(height: getSize(10)),
                ],
              ));
        }),
        actions: [
        Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          AppButton(
            borderColor: Colors.grey.withOpacity(0.5),
            buttonHeight: 20,
            buttonColor: Colors.grey.withOpacity(0.5),
            buttonName: S.of(context).no,
            fontFamily: AppAssetsConstants.openSans,
            fontColor: AppColorConstants.colorBlackBlue,
            onPressed: () => Navigator.of(context).pop(false),
          ),
          const SizedBox(width: 16),
          AppButton(
            fontFamily: AppAssetsConstants.openSans,
            buttonHeight: 20,
            buttonName: S.of(context).yes,
            onPressed: () => Navigator.of(context).pop(true),
          ),
        ],
      ),
        ],

      );
    },
  );
}

Future<bool?> confirmationDialogConfiguration(
    BuildContext context, {
      required Future<void> Function(bool isRevert) callBack,
    }) async {
  ApiStatus status =  ApiStatus.initial;
  return showDialog<bool>(
    context: context,
    builder: (BuildContext context) {
      return StatefulBuilder(builder: (context, setState) {
        return AlertDialog(
          surfaceTintColor: AppColorConstants.colorWhite,
          backgroundColor: AppColorConstants.colorWhite,
          insetPadding: EdgeInsets.symmetric(horizontal: getSize(10)),
          contentPadding: EdgeInsets.zero,
          titlePadding: EdgeInsets.zero,
          actionsPadding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
          title: Padding(
            padding: const EdgeInsets.only(left: 24, top: 20, right: 24, bottom: 5),
            child: Row(
              children: [
                Icon(Icons.info, size: 24, color: AppColorConstants.colorAppbar),
                SizedBox(width: getSize(18)),
                Expanded(
                  child: AppText(
                    S.of(context).changesAreNotSaved,
                    maxLines: 1,
                    style: TextStyle(
                      color: AppColorConstants.colorAppbar,
                      fontWeight: FontWeight.w700,
                      fontSize: getSize(20),
                      fontFamily: AppAssetsConstants.openSans,
                    ),
                  ),
                ),
              ],
            ),
          ),
          content: Padding(
            padding: const EdgeInsets.only(left: 24, top: 8, right: 24, bottom: 14),
            child: Container(
              width: MediaQuery.of(context).size.width * 0.3,
              child: AppText(
                S.of(context).confirmMessageConfiguration,
                style: TextStyle(
                  color: AppColorConstants.colorAppbar,
                  fontSize: getSize(14),
                  fontFamily: AppAssetsConstants.openSans,
                ),
              ),
            ),
          ),
          actions: <Widget>[
            Container(
              color: AppColorConstants.colorWhiteShade,
              child: Row(
                children: [
                  const Spacer(),
                  Padding(
                    padding: const EdgeInsets.only(left: 16, bottom: 16, top: 10),
                    child: AppButton(
                      fontSize: 14,
                      buttonWidth: 45,
                      buttonHeight: 30,
                      fontFamily: AppAssetsConstants.openSans,
                      buttonName: S.of(context).cancel,
                      fontColor: AppColorConstants.colorBlackBlue,
                      onPressed: () async {
                        if(status == ApiStatus.loading)return;
                        callBack.call(false);
                        Navigator.pop(context); // return false
                      },
                      borderColor: AppColorConstants.colorBackgroundDark,
                      buttonColor: AppColorConstants.colorWhite1,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(
                        left: 10, right: 10, bottom: 16, top: 8),
                    child: AppButton(
                      fontSize: 14,
                      buttonWidth: 60,
                      buttonHeight: 30,
                      fontFamily: AppAssetsConstants.openSans,
                      buttonName: S.of(context).discard,
                      loadingStatus: status,
                      onPressed: () async {
                        if(status == ApiStatus.loading)return;
                        setState(() => status = ApiStatus.loading);
                        await callBack.call(true);
                        setState(() => status = ApiStatus.success);
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      });
    },
  );
}

