

import 'package:flutter/material.dart';
import 'package:quantumlink_node/app/ui/app_screen_layout.dart';

import '../app/constants/app_constant.dart';
import '../app/helper/app_ui_helper.dart';
import '../app/ui/app_text.dart';

class ComponentsBuilder {
  static Widget get1RowView({required List<Widget> children}) {
    return ScreenLayoutTypeBuilder(
      builder: (context, screenType, constraints) {
        return Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: children,
        );
      },
    );
  }

  static Widget getPageAppBar(String title) {
    return Container(
      height: getSize(70),
      decoration: BoxDecoration(
          color: AppColorConstants.colorAppbar,
          borderRadius: BorderRadius.only(topRight: Radius.circular(getSize(7)), topLeft: Radius.circular(getSize(7)))),
      alignment: Alignment.centerLeft,
      padding: EdgeInsets.only(left: getSize(16)),
      child: AppText(
        title,
        style: TextStyle(fontWeight: getMediumFontWeight(), fontSize: getSize(22), color: AppColorConstants.colorWhite),
      ),
    );
  }

  static Widget getTooltip({required String title,required Widget child}){
    return Tooltip(verticalOffset: -40,
      message: title ,
      triggerMode: TooltipTriggerMode.tap,
      child: child,
    );
  }
}