import 'package:json_annotation/json_annotation.dart';

part 'dashboard.g.dart';

@JsonSerializable()
class DashboardModel {
  @Json<PERSON>ey(name: 'amplifier_with_alarms')
   dynamic amplifierWithAlarms;
  @J<PERSON><PERSON>ey(name: 'nodegw_dongle_connection')
   dynamic nodegwDongleConnection;
  @JsonKey(name: 'sites')
   dynamic sites;
   dynamic vlgws;
   dynamic rpds;
   @Json<PERSON>ey(name: 'active_amplifiers')
   dynamic activeAmplifiers;
   @Json<PERSON>ey(name: 'vlgws_without_site')
   dynamic vlgwsWithoutSite;
   @J<PERSON><PERSON>ey(name: 'rpds_without_site')
   dynamic rpdsWithoutSite;
   @ Json<PERSON>ey(name: 'amps_without_site')
   dynamic ampsWithoutSite;

  DashboardModel({
    this.amplifierWithAlarms,
    this.nodegwDongleConnection,
    this.sites,
    this.vlgws,
    this.rpds,
    this.activeAmplifiers,
    this.vlgwsWithoutSite,
    this.rpdsWithoutSite,
    this.ampsWithoutSite
  });

  factory DashboardModel.fromJson(Map<String, dynamic> json) => _$DashboardModelFromJson(json);

  Map<String, dynamic> toJson() => _$DashboardModelToJson(this);
  DashboardModel.empty();
}
@JsonSerializable()
class NodeBatteryInfoItem {
  @JsonKey(name: "bt_degC")
  dynamic btDegC;
  @JsonKey(name: "v_bat_mV")
  dynamic vBatMV;
  @JsonKey(name: "sv_mV")
  dynamic svMV;
  @JsonKey(name: "v_bus_mV")
  dynamic vBusMV;
  @JsonKey(name: "power_source")
  dynamic powerSource;
  @JsonKey(name: "charging_current_mA")
  dynamic chargingCurrentMA;
  @JsonKey(name: "fully_charged")
  dynamic fullyCharged;
  @JsonKey(name: "charging_current_dcp_mA")
  dynamic chargingCurrentDcpMA;
  @JsonKey(name: "battery_status")
  dynamic batteryStatus;

  NodeBatteryInfoItem({
    this.btDegC,
    this.vBatMV,
    this.svMV,
    this.vBusMV,
    this.powerSource,
    this.chargingCurrentMA,
    this.fullyCharged,
    this.chargingCurrentDcpMA,
    this.batteryStatus,
  });

  factory NodeBatteryInfoItem.fromJson(Map<String, dynamic> json) => _$NodeBatteryInfoItemFromJson(json);

  Map<String, dynamic> toJson() => _$NodeBatteryInfoItemToJson(this);
  NodeBatteryInfoItem.empty();
}
@JsonSerializable()
class NodeIdInfoItem {
  @JsonKey(name: "gweui")
  dynamic gwEui;
  dynamic model;
  @JsonKey(name: "fw_verion")
  dynamic fwVersion;
  @JsonKey(name: "bluetooth_id")
  dynamic bluetoothId;
  @JsonKey(name: "wifi_ssid")
  dynamic wifiSsid;

  NodeIdInfoItem({
    this.gwEui,
    this.model,
    this.fwVersion,
    this.bluetoothId,
    this.wifiSsid,
  });

  factory NodeIdInfoItem.fromJson(Map<String, dynamic> json) => _$NodeIdInfoItemFromJson(json);

  Map<String, dynamic> toJson() => _$NodeIdInfoItemToJson(this);
  NodeIdInfoItem.empty();
}