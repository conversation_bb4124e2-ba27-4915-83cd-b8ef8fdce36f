import 'package:json_annotation/json_annotation.dart';

part 'diagnostics.g.dart'; // Generated file

@JsonSerializable()
class DiagnosticsDataModel {
  dynamic id;
  dynamic time;
  dynamic description;
  Body? body;
  Properties? properties;

  DiagnosticsDataModel(this.id, this.time, this.description, this.body, this.properties);

  factory DiagnosticsDataModel.fromJson(Map<String, dynamic> json) => _$DiagnosticsDataModelFromJson(json);
  Map<String, dynamic> toJson() => _$DiagnosticsDataModelToJson(this);
}

@JsonSerializable()
class Body {
  @JsonKey(name: 'phy_payload')
  PhyPayload? phyPayload;
  @JsonKey(name: 'tx_info')
  TxInfo ?txInfo;
  @JsonKey(name: 'rx_info')
  List<RxInfo>? rxInfo;

  Body(this.phyPayload, this.txInfo, this.rxInfo);

  factory Body.fromJson(Map<String, dynamic> json) => _$Body<PERSON>rom<PERSON>son(json);
  Map<String, dynamic> toJson() => _$BodyTo<PERSON>son(this);
}

@JsonSerializable()
class RxInfo {
  dynamic context;
  @JsonKey(name: "crcStatus")
  dynamic crcStatus;
  @JsonKey(name: "gatewayId")
  dynamic gatewayId;
  @JsonKey(name: "gwTime")
  dynamic gwTime;
  dynamic location;
  dynamic metadata;
  @JsonKey(name: "nsTime")
  dynamic nsTime;
  dynamic rssi;
  dynamic snr;
  @JsonKey(name: "timeSinceGpsEpoch")
  dynamic timeSinceGpsEpoch;
  @JsonKey(name: "uplinkId")
  dynamic uplinkId;

  RxInfo(
      this.context,
      this.crcStatus,
      this.gatewayId,
      this.gwTime,
      this.location,
      this.metadata,
      this.nsTime,
      this.rssi,
      this.snr,
      this.timeSinceGpsEpoch,
      this.uplinkId,
      );

  factory RxInfo.fromJson(Map<String, dynamic> json) => _$RxInfoFromJson(json);
  Map<String, dynamic> toJson() => _$RxInfoToJson(this);
}

@JsonSerializable()
class PhyPayload {
  dynamic mhdr;
  List<int>? mic;
  dynamic payload;

  PhyPayload(this.mhdr, this.mic, this.payload);

  factory PhyPayload.fromJson(Map<String, dynamic> json) => _$PhyPayloadFromJson(json);
  Map<String, dynamic> toJson() => _$PhyPayloadToJson(this);
}

@JsonSerializable()
class TxInfo {
  dynamic frequency;
  dynamic modulation;

  TxInfo(this.frequency, this.modulation);

  factory TxInfo.fromJson(Map<String, dynamic> json) => _$TxInfoFromJson(json);
  Map<String, dynamic> toJson() => _$TxInfoToJson(this);
}


@JsonSerializable()
class Properties {
  @JsonKey(name: 'DevEUI')
  dynamic devEUI;
  @JsonKey(name: 'Gateway ID')
  dynamic gatewayID;
  @JsonKey(name: 'DevAddr')
  dynamic devAddr;

  Properties(this.devEUI, this.gatewayID, this.devAddr);

  factory Properties.fromJson(Map<String, dynamic> json) => _$PropertiesFromJson(json);
  Map<String, dynamic> toJson() => _$PropertiesToJson(this);
}

