import 'package:json_annotation/json_annotation.dart';

part 'auditlog.g.dart';

@JsonSerializable()
class AuditLogResponse {
  List<AuditLogData>? data;
  AuditLogMeta? meta;

  AuditLogResponse({
    this.data,
    this.meta,
  });

  factory AuditLogResponse.fromJson(Map<String, dynamic> json) => _$AuditLogResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AuditLogResponseToJson(this);
}

@JsonSerializable()
class AuditLogData {
  @JsonKey(name: 'dev_eui')
  dynamic devEui;
  @Json<PERSON>ey(name: 'device_type')
  dynamic deviceType;
  dynamic operation;
  dynamic message;
  @Json<PERSON>ey(name: 'source_service')
  dynamic sourceService;
  dynamic application;
  @JsonKey(name: 'request_path')
  dynamic requestPath;
  @JsonKey(name: 'request_method')
  dynamic requestMethod;
  @Json<PERSON>ey(name: 'user_email')
  dynamic userEmail;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'user_domain')
  dynamic userDomain;
  dynamic timestamp;

  AuditLogData({
    this.devEui,
    this.deviceType,
    this.operation,
    this.message,
    this.sourceService,
    this.application,
    this.requestPath,
    this.requestMethod,
    this.userEmail,
    this.userDomain,
    this.timestamp,
  });

  factory AuditLogData.fromJson(Map<String, dynamic> json) => _$AuditLogDataFromJson(json);

  Map<String, dynamic> toJson() => _$AuditLogDataToJson(this);
}

@JsonSerializable()
class AuditLogUserResponse {
  List<AuditLogUserData>? data;
  AuditLogMeta? meta;

  AuditLogUserResponse({
    this.data,
    this.meta,
  });

  factory AuditLogUserResponse.fromJson(Map<String, dynamic> json) => _$AuditLogUserResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AuditLogUserResponseToJson(this);
}

@JsonSerializable()
class AuditLogUserData {
  dynamic operation;
  dynamic message;
  @JsonKey(name: 'ip_address')
  dynamic ipAddress;
  @JsonKey(name: 'request_path',includeToJson: false)
  dynamic requestPath;
  @JsonKey(name: 'request_method',includeToJson: false)
  dynamic requestMethod;
  @JsonKey(name: 'source_service')
  dynamic sourceService;
  dynamic application;
  @JsonKey(name: 'user_email')
  dynamic userEmail;
  @JsonKey(name: 'user_domain',includeToJson: false)
  dynamic userDomain;
  @JsonKey(includeToJson: false)
  dynamic timestamp;

  AuditLogUserData({
    this.operation,
    this.message,
    this.ipAddress,
    this.requestPath,
    this.requestMethod,
    this.sourceService,
    this.application,
    this.userEmail,
    this.userDomain,
    this.timestamp,
  });

  factory AuditLogUserData.fromJson(Map<String, dynamic> json) => _$AuditLogUserDataFromJson(json);

  Map<String, dynamic> toJson() => _$AuditLogUserDataToJson(this);
}

@JsonSerializable()
class AuditLogMeta {
  @JsonKey(name: 'total_records')
  dynamic totalRecords;
  dynamic offset;
  dynamic limit;
  @JsonKey(name: 'total_pages')
  dynamic totalPages;
  @JsonKey(name: 'records_on_page')
  dynamic recordsOnPage;
  @JsonKey(name: 'filters_applied')
  final AuditLogFilters? filtersApplied;

  AuditLogMeta({
    this.totalRecords,
    this.offset,
    this.limit,
    this.totalPages,
    this.recordsOnPage,
    this.filtersApplied,
  });

  factory AuditLogMeta.fromJson(Map<String, dynamic> json) => _$AuditLogMetaFromJson(json);

  Map<String, dynamic> toJson() => _$AuditLogMetaToJson(this);
}

@JsonSerializable()
class AuditLogFilters {
  @JsonKey(name: 'user_email')
  dynamic userEmail;
  @JsonKey(name: 'user_domain')
  dynamic userDomain;
  @JsonKey(name: 'dev_eui')
  dynamic devEui;
  @JsonKey(name: 'device_type')
  dynamic deviceType;
  dynamic operation;
  dynamic message;
  @JsonKey(name: 'source_service')
  dynamic sourceService;
  dynamic application;
  @JsonKey(name: 'request_path')
  dynamic requestPath;
  @JsonKey(name: 'request_method')
  dynamic requestMethod;
  @JsonKey(name: 'start_time')
  dynamic startTime;
  @JsonKey(name: 'end_time')
  dynamic endTime;

  AuditLogFilters({
    this.userEmail,
    this.userDomain,
    this.devEui,
    this.deviceType,
    this.operation,
    this.message,
    this.sourceService,
    this.application,
    this.requestPath,
    this.requestMethod,
    this.startTime,
    this.endTime,
  });

  factory AuditLogFilters.fromJson(Map<String, dynamic> json) => _$AuditLogFiltersFromJson(json);

  Map<String, dynamic> toJson() => _$AuditLogFiltersToJson(this);
}
