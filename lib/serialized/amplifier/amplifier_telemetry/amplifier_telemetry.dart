import 'package:json_annotation/json_annotation.dart';
part 'amplifier_telemetry.g.dart';
@JsonSerializable()
class TelemetryData {

  dynamic message;
  List<TelemetryItem>? result;

  TelemetryData({

    required this.message,
    required this.result,
  });

  factory TelemetryData.fromJson(Map<String, dynamic> json) => _$TelemetryDataFromJson(json);

  Map<String, dynamic> toJson() => _$TelemetryDataToJson(this);
  TelemetryData.empty();
}

@JsonSerializable()
class TelemetryItem {
  @Json<PERSON>ey(name: "ac_current_1")
  dynamic acCurrent1;
  dynamic temperature;
  @Json<PERSON>ey(name: "ac_voltage")
  dynamic acVoltage;
  dynamic tamper;
  @Json<PERSON>ey(name: "diplexer_id")
  dynamic diplexerId;
  dynamic id;
  dynamic timestamp;
  @Json<PERSON>ey(name: "ac_current_2")
  dynamic acCurrent2;
  @<PERSON>son<PERSON>ey(name: "detout_agc")
  dynamic detoutAgc;
  @<PERSON><PERSON><PERSON><PERSON>(name: "us_gain_adjust")
  dynamic usGainAdjust;
  @J<PERSON><PERSON><PERSON>(name: "us_slope_adjust")
  dynamic usSlopAdjust;
  @Json<PERSON>ey(name: "ds_gain_adjust")
  dynamic dsGainAdjust;
  @JsonKey(name: "ds_slope_adjust")
  dynamic dsSlopAdjust;
  @JsonKey(name: "rev_eq_plug_in_id")
  dynamic revEqPlugInId;
  @JsonKey(name: "device_eui")
  dynamic deviceEui;
  @JsonKey(name: "pilot_backup_0")
  dynamic pilotBackup0;
  @JsonKey(name: "pilot_backup_1")
  dynamic pilotBackup1;
  @JsonKey(name: "pilot_measured_pwr_0")
  dynamic pilotMeasuredPwr0;
  @JsonKey(name: "pilot_measured_pwr_1")
  dynamic pilotMeasuredPwr1;
  @JsonKey(name: "pilot_primary_0")
  dynamic pilotPrimary0;
  @JsonKey(name: "pilot_primary_1")
  dynamic pilotPrimary1;
  @JsonKey(name: "pilot_backup_level_0")
  dynamic pilotBackupLevel0;
  @JsonKey(name: "pilot_backup_level_1")
  dynamic pilotBackupLevel1;
  @JsonKey(name: "pilot_primary_level_0")
  dynamic pilotPrimaryLevel0;
  @JsonKey(name: "pilot_primary_level_1")
  dynamic pilotPrimaryLevel1;
  @JsonKey(name: "pilot_marker_0")
  dynamic pilotMarker0;
  @JsonKey(name: "pilot_marker_1")
  dynamic pilotMarker1;
  @JsonKey(name: "pilot_marker_2")
  dynamic pilotMarker2;
  @JsonKey(name: "pilot_marker_3")
  dynamic pilotMarker3;
  @JsonKey(name: "pilot_marker_level_0")
  dynamic pilotMarkerLevel0;
  @JsonKey(name: "pilot_marker_level_1")
  dynamic pilotMarkerLevel1;
  @JsonKey(name: "pilot_marker_level_2")
  dynamic pilotMarkerLevel2;
  @JsonKey(name: "pilot_marker_level_3")
  dynamic pilotMarkerLevel3;
  @JsonKey(name: "etag")
  dynamic etag;
  @JsonKey(name: "up_time")
  dynamic upTime;
  @JsonKey(name: "alarm_flags")
  dynamic alarmFlags;
  @JsonKey(name: "auto_mode_enabled")
  bool? autoModeEnabled;
  @JsonKey(name: "auto_mode_interval")
  int? autoModeInterval;
  @JsonKey(name: "config_bitmask")
  dynamic configBitmask;
  @JsonKey(name: "dc_5v")
  dynamic dc5v;
  @JsonKey(name: "dc_8v", defaultValue: 0)
  dynamic dc8v;
  @JsonKey(name: "dc_24v")
  dynamic dc24v;
  @JsonKey(name: "vdd_3_3v")
  dynamic vdd33v;


  //TODO: add dc8v afterwards
  TelemetryItem({
    this.acCurrent1,
    this.temperature,
    this.acVoltage,
    this.tamper,
    this.diplexerId,
    this.id,
    this.timestamp,
    this.acCurrent2,
    this.detoutAgc,
    this.usGainAdjust,
    this.usSlopAdjust,
    this.dsGainAdjust,
    this.dsSlopAdjust,
    this.revEqPlugInId,
    this.deviceEui,
    this.pilotBackup0,
    this.pilotBackup1,
    this.pilotMeasuredPwr0,
    this.pilotMeasuredPwr1,
    this.pilotPrimary0,
    this.pilotPrimary1,
    this.pilotBackupLevel0,
    this.pilotBackupLevel1,
    this.pilotPrimaryLevel0,
    this.pilotPrimaryLevel1,
    this.pilotMarker0,
    this.pilotMarker1,
    this.pilotMarkerLevel0,
    this.pilotMarkerLevel1,
    this.etag,
    this.upTime,
    this.alarmFlags,
    this.autoModeEnabled,
    this.autoModeInterval,
    this.configBitmask,
    this.dc24v,
    this.dc5v,
    this.dc8v,
    this.vdd33v,
  });

  factory TelemetryItem.fromJson(Map<String, dynamic> json) =>
      _$TelemetryItemFromJson(json);

  Map<String, dynamic> toJson() => _$TelemetryItemToJson(this);

  String divideBy10(dynamic value){
    if(value == null){
      return "";
    }
    return (value / 10).toString();
  }

  String divideMeasuredBy100(dynamic value) {
    if (value == null) {
      return "-";
    }else if (value == 0) {
      return value.toString();
    } else if(value < 0){
      return "Not Available";
    }
    return (value / 100).toStringAsFixed(1);
  }
  String checkFrequency(dynamic value) {
    if (value == null) {
      return "-";
    }else if (value == 0) {
      return "Not Configured";
    } else if(value < 0){
      return "Not Available";
    }
    return value.toString();
  }
  double multiplyBy10(String value){
    return (double.parse(value)*10);
  }

  String changeTemp(int value, {bool isCelsius = true}) {
    double celsius = value / 1000.0;
    if (isCelsius) {
      return celsius.toStringAsFixed(1);
    } else {
      double fahrenheit = (celsius * 9 / 5) + 32;
      return fahrenheit.toStringAsFixed(1);
    }
  }
}

@JsonSerializable()
class TelemetryThreshold {
  TelemetryThresholdItem temperature = TelemetryThresholdItem.empty();
  @JsonKey(name: "dc_5v", defaultValue: TelemetryThresholdItem.empty)
  TelemetryThresholdItem dc5v = TelemetryThresholdItem.empty();
  @JsonKey(name: "dc_8v", defaultValue: TelemetryThresholdItem.empty)
  TelemetryThresholdItem dc8v = TelemetryThresholdItem.empty();
  @JsonKey(name: "dc_24v", defaultValue: TelemetryThresholdItem.empty)
  TelemetryThresholdItem dc24v = TelemetryThresholdItem.empty();
  @JsonKey(name: "ac_voltage", defaultValue: TelemetryThresholdItem.empty)
  TelemetryThresholdItem acVoltage = TelemetryThresholdItem.empty();
  @JsonKey(name: "vdd_3_3v", defaultValue: TelemetryThresholdItem.empty)
  TelemetryThresholdItem vdd33v = TelemetryThresholdItem.empty();
  @JsonKey(name: "ds_gain_adjust", defaultValue: TelemetryThresholdItem.empty)
  TelemetryThresholdItem dsGainAdjust = TelemetryThresholdItem.empty();
  @JsonKey(name: "ds_slope_adjust", defaultValue: TelemetryThresholdItem.empty)
  TelemetryThresholdItem dsSlopeAdjust = TelemetryThresholdItem.empty();
  @JsonKey(name: "us_gain_adjust", defaultValue: TelemetryThresholdItem.empty)
  TelemetryThresholdItem usGainAdjust = TelemetryThresholdItem.empty();
  @JsonKey(name: "us_slope_adjust", defaultValue: TelemetryThresholdItem.empty)
  TelemetryThresholdItem usSlopeAdjust = TelemetryThresholdItem.empty();

  TelemetryThreshold({
    required this.temperature,
    required this.dc5v,
    required this.dc8v,
    required this.dc24v,
    required this.acVoltage,
    required this.vdd33v,
    required this.dsGainAdjust,
    required this.dsSlopeAdjust,
    required this.usGainAdjust,
    required this.usSlopeAdjust
  });

  TelemetryThreshold.empty();

  factory TelemetryThreshold.fromJson(Map<String, dynamic> json) => _$TelemetryThresholdFromJson(json);

  Map<String, dynamic> toJson() => _$TelemetryThresholdToJson(this);
}

@JsonSerializable()
class TelemetryThresholdItem {
  dynamic low = 0.0;
  dynamic high = 0.0;

  TelemetryThresholdItem({
    this.low,
    this.high,
  });

  TelemetryThresholdItem.empty();
  factory TelemetryThresholdItem.fromJson(Map<String, dynamic> json) => _$TelemetryThresholdItemFromJson(json);

  Map<String, dynamic> toJson() => _$TelemetryThresholdItemToJson(this);

}
