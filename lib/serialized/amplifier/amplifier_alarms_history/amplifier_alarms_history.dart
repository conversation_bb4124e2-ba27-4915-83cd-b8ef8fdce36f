import 'package:json_annotation/json_annotation.dart';
import 'package:quantumlink_node/serialized/amplifier/amplifier_device_info/amplifier_device_info.dart';
part 'amplifier_alarms_history.g.dart';
@JsonSerializable()
class AlarmHistoryData {
  @JsonKey(name: 'device_eui')
  dynamic deviceEUI;
  dynamic alarm;
  dynamic timestamp;
  dynamic type;
  dynamic message;

  AlarmHistoryData({
    this.deviceEUI,
    this.alarm,
    this.timestamp,
    this.type,
    this.message,
  });

  factory AlarmHistoryData.fromJson(Map<String, dynamic> json) => _$AlarmHistoryDataFromJson(json);

  Map<String, dynamic> toJson() => _$AlarmHistoryDataToJson(this);
}




@JsonSerializable()
class AlarmModel {
  dynamic success;
  dynamic message;
  AlarmItem?result;

  AlarmModel({

    this.message,
    required this.result,
  });

  factory AlarmModel.fromJson(Map<String, dynamic> json) => _$AlarmModelFromJson(json);
  Map<String, dynamic> toJson() => _$AlarmModelToJson(this);
  AlarmModel.empty();
}
@JsonSerializable()
class AlarmItem {
  dynamic status;
  Alarms alarms;
  @JsonKey(name: 'alarm_flags')
  final List<AlarmFlag> alarmFlags;

  AlarmItem({
    this.status,
    required this.alarms,
    required this.alarmFlags,
  });

  factory AlarmItem.fromJson(Map<String, dynamic> json) => _$AlarmItemFromJson(json);
  Map<String, dynamic> toJson() => _$AlarmItemToJson(this);
}

@JsonSerializable()
class AlarmFlag {
  final String flag;
  final String severity;

  AlarmFlag({
    required this.flag,
    required this.severity,
  });

  factory AlarmFlag.fromJson(Map<String, dynamic> json) => _$AlarmFlagFromJson(json);
  Map<String, dynamic> toJson() => _$AlarmFlagToJson(this);
}