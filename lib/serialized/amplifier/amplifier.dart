import 'package:json_annotation/json_annotation.dart';
import 'package:quantumlink_node/app_import.dart';

part 'amplifier.g.dart';

@JsonSerializable()
class AmplifierDeviceList {
  AmplifierDeviceList(this.result, this.message);

  factory AmplifierDeviceList.fromJson(Map<String, dynamic> json) =>
      _$AmplifierDeviceListFromJson(json);

  Map<String, dynamic> toJson() => _$AmplifierDeviceListToJson(this);
  List<AmplifierDeviceItem> result;
  String message;

  AmplifierDeviceList.empty()
      : result = [],
        message = "";
}
@JsonSerializable()
class AmplifierDeviceItem {
  @JsonKey(name: 'last_alarm_at')
  dynamic lastAlarmAt;
  @JsonKey(name: 'last_notification_at')
  dynamic lastNotificationAt;
  @JsonKey(name: 'device_eui')
  dynamic deviceEui;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'app_key')
  dynamic appKey;
  dynamic type;
  dynamic location;
  dynamic status;
  dynamic state;
  @JsonKey(name: 'last_seen')
  dynamic lastSeen;
  @JsonKey(name: 'product_id')
  dynamic productId;
  dynamic selected;
  dynamic latitude;
  @JsonKey(name: 'ds_amps')
  dynamic dsAmps;
  @JsonKey(name: 'site_id')
  dynamic siteId;
  SiteDataModel? site;
  @JsonKey(name: 'asset_id')
  dynamic assetId;
  @JsonKey(name: 'xponder_fw_version')
  dynamic xponderFwVersion;
  @JsonKey(name: 'amp_fw_version')
  dynamic ampFwVersion;
  dynamic placement;
  @JsonKey(name: 'alarm_flags')
  dynamic alarmFlags;
  @JsonKey(name: 'device_alias', fromJson: Uri.decodeComponent, defaultValue: "")
  dynamic deviceAlias;
  dynamic alarm;
  @JsonKey(name: 'alarm_flag_severity',defaultValue: <AlarmFlag>[])
  late List<AlarmFlag> alarmFlagsSeverity;
  @JsonKey(name: 'auto_discovered')
  dynamic autoDiscovered;
  List<Gateway> ? gateway;
  List<dynamic> ? config;
  @JsonKey(name: 'config_bitmask')
  dynamic configBitmask;
  dynamic longitude;
  dynamic spectrumEndPoint;
  dynamic initialAmpDetailTabValue;
  dynamic spectrumStartRef;
  dynamic spectrumStartLevel;
  dynamic totalCompositePowerValue;
  late ApiStatus dashboardRefreshStatus;
  late ApiStatus aMPInfoRefreshStatus;
  late ApiStatus pAInfoRefreshStatus;
  late ApiStatus transponderRefreshStatus;
  late ApiStatus pSInfoRefreshStatus;
  late ApiStatus configStatusRefreshStatus;
  late ApiStatus configRefreshStatus;
  late ApiStatus testPointConfigRefreshStatus;
  late ApiStatus spectrumStatus;
  late ApiStatus refreshSpectrumStatus;
  late ApiStatus telemetryStatus;
  late ApiStatus alarmHistoryStatus;
  late ApiStatus alarmStatus;
  late ApiStatus setIngressStatus;
  late ApiStatus ampBankRebootStatus;
  late ApiStatus transponderBankRebootStatus;
  late ApiStatus ampFWImageInfoStatus;
  late ApiStatus transponderFWImageInfoStatus;
  @JsonKey(defaultValue: AmpDeviceSummary.empty)
  AmpDeviceSummary ampDeviceSummary=AmpDeviceSummary.empty();
  @JsonKey(defaultValue: TransponderInfo.empty)
  late TransponderInfo transponderInfo=TransponderInfo.empty();
  @JsonKey(defaultValue: IdentificationItem.empty)
  late IdentificationItem identificationItem =IdentificationItem.empty();
  @JsonKey(defaultValue: FirmwareImageInfo.empty)
  late FirmwareImageInfo ampFirmwareImageInfo =FirmwareImageInfo.empty();
  @JsonKey(defaultValue: FirmwareImageInfo.empty)
  late FirmwareImageInfo transponderFWImageInfo =FirmwareImageInfo.empty();
  @JsonKey(defaultValue: SensorDataItem.empty)
  late SensorDataItem sensorDataItem =SensorDataItem.empty();
  @JsonKey(defaultValue:  AmpDownStreamItem.empty)
  late AmpDownStreamItem ampDownStreamItem= AmpDownStreamItem.empty();
  @JsonKey(defaultValue:  AmpUpStreamItem.empty)
  late AmpUpStreamItem ampUpStreamItem=AmpUpStreamItem.empty();
  @JsonKey(defaultValue: [])
  List<TestPointItem> testPointConfigList = [];
  @JsonKey(defaultValue:  [])
  List<IngressSwitchItem> ingressSwitchItemList =[];
  @JsonKey(defaultValue:  SpectrumData.empty)
  late SpectrumData spectrumData =SpectrumData.empty();
  @JsonKey(defaultValue:  <TelemetryItem>[] )
  late List<TelemetryItem> telemetryModel;
  @JsonKey(defaultValue:  <AlarmHistoryData>[] )
  late List<AlarmHistoryData> alarmStatusModel;
  @JsonKey(defaultValue:  <PointData>[] )
  late List<PointData> levelPoints;
  @JsonKey(defaultValue:  <PointData>[] )
  late List<PointData> refPoints;
  @JsonKey(defaultValue: DsAutoAlignmentModel.empty)
  late DsAutoAlignmentModel dsAutoAlignmentModel =DsAutoAlignmentModel.empty();
  @JsonKey(defaultValue:  <PointData>[] )
  late List<PointData> dsLevelPoints;
  @JsonKey(defaultValue:  <PointData>[] )
  late List<PointData> dsRefPoints;
  dynamic ampDeviceSummaryError;
  dynamic ampDeviceTransponderError;
  dynamic placementAndIdentityError;
  dynamic powerSupplyError;
  dynamic configStatusError;
  dynamic ampSensorError;
  dynamic downStreamAutoAlignmentError;
  dynamic downStreamManualAlignmentError;
  dynamic downStreamAmpsError;
  dynamic ingressSwitchError;
  dynamic upStreamAutoAlignmentError;
  dynamic testPointConfigError;
  dynamic transponderImageInfoError;
  dynamic ampImageInfoError;
  dynamic spectrumError;
  dynamic spectrumTimeValue;
  @JsonKey(defaultValue: 0)
  dynamic totalCalls;
  @JsonKey(defaultValue: 0)
  dynamic completedCalls;
  @JsonKey(defaultValue: 0)
  dynamic isLevelSuccessCalls;
  @JsonKey(defaultValue: 0)
  dynamic isRefSuccessCalls;
  @JsonKey(defaultValue: [])
  late List<dynamic> levelIndicatorColors;
  @JsonKey(defaultValue: [])
  late List<dynamic> refIndicatorColors;
  DateTime? dashboardUpdateTime;
  DateTime? placementAndIdentityUpdateTime;
  DateTime? aMPInfoUpdateTime;
  DateTime? transponderInfoUpdateTime;
  DateTime? powerSupplyInfoUpdateTime;
  DateTime? configStatusUpdateTime;
  DateTime? telemetryUpdateTime;
  DateTime? alarmUpdateTime;
  DateTime? spectrumUpdateTime;
  DateTime? ampTestPointUpdateTime;
  DateTime? ampIngressUpdateTime;
  DateTime? downStreamAmpsUpdateTime;
  DateTime? downStreamUpdateTime;
  DateTime? dsAutoAlignUpdateTime;
  DateTime? usAutoAlignUpdateTime;
  DateTime? dsSpectrumUpdateTime;
  DateTime? upStreamUpdateTime;
  DateTime? onTapTimeOfAI;
  Duration ? differenceTimeOfAI;
  @JsonKey(defaultValue: true)
  dynamic isShowTextOfAI;
  @JsonKey(includeFromJson: false, includeToJson: false)
  Timer? refreshTimerOfAI;
  DateTime? onTapTimeOfPAI;
  Duration ? differenceTimeOfPAI;
  @JsonKey(defaultValue: true)
  dynamic isShowTextOfPAI;
  @JsonKey(includeFromJson: false, includeToJson: false)
  Timer? refreshTimerOfPAI;
  DateTime? onTapTimeOfPSI;
  Duration ? differenceTimeOfPSI;
  @JsonKey(defaultValue: true)
  dynamic isShowTextOfPSI ;
  @JsonKey(includeFromJson: false, includeToJson: false)
  Timer? refreshTimerOfPSI;
  DateTime? onTapTimeOfTIImageInfo;
  DateTime? tiImageInfoUpdateTime;
  Duration ? differenceTimeOfTIImageInfo;
  @JsonKey(defaultValue: true)
  dynamic isShowTextOfTIImageInfo ;
  @JsonKey(includeFromJson: false, includeToJson: false)
  Timer? refreshTimerOfTIImageInfo;
  DateTime? onTapTimeOfAmpImageInfo;
  DateTime? ampImageInfoUpdateTime;
  Duration ? differenceTimeOfAmpImageInfo;
  @JsonKey(defaultValue: true)
  dynamic isShowTextOfAmpImageInfo ;
  @JsonKey(includeFromJson: false, includeToJson: false)
  Timer? refreshTimerOfAmpImageInfo;

  //--Config Status---
  DateTime? onTapTimeOfConfigStatus;
  Duration ? differenceTimeOfConfigStatus;
  @JsonKey(defaultValue: true)
  dynamic isShowTextOfConfigStatus ;
  @JsonKey(includeFromJson: false, includeToJson: false)
  Timer? refreshTimerOfConfigStatus;

  DateTime? onTapTimeOfTI;
  Duration ? differenceTimeOfTI;
  @JsonKey(defaultValue: true)
  dynamic isShowTextOfTI ;
  @JsonKey(includeFromJson: false, includeToJson: false)
  Timer? refreshTimerOfTI;
  @JsonKey(includeFromJson: false, includeToJson: false)
  dynamic telemetryPageOffset;
  dynamic spectrumEndFrequency;
  @JsonKey(name: "bw_mode")
  dynamic bwMode;
  dynamic bwModeError;
  @JsonKey(includeFromJson: false, includeToJson: false)
  RxBool isSetDataInProgressing = false.obs;
  @JsonKey(includeFromJson: false, includeToJson: false)
  Map<int,int> mapCtrlStage = {1:1};
  @JsonKey(includeFromJson: false, includeToJson: false)
  Map<Map<int,int>,int> mapWrittenCtrlDS = {};
  @JsonKey(includeFromJson: false, includeToJson: false)
  Map<Map<int,int>,int> mapWrittenCtrlUS = {};
  @JsonKey(includeFromJson: false, includeToJson: false)
  Map<Map<int,int>,int> mapWrittenCtrlDSSpectrum = {};
  dynamic isShowConfigurationAndDiagnostics ;
  dynamic isShowManualConfiguration ;
  dynamic isShowAlignmentConfiguration ;
  dynamic isShowSingleSpectrumCapture ;
  dynamic isShowSwitchBankAndReboot;
  dynamic isShowTelemetryALSCPilots;
  dynamic isAMPBankReboot ;
  dynamic isTransponderBankReboot;
  dynamic isMultiIngressSwitch;


  AmplifierDeviceItem(
      {this.lastAlarmAt,
      this.lastNotificationAt,
      this.deviceEui,
      this.appKey,
      this.selected = false,
      this.type,
      this.status,
      this.lastSeen,
      this.location ,
      this.latitude,
      this.alarm,
      this.longitude,
      this.dsAmps,
      this.siteId,
      this.site,
      this.state,
      this.assetId,
      this.alarmFlags,
      this.placement,
      this.autoDiscovered,
      this.spectrumEndPoint,
      this.spectrumStartRef,
      this.spectrumStartLevel,
      this.totalCompositePowerValue,
      required this.alarmFlagsSeverity,
      this.config,
      this.configBitmask,
      this.gateway,
      this.ampFwVersion,
      this.xponderFwVersion,
      this.dashboardRefreshStatus = ApiStatus.initial,
      required this.identificationItem,
      required this.ampFirmwareImageInfo,
      required this.transponderFWImageInfo,
      required this.sensorDataItem,
      required this.ampDeviceSummary,
      required this.transponderInfo,
      this.configRefreshStatus = ApiStatus.initial,
      this.testPointConfigRefreshStatus = ApiStatus.initial,
      this.spectrumStatus = ApiStatus.initial,
      this.refreshSpectrumStatus = ApiStatus.initial,
      this.telemetryStatus = ApiStatus.initial,
      this.alarmHistoryStatus = ApiStatus.initial,
      this.alarmStatus = ApiStatus.initial,
      this.setIngressStatus = ApiStatus.initial,
      this.aMPInfoRefreshStatus = ApiStatus.initial,
      this.pSInfoRefreshStatus = ApiStatus.initial,
      this.configStatusRefreshStatus = ApiStatus.initial,
      this.transponderRefreshStatus = ApiStatus.initial,
      this.pAInfoRefreshStatus = ApiStatus.initial,
      this.ampBankRebootStatus = ApiStatus.initial,
      this.transponderBankRebootStatus = ApiStatus.initial,
        this.ampFWImageInfoStatus = ApiStatus.initial,
        this.transponderFWImageInfoStatus =ApiStatus.initial,
      required this.ampDownStreamItem,
      required this.ampUpStreamItem,
      required this.testPointConfigList,
      required this.ingressSwitchItemList,
      this.ampDeviceSummaryError,
      this.ampSensorError,
      this.downStreamAutoAlignmentError,
      this.downStreamManualAlignmentError,
      this.ingressSwitchError,
      this.upStreamAutoAlignmentError,
      this.testPointConfigError,
      this.spectrumError,
      this.downStreamAmpsError,
      this.ampDeviceTransponderError,
      this.powerSupplyError,
      this.configStatusError,
      this.placementAndIdentityError,
      this.transponderImageInfoError,
      this.ampImageInfoError,
      this.spectrumTimeValue,
      required this.spectrumData,
      required this.telemetryModel,
      required this.alarmStatusModel,
      required this.levelPoints,
      required this.refPoints,
      required this.dsAutoAlignmentModel,
      required this.dsLevelPoints,
      required this.dsRefPoints,
      this.initialAmpDetailTabValue,
      required this.dashboardUpdateTime,
      required this.telemetryUpdateTime,
      required this.alarmUpdateTime,
      required this.spectrumUpdateTime,
      required this.ampTestPointUpdateTime,
      required this.ampIngressUpdateTime,
      required this.downStreamAmpsUpdateTime,
      required this.downStreamUpdateTime,
      required this.upStreamUpdateTime,
      required this.placementAndIdentityUpdateTime,
      required this.aMPInfoUpdateTime,
      required this.transponderInfoUpdateTime,
      required this.powerSupplyInfoUpdateTime,
      required this.configStatusUpdateTime,
      this.isLevelSuccessCalls,
      this.completedCalls,
      this.isRefSuccessCalls,
      this.totalCalls,
      required this.levelIndicatorColors,
      required this.refIndicatorColors,
      this.differenceTimeOfAI,
      required this.isShowTextOfAI,
      this.refreshTimerOfAI,
      this.onTapTimeOfAI,
      this.differenceTimeOfPAI,
      required this.isShowTextOfPAI,
      this.refreshTimerOfPAI,
      this.onTapTimeOfPAI,
      this.differenceTimeOfPSI,
      required this.isShowTextOfPSI,
      this.refreshTimerOfPSI,
      this.onTapTimeOfPSI,
        this.differenceTimeOfTIImageInfo,
        required this.isShowTextOfTIImageInfo,
        this.refreshTimerOfTIImageInfo,
        this.tiImageInfoUpdateTime,
        this.onTapTimeOfTIImageInfo,
        this.differenceTimeOfAmpImageInfo,
        required this.isShowTextOfAmpImageInfo,
        this.refreshTimerOfAmpImageInfo,
        this.onTapTimeOfAmpImageInfo,
        this.ampImageInfoUpdateTime,

      this.differenceTimeOfConfigStatus,
      required this.isShowTextOfConfigStatus,
      this.refreshTimerOfConfigStatus,
      this.onTapTimeOfConfigStatus,
      this.differenceTimeOfTI,
      required this.isShowTextOfTI,
      this.refreshTimerOfTI,
      this.onTapTimeOfTI,
      this.telemetryPageOffset=0,
      this.spectrumEndFrequency,
      this.bwMode,
      this.bwModeError,
      this.deviceAlias,
      required this.dsAutoAlignUpdateTime,
      required this.dsSpectrumUpdateTime,
      required this.usAutoAlignUpdateTime,
        this.isShowConfigurationAndDiagnostics=false,
        this.isShowSingleSpectrumCapture=false,
        this.isShowManualConfiguration=true,
        this.isTransponderBankReboot=false,
        this.isAMPBankReboot=false,
        this.isShowSwitchBankAndReboot=false,
        this.isShowTelemetryALSCPilots=false,
        this.isMultiIngressSwitch=false,
        this.isShowAlignmentConfiguration=false,
      }){
       location = getLocation(latitude, longitude, location);
  }

  AmplifierDeviceItem.empty() ;


  static dynamic getLocation(dynamic lat, dynamic long, dynamic location) {
    String addDecimal(String value) {
      if (!value.contains('.')) {
        value += '.0';
      }
      return value;
    }
    if (lat != null && long != null) {
      return "${addDecimal(lat.toString())},${addDecimal(long.toString())}";
    }
    return location;
  }
  factory AmplifierDeviceItem.fromJson(Map<String, dynamic> json) => _$AmplifierDeviceItemFromJson(json);
  Map<String, dynamic> toJson() => _$AmplifierDeviceItemToJson(this);
}

@JsonSerializable()
class Gateway {
  @JsonKey(name: 'gw_eui')
 dynamic gwEui;

  @JsonKey(name: 'gw_type')
  dynamic gwType;

  dynamic name;
  SiteDataModel? site;

  @JsonKey(name: 'last_join')
  dynamic lastJoin;

  Gateway({
    this.gwEui,
    this.gwType,
    this.name,
    this.site,
    this.lastJoin,
  });

  factory Gateway.fromJson(Map<String, dynamic> json) => _$GatewayFromJson(json);
  Map<String, dynamic> toJson() => _$GatewayToJson(this);
}

@JsonSerializable(includeIfNull: false)
class AmplifierDeviceItem2 {

  dynamic location;
  dynamic placement;
  @JsonKey(name: 'asset_id')
  dynamic assetId;
  @JsonKey(name: 'device_alias', fromJson: Uri.decodeComponent, defaultValue: "")
  dynamic deviceAlias;
  @JsonKey(fromJson: Uri.decodeComponent, defaultValue: "")
  dynamic description;

  AmplifierDeviceItem2({
    this.deviceAlias,
    this.description,
    this.location,
    this.assetId,
    this.placement,
  });

  AmplifierDeviceItem2.empty();

  factory AmplifierDeviceItem2.fromJson(Map<String, dynamic> json) {
    dynamic lat = json['latitude'];
    dynamic long = json['longitude'];
    dynamic location = json['location'];
    return AmplifierDeviceItem2(
      location: json['location'] == null ? getLocation(lat, long, location) : location,
      assetId: json['asset_id'],
      placement: json['placement'],
      deviceAlias: json['device_alias'] == null
          ? ''
          : Uri.decodeComponent(json['device_alias'] as String),
      description:  json['description'] == null
          ? ''
          : Uri.decodeComponent(json['description'] as String),
    );
  }

  static dynamic getLocation(dynamic lat, dynamic long, dynamic location) {
    String addDecimal(String value) {
      if (!value.contains('.')) {
        value += '.0';
      }
      return value;
    }
    if (lat != null && long != null) {
      return "${addDecimal(lat.toString())},${addDecimal(long.toString())}";
    }
    return location;
  }

  Map<String, dynamic> toJson() => _$AmplifierDeviceItem2ToJson(this);
}




@JsonSerializable()
class DsManualAlignmentModel {

  dynamic message;
  @JsonKey(name: 'result')
  DsManualAlignmentItem? result;

  DsManualAlignmentModel({this.message, this.result});

  factory DsManualAlignmentModel.fromJson(Map<String, dynamic> json) =>
      _$DsManualAlignmentModelFromJson(json);

  DsManualAlignmentModel.empty();

  Map<String, dynamic> toJson() => _$DsManualAlignmentModelToJson(this);
}

@JsonSerializable()
class DsManualAlignmentItem {
  @JsonKey(name: "values")
  List<DSManualValues> dsValues = [];

  // samp_auto_alignment_op_e {
  // PRE_TUNE        = 1;
  // COARSE_TUNE     = 2;
  // FINE_TUNE       = 3;
  // FULL_TUNE       = 4; //this includes 1, 2, and 3
  // SAVE            = 5;
  // REVERT          = 6;
  // CSM_CALC        = 7;
  // RSME_CALC       = 8;
  // SAVE_COMPLETE   = 9;
  // }
  @JsonKey(includeToJson: false)
  int? manual_align_ctrl_type_enum;

  @JsonKey(defaultValue: 1, fromJson: _valueFromJson,includeToJson: false)
  int factor = 1;
  @JsonKey(includeFromJson: false,includeToJson: false)
  RxBool isProgressing = false.obs;

  DsManualAlignmentItem(
    this.dsValues, {this.manual_align_ctrl_type_enum,
  });

  DsManualAlignmentItem.empty();

  factory DsManualAlignmentItem.fromJson(Map<String, dynamic> json) => _$DsManualAlignmentItemFromJson(json);

  Map<String, dynamic> toJson() => _$DsManualAlignmentItemToJson(this);

  static dynamic _valueFromJson(dynamic json) {
    if (json is num && json == 0) {
      return 10;
    }
    return json;
  }

  dynamic toJsonWithControlType() {
    var json = toJson();
    if (manual_align_ctrl_type_enum != null) {
      json["manual_align_ctrl_type_enum"] = manual_align_ctrl_type_enum;
    }
    return json;
  }
}


@JsonSerializable()
class DSManualValues {
  //----Manual Alignment----//
  int stage=0;
  @JsonKey(name: "ctrl_type")
  int ctrlType=0;
  @JsonKey(name: "min_val")
  double minVal=0;
  @JsonKey(name: "max_val")
  double maxVal=0;
  double value=0;
  @JsonKey(includeFromJson: false, includeToJson: false)
  double initialValue = 0;

  @JsonKey(defaultValue: true)
  bool? dirty = true;
  @JsonKey(name: "savedvalue", includeToJson: false)
  double savedValue=0;

  @JsonKey(includeFromJson: false, includeToJson: false)
  bool isSelected = false;

  bool get isDirty {
    if (stage == 9) return false;

    final normValue = round1(value / 10);
    final normSavedValue = round1(savedValue / 10);
    const ignoreDirty = 0;
    final allowedDiffs = <double>[];
    for (int i = 1; i <= 6; i++) {
      double diff = i / 10.0;
      allowedDiffs.add(round1(diff));
      allowedDiffs.add(round1(-diff));
    }
    for (final diff in allowedDiffs) {
      if (normSavedValue == ignoreDirty || normValue == normSavedValue || normValue == round1(normSavedValue + diff)) {
        return false;
      }
    }
    return true;
  }
  double round1(num value) => (value * 10).roundToDouble() / 10;

  DSManualValues.empty();

  DSManualValues(
      this.stage, this.ctrlType, this.minVal, this.maxVal, this.value,this.dirty,this.savedValue){
    initialValue = value;
  }

  factory DSManualValues.fromJson(Map<String, dynamic> json) => _$DSManualValuesFromJson(json);

  Map<String, dynamic> toJson() => _$DSManualValuesToJson(this);

  DSManualValues copy() {
    return DSManualValues(
      stage,
      ctrlType,
      minVal,
      maxVal,
      value,
      dirty,
      savedValue
    )
      ..isSelected = isSelected
      ..initialValue = initialValue;
  }
}





