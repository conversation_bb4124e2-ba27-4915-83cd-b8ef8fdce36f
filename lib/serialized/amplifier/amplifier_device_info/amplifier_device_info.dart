import 'package:json_annotation/json_annotation.dart';
part 'amplifier_device_info.g.dart';
@JsonSerializable()
class AmpDeviceSummary {

  dynamic message;
  DeviceSummaryDetail result;

  AmpDeviceSummary({required this.message, required this.result});

  factory AmpDeviceSummary.fromJson(Map<String, dynamic> json) => _$AmpDeviceSummaryFromJson(json);

  AmpDeviceSummary.empty() : this(message: '', result: DeviceSummaryDetail.empty());

  Map<String, dynamic> toJson() => _$AmpDeviceSummaryToJson(this);
}
@JsonSerializable()
class DeviceSummaryDetail {
  @JsonKey(name: 'bw_mode')
  int ?bwMode ;
  @JsonKey(name: 'device_identity')
  DeviceIdentity deviceIdentity;
  @Json<PERSON>ey(name: 'version_info')
  VersionInfo versionInfo;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'user_device_info')
  UserDeviceInfo userDeviceInfo;
  @JsonKey(name: 'power_supply_info')
  PowerSupplyInfo powerSupplyInfo;
  @JsonKey(name: 'config_status')
  ConfigStatus configStatus;
  Alarms alarms;
  @JsonKey(name: 'diplex_info')
  int diplexInfo;

  DeviceSummaryDetail({
    required this.deviceIdentity,
    required this.versionInfo,
    required this.userDeviceInfo,
    required this.powerSupplyInfo,
    required this.alarms,
    required this.diplexInfo,
    required this.configStatus,
  });
  DeviceSummaryDetail.empty() : this(
    deviceIdentity: DeviceIdentity.fromJson({}),
    versionInfo: VersionInfo.fromJson({}),
    userDeviceInfo: UserDeviceInfo.fromJson({}),
    powerSupplyInfo: PowerSupplyInfo.fromJson({}),
    alarms: Alarms.fromJson({}),
    configStatus: ConfigStatus.fromJson({}),
    diplexInfo: -1,
  );
  factory DeviceSummaryDetail.fromJson(Map<String, dynamic> json) => _$DeviceSummaryDetailFromJson(json);

  Map<String, dynamic> toJson() => _$DeviceSummaryDetailToJson(this);
}

@JsonSerializable()
class DeviceIdentity {
  @JsonKey(name: 'device_type')
  dynamic deviceType;
  dynamic model;
  @JsonKey(name: 'serial_num')
  dynamic serialNum;
  @JsonKey(name: 'mfg_date_time')
  dynamic mfgDateTime;
  @JsonKey(name: 'mac_address')
  dynamic macAddress;
  @JsonKey(name: 'asset_id')
  dynamic assetId;

  DeviceIdentity({
    this.deviceType,
    this.model,
    this.serialNum,
    this.mfgDateTime,
    this.macAddress,
    this.assetId
  });

  factory DeviceIdentity.fromJson(Map<String, dynamic> json) => _$DeviceIdentityFromJson(json);

  Map<String, dynamic> toJson() => _$DeviceIdentityToJson(this);
}

@JsonSerializable()
class VersionInfo {
  @JsonKey(name: 'product_id')
  dynamic productId;
  @JsonKey(name: 'api_version')
  dynamic apiVersion;
  @JsonKey(name: 'fw_version')
  dynamic fwVersion;
  @JsonKey(name: 'fpga_version')
  dynamic fpgaVersion;
  @JsonKey(name: 'hw_version')
  dynamic hwVersion;

  VersionInfo({
    this.productId,
    this.apiVersion,
    this.fwVersion,
    this.fpgaVersion,
    this.hwVersion,
  });

  factory VersionInfo.fromJson(Map<String, dynamic> json) => _$VersionInfoFromJson(json);

  Map<String, dynamic> toJson() => _$VersionInfoToJson(this);
}

@JsonSerializable()
class UserDeviceInfo {
  @JsonKey(name: 'device_alias', fromJson: Uri.decodeComponent, defaultValue: "")
  dynamic deviceAlias;
  @JsonKey(name: 'asset_id')
  dynamic assetId;
  dynamic location;
  dynamic placement;
  @JsonKey(fromJson: Uri.decodeComponent, defaultValue: "")
  dynamic description;

  UserDeviceInfo({
    this.deviceAlias,
    this.location,
    this.placement,
    this.description,
    this.assetId,
  });

  factory UserDeviceInfo.fromJson(Map<String, dynamic> json) => _$UserDeviceInfoFromJson(json);

  Map<String, dynamic> toJson() => _$UserDeviceInfoToJson(this);
}

@JsonSerializable()
class ConfigStatus {
  @JsonKey(name: 'config_bitmask', defaultValue: 0)
  int configBitMask = 0;

  ConfigStatus(this.configBitMask);

  factory ConfigStatus.fromJson(Map<String, dynamic> json) => _$ConfigStatusFromJson(json);

  Map<String, dynamic> toJson() => _$ConfigStatusToJson(this);

  ConfigStatus.empty();

  List<String> get getConfigStatusList {
    List<String> statusNames = [];

    if ((configBitMask & 1) == 0) statusNames.add("DS ALIGNMENT UNKNOWN");
    if ((configBitMask & 2) == 0) statusNames.add("US ALIGNMENT UNKNOWN");
    if ((configBitMask & 4) == 0) statusNames.add("ALSC UNKNOWN");
    if ((configBitMask & 8) == 0) statusNames.add("LOCATION UNKNOWN");
    return statusNames;
  }
}

@JsonSerializable()
class PowerSupplyInfo {
  dynamic model;
  @JsonKey(name: 'serial_num')
  dynamic serialNum;
  @JsonKey(name: 'mfg_date_time')
  dynamic mfgDateTime;
  @JsonKey(name: 'hw_rev')
  dynamic hwRev;
  @JsonKey(name: 'asset_id')
  dynamic assetId;


  PowerSupplyInfo({
    this.model,
    this.serialNum,
    this.mfgDateTime,
    this.hwRev,
    this.assetId
  });

  factory PowerSupplyInfo.fromJson(Map<String, dynamic> json) => _$PowerSupplyInfoFromJson(json);

  Map<String, dynamic> toJson() => _$PowerSupplyInfoToJson(this);
}

@JsonSerializable()
class Alarms {
  @JsonKey(name: 'alarm_flags')
  dynamic alarmFlags;
  @JsonKey(name: 'adc_alarms')
  List<dynamic> ?adcAlarms;
  dynamic attenuation;
  dynamic slope;
  dynamic temperature;

  Alarms({
    this.alarmFlags,
    this.adcAlarms,
    this.attenuation,
    this.slope,
    this.temperature,
  });

  factory Alarms.fromJson(Map<String, dynamic> json) => _$AlarmsFromJson(json);

  Map<String, dynamic> toJson() => _$AlarmsToJson(this);
}

@JsonSerializable()
class DiplexInfo {
  @JsonKey(name: 'diplex_low')
  dynamic diplexLow;
  @JsonKey(name: 'diplex_high')
  dynamic diplexHigh;

  DiplexInfo({
    required this.diplexLow,
    required this.diplexHigh,
  });

  factory DiplexInfo.fromJson(Map<String, dynamic> json) => _$DiplexInfoFromJson(json);

  Map<String, dynamic> toJson() => _$DiplexInfoToJson(this);
}

@JsonSerializable()
class AmplifierSensorData {

  String message;
  SensorDataItem result;

  AmplifierSensorData({
    required this.message,
    required this.result,
  });

  factory AmplifierSensorData.fromJson(Map<String, dynamic> json) =>
      _$AmplifierSensorDataFromJson(json);

  Map<String, dynamic> toJson() => _$AmplifierSensorDataToJson(this);
}

@JsonSerializable()
class SensorDataItem {
  dynamic model;
  @JsonKey(name: 'serial_number')
  dynamic serialNumber;
  @JsonKey(name: 'lid_status')
  dynamic lidStatus;
  @JsonKey(name: 'alarm_status')
  dynamic alarmStatus;
  @JsonKey(name: 'ac_voltage')
  dynamic acVoltage;
  @JsonKey(name: 'dc_24_v')
  dynamic twentyFourVDC;
  @JsonKey(name: 'dc_5_v')
  dynamic fiveVDC;
  @JsonKey(name: 'dc_3_p3_v')
  dynamic threeVDc;
  @JsonKey(name: 'diplex_low')
  dynamic diplexLow;
  @JsonKey(name: 'diplex_high')
  dynamic diplexHigh;
  @JsonKey(name: 'up_time')
  dynamic upTime;
  dynamic currentVersion;
  dynamic temperature;

  SensorDataItem({
    this.model,
    this.serialNumber,
    this.lidStatus,
    this.alarmStatus,
    this.acVoltage,
    this.twentyFourVDC,
    this.fiveVDC,
    this.threeVDc,
    this.temperature,
    this.diplexLow,
    this.diplexHigh,
    this.upTime,
    this.currentVersion,
  });

  factory SensorDataItem.fromJson(Map<String, dynamic> json) => _$SensorDataItemFromJson(json);

  Map<String, dynamic> toJson() => _$SensorDataItemToJson(this);

  SensorDataItem.empty();
}

@JsonSerializable()
class AmplifierIdentification {

  String message;
  IdentificationItem result;

  AmplifierIdentification({
    required this.message,
    required this.result,
  });

  factory AmplifierIdentification.fromJson(Map<String, dynamic> json) =>
      _$AmplifierIdentificationFromJson(json);

  Map<String, dynamic> toJson() => _$AmplifierIdentificationToJson(this);
}

@JsonSerializable()
class IdentificationItem {
  @JsonKey(name: 'model_number')
  dynamic modelNumber;
  @JsonKey(name: 'serial_number')
  dynamic serialNumber;
  @JsonKey(name: 'software_version')
  dynamic swVersion;
  @JsonKey(name: 'mac_addr')
  dynamic macAddress;

  IdentificationItem({
    this.modelNumber,
    this.serialNumber,
    this.swVersion,
    this.macAddress,
  });

  factory IdentificationItem.fromJson(Map<String, dynamic> json) =>
      _$IdentificationItemFromJson(json);

  Map<String, dynamic> toJson() => _$IdentificationItemToJson(this);

  IdentificationItem.empty();
}
@JsonSerializable()
class TransponderInfo {

  dynamic message;
  TransponderItem  result;

  TransponderInfo({

    this.message,
    required this.result,
  });
  factory TransponderInfo.fromJson(Map<String, dynamic> json) => _$TransponderInfoFromJson(json);
  TransponderInfo.empty() :this(message: '', result: TransponderItem.empty());
  Map<String, dynamic> toJson() => _$TransponderInfoToJson(this);
}

@JsonSerializable(includeIfNull: false)
class TransponderItem {
  dynamic model;
  @JsonKey(name: "serial_num")
  dynamic serialNum;
  @JsonKey(name: "mfg_date_time")
  dynamic mfgDateTime;
  @JsonKey(name: "hw_version")
  dynamic hwVersion;
  @JsonKey(name: "api_version")
  dynamic apiVersion;
  @JsonKey(name: "asset_id")
  dynamic assetId;
  @JsonKey(name: "fw_version")
  String fWVersion = '';

  TransponderItem({
    this.model,
    this.serialNum,
    this.mfgDateTime,
    this.hwVersion,
    this.apiVersion,
    this.assetId,
    this.fWVersion = ''
  });
  TransponderItem.empty() ;
  factory TransponderItem.fromJson(Map<String, dynamic> json) => _$TransponderItemFromJson(json);

  Map<String, dynamic> toJson() => _$TransponderItemToJson(this);
}

@JsonSerializable()
class FirmwareImageInfo {
  dynamic message;
  @JsonKey(name: 'result')
  FirmwareImageResult result;

  FirmwareImageInfo({this.message, required this.result});

  factory FirmwareImageInfo.fromJson(Map<String, dynamic> json) =>
      _$FirmwareImageInfoFromJson(json);

  Map<String, dynamic> toJson() => _$FirmwareImageInfoToJson(this);
  FirmwareImageInfo.empty() :this(message: '', result: FirmwareImageResult.empty());
}

@JsonSerializable()
class FirmwareImageResult {
  @JsonKey(name: 'boot_version')
  dynamic bootVersion;

  @JsonKey(name: 'active_bank_index')
  dynamic activeBankIndex;

  @JsonKey(name: 'active_version')
  dynamic activeVersion;

  @JsonKey(name: 'backup_bank_index')
  dynamic backupBankIndex;

  @JsonKey(name: 'backup_version')
  dynamic backupVersion;

  @JsonKey(name: 'active_update_time')
  dynamic activeUpdateTime;

  @JsonKey(name: 'backup_update_time')
  dynamic backupUpdateTime;

  dynamic result;
  dynamic type;

  FirmwareImageResult({
    this.bootVersion,
    this.activeBankIndex,
    this.activeVersion,
    this.backupBankIndex,
    this.backupVersion,
    this.activeUpdateTime,
    this.backupUpdateTime,
    this.result,
    this.type,
  });

  factory FirmwareImageResult.fromJson(Map<String, dynamic> json) =>
      _$FirmwareImageResultFromJson(json);

  Map<String, dynamic> toJson() => _$FirmwareImageResultToJson(this);
  FirmwareImageResult.empty();
}
