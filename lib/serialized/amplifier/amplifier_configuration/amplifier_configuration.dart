import 'package:json_annotation/json_annotation.dart';
part 'amplifier_configuration.g.dart';


@JsonSerializable()
class DsAutoAlignmentModel {

  dynamic message;
  @JsonKey(name: 'result')
  DsAutoAlignmentItem? result;

  DsAutoAlignmentModel({this.message, this.result});

  factory DsAutoAlignmentModel.fromJson(Map<String, dynamic> json) =>
      _$DsAutoAlignmentModelFromJson(json);

  DsAutoAlignmentModel.empty();

  Map<String, dynamic> toJson() => _$DsAutoAlignmentModelToJson(this);
}

@JsonSerializable()
class DsAutoAlignmentItem {
  @JsonKey(name: "samp_downstream_auto_align_status")
  SampDownstreamAutoAlignStatus ?sampDownstreamAutoAlignStatus;
  @JsonKey(name: "spectrum_values")
  List<DSValue> ?dsValues;

  DsAutoAlignmentItem({
    this.sampDownstreamAutoAlignStatus,
    this.dsValues,
  });

  factory DsAutoAlignmentItem.fromJson(Map<String, dynamic> json) => _$DsAutoAlignmentItemFromJson(json);

  Map<String, dynamic> toJson() => _$DsAutoAlignmentItemToJson(this);
}

@JsonSerializable()
class SampDownstreamAutoAlignStatus {
  @JsonKey(name: "auto_align_status")
  dynamic autoAlignStatus;
  @JsonKey(name: "rf_det_mode")
  dynamic rfDetMode;
  @JsonKey(name: "error_code")
  dynamic errorCode;
  @JsonKey(name: "error_desc")
  dynamic errorDesc;
  @JsonKey(name: "result")
  dynamic result;
  @JsonKey(name: "csm_cable_length")
  dynamic csmCableLength;
  @JsonKey(name: "csm_gamma")
  dynamic csmGamma;
  @JsonKey(name: "csm_phi")
  dynamic csmPhi;

  SampDownstreamAutoAlignStatus({
    this.autoAlignStatus,
    this.rfDetMode,
    this.errorCode,
    this.errorDesc,
    this.result,
    this.csmCableLength,
    this.csmGamma,
    this.csmPhi,
  });

  factory SampDownstreamAutoAlignStatus.fromJson(Map<String, dynamic> json) => _$SampDownstreamAutoAlignStatusFromJson(json);

  Map<String, dynamic> toJson() => _$SampDownstreamAutoAlignStatusToJson(this);
}

@JsonSerializable()
class DSValue {
  @JsonKey(name: "start_freq")
  dynamic startFreq;
  @JsonKey(name: "step_size")
  dynamic stepSize;
  @JsonKey(name: "end_freq")
  dynamic endFreq;
  @JsonKey(name: "values")
  List<dynamic>? dsSpectrumValues;

  DSValue({
    this.startFreq,
    this.stepSize,
    this.endFreq,
    this.dsSpectrumValues,
  });

  factory DSValue.fromJson(Map<String, dynamic> json) => _$DSValueFromJson(json);

  Map<String, dynamic> toJson() => _$DSValueToJson(this);
}

@JsonSerializable()
class AmpDownStreamModel {
  String message;
  AmpDownStreamItem result;

  AmpDownStreamModel({
    required this.message,
    required this.result,
  });

  factory AmpDownStreamModel.fromJson(Map<String, dynamic> json) =>
      _$AmpDownStreamModelFromJson(json);

  Map<String, dynamic> toJson() => _$AmpDownStreamModelToJson(this);
}

@JsonSerializable()
class AmpDownStreamItem {
  @JsonKey(name: 'gain')
  dynamic downGain;
  @JsonKey(name: 'slope')
  dynamic downSlope;
  @JsonKey(name: 'agc_config')
  dynamic agcConfig;
  @JsonKey(name: 'univ_plugin_present')
  dynamic universalPlugin;
  @JsonKey(name: 'result')
  dynamic downResult;

  AmpDownStreamItem({
    this.downGain,
    this.downSlope,
    this.agcConfig,
    this.universalPlugin,
    this.downResult,
  });

  factory AmpDownStreamItem.fromJson(Map<String, dynamic> json) =>
      _$AmpDownStreamItemFromJson(json);

  Map<String, dynamic> toJson() => _$AmpDownStreamItemToJson(this);

  AmpDownStreamItem.empty();
}

@JsonSerializable()
class AmpUpStreamModel {
  String message;
  AmpUpStreamItem result;

  AmpUpStreamModel({
    required this.message,
    required this.result,
  });

  factory AmpUpStreamModel.fromJson(Map<String, dynamic> json) => _$AmpUpStreamModelFromJson(json);

  Map<String, dynamic> toJson() => _$AmpUpStreamModelToJson(this);
}

@JsonSerializable()
class AmpUpStreamItem {
  @JsonKey(name: 'gain')
  dynamic upGain;
  @JsonKey(name: 'slope')
  dynamic upSlope;
  @JsonKey(name: 'ingress_switch')
  dynamic ingressSwitch;
  @JsonKey(name: 'result')
  dynamic upResult;

  AmpUpStreamItem({
    this.upGain,
    this.upSlope,
    this.ingressSwitch,
    this.upResult,
  });

  factory AmpUpStreamItem.fromJson(Map<String, dynamic> json) => _$AmpUpStreamItemFromJson(json);

  Map<String, dynamic> toJson() => _$AmpUpStreamItemToJson(this);

  AmpUpStreamItem.empty();
}

class DSPointData {
  final double freq;
  final double level;
  final double reference;

  DSPointData({required this.freq, required this.level, required this.reference});
}

@JsonSerializable()
class DSAutoAlignSpectrum {
   dynamic message;
   DSAutoAlignSpectrumItem ?result;

  DSAutoAlignSpectrum({required this.message, required this.result});

  factory DSAutoAlignSpectrum.fromJson(Map<String, dynamic> json) =>
      _$DSAutoAlignSpectrumFromJson(json);

  Map<String, dynamic> toJson() => _$DSAutoAlignSpectrumToJson(this);
  DSAutoAlignSpectrum.empty() ;
}

@JsonSerializable()
class DSAutoAlignSpectrumItem {
   List<dynamic> freq;
  @JsonKey(name: 'spectrum_data')
   List<DSSpectrumData> dsSpectrumData;
   dynamic result;

  DSAutoAlignSpectrumItem({required this.freq, required this.dsSpectrumData, required this.result});

  factory DSAutoAlignSpectrumItem.fromJson(Map<String, dynamic> json) => _$DSAutoAlignSpectrumItemFromJson(json);

  Map<String, dynamic> toJson() => _$DSAutoAlignSpectrumItemToJson(this);
}

@JsonSerializable()
class DSSpectrumData {
  final dynamic frequency;
  final dynamic level;
  final dynamic reference;

  DSSpectrumData({required this.frequency, required this.level, required this.reference});

  factory DSSpectrumData.fromJson(Map<String, dynamic> json) => _$DSSpectrumDataFromJson(json);

  Map<String, dynamic> toJson() => _$DSSpectrumDataToJson(this);
}

@JsonSerializable(explicitToJson: true)
class AlignmentConfig {
  @JsonKey(name: 'us_autoalign_offset')
  dynamic usAutoalignOffset;

  @JsonKey(name: 'test_pilots_revert_in_secs')
  dynamic testPilotsRevertInSecs;

  @JsonKey(name: 'alsc_enable_in_secs')
  dynamic alscEnableInSecs;

  @JsonKey(name: 'ingress_switch_revert_in_secs')
  dynamic ingressSwitchRevertInSecs;
  @JsonKey(name: '_comment_oper_mode')
  dynamic commentOperationalMOde;
  @JsonKey(name: '_comment_diplexfilter')
  dynamic commentDiplexfilter;
  @JsonKey(name: 'operational_mode')
  dynamic operationalMode;
  dynamic diplexfilter;

  @JsonKey(name: 'auto_config')
   AutoConfig? autoConfig;

  @JsonKey(name: 'alsc_config')
   AlscConfig? alscConfig;


  AlignmentConfig({
     this.usAutoalignOffset,
     this.testPilotsRevertInSecs,
     this.alscEnableInSecs,
     this.operationalMode,
     this.ingressSwitchRevertInSecs,
     this.diplexfilter,
     this.commentOperationalMOde,
     this.commentDiplexfilter,
     this.autoConfig,
     this.alscConfig,
  });
  static double divideBy10(dynamic value) {
    if (value == null) return 0.0;
    return (value is num) ? value / 10.0 : 0.0;
  }
  factory AlignmentConfig.fromConfigFile(ConfigFile config) {
    return AlignmentConfig(
      ingressSwitchRevertInSecs:config.ingressSwitchRevertInSecs ,
      alscConfig: AlscConfig(backupPilots:config.backupPilots ,mainPilots: config.mainPilots),
      alscEnableInSecs: config.alscEnableInSecs,
      autoConfig: AutoConfig(
          step1: FreqPower(
              freq: config.step1?.freq, power: divideBy10(config.step1?.power)),
          start: FreqPower(
              freq: config.start?.freq, power: divideBy10(config.start?.power)),
          end: FreqPower(
              freq: config.end?.freq, power: divideBy10(config.end?.power)),
          markerPilots: config.markerPilots),
      commentDiplexfilter: "",
      commentOperationalMOde: "",
      diplexfilter: config.diplexfilter ,
      operationalMode: config.operationalMode,
      testPilotsRevertInSecs:config.testPilotsRevertInSecs ,
      usAutoalignOffset:config.usAutoalignOffset ,
    );
  }

  ConfigFileResult toConfigFileResponse() {
    return ConfigFileResult(configFile: ConfigFile(
      alscEnableInSecs: alscEnableInSecs,
      ingressSwitchRevertInSecs: ingressSwitchRevertInSecs,
      testPilotsRevertInSecs: testPilotsRevertInSecs,
      usAutoalignOffset: usAutoalignOffset,
      operationalMode: operationalMode,
      diplexfilter: diplexfilter,
      step1: FreqPower(
          freq: autoConfig?.step1?.freq, power: multiplyBy10(autoConfig?.step1?.power)),
      start: FreqPower(
          freq: autoConfig?.start?.freq, power: multiplyBy10(autoConfig?.start?.power)),
      end: FreqPower(
          freq: autoConfig?.end?.freq, power: multiplyBy10(autoConfig?.end?.power)),
      markerPilots: autoConfig?.markerPilots,
      mainPilots: alscConfig?.mainPilots,
      backupPilots: alscConfig?.backupPilots,
    ));
  }

  double multiplyBy10(dynamic value){
    return (double.parse(value.toString())*10);
  }

  factory AlignmentConfig.fromJson(Map<String, dynamic> json) => _$AlignmentConfigFromJson(json);
  Map<String, dynamic> toJson() => _$AlignmentConfigToJson(this);
}

@JsonSerializable()
class AutoConfig {
   FreqPower ? start;
   FreqPower ? end;
   FreqPower ? step1;
  @JsonKey(name: 'marker_pilots')
  final List<dynamic> ?markerPilots;

  AutoConfig({
   this.start,
   this.end,
   this.step1,
   this.markerPilots,
  });

  factory AutoConfig.fromJson(Map<String, dynamic> json) => _$AutoConfigFromJson(json);
  Map<String, dynamic> toJson() => _$AutoConfigToJson(this);
}

@JsonSerializable()
class FreqPower {
  dynamic freq;
  dynamic power;

  FreqPower({required this.freq, required this.power});

  factory FreqPower.fromJson(Map<String, dynamic> json) => _$FreqPowerFromJson(json);
  Map<String, dynamic> toJson() => _$FreqPowerToJson(this);
}

@JsonSerializable()
class AlscConfig {
  @JsonKey(name: 'main_pilots')
   List<dynamic> ? mainPilots;

  @JsonKey(name: 'backup_pilots')
   List<dynamic> ? backupPilots;

  AlscConfig({
    this.mainPilots,
    this.backupPilots,
  });

  factory AlscConfig.fromJson(Map<String, dynamic> json) => _$AlscConfigFromJson(json);
  Map<String, dynamic> toJson() => _$AlscConfigToJson(this);
}
@JsonSerializable(explicitToJson: true)
class ConfigFileResponse {
   dynamic message;
   ConfigFileResult ? result;

  ConfigFileResponse({ this.message,  this.result});

  factory ConfigFileResponse.fromJson(Map<String, dynamic> json) =>
      _$ConfigFileResponseFromJson(json);

  Map<String, dynamic> toJson() => _$ConfigFileResponseToJson(this);
}

@JsonSerializable(explicitToJson: true)
class ConfigFileResult {
  @JsonKey(name: 'config_file')
  final ConfigFile configFile;

  ConfigFileResult({required this.configFile});

  factory ConfigFileResult.fromJson(Map<String, dynamic> json) =>
      _$ConfigFileResultFromJson(json);

  Map<String, dynamic> toJson() => _$ConfigFileResultToJson(this);
}

@JsonSerializable()
class ConfigFile {
  @JsonKey(name: 'us_autoalign_offset',includeToJson: false)
  dynamic usAutoalignOffset;

  @JsonKey(name: 'test_pilots_revert_in_secs',includeToJson: false)
  dynamic testPilotsRevertInSecs;

  @JsonKey(name: 'alsc_enable_in_secs',includeToJson: false)
  dynamic alscEnableInSecs;

  @JsonKey(name: 'ingress_switch_revert_in_secs',includeToJson: false)
  dynamic ingressSwitchRevertInSecs;

  @JsonKey(name: 'operational_mode')
  dynamic operationalMode;

  dynamic diplexfilter;

  FreqPower? start;
  FreqPower? end;
  FreqPower? step1;

  @JsonKey(name: 'main_pilots')
  List<dynamic>? mainPilots;

  @JsonKey(name: 'backup_pilots')
  List<dynamic>? backupPilots;

  @JsonKey(name: 'marker_pilots')
  List<dynamic>? markerPilots;

  ConfigFile({
    this.usAutoalignOffset,
    this.testPilotsRevertInSecs,
    this.alscEnableInSecs,
    this.ingressSwitchRevertInSecs,
    this.operationalMode,
    this.diplexfilter,
    this.start,
    this.end,
    this.step1,
    this.mainPilots,
    this.backupPilots,
    this.markerPilots,
  });

  factory ConfigFile.fromJson(Map<String, dynamic> json) => _$ConfigFileFromJson(json);

  Map<String, dynamic> toJson() => _$ConfigFileToJson(this);
}
