import 'package:json_annotation/json_annotation.dart';
part 'amplifier_spectrum.g.dart';
@JsonSerializable(explicitToJson: true)
class SpectrumModel {
  dynamic message;
  Result ?result;

  SpectrumModel({
    this.message,
    this.result,
  });

  factory SpectrumModel.fromJson(Map<String, dynamic> json) => _$SpectrumModelFromJson(json);
  SpectrumModel.empty();
  Map<String, dynamic> toJson() => _$SpectrumModelToJson(this);
}

@JsonSerializable()
class Result {
  @JsonKey(name: "response")
  SpectrumData? response;

  Result({
    this.response,
  });

  factory Result.fromJson(Map<String, dynamic> json) => _$ResultFromJson(json);

  Map<String, dynamic> toJson() => _$ResultToJson(this);
}

@JsonSerializable()
class SpectrumData {
  @JsonKey(name: 'spectrum_capture_data_type_e')
  dynamic spectrumCaptureDataTypeE;
  List<SpectrumValues>? values;
  @JsonKey(name: 'total_composite_power')
  dynamic totalCompositePower;

  SpectrumData({
    this.spectrumCaptureDataTypeE,
    required this.values,
    this.totalCompositePower
  });

  SpectrumData.empty();

  factory SpectrumData.fromJson(Map<String, dynamic> json) => _$SpectrumDataFromJson(json);

  Map<String, dynamic> toJson() => _$SpectrumDataToJson(this);
}

@JsonSerializable()
class SpectrumValues {
  @JsonKey(name: 'start_freq')
  dynamic startFreq;
  @JsonKey(name: 'step_size')
  dynamic stepSize;
  @JsonKey(name: 'end_freq')
  dynamic endFreq;
  final List<num>? values;

  SpectrumValues({
    required this.startFreq,
    required this.stepSize,
    required this.endFreq,
    required this.values,
  });

  factory SpectrumValues.fromJson(Map<String, dynamic> json) => _$SpectrumValuesFromJson(json);

  Map<String, dynamic> toJson() => _$SpectrumValuesToJson(this);
}


@JsonSerializable()
class PointData {
  dynamic freq;
  dynamic value;

  PointData({
    required this.freq,
    required this.value,
  });

  factory PointData.fromJson(Map<String, dynamic> json) => _$PointDataFromJson(json);

  Map<String, dynamic> toJson() => _$PointDataToJson(this);
}