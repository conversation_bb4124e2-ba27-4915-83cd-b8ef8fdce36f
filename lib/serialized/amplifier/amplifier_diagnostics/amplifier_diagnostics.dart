
import 'package:get/get.dart';
import 'package:json_annotation/json_annotation.dart';

import '../../../generated/l10n.dart';
part 'amplifier_diagnostics.g.dart';

@JsonSerializable()
class DownstreamAmplifiers {
  @JsonKey(disallowNullValue: true)
  List<String> devices;
  @JsonKey(name: 'device_eui')
  String deviceEui;
  DownstreamAmplifiers({
    required this.devices,
    required this.deviceEui,
  });
  factory DownstreamAmplifiers.fromJson(Map<String, dynamic> json) => _$DownstreamAmplifiersFromJson(json);
  Map<String, dynamic> toJson() => _$DownstreamAmplifiersToJson(this);
}

@JsonSerializable()
class TestPointModel {

  String message;
  @JsonKey(name: 'result')
  TestPointData testPointData;

  TestPointModel({
    required this.message,
    required this.testPointData,
  });

  factory TestPointModel.fromJson(Map<String, dynamic> json) => _$TestPointModelFromJson(json);

  Map<String, dynamic> toJson() => _$TestPointModelToJson(this);
}

@JsonSerializable()
class TestPointData {

  @JsonKey(name: 'config_values')
  List<TestPointItem> testPointList;

  TestPointData({
    required this.testPointList,
  });

  factory TestPointData.fromJson(Map<String, dynamic> json) => _$TestPointDataFromJson(json);

  Map<String, dynamic> toJson() => _$TestPointDataToJson(this);
}

@JsonSerializable()
class TestPointItem {
  dynamic id;
  dynamic value;
  dynamic result;

  @JsonKey(includeFromJson: false,includeToJson: false)
  RxBool isProgressing = false.obs;

  @JsonKey(includeFromJson: false,includeToJson: false)
  RxBool isValueChanged = false.obs;

  TestPointItem({
    this.id,
    this.value,
    this.result,
  });

  factory TestPointItem.fromJson(Map<String, dynamic> json) =>
      _$TestPointItemFromJson(json);

  Map<String, dynamic> toJson() => _$TestPointItemToJson(this);

  TestPointItem.empty();

  getName(dynamic value) {
    return (value == 1)
        ? "FWD Input Test Point"
        : (value == 2)
            ? "FWD Output Test Point"
            : (value == 3)
                ? "AUX 1 Test Point"
                : (value == 4)
                    ? "AUX 2 Test Point"
                    : "Test Point";
  }
}

@JsonSerializable()
class IngressSwitchControl {
  @JsonKey(name: "message")
  final String? message;

  @JsonKey(name: "result", fromJson: IngressSwitchControl.parseResult)
  final List<IngressSwitchItem> items;

  IngressSwitchControl({
    this.message,
    required this.items,
  });

  factory IngressSwitchControl.fromJson(Map<String, dynamic> json) =>
      _$IngressSwitchControlFromJson(json);

  Map<String, dynamic> toJson() => _$IngressSwitchControlToJson(this);

  // Custom logic to normalize both types of "result"
  static List<IngressSwitchItem> parseResult(dynamic result) {
    if (result is Map<String, dynamic> && !result.containsKey('ctrl') ) {
      // Single object case
      return [IngressSwitchItem.fromJson(result)];
    } else if (result is Map<String, dynamic> && result['ctrl'] is List) {
      // List case inside 'ctrl' key
      return (result['ctrl'] as List)
          .map((e) => IngressSwitchItem.fromJson(e))
          .toList();
    } else {
      return [];
    }
  }
}


@JsonSerializable()
class IngressSwitchItem {
  @JsonKey(name: "ingress_switch")
  dynamic ingressSwitch;
  dynamic result;
  @JsonKey(name: "port_select")
  dynamic portSelect;

  IngressSwitchItem({
    this.ingressSwitch,
    this.result,
    this.portSelect
  });

  factory IngressSwitchItem.fromJson(Map<String, dynamic> json) => _$IngressSwitchItemFromJson(json);

  Map<String, dynamic> toJson() => _$IngressSwitchItemToJson(this);
  IngressSwitchItem.empty();
}