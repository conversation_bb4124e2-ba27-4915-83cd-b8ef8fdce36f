import 'package:json_annotation/json_annotation.dart';

part 'site_regions.g.dart';

@JsonSerializable()
class SiteDataModel {
  dynamic id;
  dynamic description;
  dynamic name;
  @Json<PERSON>ey(name: 'amps_count')
  dynamic ampsCount;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: false)
  dynamic selected;
  @JsonKey(name: 'vlgw_count')
  dynamic vlgwCount;
  @<PERSON>son<PERSON><PERSON>(name: 'rpd_count')
  dynamic rpdCount;

  SiteDataModel({
    this.id,
    this.description,
    this.name,
    this.selected = false,
    this.ampsCount,
    this.vlgwCount,
    this.rpdCount,
  });

  factory SiteDataModel.fromJson(Map<String, dynamic> json) =>
      _$SiteDataModelFromJson(json);
  Map<String, dynamic> toJson() => _$SiteDataModelToJson(this);

  SiteDataModel.empty();
}
