import 'package:json_annotation/json_annotation.dart';

part 'amp_provider.g.dart';

@JsonSerializable()
class AmplifierProviderItem {
  final String? name;

  @Json<PERSON>ey(name: 'chirpstack_app_name')
  final String? chirpstackAppName;

  @J<PERSON><PERSON><PERSON>(name: 'is_active',includeToJson: false)
  final bool? isActive;

  @<PERSON>son<PERSON><PERSON>(name: 'chirpstack_app_id',includeToJson: false)
  final dynamic chirpstackAppId;

  final String? code;

  AmplifierProviderItem({
    this.name,
    this.chirpstackAppName,
    this.isActive,
    this.chirpstackAppId,
    this.code,
  });

  factory AmplifierProviderItem.fromJson(Map<String, dynamic> json) =>
      _$AmplifierProviderItemFromJson(json);

  Map<String, dynamic> toJson() => _$AmplifierProviderItemToJson(this);
}


@JsonSerializable()
class VendorKeyItem {
   dynamic key;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'vendor_code')
  dynamic vendorCode;

  @J<PERSON><PERSON><PERSON>(name: 'created_at')
  dynamic createdAt;

  @JsonKey(name: 'last_updated_at')
  dynamic lastUpdatedAt;

   dynamic id;

  VendorKeyItem({
     this.key,
     this.vendorCode,
     this.createdAt,
     this.lastUpdatedAt,
     this.id,
  });

  factory VendorKeyItem.fromJson(Map<String, dynamic> json) =>
      _$VendorKeyItemFromJson(json);

  Map<String, dynamic> toJson() => _$VendorKeyItemToJson(this);

  Map<String, dynamic> addVendorPayload() => {"key": key, "vendor_code": vendorCode};
}

