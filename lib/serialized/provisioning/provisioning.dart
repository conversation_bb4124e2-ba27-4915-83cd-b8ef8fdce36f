import 'package:json_annotation/json_annotation.dart';
import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/serialized/site_regions/site_regions.dart';

part 'provisioning.g.dart';

@JsonSerializable()
class ProvisioningDeviceList {
  ProvisioningDeviceList(this.result, this.message);
  ProvisioningDeviceList.empty()
      : result = [],
        message = '';
  factory ProvisioningDeviceList.fromJson(Map<String, dynamic> json) => _$ProvisioningDeviceListFromJson(json);
  Map<String, dynamic> toJson() => _$ProvisioningDeviceListToJson(this);
  List<ProvisioningDeviceItem> result;
  String message;

}

@JsonSerializable(includeIfNull: false)
class ProvisioningDeviceItem {
  @JsonKey(name: 'device_eui')
  dynamic deviceEui;
  @JsonKey(name: 'app_key')
  dynamic appKey;
  dynamic type;
  dynamic location;
  dynamic status;
  @JsonKey(name: 'last_seen')
  dynamic lastSeen;
  dynamic selected ;
  dynamic latitude;
  @Json<PERSON>ey(name: 'ds_amps')
  dynamic dsAmps;
  @JsonKey(name: 'site_id')
  dynamic siteId;
  dynamic state;
  SiteDataModel? site;
  @JsonKey(name: 'asset_id')
  dynamic assetId;
  dynamic placement;
  dynamic alarm;
  @JsonKey(name: 'auto_discovered')
  dynamic autoDiscovered;
  dynamic longitude;
  @JsonKey(name: 'fw_version')
  dynamic fwVersion;
  @JsonKey(name: 'xponder_fw_version')
  dynamic transponderFwVersion;
  @JsonKey(name: 'amp_fw_version')
  dynamic ampFwVersion;
  @JsonKey(name: 'vlgw')
  dynamic vlgw;
  @JsonKey(name: 'is_enabled')
  dynamic isEnabled;
  @JsonKey(name: 'vendor_code')
  dynamic vendorCode;
  @JsonKey(includeToJson: false,includeFromJson: false)
  late ApiStatus updateApiStatus;




  ProvisioningDeviceItem({
    this.deviceEui,
    this.appKey,
    this.selected = false,
    this.type,
    this.status,
    this.lastSeen,
    this.location,
    this.latitude,
    this.state,
    this.alarm,
    this.longitude,
    this.dsAmps,
    this.siteId,
    this.site,
    this.assetId,
    this.placement,
    this.autoDiscovered,
    this.fwVersion,
    this.transponderFwVersion,
    this.ampFwVersion,
    this.vlgw,
    this.vendorCode,
    this.isEnabled,
    this.updateApiStatus = ApiStatus.initial,
  });

  ProvisioningDeviceItem.empty();

  factory ProvisioningDeviceItem.fromJson(Map<String, dynamic> json) =>
      _$ProvisioningDeviceItemFromJson(json);

  Map<String, dynamic> toJson() => _$ProvisioningDeviceItemToJson(this);
}
