import 'package:json_annotation/json_annotation.dart';
part 'topology.g.dart';

@JsonSerializable(explicitToJson: true)
class TopologyDeviceModel {
  @J<PERSON><PERSON><PERSON>(name: 'last_alarm_at')
  final double? lastAlarmAt;

  @J<PERSON><PERSON><PERSON>(name: 'last_notification_at')
  final double? lastNotificationAt;

  final String? error;
  final double? altitude;

  @J<PERSON><PERSON><PERSON>(name: 'ds_amps')
  final dynamic dsAmps;

  final String? location;
  final double? latitude;
  final double? longitude;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'last_seen')
  final double? lastSeen;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'asset_id')
  final String? assetId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'fw_version')
  final String? fwVersion;

  @J<PERSON><PERSON><PERSON>(name: 'alarm_flags')
  final int? alarmFlags;

  final dynamic attenuation;
  final dynamic slope;
  final dynamic temperature;

  @<PERSON><PERSON><PERSON>ey(name: 'config_bitmask')
  final int? configBitmask;

  final String? state;
  final String? status;
  final String? placement;
  final bool? provisioned;
  final String? type;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'auto_discovered')
  final dynamic autoDiscovered;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'bw_mode')
  final int? bwMode;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'xponder_fw_version')
  final String? xponderFwVersion;

  @Json<PERSON>ey(name: 'amp_fw_version')
  final String? ampFwVersion;

  @JsonKey(name: 'prod_id')
  final int? prodId;

  @JsonKey(name: 'bootloader_version')
  final String? bootloaderVersion;

  @JsonKey(name: 'device_alias', fromJson: Uri.decodeComponent, defaultValue: "")
  final String? deviceAlias;

  @JsonKey(name: 'description', fromJson: Uri.decodeComponent, defaultValue: "")
  final String? description;

  @JsonKey(name: 'device_eui')
  final String? deviceEui;

  final String? parent;

  final List<TopologyDeviceModel>? children;

  final String? alarm;

  @JsonKey(name: 'alarm_flag_severity')
  final List<dynamic>? alarmFlagSeverity;

  final List<String>? config;

  @JsonKey(name: 'product_id')
  final String? productId;

  TopologyDeviceModel({
    this.lastAlarmAt,
    this.lastNotificationAt,
    this.error,
    this.altitude,
    this.dsAmps,
    this.location,
    this.latitude,
    this.longitude,
    this.lastSeen,
    this.assetId,
    this.fwVersion,
    this.alarmFlags,
    this.attenuation,
    this.slope,
    this.temperature,
    this.configBitmask,
    this.state,
    this.status,
    this.placement,
    this.provisioned,
    this.type,
    this.autoDiscovered,
    this.bwMode,
    this.xponderFwVersion,
    this.ampFwVersion,
    this.prodId,
    this.bootloaderVersion,
    this.deviceAlias,
    this.description,
    this.deviceEui,
    this.parent,
    this.children,
    this.alarm,
    this.alarmFlagSeverity,
    this.config,
    this.productId,
  });

  factory TopologyDeviceModel.fromJson(Map<String, dynamic> json) => _$TopologyDeviceModelFromJson(json);
  Map<String, dynamic> toJson() => _$TopologyDeviceModelToJson(this);
}
