import 'package:json_annotation/json_annotation.dart';

part 'firmware_devices.g.dart';

@JsonSerializable()
class FirmwareDevices {
  dynamic success;
  dynamic message;
  DevicesResult result;

  FirmwareDevices(this.success, this.message, this.result);

  FirmwareDevices.empty()
      : success = false,
        message = '',
        result = DevicesResult.empty();

  factory FirmwareDevices.fromJson(Map<String, dynamic> json) =>
      _$FirmwareDevicesFromJson(json);

  Map<String, dynamic> toJson() => _$FirmwareDevicesToJson(this);
}

@JsonSerializable()
class DevicesResult {
  List<DeviceItem> devices;

  DevicesResult(this.devices);

  DevicesResult.empty() : devices = [];

  factory DevicesResult.fromJson(Map<String, dynamic> json) =>
      _$DevicesResultFromJson(json);

  Map<String, dynamic> toJson() => _$DevicesResultToJson(this);
}

@JsonSerializable()
class DeviceItem {
  @JsonKey(name: 'device_eui')
  String deviceEui;

  String status;

  @JsonKey(name: 'product_id')
  String productId;

  @JsonKey(name: 'device_type')
  String deviceType;

  @JsonKey(name: 'xponder_fw_version')
  String xponderFwVersion;

  @JsonKey(name: 'amp_fw_version')
  String ampFwVersion;

  @JsonKey(name: 'bootloader_version')
  String bootloaderVersion;

  bool secure;
  dynamic selected ;

  DeviceItem(
      this.deviceEui,
      this.status,
      this.productId,
      this.deviceType,
      this.xponderFwVersion,
      this.ampFwVersion,
      this.bootloaderVersion,
      this.secure,
      );

  factory DeviceItem.fromJson(Map<String, dynamic> json) =>
      _$DeviceItemFromJson(json);

  Map<String, dynamic> toJson() => _$DeviceItemToJson(this);
}
