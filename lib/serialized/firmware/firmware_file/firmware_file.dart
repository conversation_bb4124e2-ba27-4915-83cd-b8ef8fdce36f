import 'package:json_annotation/json_annotation.dart';

part 'firmware_file.g.dart';

@JsonSerializable()
class FirmwareFiles {
  dynamic success;
  dynamic message;
  FirmwareItem result;
  FirmwareFiles(this.success, this.message, this.result);

  FirmwareFiles.empty()
      : success = false,
        message = '',
        result = FirmwareItem.empty();

  factory FirmwareFiles.fromJson(Map<String, dynamic> json) =>
      _$FirmwareFilesFromJson(json);

  Map<String, dynamic> toJson() => _$FirmwareFilesToJson(this);


}

@JsonSerializable()
class FirmwareItem {
  FirmwareItem(this.totalCount, this.dataList);

  FirmwareItem.empty()
      :dataList = [];

  factory FirmwareItem.fromJson(Map<String, dynamic> json) =>
      _$FirmwareItemFromJson(json);

  Map<String, dynamic> toJson() => _$FirmwareItemToJson(this);

  @<PERSON><PERSON><PERSON><PERSON>(name: 'total_count')
  dynamic totalCount;

  @<PERSON><PERSON><PERSON>ey(name: 'data_list')
  List<FirmwareData> dataList;
}

@JsonSerializable(includeIfNull: false)
class FirmwareData {
  dynamic type;
  @JsonKey(name: 'firmware_id')
  dynamic firmwareId;
  dynamic filename;
  @JsonKey(name: 'uploaded_at')
  dynamic uploadedAt;
  dynamic version;
  dynamic size;
  dynamic selected;
  @JsonKey(name: 'product_name')
  dynamic productName;
  dynamic signed;
  List<Deployment> deployments;


  FirmwareData({
    this.type,
    this.firmwareId,
    this.filename,
    this.uploadedAt,
    this.version,
    this.size,
    this.selected,
    this.signed,
    this.productName,
    this.deployments = const [],
  });

  FirmwareData.empty()
      : deployments = [];

  factory FirmwareData.fromJson(Map<String, dynamic> json) =>
      _$FirmwareDataFromJson(json);

  Map<String, dynamic> toJson() => _$FirmwareDataToJson(this);


}

@JsonSerializable(includeIfNull: false)
class Deployment {
  dynamic id;
  @JsonKey(name: 'created_at')
  dynamic createdAt;
  @JsonKey(name: 'file_type')
  dynamic fileType;
  List<DevicesDetail> devices;
  @JsonKey(name: 'firmware_file')
  FirmwareFile ?firmwareFile;
  @JsonKey(name: "total_fragments")
  dynamic totalFragments;
  @JsonKey(name: "redundancy_pct")
  dynamic redundancyPct;
  @JsonKey(name: "fragment_rate_ms")
  dynamic fragmentRateMs;
  @JsonKey(name: "status")
  dynamic status;
  @JsonKey(name: "pct_complete")
  dynamic pctComplete;
  @JsonKey(name: "device_count")
  dynamic deviceCount;
  @JsonKey(name: "total_device_count")
  dynamic totalDeviceCount;
  @JsonKey(name: "upgraded_device_count")
  dynamic upgradedDeviceCount;
  @JsonKey(name: "reboot_sent")
  dynamic rebootSent;
  @JsonKey(name: "enable_upgrade")
  dynamic enableUpgrade;
  dynamic remarks;
  @JsonKey(name: "update_mode")
  dynamic updateMode;
  @JsonKey(name: "summary_upgrade_status")
  dynamic summaryUpgradeStatus;
  @JsonKey(name: "summary_status")
  dynamic summaryStatus;
  @JsonKey(name: "duration_sec")
  dynamic durationSec;
  @JsonKey(name: "gw_eui")
  dynamic gwEui;
  dynamic domain;
  dynamic selected;
  @JsonKey(includeFromJson: false, includeToJson: false)
  dynamic isUpgradeLoading;
  Deployment({
    this.id,
    this.createdAt,
    this.fileType,
    this.devices = const [],
    this.firmwareFile,
    this.totalFragments,
    this.redundancyPct,
    this.fragmentRateMs,
    this.status,
    this.pctComplete,
    this.deviceCount,
    this.totalDeviceCount,
    this.upgradedDeviceCount,
    this.rebootSent,
    this.enableUpgrade,
    this.summaryUpgradeStatus,
    this.remarks,
    this.updateMode,
    this.summaryStatus,
    this.selected,
    this.isUpgradeLoading = false,
    this.durationSec,
    this.gwEui,
    this.domain
  });

  Deployment.empty()
      : devices=[],firmwareFile = FirmwareFile.empty();

  factory Deployment.fromJson(Map<String, dynamic> json) =>
      _$DeploymentFromJson(json);

  Map<String, dynamic> toJson() => _$DeploymentToJson(this);


}

@JsonSerializable(includeIfNull: false)
class FirmwareFile {
  @JsonKey(name: 'firmware_id')
  dynamic firmwareId;
  dynamic filename;
  dynamic version;
  dynamic type;
  @JsonKey(name: 'product_name')
  dynamic productName;

  FirmwareFile({
    this.firmwareId,
    this.filename,
    this.version,
    this.type,
    this.productName,
  });

  FirmwareFile.empty();

  factory FirmwareFile.fromJson(Map<String, dynamic> json) =>
      _$FirmwareFileFromJson(json);

  Map<String, dynamic> toJson() => _$FirmwareFileToJson(this);


}
@JsonSerializable(includeIfNull: false)
class DevicesDetail {
  @JsonKey(name: "dev_eui")
  String? deviceEui;
  String? currentFWVersion;
  @JsonKey(name: "reboot_send_status")
  String? rebootSendStatus;
  @JsonKey(name: "missing_frag_count")
  int? missingFragCount;
  @JsonKey(name: "before_version")
  String? beforeVersion;
  String? remarks;
  @JsonKey(name: "after_version")
  String? afterVersion;
  @JsonKey(name: "reboot_sent_at")
  String? rebootSentAt;
  @JsonKey(name: "image_version")
  String? imageVersion;
  @JsonKey(name: "compatibility_status")
  String? compatibilityStatus;
  @JsonKey(name: "frag_setup_status")
  String? fragSetupStatus;
  @JsonKey(name: "deployment_id")
  String? deploymentId;
  String? status;
  @JsonKey(name: "group_setup_status")
  String? groupSetupStatus;
  @JsonKey(name: "display_status")
  String? displayStatus;
  @JsonKey(name: "current_version")
  String? currentVersion;
  @JsonKey(name: "summary_status")
  String? summaryStatus;
  @JsonKey(name: "progress_dots")
  List<ProgressDot> ? progressDots;

  DevicesDetail({
    this.deviceEui,
    this.currentFWVersion,
    this.rebootSendStatus,
    this.missingFragCount,
    this.beforeVersion,
    this.remarks,
    this.afterVersion,
    this.rebootSentAt,
    this.imageVersion,
    this.compatibilityStatus,
    this.fragSetupStatus,
    this.deploymentId,
    this.status,
    this.groupSetupStatus,
    this.displayStatus,
    this.currentVersion,
    this.progressDots,
    this.summaryStatus
  });

  factory DevicesDetail.fromJson(Map<String, dynamic> json) =>
      _$DevicesDetailFromJson(json);

  Map<String, dynamic> toJson() => _$DevicesDetailToJson(this);
}

@JsonSerializable()
class CreateDeploymentFiles {
   dynamic success;
   dynamic message;
   DeploymentFilesItem? result;

  CreateDeploymentFiles({
     this.success,
     this.message,
     this.result,
  });
   CreateDeploymentFiles.empty();

  factory CreateDeploymentFiles.fromJson(Map<String, dynamic> json) => _$CreateDeploymentFilesFromJson(json);

  Map<String, dynamic> toJson() => _$CreateDeploymentFilesToJson(this);
}

@JsonSerializable()
class DeploymentFilesItem {
   Deployment ?deployment;
  @JsonKey(name: "not_provisioned")
   dynamic notProvisioned;
  @JsonKey(name: "skipped_devices")
   List<SkippedDevice> skippedDevices;

  DeploymentFilesItem({
     this.deployment,
    this.notProvisioned,
    this.skippedDevices = const [],
  });

  factory DeploymentFilesItem.fromJson(Map<String, dynamic> json) => _$DeploymentFilesItemFromJson(json);

  Map<String, dynamic> toJson() => _$DeploymentFilesItemToJson(this);
}

@JsonSerializable()
class SkippedDevice {
  @JsonKey(name: 'device_eui')
   dynamic deviceEui;
  @JsonKey(name: 'skipped_reason')
  dynamic skippedReason;
  dynamic message;

  @JsonKey(name: 'deployment_id')
  dynamic deploymentId;

  SkippedDevice({
    this.deviceEui,
    this.skippedReason,
    this.message,
    this.deploymentId,
  });

  factory SkippedDevice.fromJson(Map<String, dynamic> json) => _$SkippedDeviceFromJson(json);

  Map<String, dynamic> toJson() => _$SkippedDeviceToJson(this);
}

@JsonSerializable()
class ProgressDot {
  final String name;
  final String status;
  final String tooltip;

  ProgressDot({
    required this.name,
    required this.status,
    required this.tooltip,
  });

  factory ProgressDot.fromJson(Map<String, dynamic> json) =>
      _$ProgressDotFromJson(json);

  Map<String, dynamic> toJson() => _$ProgressDotToJson(this);
}

