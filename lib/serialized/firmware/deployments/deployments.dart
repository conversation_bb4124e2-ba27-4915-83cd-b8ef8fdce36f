import 'package:json_annotation/json_annotation.dart';
import 'package:quantumlink_node/serialized/firmware/firmware_file/firmware_file.dart';

part 'deployments.g.dart';

@JsonSerializable()
class DeploymentList {
  List<Deployment>? deployments;
  DeploymentList(this.deployments,);
  DeploymentList.empty() : deployments = [] ;

  factory DeploymentList.fromJson(List<dynamic> json) =>
      DeploymentList(json.map((e) => Deployment.fromJson(e)).toList());
  List<Map<String, dynamic>> toJson() =>
      deployments!.map((deployment) => deployment.toJson()).toList();

}


