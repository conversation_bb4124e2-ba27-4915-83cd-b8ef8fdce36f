
import 'package:json_annotation/json_annotation.dart';

import 'package:quantumlink_node/serialized/firmware/firmware_file/firmware_file.dart';

part 'deployment_detail.g.dart';

@JsonSerializable()
class DeploymentDetail {
  @JsonKey(name: "success")
  dynamic success;
  @JsonKey(name: "message")
  dynamic message;
  @JsonKey(name: "result")
  DeploymentDetailItem ? deploymentDetailItem;

  DeploymentDetail({
    this.success,
    this.message,
    this.deploymentDetailItem,
  });
  DeploymentDetail.empty() ;

  factory DeploymentDetail.fromJson(Map<String, dynamic> json) => _$DeploymentDetailFromJson(json);

  Map<String, dynamic> toJson() => _$DeploymentDetailToJson(this);
}

@JsonSerializable()
class DeploymentDetailItem {
  @JsonKey(name: "completed")
  dynamic completed;
  @Json<PERSON>ey(name: "message")
  dynamic message;
  @<PERSON><PERSON><PERSON><PERSON>(name: "deployment")
  Deployment ?deployment;


  DeploymentDetailItem({
    this.completed,
    this.message,
    this.deployment
  });
  DeploymentDetailItem.empty();

  factory DeploymentDetailItem.fromJson(Map<String, dynamic> json) => _$DeploymentDetailItemFromJson(json);

  Map<String, dynamic> toJson() => _$DeploymentDetailItemToJson(this);
}


@JsonSerializable(includeIfNull: false)
class DeploymentDetailDeviseList {
   dynamic success;
   dynamic message;
   @JsonKey(name: "result")
   List<DevicesDetail> deviseList;

  DeploymentDetailDeviseList({
    required this.success,
    required this.message,
    required this.deviseList,
  });

  factory DeploymentDetailDeviseList.fromJson(Map<String, dynamic> json) =>
      _$DeploymentDetailDeviseListFromJson(json);

  Map<String, dynamic> toJson() => _$DeploymentDetailDeviseListToJson(this);
   DeploymentDetailDeviseList.empty() : deviseList = [];
}