import 'package:json_annotation/json_annotation.dart';

part 'gateway_stats.g.dart';

@JsonSerializable()
class GatewayStatsResponse {
  @JsonKey(name: "result")
   GatewayStatsResult ? result;

  GatewayStatsResponse({required this.result});

  factory GatewayStatsResponse.fromJson(Map<String, dynamic> json) =>
      _$GatewayStatsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$GatewayStatsResponseToJson(this);
}

@JsonSerializable()
class GatewayStatsResult {
   Map<String, dynamic> ? stats;
   List<dynamic> ? rssi;
   dynamic gain;

  GatewayStatsResult({
     this.stats,
     this.rssi,
     this.gain,
  });

  factory GatewayStatsResult.fromJson(Map<String, dynamic> json) =>
      _$GatewayStatsResultFromJson(json);

  Map<String, dynamic> toJson() => _$GatewayStatsResultToJson(this);
  GatewayStatsResult.empty();
}
@JsonSerializable()
class Gateways {
   List<GatewayItem> ?items;
   dynamic total;
   dynamic limit;
   dynamic offset;

  Gateways({
    this.items,
    this.total,
    this.limit,
    this.offset,
  });

  factory Gateways.fromJson(Map<String, dynamic> json) =>
      _$GatewaysFromJson(json);

  Map<String, dynamic> toJson() => _$GatewaysToJson(this);
}

@JsonSerializable()
class GatewayItem {
  @JsonKey(name: 'gw_eui')
  dynamic gwEui;

  @JsonKey(name: 'gw_type')
  dynamic gwType;

  @JsonKey(name: 'site_id')
  dynamic siteId;

  dynamic name;
  dynamic latitude;
  dynamic longitude;

  @JsonKey(name: 'hw_version')
  dynamic hwVersion;

  @JsonKey(name: 'sw_version')
  dynamic swVersion;

  @JsonKey(name: 'user_email')
  dynamic userEmail;
  @JsonKey(name: 'last_seen')
  dynamic lastSeen;

  dynamic domain;
  dynamic status;

  GatewayItem({
    this.gwEui,
    this.gwType,
    this.siteId,
    this.name,
    this.latitude,
    this.longitude,
    this.hwVersion,
    this.swVersion,
    this.userEmail,
    this.domain,
    this.lastSeen,
    this.status
  });

  factory GatewayItem.fromJson(Map<String, dynamic> json) => _$GatewayItemFromJson(json);

  Map<String, dynamic> toJson() => _$GatewayItemToJson(this);
  GatewayItem.empty();
}