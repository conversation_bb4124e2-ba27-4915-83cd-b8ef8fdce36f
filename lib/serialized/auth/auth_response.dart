import 'package:json_annotation/json_annotation.dart';

part 'auth_response.g.dart';

@JsonSerializable()
class AuthResponse {
  @J<PERSON><PERSON><PERSON>(name: 'token_type')
  dynamic tokenType;

  dynamic scope;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'expires_in')
  dynamic expiresIn;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'ext_expires_in')
  dynamic extExpiresIn;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'access_token')
  dynamic accessToken;

  @<PERSON>son<PERSON><PERSON>(name: 'refresh_token')
  dynamic refreshToken;

  @Json<PERSON><PERSON>(name: 'id_token')
  dynamic idToken;

  AuthResponse({
     this.tokenType,
     this.scope,
     this.expiresIn,
     this.extExpiresIn,
     this.accessToken,
     this.refreshToken,
     this.idToken,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) =>
      _$AuthResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AuthResponseToJson(this);
  AuthResponse.empty() ;
}
