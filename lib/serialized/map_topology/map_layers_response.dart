import 'package:latlong2/latlong.dart';
import 'package:quantumlink_node/model/amplifier_status_item.dart';
import 'package:quantumlink_node/pages/inventory/map_topology/map_topology_helper.dart';
/// Request model for map layers query API
class MapLayersRequest {
  final List<String> layers;
  final Map<String, dynamic> filters;
  final List<double> bbox;
  final Map<String, dynamic> context;
  final bool autoFit;

  MapLayersRequest({
    required this.layers,
    required this.filters,
    required this.bbox,
    required this.context,
    required this.autoFit,
  });

  Map<String, dynamic> toJson() {
    return {
      "layers": layers,
      "filters": filters,
      "bbox": bbox,
      "context": context,
      "auto_fit": autoFit,
    };
  }
}

/// Response model for map layers query API
class MapLayersResponse {
  final Map<String, dynamic> layers;

  MapLayersResponse({
    required this.layers,
  });

  factory MapLayersResponse.fromJson(Map<String, dynamic> json) {
    return MapLayersResponse(
      layers: json['layers'] ?? {},
    );
  }

  factory MapLayersResponse.empty() {
    return MapLayersResponse(
      layers: {},
    );
  }
}

/// Device model for map layers response
class MapDevice {
  final String deviceEui;
  final String deviceAlias;
  final DetectedStatusType status;
  final double lastSeen;
  final String vendorCode;
  final String description;
  final String type;
  final String placement;
  final int bwMode;
  final LatLng position;

  MapDevice({
    required this.deviceEui,
    required this.deviceAlias,
    required this.status,
    required this.lastSeen,
    required this.vendorCode,
    required this.description,
    required this.type,
    required this.placement,
    required this.bwMode,
    required this.position,
  });

  factory MapDevice.fromJson(Map<String, dynamic> json) {
    final geometry = json['geometry'] as Map<String, dynamic>;
    final properties = json['properties'] as Map<String, dynamic>;
    final coordinates = geometry['coordinates'] as List<dynamic>;

    DetectedStatusType parseDeviceStatus(String status) {
      switch (status.toLowerCase()) {
        case 'online':
          return DetectedStatusType.online;
        case 'offline':
          return DetectedStatusType.offline;
        case 'pending':
          return DetectedStatusType.pending;
        case 'missing_key':
          return DetectedStatusType.missingKey;
        case 'missing_vendor':
          return DetectedStatusType.missingVendor;
        case 'fwdnld':
          return DetectedStatusType.fwDownload;
        default:
          return DetectedStatusType.offline;
      }
    }

    return MapDevice(
      deviceEui: properties['device_eui'] ?? '',
      deviceAlias: properties['device_alias'] ?? '',
      lastSeen: properties['last_seen'] ?? '',
      status:parseDeviceStatus(properties['status'] ?? '') ,
      vendorCode: properties['vendor_code'] ?? '',
      description: properties['description'] ?? '',
      type: properties['type'] ?? '',
      placement: properties['placement'] ?? '',
      bwMode: properties['bw_mode'] ?? -1,
      position: LatLng(
        coordinates[1] as double,
        coordinates[0] as double,
      ), // lat, lng order
    );

  }


}

/// Gateway model for map layers response
class MapGateway {
  final String gatewayId;
  final String type;
  final String name;
  final String status;
  final double? lastSeen;
  final String? domain;
  final String hwVersion;
  final String swVersion;
  final String userEmail;
  final LatLng position;

  MapGateway({
    required this.gatewayId,
    required this.type,
    required this.name,
    required this.status,
    required this.lastSeen,
    required this.domain,
    required this.hwVersion,
    required this.swVersion,
    required this.userEmail,
    required this.position,
  });

  factory MapGateway.fromJson(Map<String, dynamic> json) {
    final geometry = json['geometry'] as Map<String, dynamic>;
    final properties = json['properties'] as Map<String, dynamic>;
    final coordinates = geometry['coordinates'] as List<dynamic>;
    
    return MapGateway(
      gatewayId: properties['gw_eui'] ?? '',
      type: properties['type'] ?? '',
      name: properties['name'] ?? '',
      status: properties['status'] ?? '',
      lastSeen: (properties['last_seen'] as num?)?.toDouble(),
      domain: properties['domain'],
      hwVersion: properties['hw_version'] ?? '',
      swVersion: properties['sw_version'] ?? '',
      userEmail: properties['user_email'] ?? '',
      position: LatLng(coordinates[1], coordinates[0]), // lat, lng order
    );
  }
}

/// Device link model for map layers response
class MapDeviceLink {
  final String sourceId;
  final String targetId;
  final Map<String, dynamic> styleHint;
  final List<LatLng> polylinePoints;

  MapDeviceLink({
    required this.sourceId,
    required this.targetId,
    required this.styleHint,
    required this.polylinePoints,
  });

      factory MapDeviceLink.fromJson(Map<String, dynamic> json) {
    final geometry = json['geometry'] as Map<String, dynamic>;
    final properties = json['properties'] as Map<String, dynamic>;
    final coordinates = geometry['coordinates'] as List<dynamic>;
    
    final polylinePoints = coordinates.map<LatLng>((coord) {
      final coordList = coord as List<dynamic>;
      return LatLng(coordList[1], coordList[0]); // lat, lng order
    }).toList();
    
    return MapDeviceLink(
      sourceId: properties['device_eui'] ?? '',
      targetId: properties['parent'] ?? '',
      styleHint: properties['style_hint'] ?? {},
      polylinePoints: polylinePoints,
    );
  }
}
/// Node model for map layers response
class MapNode {
  final String id;
  final String alias;
  final String description;
  final String placement;
  final double latitude;
  final double longitude;
  final bool isLocationConfirmed;
  final String manufacturer;
  final String model;
  final String? gwEui;
  final LatLng position;

  MapNode({
    required this.id,
    required this.alias,
    required this.description,
    required this.placement,
    required this.latitude,
    required this.longitude,
    required this.isLocationConfirmed,
    required this.manufacturer,
    required this.model,
    required this.gwEui,
    required this.position,
  });

  factory MapNode.fromJson(Map<String, dynamic> json) {
    final geometry = json['geometry'] as Map<String, dynamic>;
    final properties = json['properties'] as Map<String, dynamic>;
    final coordinates = geometry['coordinates'] as List<dynamic>;
    return MapNode(
      id: properties['id'] ?? '',
      gwEui: properties['gw_eui'],
      alias: properties['alias'] ?? '',
      description: properties['description'] ?? '',
      placement: properties['placement'] ?? '',
      latitude: (properties['latitude'] as num?)?.toDouble() ?? 0.0,
      longitude: (properties['longitude'] as num?)?.toDouble() ?? 0.0,
      isLocationConfirmed: properties['is_location_confirmed'] == true,
      manufacturer: properties['manufacturer'] ?? '',
      model: properties['model'] ?? '',
      position: LatLng((coordinates[1] as num).toDouble(), (coordinates[0] as num).toDouble()),
    );
  }
}

/// Node-Device link model for map layers response
class MapNodeDeviceLink {
  final String sourceNodeId;
  final String targetDeviceEui;
  final Map<String, dynamic> styleHint;
  final List<LatLng> polylinePoints;

  MapNodeDeviceLink({
    required this.sourceNodeId,
    required this.targetDeviceEui,
    required this.styleHint,
    required this.polylinePoints,
  });

  factory MapNodeDeviceLink.fromJson(Map<String, dynamic> json) {
    final geometry = json['geometry'] as Map<String, dynamic>;
    final properties = json['properties'] as Map<String, dynamic>;
    final coordinates = geometry['coordinates'] as List<dynamic>;

    final polylinePoints = coordinates.map<LatLng>((coord) {
      final coordList = coord as List<dynamic>;
      return LatLng((coordList[1] as num).toDouble(), (coordList[0] as num).toDouble());
    }).toList();

    return MapNodeDeviceLink(
      sourceNodeId: properties['node_id'] ?? properties['source_id'] ?? '',
      targetDeviceEui: properties['device_eui'] ?? properties['target_id'] ?? '',
      styleHint: properties['style_hint'] ?? {},
      polylinePoints: polylinePoints,
    );
  }
}
// Cluster data model
class DeviceCluster {
  final double lat;
  final double lng;
  final int count;

  DeviceCluster({
    required this.lat,
    required this.lng,
    required this.count,
  });

  factory DeviceCluster.fromJson(Map<String, dynamic> json) {
    final coordinates = json['geometry']['coordinates'] as List<dynamic>;
    final properties = json['properties'] as Map<String, dynamic>;
    return DeviceCluster(
      lng: coordinates[0] as double,
      lat: coordinates[1] as double,
      count: properties['count'] as int,
    );
  }
}
/// Parsed map layers data structure
class ParsedMapLayersData {
  final List<MapDevice> devices;
  final List<MapGateway> gateways;
  final List<MapDeviceLink> deviceLinks;
  final List<DeviceCluster> deviceClusters;
  final List<MapNode> nodes;
  final List<MapNodeDeviceLink> nodeDeviceLinks;

  ParsedMapLayersData({
    required this.devices,
    required this.gateways,
    required this.deviceLinks,
    required this.deviceClusters,
    required this.nodes,
    required this.nodeDeviceLinks,
  });

  factory ParsedMapLayersData.fromMapLayersResponse(MapLayersResponse response) {
    final devices = <MapDevice>[];
    final gateways = <MapGateway>[];
    final deviceLinks = <MapDeviceLink>[];
    final nodeDeviceLinks = <MapNodeDeviceLink>[];
    final nodes = <MapNode>[];
    final deviceClusters = <DeviceCluster>[];

    // Parse devices
    if (response.layers['devices'] != null) {
      final deviceFeatures = response.layers['devices']['features'] as List<dynamic>?;
      if (deviceFeatures != null) {
        for (final feature in deviceFeatures) {
          devices.add(MapDevice.fromJson(feature as Map<String, dynamic>));
        }
      }
    }

    // Parse gateways
    if (response.layers['gateways'] != null) {
      final gatewayFeatures = response.layers['gateways']['features'] as List<dynamic>?;
      if (gatewayFeatures != null) {
        for (final feature in gatewayFeatures) {
          gateways.add(MapGateway.fromJson(feature as Map<String, dynamic>));
        }
      }
    }

    // Parse device links
    if (response.layers['device_links'] != null) {
      final linkFeatures = response.layers['device_links']['features'] as List<dynamic>?;
      if (linkFeatures != null) {
        for (final feature in linkFeatures) {
          deviceLinks.add(MapDeviceLink.fromJson(feature as Map<String, dynamic>));
        }
      }
    }

    // Parse node-device links
    if (response.layers['node_device_links'] != null) {
      final linkFeatures = response.layers['node_device_links']['features'] as List<dynamic>?;
      if (linkFeatures != null) {
        for (final feature in linkFeatures) {
          nodeDeviceLinks.add(MapNodeDeviceLink.fromJson(feature as Map<String, dynamic>));
        }
      }
    }

    // Parse nodes
    if (response.layers['nodes'] != null) {
      final nodeFeatures = response.layers['nodes']['features'] as List<dynamic>?;
      if (nodeFeatures != null) {
        for (final feature in nodeFeatures) {
          nodes.add(MapNode.fromJson(feature as Map<String, dynamic>));
        }
      }
    }

    if (response.layers['device_clusters'] != null) {
      final clusterFeatures = response.layers['device_clusters']['features'] as List<dynamic>?;
      if (clusterFeatures != null) {
        for (final feature in clusterFeatures) {
          deviceClusters.add(DeviceCluster.fromJson(feature as Map<String, dynamic>));
        }
      }
    }

    return ParsedMapLayersData(
      devices: devices,
      gateways: gateways,
      deviceLinks: deviceLinks,
      deviceClusters: deviceClusters,
      nodes: nodes,
      nodeDeviceLinks: nodeDeviceLinks,
    );
  }

  factory ParsedMapLayersData.empty() {
    return ParsedMapLayersData(
      devices: [],
      gateways: [],
      deviceLinks: [],
      deviceClusters: [],
      nodes: [],
      nodeDeviceLinks: [],
    );
  }
} 