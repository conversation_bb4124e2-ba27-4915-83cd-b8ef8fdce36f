import 'package:json_annotation/json_annotation.dart';

import '../site_regions/site_regions.dart';

part 'vlgw.g.dart';

@JsonEnum()
enum VlgwState {
  @JsonValue("registered")
  online,
  @JsonValue("unregistered")
  offline,
  @JsonValue("unknown")
  unknown,
}

@JsonSerializable()
class VLGWs {
  VLGWs(this.result, this.message);

  VLGWs.empty()
      : result = [],
        message = '';

  factory VLGWs.fromJson(Map<String, dynamic> json) => _$VLGWsFromJson(json);

  Map<String, dynamic> toJson() => _$VLGWsToJson(this);
  List<VLGW> result;
  String message;
}
@JsonSerializable(includeIfNull: false, explicitToJson: true)
class VLGW {
  dynamic id;
  dynamic name;
  dynamic description;
  dynamic eui;

  @JsonKey(name: 'site_id')
  dynamic siteId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  dynamic createdAt;

  @JsonKey(name: 'updated_at')
  dynamic updatedAt;

  @Json<PERSON>ey(name: 'last_seen')
  dynamic lastSeen;

  @JsonKey(name: 'start_time')
  dynamic startTime;

  dynamic lat;
  dynamic lng;
  dynamic alt;

  dynamic status;
  dynamic state;

  @JsonKey(name: 'ndr_config')
  SessionConfig? ndrConfig;

  @JsonKey(name: 'ndf_config')
  SessionConfig? ndfConfig;

  @JsonKey(name: 'amps_count')
  dynamic ampsCount;

  SiteDataModel? site;

  @JsonKey(name: 'chirpstack_server_host')
  dynamic chirpStackServerHost;

  @JsonKey(name: 'chirpstack_server_port')
  dynamic chirpStackServerPort;

  @JsonKey(name: 'vlgw_ip')
  dynamic vlgwIp;

  @JsonKey(name: 'vlgw_port')
  dynamic vlgwPort;

  dynamic interface;

  @JsonKey(name: 'rpd_ip')
  dynamic? rpdIp;

  @JsonKey(name: 'ndr_session_id')
  dynamic ndrSessionId;

  @JsonKey(name: 'ndf_session_id')
  dynamic ndfSessionId;

  @JsonKey(name: 'ndf_dest_ip')
  dynamic ndfDestIp;

  VLGW({
    this.id,
    this.name,
    this.description,
    this.eui,
    this.siteId,
    this.createdAt,
    this.updatedAt,
    this.lastSeen,
    this.startTime,
    this.lat,
    this.lng,
    this.alt,
    this.status,
    this.state,
    this.ndrConfig,
    this.ndfConfig,
    this.ampsCount,
    this.site,
    this.chirpStackServerHost,
    this.chirpStackServerPort,
    this.vlgwIp,
    this.vlgwPort,
    this.interface,
    this.rpdIp,
    this.ndrSessionId,
    this.ndfSessionId,
    this.ndfDestIp,
  });
  VLGW.empty();

  factory VLGW.fromJson(Map<String, dynamic> json) => _$VLGWFromJson(json);

  Map<String, dynamic> toJson() => _$VLGWToJson(this);
}

@JsonSerializable(includeIfNull: false)
class VLGWFSKStats {
  int? demodGoodPkts;
  int? demodBadPkts;
  int? demodDroppedPkts;
  int? demodNotFoundPkts;
  bool? demodDiagnostic;
  int? demoInputLevel;
  bool? modDiagnostic;
  int? modOutputLevel;
  int? modPkts;
  int? badChannels;
  int? framesDropped;

  VLGWFSKStats();
  VLGWFSKStats.empty();

  factory VLGWFSKStats.fromJson(Map<String, dynamic> json) =>
      _$VLGWFSKStatsFromJson(json);

  Map<String, dynamic> toJson() => _$VLGWFSKStatsToJson(this);
}
//---------------------------->Multiple VLGW Details Model <----------------------------------------------

// @JsonSerializable()
// class VLGWs {
//   @JsonKey(name: "success")

//   @JsonKey(name: "message")
//   String message;
//   @JsonKey(name: "result")
//   List<VLGW> result;
//   VLGWs.empty()
//       : result = [],
//         message = '',
//         success = false;
//
//   VLGWs({

//     required this.message,
//     required this.result,
//   });
//
//   factory VLGWs.fromJson(Map<String, dynamic> json) => _$VLGWsFromJson(json);
//
//   Map<String, dynamic> toJson() => _$VLGWsToJson(this);
// }
//
// @JsonSerializable()
// class VLGW {
//   @JsonKey(name: "id")
//   dynamic id;
//   @JsonKey(name: "name")
//   dynamic name;
//   @JsonKey(name: "description")
//   dynamic description;
//   @JsonKey(name: "eui")
//   dynamic eui;
//   @JsonKey(name: "last_seen")
//   dynamic lastSeen;
//   @JsonKey(name: "chirpstack_bridge_ip")
//   dynamic chirpstackBridgeIp;
//   @JsonKey(name: "chirpstack_bridge_port")
//   dynamic chirpstackBridgePort;
//   @JsonKey(name: "status")
//   dynamic status;
//   @JsonKey(name: "deviceDetail")
//   List<DeviceDetail>? deviceDetail;
//
//   VLGW({
//     this.id,
//     this.name,
//     this.description,
//     this.eui,
//     this.lastSeen,
//     this.status,
//     this.chirpstackBridgePort,
//     this.deviceDetail,
//     this.chirpstackBridgeIp
//   });
//
//   factory VLGW.fromJson(Map<String, dynamic> json) => _$VLGWFromJson(json);
//
//   Map<String, dynamic> toJson() => _$VLGWToJson(this);
// }
//
// @JsonSerializable()
// class DeviceDetail {
//   @JsonKey(name: "created_at")
//   dynamic createdAt;
//   @JsonKey(name: "updated_at")
//   dynamic updatedAt;
//   @JsonKey(name: "last_seen")
//   dynamic startTime;
//   @JsonKey(name: "lat")
//   dynamic lat;
//   @JsonKey(name: "lng")
//   dynamic lng;
//   @JsonKey(name: "alt")
//   dynamic alt;
//   @JsonKey(name: "state")
//   dynamic state;
//   @JsonKey(name: "ndr_config")
//   SessionConfig? ndrConfig;
//   @JsonKey(name: "ndf_config")
//   SessionConfig ?ndfConfig;
//
//   DeviceDetail({
//     this.createdAt,
//     this.updatedAt,
//     this.startTime,
//     this.lat,
//     this.lng,
//     this.alt,
//     this.state,
//     this.ndrConfig,
//     this.ndfConfig,
//   });
//
//   factory DeviceDetail.fromJson(Map<String, dynamic> json) => _$DeviceDetailFromJson(json);
//
//   Map<String, dynamic> toJson() => _$DeviceDetailToJson(this);
//
//   getNDRSessionValue() {
//     if (ndrConfig != null) {
//       return '0x${ndrConfig!.sessionId}, ${ndrConfig!.src}, ${ndrConfig!.dst}, ${ndrConfig!.interface}';
//     } else {
//       return null;
//     }
//   }
//
//   getNDFSessionValue() {
//     if (ndfConfig != null) {
//       return '0x${ndfConfig!.sessionId}, ${ndfConfig!.src}, ${ndfConfig!.dst}, ${ndfConfig!.interface}';
//     }
//     return null;
//   }
// }

@JsonSerializable()
class VLGWConfig {
  VLGWConfig(this.result, this.message);
  factory VLGWConfig.fromJson(Map<String, dynamic> json) => _$VLGWConfigFromJson(json);

  Map<String, dynamic> toJson() => _$VLGWConfigToJson(this);

  VLGWResult? result;
  String? message;

  VLGWConfig.empty() : result = null, message = null;
}

@JsonSerializable()
class VLGWResult {
  VLGWResult(this.ndr, this.ndf, this.channels);
  factory VLGWResult.fromJson(Map<String, dynamic> json) => _$VLGWResultFromJson(json);
  Map<String, dynamic> toJson() => _$VLGWResultToJson(this);

  SessionConfig? ndr;
  SessionConfig? ndf;
  List<Channel>? channels;

  VLGWResult.empty()
      : ndr = null,
        ndf = null,
        channels = [];
}

@JsonSerializable()
class SessionConfig {
  @JsonKey(name: 'api_port')
  dynamic apiPort;
  @JsonKey(name: 'session_id')
  dynamic sessionId;
  dynamic src;
  dynamic dst;
  dynamic interface;
  @JsonKey(name: 'active_config')
  ActiveConfig ?activeConfig;

  SessionConfig(
    this.apiPort,
    this.sessionId,
    this.src,
    this.dst,
    this.interface,
    this.activeConfig,
  );

  factory SessionConfig.fromJson(Map<String, dynamic> json) =>
      _$SessionConfigFromJson(json);

  Map<String, dynamic> toJson() => _$SessionConfigToJson(this);
   SessionConfig.empty();
}

@JsonSerializable()
class Channel {
  Channel(this.ch, this.us, this.ds, this.state);
  factory Channel.fromJson(Map<String, dynamic> json) => _$ChannelFromJson(json);
  Map<String, dynamic> toJson() => _$ChannelToJson(this);

  dynamic ch;
  dynamic us;
  dynamic  ds;
  dynamic state;

  Channel.empty();
}


@JsonSerializable()
class ActiveConfig {
   dynamic description;
   dynamic name;
   dynamic pulseShapingBt;

  ActiveConfig({
     this.description,
     this.name,
     this.pulseShapingBt,
  });

  factory ActiveConfig.fromJson(Map<String, dynamic> json) =>
      _$ActiveConfigFromJson(json);

  Map<String, dynamic> toJson() => _$ActiveConfigToJson(this);
}

@JsonSerializable()
class ConfigVersion {
  ConfigVersion(this.result, this.message);
  factory ConfigVersion.fromJson(Map<String, dynamic> json) => _$ConfigVersionFromJson(json);
  Map<String, dynamic> toJson() => _$ConfigVersionToJson(this);

  Version? result;
  String? message;

  ConfigVersion.empty() : result = null, message = null;
}

@JsonSerializable()
class Version {
  Version(this.version, this.ndrVersion, this.ndfVersion);
  factory Version.fromJson(Map<String, dynamic> json) => _$VersionFromJson(json);
  Map<String, dynamic> toJson() => _$VersionToJson(this);

  dynamic version;
  @JsonKey(name: 'ndr_version')
  dynamic ndrVersion;
  @JsonKey(name: 'ndf_version')
  dynamic ndfVersion;

  Version.empty();
}
