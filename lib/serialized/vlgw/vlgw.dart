import 'package:json_annotation/json_annotation.dart';

import '../site_regions/site_regions.dart';

part 'vlgw.g.dart';

@JsonEnum()
enum VlgwState {
  @JsonValue("registered")
  online,
  @JsonValue("unregistered")
  offline,
  @JsonValue("unknown")
  unknown,
}

@JsonSerializable()
class VLGWs {
  VLGWs(this.result, this.message);

  VLGWs.empty()
      : result = [],
        message = '';

  factory VLGWs.fromJson(Map<String, dynamic> json) {
    return VLGWs(
      (json['items'] as List?)?.map((e) => VLGW.fromJson(e)).toList() ?? [],
      '',
    );
  }

  Map<String, dynamic> toJson() => _$VLGWsToJson(this);
  List<VLGW> result;
  String message;
}

@JsonSerializable(includeIfNull: false, explicitToJson: true)
class VLGW {
  dynamic id;
  dynamic name;
  dynamic description;
  @Json<PERSON>ey(name: 'gw_eui')
  dynamic eui;

  @Json<PERSON>ey(name: 'site_id')
  dynamic siteId;

  @JsonKey(name: 'created_at')
  dynamic createdAt;

  @JsonKey(name: 'updated_at')
  dynamic updatedAt;

  @JsonKey(name: 'last_seen')
  dynamic lastSeen;

  @JsonKey(name: 'start_time')
  dynamic startTime;

  dynamic lat;
  dynamic lng;
  dynamic alt;

  dynamic status;
  dynamic state;

  @JsonKey(name: 'ndr_config')
  SessionConfig? ndrConfig;

  @JsonKey(name: 'ndf_config')
  SessionConfig? ndfConfig;

  @JsonKey(name: 'amps_count')
  dynamic ampsCount;

  SiteDataModel? site;

  @JsonKey(name: 'chirpstack_server_host')
  dynamic chirpStackServerHost;

  @JsonKey(name: 'chirpstack_server_port')
  dynamic chirpStackServerPort;

  @JsonKey(name: 'vlgw_ip')
  dynamic vlgwIp;

  @JsonKey(name: 'vlgw_port')
  dynamic vlgwPort;

  dynamic interface;

  @JsonKey(name: 'rpd_ip')
  dynamic? rpdIp;

  @JsonKey(name: 'ndr_session_id')
  dynamic ndrSessionId;

  @JsonKey(name: 'ndf_session_id')
  dynamic ndfSessionId;

  @JsonKey(name: 'ndf_dest_ip')
  dynamic ndfDestIp;

  @JsonKey(name: 'snapshot')
  Map<String, dynamic>? snapshot;


  VLGW({
    this.id,
    this.name,
    this.description,
    this.eui,
    this.siteId, 
    this.createdAt,
    this.updatedAt,
    this.lastSeen,
    this.startTime,
    this.lat,
    this.lng,
    this.alt,
    this.status,
    this.state,
    this.ndrConfig,
    this.ndfConfig,
    this.ampsCount,
    this.site,
    this.chirpStackServerHost,
    this.chirpStackServerPort,
    this.vlgwIp,
    this.vlgwPort,
    this.interface,
    this.rpdIp,
    this.ndrSessionId,
    this.ndfSessionId,
    this.ndfDestIp,
    this.snapshot,
    // hwVersion,
    // swVersion,
    // userEmail,
    // domain,
  });
  VLGW.empty();

  factory VLGW.fromJson(Map<String, dynamic> json) {
    final instance = _$VLGWFromJson(json);
    // Add logic to extract rpd_ip from the snapshot if it exists
    final snapshot = json['snapshot'] as Map<String, dynamic>?;
    if (snapshot != null) {
      instance.ndrSessionId = snapshot['ndr_session_id'];
      instance.ndfSessionId = snapshot['ndf_session_id'];
      instance.interface = snapshot['interface'];
      instance.rpdIp = snapshot['rpd_ip'];
      instance.vlgwIp =snapshot['vlgw_ip'];
      instance.vlgwPort =snapshot['vlgw_port'];
      instance.chirpStackServerHost= snapshot['chirpstack_server_host'];
      instance.chirpStackServerPort = snapshot['chirpstack_server_port'];
    }
    // Extract amps_count from nested site
    final site = json['site'] as Map<String, dynamic>?;
    if (site != null) {
      instance.ampsCount = site['amps_count'];
    }
    return instance;
  }

  Map<String, dynamic> toJson() => _$VLGWToJson(this);
}

@JsonSerializable(includeIfNull: false)
class VLGWFSKStats {
  int? demodGoodPkts;
  int? demodBadPkts;
  int? demodDroppedPkts;
  int? demodNotFoundPkts;
  bool? demodDiagnostic;
  int? demodInputLevel;
  bool? modDiagnostic;
  int? modOutputLevel;
  int? modPkts;
  int? badChannels;
  int? framesDropped;

  VLGWFSKStats();
  VLGWFSKStats.empty();

  factory VLGWFSKStats.fromJson(Map<String, dynamic> json) =>
      _$VLGWFSKStatsFromJson(json);

  Map<String, dynamic> toJson() => _$VLGWFSKStatsToJson(this);
}

@JsonSerializable()
class VLGWConfig {
  VLGWConfig(this.result, this.message);
  factory VLGWConfig.fromJson(Map<String, dynamic> json) => _$VLGWConfigFromJson(json);

  Map<String, dynamic> toJson() => _$VLGWConfigToJson(this);

  VLGWResult? result;
  String? message;

  VLGWConfig.empty() : result = null, message = null;
}

@JsonSerializable()
class VLGWResult {
  VLGWResult(this.ndr, this.ndf, this.channels);
  factory VLGWResult.fromJson(Map<String, dynamic> json) => _$VLGWResultFromJson(json);
  Map<String, dynamic> toJson() => _$VLGWResultToJson(this);

  SessionConfig? ndr;
  SessionConfig? ndf;
  List<Channel>? channels;

  VLGWResult.empty()
      : ndr = null,
        ndf = null,
        channels = [];
}

@JsonSerializable()
class SessionConfig {
  @JsonKey(name: 'api_port')
  dynamic apiPort;
  @JsonKey(name: 'session_id')
  dynamic sessionId;
  dynamic src;
  dynamic dst;
  dynamic interface;
  @JsonKey(name: 'active_config')
  ActiveConfig? activeConfig;

  SessionConfig(
    this.apiPort,
    this.sessionId,
    this.src,
    this.dst,
    this.interface,
    this.activeConfig,
  );

  factory SessionConfig.fromJson(Map<String, dynamic> json) =>
      _$SessionConfigFromJson(json);

  Map<String, dynamic> toJson() => _$SessionConfigToJson(this);
  SessionConfig.empty();
}

@JsonSerializable()
class Channel {
  Channel(this.ch, this.us, this.ds, this.state);
  factory Channel.fromJson(Map<String, dynamic> json) => _$ChannelFromJson(json);
  Map<String, dynamic> toJson() => _$ChannelToJson(this);

  dynamic ch;
  dynamic us;
  dynamic ds;
  dynamic state;

  Channel.empty();
}

@JsonSerializable()
class ActiveConfig {
  dynamic description;
  dynamic name;
  dynamic pulseShapingBt;

  ActiveConfig({
    this.description,
    this.name,
    this.pulseShapingBt,
  });

  factory ActiveConfig.fromJson(Map<String, dynamic> json) =>
      _$ActiveConfigFromJson(json);

  Map<String, dynamic> toJson() => _$ActiveConfigToJson(this);
}

@JsonSerializable()
class ConfigVersion {
  ConfigVersion(this.result, this.message);
  factory ConfigVersion.fromJson(Map<String, dynamic> json) => _$ConfigVersionFromJson(json);
  Map<String, dynamic> toJson() => _$ConfigVersionToJson(this);

  Version? result;
  String? message;

  ConfigVersion.empty() : result = null, message = null;
}

@JsonSerializable()
class Version {
  Version(this.version, this.ndrVersion, this.ndfVersion);
  factory Version.fromJson(Map<String, dynamic> json) => _$VersionFromJson(json);
  Map<String, dynamic> toJson() => _$VersionToJson(this);

  dynamic version;
  @JsonKey(name: 'ndr_version')
  dynamic ndrVersion;
  @JsonKey(name: 'ndf_version')
  dynamic ndfVersion;

  Version.empty();
}