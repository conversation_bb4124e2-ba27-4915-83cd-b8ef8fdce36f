import 'package:quantumlink_node/app_import.dart';

class SiteRegionsController extends GetxController {
  final SiteRepository _siteRepository = GetIt.instance.get<SiteRepository>();
  final DashboardRepository dashboardRepository = GetIt.instance.get<DashboardRepository>();

  Future<List<dynamic>> getSiteRegionsDeviceList({required BuildContext context}) async {
    return await _siteRepository.getSiteList(context: context);
  }

  Future<Map<String, dynamic>?> addSiteDevice(
      {required SiteDataModel itemData, required BuildContext context}) async {
    final response = await _siteRepository.addSite(siteData: itemData, context: context);
    return response;
  }
  Future<Map<String, dynamic>?> updateSiteDevice(
      {required SiteDataModel itemData,required BuildContext context}) async {
    final response = await _siteRepository.updateSite(siteData: itemData,context: context);
    return response;
  }
  Future<Map<String,dynamic>?> deleteSiteDevice(
      {required String siteId}) async {
    return await _siteRepository.deleteSiteDevice(siteId: siteId);
  }

  Future<dynamic> getDashboardDataList(BuildContext context) async {
    return await dashboardRepository.getDashboardData(context);
  }
}
