import 'dart:html';

import 'package:quantumlink_node/app/cache/app_shared_preference.dart';
import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/main_ql_node.dart';

class AuthController extends GetxController {
  final GoogleAuthRepository googleAuth = getIt.get<GoogleAuthRepository>();
  Config? msConfig;
  updateMsConfig(email) async {

    final regex = RegExp(r'^[^@\s]+@([^@\s]+\.[^@\s]+)$');
    final match = regex.firstMatch(email);
    var domain = match != null ? match.group(1) : null;

    if(domain!=null){
      await AppConfig.shared.fetchConfigFromFirebase(domain);
    }

    msConfig = Config(
      tenant: AppConfig.shared.tenant,
      clientId: AppConfig.shared.clientId,
      scope: "openid profile offline_access ${AppConfig.shared.clientId}/.default",
      redirectUri: window.location.origin,loginHint: email ,
      postLogoutRedirectUri: window.location.origin,
      navigatorKey: rootNavigator<PERSON>ey,
      loader: const AppLoader(),
    );
  }


  AuthRepository authRepository = GetIt.instance<AuthRepository>();

  Future<void> signIn(context, email) async {
    if (!AppConfig.shared.aadAuth) {
      await setAT("Guest");
      await validateToken(context);
      return;
    }
    await authRepository.logOut(msConfig);
    await authRepository.signIn(msConfig, (t) async {
      if(t.toString().isNotEmpty){
        validateDomainAfterSignIn(context, t.toString(), email);
      }
    });
  }

  Future<void> signInWithGoogle(context,email) async {
    if (!AppConfig.shared.aadAuth) {
      await setAT("Guest");
      await validateToken(context);
      return;
    }
    Map<String, dynamic>? userTokenData = await googleAuth.signInWithGoogle(email);
    if(userTokenData != null){
      accessToken = userTokenData["idToken"] ?? "";
      validateDomainAfterSignIn(context, accessToken, email);
    }
  }

  validateDomainAfterSignIn(context, token, email) async {
    await setAT(token.toString());
    await getUserInfoFromToken();
    final enterDomain = email.toString().split('@').last.toLowerCase().trim();
    final loggedInDomain = userInformation.uniqueName.toString().split('@').last.toLowerCase().trim();
    if(enterDomain==loggedInDomain){
      setAuditLogsData(c: context, message: AppStringConstants.loginSuccess, operation: AppStringConstants.userLogin);
      await validateToken(context);
    }
    else{
      S.of(context).domainMisMatch.showMessage();
    }

  }


  Future<void> setAuditLogsData(
      {required BuildContext c, required String operation, required String message}) async {

    await getUserInfoFromToken();
    String ipAddress = await authRepository.getPublicIP();
    AuditLogUserData userData = AuditLogUserData(
        operation: operation,
        message: message,
        ipAddress: ipAddress,
        sourceService: "",
        application: AppConfig.shared.isQLCentral ? AppStringConstants.qlCentral : AppStringConstants.qlNode,
        userEmail: userInformation.uniqueName);
     await  authRepository.setAuditLogUser(c, userData);
  }

  Future<void> refreshToken() async {
    await getUserInfoFromToken();

    if(userInformation.uniqueName!=null){
      await updateMsConfig(userInformation.uniqueName);
    }

   await authRepository.refreshSession(msConfig, (t) async {
     final context = rootNavigatorKey.currentContext!;
      if (t.toString().isNotEmpty) {
        await removeAT();
        await setAT(t.toString());
        await setAuditLogsData(c: context,message: AppStringConstants.tokenRefreshSuccess,operation: AppStringConstants.refreshToken);
      }else{
        await logOut(isTokenInvalid: true);
      }
    });
  }

  Future<Response?> signWithPassword(BuildContext context,
      {required String emailId, required String password}) async {
    Response? data =
    await authRepository.signWithPassword(context, email: emailId, password: password);
    return data;
  }


  Future<void> logOut({bool isTokenInvalid =false}) async {
    final context = rootNavigatorKey.currentContext!;
    try {
      await getUserInfoFromToken();
      if (userInformation.uniqueName != null) {
        await updateMsConfig(userInformation.uniqueName);
      }
      if (!AppConfig.shared.aadAuth || accessToken == "Guest") {
        await removeAT();
        Future.delayed(const Duration(milliseconds: 200), () {
          goToSignInPage();
        });
        return;
      } else {
        // for QL central and aad auth true
        if (isTokenInvalid) { // for invalid token unable to call api due to validation
         
        } else {
          await setAuditLogsData(
              c: context,
              message: AppStringConstants.logoutSuccess,
              operation: AppStringConstants.userLogout);
        }
        removeAT();
        if (AppConfig.shared.isGoogleSignIn) {
          await googleAuth.signOut();
        } else {
          await authRepository.logOut(msConfig);
        }

        Future.delayed(const Duration(milliseconds: 200), () {
          goToSignInPage();
        });
      }
    } catch (e) {
      debugLogs("logOut -->$e");
      removeAT();
      Future.delayed(const Duration(milliseconds: 200), () {
        goToSignInPage();
      });
    }
  }

  Future<void> removeAT() async {
    debugLogs("removeAT ->");
    await removePrefValue(AppSharedPreference.accessToken);
  }

  Future<void> setAT(t) async {
    debugLogs("setAT -> $t");
    await setPrefStringValue(AppSharedPreference.accessToken, t);
  }
  Future<void> setAtRefreshToken(t) async {
    debugLogs("setATRefreshToken -> $t");
    await setPrefStringValue(AppSharedPreference.refreshTokenId, t);
  }

  Future<void> getAT() async {
    debugLogs("getAT -> ");
    if (await checkPrefKey(AppSharedPreference.accessToken)) {
      accessToken = await getPrefStringValue(AppSharedPreference.accessToken);
    }
  }

  dynamic accessToken;

  Future<void> validateToken(context) async {
    debugLogs("validateToken -> ");
    await getAT();
    if (accessToken != null) {
      debugLogs("accessToken -> $accessToken");
      gotoDashboardPage();
    } else {
      // if (AppConfig.shared.isQLCentral == false) {
      //   debugLogs("Enter QL NODE ===> ");
      //   Map<String, dynamic> isWifiChecked = await authRepository.checkWifi(context);
      //   if (isWifiChecked.containsKey('internet') && isWifiChecked['internet'] == true) {
      //     goToSignInPage();
      //   } else {
      //     gotoSSIdReportPage();
      //   }
      // } else {
      goToSignInPage();
      // }
    }
  }

  goToSignInAfterSignOut() async {
     debugLogs("validateToken -> ");
     await getAT();

     if(accessToken!=null){
       debugLogs("accessToken -> $accessToken");

     }
     else{
       goToSignInPage();
       update();
     }
   }



   UserInformationModel userInformation = UserInformationModel.empty();

  getUserInfoFromToken() async {
    await getAT();
    String yourToken = "$accessToken";
    if (!AppConfig.shared.aadAuth) { // If sign in by pass
      Map<String, dynamic> guestUserData = {"name": "Guest"};
      userInformation = UserInformationModel.fromJson(guestUserData);
      return;
    }
    try {
      Map<String, dynamic> decodeToken = JwtDecoder.decode(yourToken);
      userInformation = UserInformationModel.fromJson(decodeToken);
    } catch (e, s) {
      print(s);
    }

  }

   Future<String> setUserName() async {
    await getUserInfoFromToken();
    List<String> nameParts = userInformation.name.split(" ");
    return nameParts.isNotEmpty ? nameParts[0][0] + (nameParts.length > 1 ? nameParts[1][0] : "") : "";
  }

  Future<void> getAllDomains() async {
    return await authRepository.getAllDomains();
  }

  bool isEmailDomainAllowed(String email)  {
    if (email.isEmpty) return false;
    final domain = email.split('@').last.toLowerCase();
    return AppConfig.shared.allowedEmailDomains
        .any((allowedDomain) => domain == allowedDomain.toLowerCase());
  }

  setIsGoogleOrMSSignInOnLaunchIfAlreadySignIn(){

  }

}
