import 'package:quantumlink_node/app_import.dart';


class GWSettingController extends GetxController {
  final GwSettingRepository gwSettingRepository = GetIt.instance.get<GwSettingRepository>();

  Future<dynamic> gwSettingHealthCheck({required BuildContext context}) async {
    return {"statusCode" : 200};
    return await gwSettingRepository.gwSettingHealthCheck(context: context);
  }

  Future<dynamic> getStats({required BuildContext context, String? gwID}) async {
    return await gwSettingRepository.getStats(context: context ,gwID: gwID);
  }

  Future<Map<String, dynamic>?> setDsGain(
      {required BuildContext context, required Map<String, dynamic> bodyData, String? gwID}) async {
    return await gwSettingRepository.setDsGain(context: context, bodyData: bodyData,gwID: gwID);
  }

  Future<dynamic> resetStats({required BuildContext context, String? gwID}) async {
    return await gwSettingRepository.resetStats(context: context,gwID: gwID);
  }


  Future<dynamic> getGWDetail({required BuildContext context, String? gwID}) async {
    return await gwSettingRepository.getGWDetail(context: context,gwID: gwID);
  }

}