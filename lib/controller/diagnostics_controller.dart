import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/main.dart';

class DiagnosticsController extends GetxController {


  DashboardRepository dashboardRepository = getIt.get<DashboardRepository>();

  Future<dynamic> checkHealth(BuildContext context, {String? otherBaseUrl}) async {
    return await dashboardRepository.checkHealth(context, otherBaseUrl: otherBaseUrl);
  }

  Future<dynamic> getBackendVersions(BuildContext context, {String? otherBaseUrl}) async {
    return await dashboardRepository.getBackendVersions(context, otherBaseUrl: otherBaseUrl);
  }
}