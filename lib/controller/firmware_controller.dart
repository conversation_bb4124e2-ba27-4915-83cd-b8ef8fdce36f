import 'package:quantumlink_node/app_import.dart';

class FirmwareController extends GetxController {
  final FirmwareRepository firmwareRepository = GetIt.instance.get<FirmwareRepository>();

  Future<dynamic> uploadFirmware(
      {required BuildContext context,
      required PlatformFile fileFormate}) async {
    return await firmwareRepository.uploadFiles(
        context: context, fileFormate: fileFormate);
  }

  Future<dynamic> getFirmwareFileList(
      {required BuildContext context, required int pageOffset, required int perPageLimit}) async {
    return await firmwareRepository.getFirmwareFileList(
        context: context, pageOffset: pageOffset, perPageLimit: perPageLimit);
  }

  Future<dynamic> getFirmwareFileType({required BuildContext context}) async {
    return await firmwareRepository.getFirmwareFileType(context: context);
  }

  Future<dynamic> getDeploymentsList({required BuildContext context, required int pageOffset, required int perPageLimit}) async {
    return await firmwareRepository.getDeploymentsList(context: context, pageOffset: pageOffset, perPageLimit: perPageLimit);
  }

  Future<dynamic> createDeployment(
      {required BuildContext context, required CreateDeploymentItem createDeployment}) async {
    return await firmwareRepository.createDeployment(
        context: context, createDeployment: createDeployment);
  }
  Future<dynamic> deleteFirmware({required String firmwareId}) async {
    return await firmwareRepository.deleteFirmware(firmwareId: firmwareId);
  }

  Future<dynamic> deleteDeployment({required String deploymentId}) async {
    return await firmwareRepository.deleteDeployment(deploymentId: deploymentId);
  }

  Future<dynamic> getDeploymentDetail(
      {required BuildContext context, required String deploymentId}) async {
    return await firmwareRepository.getDeploymentDetail(
        context: context, deploymentId: deploymentId);
  }

  Future<dynamic> applyDeployment(
      {required BuildContext context, required String deploymentId}) async {
    return await firmwareRepository.applyDeployment(
        context: context, deploymentId: deploymentId);
  }

  Future<dynamic> upgradeDeployment(
      {required BuildContext context, required String devEui,required String deploymentId}) async {
    return await firmwareRepository.upgradeDeployment(
        context: context, devEui: devEui, deploymentId: deploymentId);
  }

  Future<dynamic> getDeploymentDetailDeviseList(
      {required BuildContext context,
      required String deploymentId,
      required int pageOffset,
      required int perPageLimit}) async {
    return await firmwareRepository.getDeploymentDetailDeviseList(
        context: context,
        deploymentId: deploymentId,
        pageOffset: pageOffset,
        perPageLimit: perPageLimit);
  }

  Future<dynamic> deleteMultipleFirmwares(
      {required BuildContext context, required List firmwareIds}){
    return firmwareRepository.deleteMultipleFirmwares(context: context, firmwareIds: firmwareIds);
  }
  Future<dynamic> deleteMultipleDeployments(
      {required BuildContext context, required List deploymentsIds}){
    return firmwareRepository.deleteMultipleDeployments(context: context, deploymentsIds: deploymentsIds);
  }

  Future<dynamic> getFirmwareDeviseList(
      {required BuildContext context,
      required String firmwareId,
      required int pageOffset,
      required int perPageLimit}) async {
    return await firmwareRepository.getFirmwareDeviseList(
        context: context,
        firmwareId: firmwareId,
        pageOffset: pageOffset,
        perPageLimit: perPageLimit);
  }
}
