import 'package:quantumlink_node/app_import.dart';


class NodeGwController extends GetxController {

  final GwSettingRepository gwSettingRepository = GetIt.instance.get<GwSettingRepository>();

  Future<dynamic>   getNodeGateways({required BuildContext context,required int pageOffset, required int perPageLimit, required String statusType}) async {
    return await gwSettingRepository.getNodeGateways(context: context,pageOffset: pageOffset,perPageLimit: perPageLimit,statusType:statusType);
  }
  Future<dynamic> getNodeGwAmpsDeviceList(
      {required BuildContext context,
        required String gwEui,
        int? pageOffset,
        int? perPageLimit}) async {
    return await gwSettingRepository.getNodeGwAmpsDeviceList(
        context: context, pageOffset: pageOffset, perPageLimit: perPageLimit, gwEui: gwEui);
  }
}