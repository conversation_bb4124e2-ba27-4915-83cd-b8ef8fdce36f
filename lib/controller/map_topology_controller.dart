import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/serialized/map_topology/map_layers_response.dart';

class MapTopologyController extends GetxController {
  final MapTopologyRepository mapTopologyRepository = GetIt.instance.get<MapTopologyRepository>();

  /// Get map layers data with bounding box
  Future<dynamic> getMapLayersData({
    required BuildContext context,
    required MapLayersRequest request,
  }) async {
    return await mapTopologyRepository.getMapLayersData(
      context: context,
      request: request,
    );
  }
} 