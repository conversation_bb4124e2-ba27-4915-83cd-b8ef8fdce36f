import '../app_import.dart';

class SettingController extends GetxController {
  final AmpProviderRepository _ampProviderRepository = GetIt.instance.get<AmpProviderRepository>();

  Future<dynamic> getAmpProviderList(
      {required BuildContext context, int? pageOffset, int? perPageLimit}) async {
    return await _ampProviderRepository.getAmpProviderList(
        context: context, pageOffset: pageOffset, perPageLimit: perPageLimit);
  }

  Future<dynamic> addNewAmpProvider(
      {required AmplifierProviderItem itemData, required BuildContext context}) async {
    return await _ampProviderRepository.addNewAmpProvider(context: context, itemData: itemData);
  }

  Future<dynamic> updateAmpProvider(
      {required AmplifierProviderItem itemData, required BuildContext context,required String vendorCode}) async {
    return await _ampProviderRepository.updateAmpProvider(context: context, itemData: itemData,vendorCode: vendorCode);
  }
  Future<dynamic> deleteAmpProvider(
      {required BuildContext context,required String vendorCode}) async {
    return await _ampProviderRepository.deleteAmpProvider(context: context,vendorCode: vendorCode);
  }

  Future<dynamic> getVendorKeysList(
      {required BuildContext context, required String vendorCode}) async {
    return await _ampProviderRepository.getVendorKeysList(context: context, vendorCode: vendorCode);
  }

  Future<dynamic> addNewVendorKey({
    required BuildContext context,
    required VendorKeyItem keyItem,
  }) async {
    return await _ampProviderRepository.addNewVendorKey(
      context: context,
      keyItem: keyItem,
    );
  }

  Future<dynamic> deleteVendorKey({
    required BuildContext context,
    required String keyId,
  }) async {
    return await _ampProviderRepository.deleteVendorKey(
      context: context,
      keyId: keyId,
    );
  }
}
