import '../app_import.dart';

class SSIdReportController extends GetxController {
  AuthRepository authRepository = GetIt.instance<AuthRepository>();

  // Future<Response?> getDiscoveredNodeWifi(BuildContext context) async {
  //   Response? data = await authRepository.discoverWifi(context);
  //   return data;
  // }
  //
  // Future<Response?> connectToSSID(String ssid, String password, BuildContext context) async {
  //   Response? data = await authRepository.connectToSSID(ssid, password, context);
  //   return data;
  // }
  //
  // Future<dynamic> ssidReport(context) async {
  //   return await authRepository.checkWifi(context);
  // }
}
