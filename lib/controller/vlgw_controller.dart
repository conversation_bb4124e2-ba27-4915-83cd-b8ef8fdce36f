
import 'package:quantumlink_node/app_import.dart';


class VLGWController extends GetxController {
  final VLGWRepository vlgwRepository = GetIt.instance.get<VLGWRepository>();



  Future<dynamic> getVlGwList(BuildContext context) async {
    return await vlgwRepository.getVlGWs(context);
  }
  Future<dynamic> getVLGWInfo(BuildContext context,String eui) async {
    return await vlgwRepository.getVLGWInfo(context,eui);
  }
  Future<dynamic> updateSite(BuildContext context,VLGW vlgw) async {
    return await vlgwRepository.updateSite(context,vlgw);
  }
  Future<dynamic> getVLGWFSKStats(BuildContext context,String eui) async {
    return await vlgwRepository.getVLGWFSKStats(context,eui);
  }

  Future<dynamic> getConfig(BuildContext context,String eui) async {
    return await vlgwRepository.getConfig(context,eui);
  }
  Future<dynamic> getVersionOfConfig(BuildContext context,String eui) async {
    return await vlgwRepository.getVersionOfConfig(context,eui);
  }
  Future<dynamic> resetVLGWFSKStats(BuildContext context,String eui) async {
    return await vlgwRepository.resetVLGWFSKStats(context,eui);
  }

  Future<dynamic> getVLGWAmpsDeviceList(
      {required BuildContext context,
      required String gwEui,
      int? pageOffset,
      int? perPageLimit}) async {
    return await vlgwRepository.getVLGWAmpsDeviceList(
        context: context, pageOffset: pageOffset, perPageLimit: perPageLimit, gwEui: gwEui);
  }

  Future<dynamic> getVlgwTopology({required BuildContext context, required String gwEui}) async {
    return await vlgwRepository.getVlgwTopology(context: context, gwEui: gwEui);
  }
}