import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/main.dart';
import 'package:http/src/response.dart' as http;

class DashboardController extends GetxController {
  DashboardRepository dashboardRepository = getIt.get<DashboardRepository>();
  Future<dynamic> getDashboardDataList(BuildContext context) async {
    return await dashboardRepository.getDashboardData(context);
  }
  Future<dynamic> getNodeBatteryInfo(BuildContext context) async {
    return await dashboardRepository.getNodeBatteryInfo(context);
  }
  Future<dynamic> getNodeIdInfo(BuildContext context) async {
    return await dashboardRepository.getNodeIdInfo(context);
  }

  Future<dynamic> checkHealth(BuildContext context,{String? otherBaseUrl}) async {
    return await dashboardRepository.checkHealth(context,otherBaseUrl:otherBaseUrl);
  }
  Future<http.Response?> getKeyCountsFromJoinServer(BuildContext context) async {
    return await dashboardRepository.getKeyCountsFromJoinServer(context);
  }
}
