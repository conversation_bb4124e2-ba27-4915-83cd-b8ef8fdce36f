import '../app_import.dart';

class HomeController extends GetxController {
  final AuthRepository authRepository = GetIt.instance.get<AuthRepository>();
  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
  }
  ValueNotifier<Map<String, dynamic>> jsonDataListener = ValueNotifier<Map<String, dynamic>>({});
  void updateJsonData(Map<String, dynamic> newJsonData) {
    jsonDataListener.value = newJsonData;
  }

  // Future<dynamic> ssidReport(context) async {
  //   return await authRepository.checkWifi(context);
  // }
}
