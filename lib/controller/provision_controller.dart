import 'package:quantumlink_node/app_import.dart';

class ProvisionController extends GetxController {
  final ProvisioningRepository _provisioningRepository = GetIt.instance.get<ProvisioningRepository>();
  final AmpProviderRepository _ampProviderRepository = GetIt.instance.get<AmpProviderRepository>();


  Future<dynamic> getProvisionedDeviceList(
      {required BuildContext context,
        required ProvisionedRequestParams requestParams}) async {
    return await _provisioningRepository.getProvisionedDeviceList(
        context: context,
       requestParams: requestParams);
  }

  Future<dynamic> addProvisionDevice(
      {required ProvisioningDeviceItem itemData, required BuildContext context}) async {
    final response =
        await _provisioningRepository.addProvisionDevice(itemData: itemData, context: context);
    return response;
  }

  Future<dynamic> updateProvisionDevice(
      {required ProvisioningDeviceItem itemData, required BuildContext context,required String devEUI}) async {
    final response =
        await _provisioningRepository.updateProvisionDevice(itemData: itemData, context: context,devEUI: devEUI);
    return response;
  }

  Future<dynamic> updateProvisionAutoJoinDevice(
      {required bool isEnabled, required BuildContext context, required String devEUI}) async {
    final response = await _provisioningRepository.updateProvisionAutoJoinDevice(
        isEnabled: isEnabled, context: context, devEUI: devEUI);
    return response;
  }

  Future<dynamic> deleteProvisionedDevice(
      {required String deviceEui}) async {
   return await _provisioningRepository.deleteProvisionedDevice(deviceEui: deviceEui);
  }

  Future<dynamic> addCsvProvisionedDevices({ required BuildContext context, required PlatformFile csvFile}) async {
    return await _provisioningRepository.addCsvProvisionedDevices(context: context, csvFile:csvFile);
  }

  Future<dynamic> addCsvJoinServerDevices(
      {required BuildContext context, required PlatformFile csvFile}) async {
    return await _provisioningRepository.addCsvJoinServer(context: context, csvFile: csvFile);
  }
  Future<dynamic> getAmpProviderList(
      {required BuildContext context, int? pageOffset, int? perPageLimit}) async {
    return await _ampProviderRepository.getAmpProviderList(
        context: context, pageOffset: pageOffset, perPageLimit: perPageLimit);
  }
}
