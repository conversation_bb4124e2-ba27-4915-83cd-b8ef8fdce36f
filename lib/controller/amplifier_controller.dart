import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/main.dart';


class AmplifierController extends GetxController {
  AmplifierRepository amplifierRepository = getIt.get<AmplifierRepository>();

  Future<dynamic> getAmplifierList(
      BuildContext context, Function f, int pageOffset, int perPageLimit,
      {String? search}) async {
    return await amplifierRepository.getAmplifierList(context, f, pageOffset, perPageLimit,
        search: search);
  }

  Future<dynamic> getAmplifierListOfStatusData({required String deviceEui, required BuildContext context}) async {
    return await amplifierRepository.getAmplifierListOfStatusData(deviceEui: deviceEui, context: context);
  }

  Future<dynamic> getAmplifierIdentification({required String deviceEui, required BuildContext context}) async {
    return await amplifierRepository.getAmplifierIdentification(deviceEui: deviceEui, context: context);
  }


  Future<dynamic> ampDsConfig({required String deviceEui, required BuildContext context}) async {
    return await amplifierRepository.ampDsConfig(deviceEui: deviceEui, context: context);
  }

  Future<dynamic> ampUsConfig({required String deviceEui, required BuildContext context}) async {
    return await amplifierRepository.ampUsConfig(deviceEui: deviceEui, context: context);
  }

  updateAmpPlacements (BuildContext context, String deviceEui ,AmplifierDeviceItem2 ampIdentification) async {
    return await amplifierRepository.updateAmpPlacements(deviceEui: deviceEui, context: context, ampIdentification: ampIdentification);
  }

  updateDownStream (BuildContext context, String deviceEui ,AmpDownStreamItem ampDownStreamItem) async {
    await amplifierRepository.updateDsConfig(deviceEui: deviceEui, context: context, ampDownStreamItem: ampDownStreamItem);
  }

  updateUsConfig(BuildContext context, String deviceEui ,AmpUpStreamItem ampUpStreamItem) async {
   await amplifierRepository.updateUsConfig(deviceEui: deviceEui, context: context, ampUpStreamItem: ampUpStreamItem);
  }
  setTestPoint(BuildContext context, String deviceEui ,TestPointItem testPointItem) async {
    return  await amplifierRepository.setTestPointConfig(deviceEui: deviceEui, context: context, testPointItem: testPointItem );
  }

  setIngressSwitch(BuildContext context, String deviceEui ,IngressSwitchItem ingressSwitchItem,{required bool isRefresh, required bool isMultiIngressSwitch}) async {
   return await amplifierRepository.setIngressSwitchControl(deviceEui: deviceEui, context: context, ingressSwitchItem: ingressSwitchItem,isRefresh: isRefresh,isMultiIngressSwitch:isMultiIngressSwitch);
  }

  getTestPointConfiguration(BuildContext context, String deviceEui, bool isRefresh) async {
    return await amplifierRepository.getTestPointConfiguration(deviceEui: deviceEui, context: context, isRefresh: isRefresh);
  }
  getIngressSwitch(BuildContext context, String deviceEui ,IngressSwitchItem ingressSwitchItem, bool isMultiIngress ) async {
    return await amplifierRepository.getIngressSwitchControl(deviceEui: deviceEui, context: context, ingressSwitchItem: ingressSwitchItem ,isMultiIngress :isMultiIngress);
  }
  Future<dynamic> startDsAlignment(BuildContext context, String deviceEui) async {
    return  await amplifierRepository.startDsAlignment(deviceEui: deviceEui, context: context);
  }
  Future<dynamic> startUsAlignment(BuildContext context, String deviceEui ) async {
    return await amplifierRepository.startUsAlignment(deviceEui: deviceEui, context: context);
  }

  Future<dynamic> getSpectrum({required String deviceEui, required BuildContext context}) async {
    return await amplifierRepository.getSpectrum(deviceEui: deviceEui, context: context);
  }
  Future<dynamic> getTelemetry({required String deviceEui, required BuildContext context,required String formDate, required String toDate, required int pageOffset, required int perPageLimit}) async {
    return await amplifierRepository.getTelemetry(deviceEui: deviceEui, context: context,formDate: formDate, toDate: toDate , pageOffset: pageOffset, perPageLimit: perPageLimit);
  }

  Future<dynamic> getAlarmsHistoryData(
      {required String deviceEui,
      required BuildContext context,
      required String fromDate,
      required String toDate}) async {
    return await amplifierRepository.getAlarmsHistoryData(
        deviceEui: deviceEui,
        context: context,
        fromDate: fromDate,
        toDate: toDate);
  }

  Future<dynamic> getTelemetryThresholds({required String deviceType, required BuildContext context}) async {
    return await amplifierRepository.getTelemetryThresholds(deviceType: deviceType, context: context);
  }

  Future<dynamic> addTelemetryThresholds(BuildContext context, String deviceEui ,TelemetryThreshold telemetryThreshold) async {
    return await amplifierRepository.addTelemetryThresholds(context: context,deviceType: deviceEui, itemData: telemetryThreshold);
  }

  Future<dynamic> exportTelemetryCsvFile(
      {required String deviceEui,
      required String formDate,
      required String toDate,
      required BuildContext context}) async {
    return await amplifierRepository.exportTelemetryCsvFile(
        deviceEui: deviceEui, formDate: formDate, toDate: toDate, context: context);
  }
  Future<Map<String, dynamic>> dsAutoAlignment({required String deviceEui, required BuildContext context, required bool isStatusCheck}) async {
    return await amplifierRepository.dsAutoAlignment(deviceEui: deviceEui, context: context,isStatusCheck: isStatusCheck);
  }
  Future<Map<String, dynamic>> usAutoAlignment({required String deviceEui, required BuildContext context, required bool isStatusCheck}) async {
    return await amplifierRepository.usAutoAlignment(deviceEui: deviceEui, context: context,isStatusCheck:isStatusCheck);
  }
  Future<Map<String, dynamic>> dsManualAlignment({required String deviceEui, required BuildContext context}) async {
    return await amplifierRepository.dsManualAlignment(deviceEui: deviceEui, context: context);
  }
  Future<Map<String, dynamic>> setDsManualAlignment({required DsManualAlignmentItem dsManualAlignmentItem,required String deviceEui, required BuildContext context}) async {
    return await amplifierRepository.setDsManualAlignment(dsManualAlignmentItem: dsManualAlignmentItem,deviceEui: deviceEui, context: context);
  }
  Future<Map<String, dynamic>> saveRevertDsManualAlignment({required DsManualAlignmentItem dsManualAlignmentItem,required String deviceEui, required BuildContext context}) async {
    return await amplifierRepository.saveRevertDsManualAlignment(dsManualAlignmentItem: dsManualAlignmentItem,deviceEui: deviceEui, context: context);
  }
  Future<Map<String, dynamic>> usManualAlignment({required String deviceEui, required BuildContext context}) async {
    return await amplifierRepository.usManualAlignment(deviceEui: deviceEui, context: context);
  }
  Future<Map<String, dynamic>> setUsManualAlignment({required DsManualAlignmentItem dsManualAlignmentItem,required String deviceEui, required BuildContext context}) async {
    return await amplifierRepository.setUsManualAlignment(dsManualAlignmentItem: dsManualAlignmentItem,deviceEui: deviceEui, context: context);
  }
  Future<Map<String, dynamic>> saveRevertUsManualAlignment({required DsManualAlignmentItem dsManualAlignmentItem,required String deviceEui, required BuildContext context}) async {
    return await amplifierRepository.saveRevertUsManualAlignment(dsManualAlignmentItem: dsManualAlignmentItem,deviceEui: deviceEui, context: context);
  }
  Future<Map<String, dynamic>> getDeviceSummary({required String deviceEui, required BuildContext context,bool isRefresh = false, required int bitMask}) async {
    return await amplifierRepository.getDeviceSummary(deviceEui: deviceEui, context: context,isRefresh: isRefresh ,bitMask: bitMask);
  }
  // Future<dynamic> getAlarms({required String deviceEui, required BuildContext context}) async {
  //   return await amplifierRepository.getAlarms(deviceEui: deviceEui, context: context);
  // }
  Future<Map<String, dynamic>> getSpectrumData({required String deviceEui,required SpectrumRequestItem sendSpectrumItem, required BuildContext context,required bool isRefresh}) async {
    return await amplifierRepository.getSpectrumData(deviceEui: deviceEui,spectrumDataItem: sendSpectrumItem, context: context,isRefresh: isRefresh);
  }
  Future<DownstreamAmplifiers> getDownstreamAmplifiers({required String deviceEui, required BuildContext context}) async {
    return await amplifierRepository.getDownstreamAmplifiers(deviceEui: deviceEui, context: context);
  }
  Future<Map<String, dynamic>> getDeviceTransponderInfo({required String deviceEui, required BuildContext context,bool isRefresh = false}) async {
    return await amplifierRepository.getDeviceTransponderInfo(deviceEui: deviceEui, context: context,isRefresh: isRefresh);
  }
  Future<Map<String, dynamic>> dsAutoAlignmentSpectrumData({required String deviceEui, required BuildContext context,bool isRefresh = false}) async {
    return await amplifierRepository.dsAutoAlignmentSpectrumData(deviceEui: deviceEui, context: context,isRefresh: isRefresh);
  }
  Future<Map<String, dynamic>> saveRevertDsAutoAlignment({required String deviceEui, required BuildContext context,required bool isSave}) async {
    return await amplifierRepository.saveRevertDsAutoAlignment(deviceEui: deviceEui, context: context, isSave: isSave);
  }
  Future<Map<String, dynamic>> saveRevertUsAutoAlignment({required String deviceEui, required BuildContext context,required bool isSave}) async {
    return await amplifierRepository.saveRevertUsAutoAlignment(deviceEui: deviceEui, context: context, isSave: isSave);
  }

  Future<Map<String, dynamic>> getAMPFirmwareImageInfo(
      {required String deviceEui, required BuildContext context, required bool isRefresh}) {
    return amplifierRepository.getAMPFirmwareImageInfo(
      context: context,
      deviceEui: deviceEui,
      isRefresh: isRefresh,
    );
  }

  Future<Map<String, dynamic>> setAMPSwitchBankAndReboot(
      {required String deviceEui, required BuildContext context,required int bankIndex, required bool isRefresh,}) {
    return amplifierRepository.setAMPSwitchBankAndReboot(
      context: context,
      bankIndex: bankIndex,
      deviceEui: deviceEui,
      isRefresh: isRefresh,
    );
  }

  Future<Map<String, dynamic>> getTransponderFWImageInfo(
      {required String deviceEui, required BuildContext context, required bool isRefresh}) {
    return amplifierRepository.getTransponderFWImageInfo(
      context: context,
      deviceEui: deviceEui,
      isRefresh: isRefresh,
    );
  }

  Future<Map<String, dynamic>> setTransponderSwitchBankAndReboot(
      {required String deviceEui,
      required BuildContext context,
      required int bankIndex,
      required bool isRefresh}) {
    return amplifierRepository.setTransponderSwitchBankAndReboot(
      bankIndex: bankIndex,
      context: context,
      deviceEui: deviceEui,
      isRefresh: isRefresh,
    );
  }

  Future<dynamic> getAuditLogHistory({required BuildContext context, required int pageOffset, required int perPageLimit, required String deviceEui}) async {
    return await amplifierRepository.getAuditLogHistory(context,pageOffset ,perPageLimit,deviceEui);
  }

  Future<dynamic> getAlignmentConfigFileInfo(
      {required String deviceEui, required BuildContext context, required bool isRefresh}) async {
    return await amplifierRepository.getAlignmentConfigFileInfo(deviceEui: deviceEui,context: context,isRefresh:isRefresh);
  }

  Future<dynamic> setAlignmentConfigInfo(
      {required String deviceEui, required BuildContext context,required AlignmentConfig alignmentConfig}) async {
    return await amplifierRepository.setAlignmentConfigInfo(deviceEui: deviceEui,context: context,alignmentConfig:alignmentConfig );
  }
}
