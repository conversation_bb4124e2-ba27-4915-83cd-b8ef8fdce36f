import 'package:quantumlink_node/app_import.dart';

import '../main.dart';
import '../serialized/auditlogs/auditlog.dart';

class AuditLogsController extends GetxController {
  AuditLogsRepository auditLogsRepository = getIt.get<AuditLogsRepository>();


  Future<dynamic> getAuditLogDevicesList({required BuildContext context, required int pageOffset, required int perPageLimit}) async {
    return await auditLogsRepository.getAuditLogDevicesList(context,pageOffset ,perPageLimit);
  }

  Future<dynamic> getAuditLogUserList({required BuildContext context, required int pageOffset, required int perPageLimit}) async {
    return await auditLogsRepository.getAuditLogUserList(context,pageOffset ,perPageLimit);
  }


}
