import 'package:http/http.dart';
import 'package:quantumlink_node/app_import.dart';
import 'package:rxdart/rxdart.dart';
import 'package:universal_html/html.dart';

// ignore: constant_identifier_names
enum Flavor { ql_central, ql_node }

class AppConfig {
  String appName = "";
  String gw = "", ip = "";
  String baseUrl = ""; // ql_api_gw
  String nodeMgrUrl = "";
  String foutaServerUrl = "";
  String gwSettingUrl = "";
  String auditLogUrl = "";
  String wsUrl = "";
  String tenant = "";
  String clientId = "";
  String provider = "";
  bool aadAuth = true;
  bool grantAccess = true;
  bool isGoogleSignIn = false;
  bool isOpenFromBLE=false;
  bool isLicensingService = false; // if true then enable firebase else from app config
  String showDeploymentsSinceDays = "";
  List<String> allowedEmailDomains = <String>[];
  // bool isCheckedWifi = false;
  Flavor flavor = Flavor.ql_central;
  bool isQLCentral = true;
  //String domain = "";
  static AppConfig shared = AppConfig.create();

  factory AppConfig.create({
    String appName = "",
    Flavor flavor = Flavor.ql_central,
  }) {
    return shared = AppConfig(appName, flavor);
  }

  AppConfig(this.appName, this.flavor){
    if(flavor == Flavor.ql_central){
      isQLCentral = true;
    }else{
      isQLCentral = false;
    }
    loadJsonAsset();
  }

  // Common ------------ GWDiagnostics : For Api Status And Software Download
  String joinServerUrl = "";
  String softwareDownloadUrl = "";
  // For Api Status -------

  // For Node
  String bleSvcUrl = "";

  loadJsonAsset() async {

    final configUrl = "${window.location.origin}/assets/"+ AppAssetsConstants.configPath + '?t=${DateTime.now().millisecondsSinceEpoch}';
    debugLogs("configUrl -> ");
    debugLogs(configUrl);
    final response = await get(Uri.parse(configUrl));
    String configDataString = "";
    if (response.statusCode == 200) {
      configDataString = response.body;
      debugLogs("configDataString -> from api");
    } else {
      debugLogs("configDataString -> from local");
      configDataString = await rootBundle.loadString(AppAssetsConstants.configPath);
    }

    //String configDataString = await rootBundle.loadString(AppAssetsConstants.configPath);
    Map mapData = jsonDecode(configDataString);


    //domain = mapData["domain"] ?? "";

    String? ql_api_gw_host = mapData["ql_api_gw_host"];
    if(ql_api_gw_host!.isNotEmpty){ // if empty then from window
      host = ql_api_gw_host;
    }
    // ql_api_gw
    baseUrl = protocol + "//" + host!; // foutaServerUrl
    String ql_api_gw_port = mapData["ql_api_gw_port"] ?? "";
    if (ql_api_gw_port.isNotEmpty) {
      baseUrl = baseUrl +":" + ql_api_gw_port;
      if(protocol.contains("https")){
        wsUrl = "wss://" + host! +":"+  ql_api_gw_port;
      }
      else{
        wsUrl = "ws://" + host! +":"+  ql_api_gw_port;
      }
    }
    String ql_api_gw_prefix = mapData["ql_api_gw_prefix"] ?? "";
    if (ql_api_gw_prefix.isNotEmpty) {
      baseUrl = baseUrl +"/" + ql_api_gw_prefix;
      if(protocol.contains("https")){
        wsUrl = "wss://" + host! +"/"+  ql_api_gw_prefix;
      }
      else{
        wsUrl = "ws://" + host! +"/"+  ql_api_gw_prefix;
      }
    }
    //
    String ql_node_mgr_host = mapData["ql_node_mgr_host"] ?? "";
    if (ql_node_mgr_host.isEmpty) {
      ql_node_mgr_host = host!;
    }
    nodeMgrUrl = protocol + "//" + ql_node_mgr_host +":"+  mapData["ql_node_mgr_port"];
    /*String configJoinServerBaseUrl= mapData["ql_join_server_base_url"] ?? "";
    if (configJoinServerBaseUrl.isNotEmpty) {
      if (!configJoinServerBaseUrl.contains("http")) {
        joinServerUrl = protocol + "//" + configJoinServerBaseUrl;
      }
      else{
        joinServerUrl = configJoinServerBaseUrl;
      }
    }
    else{
      String ql_join_server_host = mapData["ql_join_server_host"] ?? "";
      if (ql_join_server_host.isEmpty) {
        ql_join_server_host = host!;
      }
      joinServerUrl = protocol + "//" + ql_join_server_host +":"+  mapData["ql_join_server_port"];
    }*/

    //gwSettingUrl = mapData.containsKey("ql_gw_setting_port") ?  protocol + "//" + host! +":" + mapData["ql_gw_setting_port"]  : "";
    gwSettingUrl = baseUrl;
    softwareDownloadUrl = mapData.containsKey("ql_updater") ? (protocol + "//" + host! +":"+  mapData["ql_updater"]) : "";
    bleSvcUrl = mapData.containsKey("ql_ble_svc") ? (protocol + "//" + host! +":"+  mapData["ql_ble_svc"]) : "";

    // Fuota
    String ql_fuota_server_host = mapData["ql_fuota_server_host"] ?? "";
    if (ql_fuota_server_host.isEmpty) {
      ql_fuota_server_host = host!;
    }
    foutaServerUrl = protocol + "//" + ql_fuota_server_host; // foutaServerUrl
    String ql_fuota_server_port = mapData["ql_fuota_server_port"] ?? "";
    if (ql_fuota_server_port.isNotEmpty) {
      foutaServerUrl = foutaServerUrl +":" + ql_fuota_server_port;
    }
    String ql_fuota_server_prefix = mapData["ql_fuota_server_prefix"] ?? "";
    if (ql_fuota_server_prefix.isNotEmpty) {
      foutaServerUrl = foutaServerUrl +"/" + ql_fuota_server_prefix;
    }
    // Fuota

    // Join Server

    String ql_join_server_host = mapData["ql_join_server_host"] ?? "";
    if (ql_join_server_host.isEmpty) {
      ql_join_server_host = host!;
    }
    joinServerUrl = protocol + "//" + ql_join_server_host;
    String ql_join_server_port = mapData["ql_join_server_port"] ?? "";
    if (ql_join_server_port.isNotEmpty) {
      joinServerUrl = joinServerUrl +":" + ql_join_server_port;
    }
    String ql_join_server_prefix = mapData["ql_join_server_prefix"] ?? "";
    if (ql_join_server_prefix.isNotEmpty) {
      joinServerUrl = joinServerUrl +"/" + ql_join_server_prefix;
    }

    // Audit Log
    String ql_audit_log_host = mapData["ql_audit_log_host"] ?? "";
    if (ql_audit_log_host.isEmpty) {
      ql_audit_log_host = host!;
    }
    auditLogUrl = protocol + "//" + ql_audit_log_host;
    String ql_audit_log_prefix = mapData["ql_audit_log_prefix"] ?? "";
    if (ql_audit_log_prefix.isNotEmpty) {
      auditLogUrl = auditLogUrl +"/" + ql_audit_log_prefix;
    }

    // Deployment
    if(mapData.containsKey("show_deployments_since_days")){
      showDeploymentsSinceDays = mapData["show_deployments_since_days"] ?? "";
    }

    if(mapData.containsKey("licensing_service")){
      isLicensingService = mapData["licensing_service"]["enable"] ?? false;
      if(!isLicensingService){
        debugLogs("config from  -> local");

        provider = mapData["licensing_service"]["provider"] ?? "";
        tenant = mapData["licensing_service"]["tenant"] ?? "";
        clientId = mapData["licensing_service"]["clientId"] ?? "";
        allowedEmailDomains = List<String>.from(mapData["licensing_service"]["domain"] ?? [""]);

        if(provider.toLowerCase()==AppStringConstants.google){
          AppConfig.shared.isGoogleSignIn = true;
        }else{
          AppConfig.shared.isGoogleSignIn = false;
        }

        debugLogs("licensing_service ->");
        debugLogs(mapData["licensing_service"]);
      }
    }


    debugLogs("loadJsonAsset -> baseUrl : $baseUrl wsUrl : $wsUrl nodeMgrUrl : $nodeMgrUrl joinServerUrl : $joinServerUrl softwareDownloadUrl : $softwareDownloadUrl foutaServerUrl : $foutaServerUrl  gwSettingUrl : $gwSettingUrl  auditLogUrl : $auditLogUrl  "
        "showDeploymentsSinceDays :$showDeploymentsSinceDays "
        "isLicensingService :$isLicensingService "
        "provider :$provider "
        "tenant :$tenant "
        "clientId :$clientId "
        "allowedEmailDomains :$allowedEmailDomains "

    );
  }

  // Api Health Check Stream
  final BehaviorSubject<bool> apiHealthCheckStream = BehaviorSubject<bool>.seeded(true);
  Stream<bool> get apiHealthCheckStreamView => apiHealthCheckStream.stream;


  fetchConfigFromFirebase(domain) async {
    if(domain==null){ // Guest
      AuthController authController = Get.put(AuthController());
      await authController.getAT();
      if(authController.accessToken!=null && authController.accessToken=="Guest"){
        AppConfig.shared.aadAuth = false;
      }
      if (authController.accessToken != null && authController.accessToken != "Guest") {
        await authController.getUserInfoFromToken();
      }
    }
    else if(isLicensingService){
      debugLogs("config from  -> firebase");
      final CollectionReference coll = FirebaseFirestore.instance.collection(FirebaseKeyConstants.registrations);
      QuerySnapshot snapshot = await coll.where(FirebaseKeyConstants.domain, isEqualTo: domain.toString().toLowerCase()).get();
      if (snapshot != null && snapshot.docs != null && snapshot.docs.length > 0) {
        QueryDocumentSnapshot doc = snapshot.docs[0];
        final data = doc.data() as Map<String, dynamic>;
        AppConfig.shared.tenant = doc.get("tenantAad");
        AppConfig.shared.clientId = doc.get("clientAad");
        AppConfig.shared.aadAuth = doc.get("aadAuth");
        AppConfig.shared.grantAccess = data.containsKey("grantAccess") ? data["grantAccess"] : false;
        AppConfig.shared.provider = doc.get("provider") ?? "";
        if(provider.toLowerCase()==AppStringConstants.google){
          AppConfig.shared.isGoogleSignIn = true;
        }else{
          AppConfig.shared.isGoogleSignIn = false;
        }
      }
    }

  }
}