import 'package:quantumlink_node/app_import.dart';


Future fetchAzureUserDetails(accessToken, context) async {
  RestHelper restServices = RestHelper.instance;
  Map<String, String> headers = {"Authorization": "Bearer $accessToken"};
  final response = await restServices.getRestCall(endpoint: "https://graph.microsoft.com/v1.0/me", customHeader: headers ,context: context);

  return showDialog(
    context: context,
    builder: (context) {
      return GetBuilder<ProvisionController>(
        init: ProvisionController(),
        builder: (ProvisionController controller) {
          return AlertDialog(
            surfaceTintColor: AppColorConstants.colorWhite,
            backgroundColor: AppColorConstants.colorWhite,
            insetPadding: EdgeInsets.zero,
            contentPadding: EdgeInsets.zero,
            titlePadding: EdgeInsets.zero,
            actionsPadding: EdgeInsets.zero,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),

            content: StatefulBuilder(builder: (context, snapshot) {
              return Container(
                padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
                width: 500,
                //padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    getAppDivider(),
                    const SizedBox(height: 24),
                    AppText(response!, style: const TextStyle(fontFamily: AppAssetsConstants.openSans),),
                    const SizedBox(height: 24),
                    getAppDivider(),
                  ],
                ),
              );
            }),

          );
        },
      );
    },
  );

}