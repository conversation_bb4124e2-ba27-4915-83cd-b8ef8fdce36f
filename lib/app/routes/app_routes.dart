import 'package:quantumlink_node/app/cache/app_shared_preference.dart';
import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/controller/ss_id_report_controller.dart';
import 'package:quantumlink_node/pages/software_update/software_update_page.dart';
import 'package:quantumlink_node/pages/ss_id_report/ss_id_report_page.dart';

class GoRouterObserver extends NavigatorObserver {
  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    debugLogs('MyTest didPush: $route');
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    debugLogs('MyTest didPop: $route');
  }

  @override
  void didRemove(Route<dynamic> route, Route<dynamic>? previousRoute) {
    debugLogs('MyTest didRemove: $route');
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    debugLogs('MyTest didReplace: $newRoute');
  }
}

final rootNavigatorKey = GlobalKey<NavigatorState>();
bool isStart = true;
AuthRepository authRepository = GetIt.instance<AuthRepository>();

Future<String?> _redirect(BuildContext context, GoRouterState state) async {
  /* if login then return loginRoute here */
  dynamic accessToken;
  if (await checkPrefKey(AppSharedPreference.accessToken)) {
    accessToken = await getPrefStringValue(AppSharedPreference.accessToken);
  }
  final String currentPath = state.uri.path;
  // bool isNotInitialOrSignIn = !AppConfig.shared.isQLCentral &&
  //     currentPath != RouteHelper.routeInitial &&
  //     currentPath != RouteHelper.routeSignInPage &&
  //     currentPath != RouteHelper.routeSSIdReportPage;

  /*if (!AppConfig.shared.isCheckedWifi && isNotInitialOrSignIn) {
    try {
      Map<String, dynamic> isWifiStatus = await authRepository.checkWifi(context);
      AppConfig.shared.isCheckedWifi = isWifiStatus['internet'] ?? false;
    } catch (e) {
      debugLogs("_redirect checkWifi -->${e.toString()}");
    }
  }*/

  // if (AppConfig.shared.isCheckedWifi == false && isNotInitialOrSignIn) {
  //   if (state.uri.path == RouteHelper.routeDashboard) {
  //     router.go(RouteHelper.routeSSIdReportPage, extra: UniqueKey());
  //   }
  //   return RouteHelper.routeSSIdReportPage;
  // } else if (AppConfig.shared.isCheckedWifi == true && isStart && state.uri.path == RouteHelper.routeSSIdReportPage && accessToken == null) {
  if (!isStart && accessToken == null) {
    return RouteHelper.routeSignInPage;
  } else if (!isStart && state.uri.path == RouteHelper.routeInitial) {
    return RouteHelper.routeDashboard;
  } else if (!isStart && state.uri.path == RouteHelper.routeSignInPage && accessToken != null) {
    return RouteHelper.routeDashboard;
  }
  isStart = false;

  debugLogs("_redirect -> ${state.uri.path}");
  return state.uri.path; // initially it was '/' then path
}

final _shellNavigatorKey = GlobalKey<NavigatorState>();
final router = GoRouter(
  initialLocation: RouteHelper.routeInitial,
  navigatorKey: rootNavigatorKey,
  redirect: _redirect,
  observers: [
    GoRouterObserver(),
  ],
  routes: [
    ShellRoute(
      navigatorKey: _shellNavigatorKey,
      pageBuilder: (context, GoRouterState state, child) {
        debugLogs("state.name -> ${state.uri}");
        return NoTransitionPage(
            child: HomePage(uri: state.uri,child: child,));
      },
      routes: [
        GoRoute(
          path: RouteHelper.routeProvisionPage,
          parentNavigatorKey: _shellNavigatorKey,
          pageBuilder: (context, state) {
            return const NoTransitionPage(
              child: ProvisioningPage(),
            );
          },
        ),

        GoRoute(
          path: RouteHelper.routeAmplifierPage,
          parentNavigatorKey: _shellNavigatorKey,
          pageBuilder: (context, state) {
            debugLogs(state.extra);
            return NoTransitionPage(
              child: AmplifierPage(mapWithTabIndex: state.extra as Map<String,dynamic>?),
            );
          },
        ),

        GoRoute(
          path: RouteHelper.routeSettingsPage,
          parentNavigatorKey: _shellNavigatorKey,
          pageBuilder: (context, state) {
            return const NoTransitionPage(
              child: SettingsPage(),
            );
          },
        ),
        GoRoute(
          path: RouteHelper.routeSiteRegion,
          parentNavigatorKey: _shellNavigatorKey,
          pageBuilder: (context, state) {
            return const NoTransitionPage(
              child: SiteRegionsPage(),
            );
          },
        ),
        GoRoute(
          path: RouteHelper.routeDashboard,
          parentNavigatorKey: _shellNavigatorKey,
          pageBuilder: (context, state) {
            return const NoTransitionPage(
              child: DashboardPage(),
            );
          },
        ),
        GoRoute(
          path: RouteHelper.routeAuditLogs,
          parentNavigatorKey: _shellNavigatorKey,
          pageBuilder: (context, state) {
            return const NoTransitionPage(
              child: AuditLogsPage(),
            );
          },
        ),
        GoRoute(
          path: RouteHelper.userInfoPage,
          parentNavigatorKey: _shellNavigatorKey,
          pageBuilder: (context, state) {
            return const NoTransitionPage(
              child: UserInformation(),
            );
          },
        ),
        GoRoute(
          path: RouteHelper.routeVLGWPage,
          parentNavigatorKey: _shellNavigatorKey,
          pageBuilder: (context, state) {
            return const NoTransitionPage(
              child: VLGWPage(),
            );
          },
        ),
        GoRoute(
          path: AppConfig.shared.isQLCentral ? RouteHelper.routeDiagnostics : RouteHelper.routeAPIs,
          parentNavigatorKey: _shellNavigatorKey,
          pageBuilder: (context, state) {
            return const NoTransitionPage(
              child: DiagnosticsPage(),
            );
          },
        ),
        GoRoute(
          path: RouteHelper.routeSoftwareUpdate,
          parentNavigatorKey: _shellNavigatorKey,
          pageBuilder: (context, state) {
            return const NoTransitionPage(
              child: SoftwareUpdate(),
            );
          },
        ),
        GoRoute(
          path: RouteHelper.routeFirmwarePage,
          parentNavigatorKey: _shellNavigatorKey,
          pageBuilder: (context, state) {
            return const NoTransitionPage(
              child: FirmwarePage(),
            );
          },
        ),

        GoRoute(
          path: RouteHelper.routeGWSettingPage,
          parentNavigatorKey: _shellNavigatorKey,
          pageBuilder: (context, state) {
            return const NoTransitionPage(
              child: GateWayPage(),
            );
          },
        ),

        GoRoute(
          path: RouteHelper.routeNodeGWPage,
          parentNavigatorKey: _shellNavigatorKey,
          pageBuilder: (context, state) {
            return const NoTransitionPage(
              child: NodeGWPage(),
            );
          },
        ),
        GoRoute(
          path: RouteHelper.routeMobileAMPDetail,
          parentNavigatorKey: _shellNavigatorKey,
          pageBuilder: (context, state) {
            final extra = state.extra;
            if (extra is! AmpDetailPageParams) {
              return  NoTransitionPage(
                child: AmplifierPage(mapWithTabIndex: state.extra as Map<String,dynamic>?),
              );
            }
            AmpDetailPageParams amp  = AmpDetailPageParams(ampItem: extra.ampItem, ampPageHelper: extra.ampPageHelper);
            return NoTransitionPage(
              child: MobileAmplifierDetailGrid(
                amplifierItem: amp.ampItem,
                ampPageHelper: amp.ampPageHelper,
              ),
            );
          },
        ),
        GoRoute(
          path: '${RouteHelper.routeMobileAMPDetailView}/:initDetail',
          parentNavigatorKey: _shellNavigatorKey,
          pageBuilder: (context, state) {
            final extra = state.extra;
            String initDetail = state.pathParameters['initDetail'] ?? RouteHelper.deviceInfo;
            if (extra is! AmpDetailPageParams) {
              return NoTransitionPage(
                child: AmplifierPage(mapWithTabIndex: state.extra as Map<String, dynamic>?),
              );
            }
            AmpDetailPageParams amp =
                AmpDetailPageParams(ampItem: extra.ampItem, ampPageHelper: extra.ampPageHelper);
            return NoTransitionPage(
              child: MobileAmplifierDetailsView(
                amplifierItem: amp.ampItem,
                ampPageHelper: amp.ampPageHelper,
                initialDetail: initDetail,
              ),
            );
          },
        ),
      ],
    ),

    GoRoute(
      parentNavigatorKey: rootNavigatorKey,
      path: RouteHelper.routeInitial,
      pageBuilder: (context, state) {
        return NoTransitionPage(
          key: UniqueKey(),
          child: const SplashPage(),
        );
      },
    ),
    // GoRoute(
    //   parentNavigatorKey: rootNavigatorKey,
    //   path: RouteHelper.routeSSIdReportPage,
    //   pageBuilder: (context, state) {
    //     return NoTransitionPage(
    //       child: SsIdReportPage(key: UniqueKey()),
    //     );
    //   },
    // ),

    GoRoute(
      path: RouteHelper.routeSignInPage,
      parentNavigatorKey: rootNavigatorKey,
      pageBuilder: (context, state) {
        return const NoTransitionPage(
          child: SignInPage(),
        );
      },
    ),
  ],
);

// gotoSSIdReportPage(){
//   router.pushReplacement(RouteHelper.routeSSIdReportPage);
// }
gotoDashboardPage(){
  router.pushReplacement(RouteHelper.routeDashboard);
}

gotoAmplifierPage(){
  router.pushReplacement(RouteHelper.routeAmplifierPage);
}

goToSignInPage(){
  router.pushReplacement(RouteHelper.routeSignInPage);
}

addNewTabOnAmpPg(deviceEui){
  router.go(RouteHelper.routeAmplifierPage,
      extra: {'deviceEui': deviceEui});
}


goBack(){
  router.pop();
}
goBackWithResult(String r){
  router.pop(r);
}