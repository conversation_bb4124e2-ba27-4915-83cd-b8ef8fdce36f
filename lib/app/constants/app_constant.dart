import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';
import 'package:quantumlink_node/app_import.dart';

class RouteHelper{

  static const String routeInitial = '/';
  static const String routeAmplifierPage = '/ampl';
  static const String routeHomePage = '/Home';
  static const String routeProvisionPage = '/provision';
  static const String routeSettingsPage = '/settings';
  static const String routeSiteRegion = '/siteRegion';
  static const String userInfoPage = '/userInfo';
  static const String routeSignInPage = '/signIn';
  static const String routeAccessDenied = '/accessDenied';
  static const String routeDashboard = '/dashboard';
  static const String routeInventoryDashboard = '/inventory_dashboard';
  static const String routeDiagnostics = '/diagnostics';
  static const String routeAPIs = '/apis';
  static const String routeAuditLogs = '/auditLogs';
  static const String routeVLGWPage = '/vlgws';
  static const String routeTopology = '/inventory_topology';
  static const String routeFirmwarePage = '/firmware';
  static const String routeSoftwareUpdate = '/softwareUpdate';
  // static const String routeSSIdReportPage = '/ssIdReport';
  static const String routeGWSettingPage = '/gwSetting';
  static const String routeNodeGWPage = '/nodeGW';
  static const String routeMobileAMPDetail = '/ampDetailGrid';
  static const String routeMobileAMPDetailView = '/ampDetailView';
  static const String deviceInfo = 'deviceInfo';
  static const String telemetry = 'telemetry';
  static const String alarms = 'alarms';
  static const String spectrum = 'spectrum';
  static const String diagnostics = 'diagnostics';
  static const String configuration = 'configuration';
  static const String auditHistory = 'auditHistory';

}



class AppStringConstants {
  //     ======================= Strings =======================     //
  static const String appName = 'QuantumLink';
  static const String appNameNode = 'QuantumLink';
  static const String appNameInventory = 'Asset Management';
  static const String selectTypeLE = 'Line Extender';
  static const String selectTypeSE = 'System Amplifier';
  static const String lidOpen = 'Lid open';
  static const String dongleConnected = 'Dongle connected';
  static const String absent = 'Absent';
  static const String present = 'Present';
  static const String thermal = 'Thermal';
  static const String manual = 'Manual';
  static const String on = 'On';
  static const String pending = 'Pending';
  static const String inProgress = 'In Progress';
  static const String complete = 'Complete';
  static const String completeLowerCase = 'complete';
  static const String fail = 'Fail';
  static const String failLowerCase = 'fail';
  static const String auto = 'Auto';
  static const String waitingForDownload = 'Waiting for Download';
  static const String manualPending = 'Manual Pending';
  static const String multicastSetup = 'Multicast setup';
  static const String fragmentationSession = 'Fragmentation session';
  static const String missingFragments = 'missing fragments';
  static const String nextImageVersionIs = 'next image version is';
  static const String groupDelete = 'Group delete';
  static const String devUpgradeImage = 'Dev upgrade image';
  static const String successLowerCase = 'success';
  static const String successfulLowerCase = 'successful';
  static const String status = 'status';
  static const String message = 'message';
  static const String failed = 'failed';
  static const String error = 'error';
  static const String waiting = 'waiting';
  static const String pendingLowerCase = 'pending';
  static const String progress = 'progress';
  static const String ready = 'ready';
  static const String upgrading = 'upgrading';
  static const String notStarted = 'not started';
  static const String completed = 'completed';
  static const String manualLowerCase = 'manual';
  static const String downloadError = 'Download Error';
  static const String upgradeFail = 'Upgrade Fail';
  static const String success = 'Success';
  static const String deviceInfo = 'Device Info';
  static const String telemetry = 'Telemetry';
  static const String alarms = 'Alarms';
  static const String spectrum = 'Spectrum';
  static const String diagnostics = 'Diagnostics';
  static const String configuration = 'Configuration';
  static const String failUpperCase = 'Fail';
  static const String readyForUpgrade = 'Ready for Upgrade';
  static const String partialSuccess = 'Partial Success';
  static const String loginSuccess = 'user logged in successfully';
  static const String userLogin = 'user_login';
  static const String logoutSuccess = 'user logged out successfully';
  static const String userLogout = 'user_logout';
  static const String tokenRefreshSuccess = 'token refreshed successfully';
  static const String refreshToken = 'refresh_token';
  static const String tokenRefreshFailed = 'token refresh failed due to inactivity';
  static const String sessionExpired = 'session expired due to inactivity';
  static const String qlNode = 'ql-node';
  static const String qlCentral = 'ql-central';
  static const String google = 'google';
  static const String microsoft = 'microsoft';
  static const String auditHistory = 'Audit History';
  static const String celsius = 'Celsius';
  static const String fahrenheit = 'Fahrenheit';
  static const String healthVoltagesTemps = "Health - Voltages and Temperatures";
  static const String alscPilots = "ALSC Pilots";
  static const String attnEqSettings = "Attn / Eq Settings";
  static const String amplifierProvider = "Amplifier Provider";
  static const String quantumLinkApp = "QuantumLinkApp";
  static const String aoi = "AOI";
  static const String enable = "Enable";
  static const String disable = "Disable";
  static const String online = "Online";
  static const String offline = "Offline";
  static const String missingKey = "Missing Key";
  static const String missingVendor = "Missing Vendor";
  static const String all = "All";
  static const String devices = "devices";
  static const String gateways = "gateways";
  static const String deviceLinks = "device_links";
  static const String deviceClusters = "device_clusters";


  static const String selectTypeUnknown = 'Unknown';
  static const String selectTypeAerial = 'Aerial';
  static const String selectTypeUnderground = 'Underground';
  static const String selectTypeIndoorCommercial = 'Indoor Commercial';
  static const String selectTypeResidential = 'Residential';
  static const String selectTypeOther = 'Other';
  static const String betterFWVersion = '2.9.0';
  static const String betterAlignmentConfigVersion = '2.10.0';
  //static const String ampConfigurationVersion = '2.3.5';
  //static const String ampIngressSwitchVersion = '2.7.0';
  //static const String ampSingleSpectrumCaptureVersion = '2.8.0';
  static const String myLocation = 'Device Location';
  static const String field = 'field';
  static const String value = 'value';
  static const String matchType = 'match_type';
  static const String joinServer = 'Join Server';
  static const int refreshTimeIntervalSeconds = 60;
  static const int refreshTimeIntervalSeconds2 = 5;
  static const int refreshTimeIntervalSeconds30 = 30;
  static int ampPrePageLimit = 10;
  static int provisionPrePageLimit = 10;
  static int ampFWPrePageLimit = 25;
  static int telemetryPrePageLimit = 10;
  static int auditLogsDevicePerPageLimit = 10;
  static int auditLogsUserPerPageLimit = 10;
  static int vlgwPerPageLimit = 10;
  static int firmwarePerPageLimit = 10;
  static int firmwarePerPageLimit100 = 100;
  static int deploymentPerPageLimit = 100;
  static int nodeGWPerPageLimit = 10;
  static int deploymentDevicesPerPageLimit = 10;
  static int spectrumStartFrequency = 261;
  static int spectrumEndFrequency1719 = 1791;
  static int spectrumEndFrequency1215 = 1215;
  static int spectrumStepSize6 = 6;
  static int spectrumStepSize24 = 24;

  static const String FORMATDATETIME1 = "MM-dd-yyyy";
  static const String FORMATDATETIME2 = "hh:mm a";
  static const String FORMATDATETIME3 = "MM-dd-yyyy hh:mm a";
}

class AppColorConstants {

  //static Color colorPrimary = const Color(0xFFFE9532);
  static Color colorPrimary = const Color(0xFF5E4884);
  static Color colorAppbar = const Color(0xFF836ab7);
  static Color colorLime = const Color(0xffd3f1c3);
  static Color colorBackground =  Colors.white;
  static Color colorBackgroundDark = const Color(0xFFf5f5f5);
  static Color colorWhiteShade = const Color(0xFFFAFAFB);
  static Color colorTableHeader = const Color(0xFF474342);
  static Color colorAppBackground = const Color(0xFFfdf7ff);

  static Color colorH1Grey = Colors.grey;
  static Color colorH1 = const Color(0xFF828282);
  static Color colorChart = const Color(0xFFE0E0E0);
  static Color colorH3 = const Color(0xFF757575);




  static Color colorBlackBlue = const Color(0xFF50555C);
  static Color colorWhite1 = const Color(0xFFF0F0F0);
  static Color colorHintFormField = Colors.grey;
  static Color colorDivider = Colors.grey;
  static Color colorTableBroader =Colors.grey[300]!;
  static Color colorDotLine =Colors.grey.shade300;
  static  Color colorBlack = Colors.black;
  static  Color colorWhite = Colors.white;
  static  Color dividerColorLight = colorBlack.withOpacity(0.1);
  static  Color dividerColorDark = colorWhite.withOpacity(0.1);
  static  Color colorRed = Colors.redAccent;
  static const Color colorTransparent = Colors.transparent;
  static const Color colorActionYes = Colors.redAccent;
  static const Color colorActionCancel = Colors.blue;
  static const Color colorWhite100 = Color(0xFFFCFAFF);
  static const Color colorDarkBlue = Color(0xFF1D1C3E);
  static const Color colorWhite200 = Color(0xffF2F2F2);
  static const Color colorWhite300 = Color(0xffF0F0F0);
  static  Color colorBlack12 = Colors.black12;
  static Color colorGreyWhite = const Color(0xFFfafafa);
  static Color colorDarkGreyWhite = const Color(0xFFf2f2f2);
  static Color colorYellow = const Color(0xFFfdd486);
  static Color colorYellow100 = const Color(0xFFfffbe6);
  static Color colorYellow600 = const Color(0xFFfbb52c);


  static Color colorOrangeDark = const Color(0xFFf96d3b);
  static Color colorOrangeLight = const Color(0xFFff7f00);
  static Color colorYellow200= const Color(0xFFffe610);
  static Color colorLimeGreen = const Color(0xffb7eb8f);
  static Color colorShadowGreen = const Color(0xffe9f5ce);
  static Color colorLightGreen = const Color(0xff52c41a);
  static Color colorRedLight = const Color(0xFFff4d4f);
  static Color colorRedAccent = const Color(0xFFcc342b);
  static Color colorRedDark = const Color(0xFFFF0000);
  static Color colorLimeBlue = const Color(0xFFbfb9c4);
  static Color colorGray = const Color(0xFFA1A5AC);
  static Color colorLimeGray = const Color(0xFFececec);
  static Color colorProgress = const Color(0xFFB8C7CB);
  static Color colorChartLine = const Color(0xFF836ab7);
  static Color colorChartBackGround = const Color(0xFF836ab7);
  static Color colorPrimaryLime = const Color(0xFFeaddff);
  static Color colorChartLine1 = const Color(0xFF29abe2);
  static Color colorLightBlue = const Color(0xFF29ABE2);
  static Color colorLightBlue3 = const Color(0xFF44B5E6);
  static Color colorBlue = const Color(0xFF367FCE);
  static Color colorBlueLight = const Color(0xFF1775fa);
  static Color colorSelectText = const Color(0xFFb5d8fe);
  static Color colorLightBlue1 = const Color(0xFFE3F6FE);
  static Color colorLightBlue2 = const Color(0xFF88D8FF);
  static Color colorLightPurple = const Color(0xFFADBBF9);
  static Color colorChartBackGround1 = const Color(0xFFbbcce8);
  static Color colorGreen1 = const Color(0xFF618728);
  static Color colorGreen = const Color(0xFF198844);
  static Color colorSuccessGreen = const Color(0xFF2f9e45);
  static Color colorGreenBright = const Color(0xFF69db7c);
  static Color lightGreen = const Color(0xffc5f6ca);
  static Color lightRed = const Color(0xFFFBB0B0);
  static Color colorGreen2 = const Color(0xFF98BA50); // For Online
  static Color colorOrange = const Color(0xFFFFA800); // For Pending
  static Color colorH2 = const Color(0xFF9c9c9c); // For Offline
  static Color colorPolyLine =  Colors.black; // For Offline
  static Color colorGreen3 = const Color(0xFFC5D97C);
  static Color colorLevelChartBorder = const Color.fromARGB(255, 120, 0, 255);
  static Color colorLevelChartBackGround = const Color.fromARGB(120, 179, 113, 255);
  static Color colorRefChartBorder = const Color.fromARGB(255, 255, 165, 0);
  static Color colorRefChartBackGround = const Color.fromARGB(100, 255, 165, 0);

  static const Color colorCritical =  Color(0xFFDC0000);
  static const Color colorCriticalLite =  Color(0xFFFFCBCB);
  static const Color colorHigh =  Color(0xFFFD8C00);
  static const Color colorHighLite =  Color(0xFFFFE8CB);
  static const Color colorMedium =  Color(0xFFE9BD21);
  static const Color colorMediumLite =  Color(0xFFFFF4CB);
  static const Color colorLow =  Color(0xFF209EC7);
  static const Color colorLowLite =  Color(0xFFCBF2FF);
  static const Color colorGrn =  Color(0xFF198844);
  static const Color colorNotYetJoined =  Color(0xFFfe704c);
  static const Color colorJoined =  Color(0xFF4dc68e);
  static const Color colorDiscovered =  Color(0xFFffc65e);
  static const Color colorProvisioned =  Color(0xFFA1A5AC);
  static const Color colorTelemetryGreen =  Color(0xFF008000);
  static const Color colorTelemetryOrange =  Color(0xFFffa500);
  static const Color colorTopologyBorder =  Color(0xFFF4D438);
  static const Color colorTopology =  Color(0xFFFFEB95);
  static  Color colorTelemetryRed =  Color(0xFFFF0000);


}

Color getSeverityColor(String? severity, bool isLite) {
  if(severity==null){
    return AppColorConstants.colorGrn;
  }
  switch (severity.toUpperCase()) {
    case 'CRITICAL':
      return isLite ? AppColorConstants.colorCriticalLite : AppColorConstants.colorCritical;
    case 'HIGH':
      return isLite ? AppColorConstants.colorHighLite : AppColorConstants.colorHigh;
    case 'MEDIUM':
      return isLite ? AppColorConstants.colorMediumLite : AppColorConstants.colorMedium;
    case 'LOW':
      return isLite ? AppColorConstants.colorLowLite : AppColorConstants.colorLow;
    default:
      return AppColorConstants.colorGrn;
  }
}


class AppAssetsConstants {
  static String openStreetMapUrl = 'https://a.tile.openstreetmap.fr/osmfr/{z}/{x}/{y}.png'; // Sample -> https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png, https://a.tile.openstreetmap.fr/osmfr/{z}/{x}/{y}.png
  static String attribution = ', tiles courtesy of OSM France';
  static const String openStreetMapCopyRightURL = "https://www.openstreetmap.org/copyright";
  static const String roboto = 'OpenSans';
  static const String notoSans = 'OpenSans';
  static const String openSans = 'OpenSans';
  static const String poppins = 'OpenSans';
  static const String sourceSans = 'OpenSans';
  static const String manrope = 'OpenSans';
  static const String imagePath = 'assets/img/';
  static const String iconPath = 'assets/ic/';
  static const String ampsImagePath = 'assets/amps/';
  static const String versionPath = 'assets/version';
  static const String animationsPath = 'assets/animations/';


  static const String appLogo = '${imagePath}splash_logo.png';
  static const String homeAppLogo = '${imagePath}home_logo.png';
  static const String appBar = '${imagePath}amplifiers_logo.png';
  static const String userIcon = '${iconPath}user.svg';
  static const String amplifiersIcon = '${iconPath}ic_amplifiers.svg';
  static const String diagnosticsIcon = '${iconPath}ic_diagnostics.svg';
  static const String fileTextIcon = '${iconPath}ic_file_text.svg';
  static const String helpIcon = '${iconPath}ic_help_circle.svg';
  static const String logoutIcon = '${iconPath}ic_log-out.png';
  static const String monitorIcon = '${iconPath}ic_monitor.svg';
  static const String notificationIcon = '${iconPath}ic_notification.svg';
  static const String deviceOffline = '${iconPath}ic_device_offline.svg';
  static const String settingIcon = '${iconPath}ic_settings.svg';
  static const String stateRegionIcon = '${iconPath}ic_state_region.svg';
  static const String loginBackground = '${imagePath}login_background.png';
  static const String emptyLogo = '${imagePath}empty_box.svg';
  static const String logInUserImage = '${imagePath}user_profile.png';
  static const String searchIcon = '${iconPath}ic_search.svg';
  static const String softwareDownloadIcon = '${iconPath}ic_download_software.svg';
  static const String loaderAnimation = '${animationsPath}loader_animation.json';
  static const String refreshAnimation = '${animationsPath}refresh_animation.json';
  static const String splashLoaderAnimation = '${animationsPath}splash_loader_animation.json';
  static const String provisioningIcon = '${iconPath}ic_provisioning.png';
  static const String dashboardIcon = '${iconPath}ic_dashboard.svg';
  static const String vectorIcon = '${iconPath}ic_vector.svg';
  static const String topologyIcon = '${iconPath}ic_topology.svg';
  static const String starIcon = '${iconPath}ic_star.svg';
  static const String ic_star_online = '${iconPath}ic_star_online.svg';
  static const String ic_star_pending = '${iconPath}ic_star_pending.svg';
  static const String editIcon = '${iconPath}ic_edit.svg';
  static const String deleteIcon = '${iconPath}ic_delete.svg';
  static const String arrowRightIcon = '${iconPath}ic_arrow-right-circle.svg';
  static const String calendarIcon = '${iconPath}ic_calendar.svg';
  static const String addIcon = '${iconPath}ic_add_plus.svg';
  static const String closeMenuIcon = '${iconPath}ic_close_menu.svg';
  static const String openMenuIcon = '${iconPath}ic_open_menu.svg';
  static const String attnIcon = '${iconPath}ic_attn.svg';
  static const String equIcon = '${iconPath}ic_rectangle.svg';
  static const String equ2Icon = '${iconPath}ic_rectangle1732.svg';
  static const String equ3Icon = '${iconPath}ic_rectangle1734.svg';
  static const String batteryChargeIcon = '${iconPath}ic_battery_charge.svg';
  static const String batteryIcon = '${iconPath}ic_battery.svg';
  static const String chargingIcon = '${iconPath}ic_charg_icon.svg';
  static const String chargingPlugOutIcon = '${iconPath}ic_charg_plug_out.svg';
  static const String rightArrowIcon = '${iconPath}ic_right_arrow.svg';
  static const String searchAddIcon = '${iconPath}ic_add_search.png';
  static const String apiStatusIcon = '${iconPath}ic_api_status.svg';
  static const String dataPath = 'assets/data/';
  static const String configPath = '${dataPath}app_config.json';
  static const String q18LineExtender = '${ampsImagePath}Q18_LE.png';
  static const String q18HGDExtender = '${ampsImagePath}Q18_HGD.png';
  static const String q18HGBT = '${ampsImagePath}Q18_HGBT.png';
  static const String QB18_SYS_AMP_BLE = '${ampsImagePath}QB18_SYS_AMP_BLE.png';
  static const String QB18_SYS_AMP_MB = '${ampsImagePath}QB18_SYS_AMP_MB.png';
  static const String icReset = '${iconPath}ic_reset.svg';
  static const String icCircleCheck = '${iconPath}ic_circle-check.svg';
  static const String deleteActiveIcon = '${iconPath}ic_delete_active.svg';
  static const String downloadIcon = '${iconPath}ic_download.svg';
  static const String radioOffIcon = '${iconPath}radio-button-off.svg';
  static const String addListIcon = '${iconPath}ic_add_list.svg';
  static const String deleteEnabledIcon = '${iconPath}ic_delete_enabled_blue.svg';
  static const String alertTriangleIcon = '${iconPath}ic_alert_triangle.svg';


  static const String connectedIcon = '${iconPath}ic_connected.svg';
  static const String disconnectedIcon = '${iconPath}ic_disconnected.svg';
  static const String tableIcon = '${iconPath}ic_table.svg';
  static const String listTableIcon = '${iconPath}ic_list.svg';
  static const String errorIcon = '${iconPath}ic_error.png';
  static const String successIcon = '${iconPath}ic_checkmark.png';
  static const String playIcon = '${iconPath}ic_play.png';
  static const String softwareUpdateIcon = '${iconPath}ic_update.svg';
  static const String joinServerIcon = '${iconPath}ic_joinserver.svg';
  static const String apiIcon = '${iconPath}ic_apis.svg';
  static const String nodeGWIcon = '${iconPath}ic_node_gw.svg';
  static const String icMarkerDevice = '${iconPath}ic_marker_device.svg';
  static const String icMarkerDeviceIcon = '${iconPath}ic_marker_device_icon.svg';
  static const String icGwDevice = '${iconPath}ic_gw_device.svg';
  static const String icGwDeviceIcon = '${iconPath}ic_gw_device_icon.svg';
}
class AmpSubTabConstants {
  static const int dashboard = 0;
  static const int telemetry = 1;
  static const int alarmHistory = 2;
  static const int spectrum = 3;
  static const int auditLog = 4;
  static const int diagnostics = 5;
  static const int configuration = 6;
}
class AmpTabConstants {
  static const int ampList = 0;
  static const int ampMap = 1;

}
class FirebaseKeyConstants {
  static const String registrations = 'registrations';
  static const String config = 'config';
  static const String domain = 'domain';
}
const LatLng defaultLatLng = LatLng(33.46186, -84.23398);

class MockData {

  static List<ApiStatusItem> listApiStatus = [
    ApiStatusItem(title: "API Gateway", healthResponse: {}, versionResponse: {}, key: "ql-api-gw",apiUrl: AppConfig.shared.baseUrl, apiStatus: ApiStatus.initial),
    ApiStatusItem(title: "Join Server", healthResponse: {}, versionResponse: {}, key: "ql_join_server",apiUrl: AppConfig.shared.joinServerUrl, apiStatus: ApiStatus.initial),
    ApiStatusItem(title: "FUOTA Service", healthResponse: {}, versionResponse: {}, key: "ql_fuota_svc",apiUrl: AppConfig.shared.foutaServerUrl, apiStatus: ApiStatus.initial),
  ];

  static listForNodeMgr(){
    if(!AppConfig.shared.isQLCentral){
      listApiStatus = [
        ApiStatusItem(title: "API Gateway", healthResponse: {}, versionResponse: {}, key: "ql-api-gw",apiUrl: AppConfig.shared.baseUrl, apiStatus: ApiStatus.initial),
        // ApiStatusItem(title: "Node Manager", healthResponse: {}, versionResponse: {}, key: "ql-node-mgr",apiUrl: AppConfig.shared.nodeMgrUrl, apiStatus: ApiStatus.initial),
        ApiStatusItem(title: "Join Server", healthResponse: {}, versionResponse: {}, key: "ql_join_server",apiUrl: AppConfig.shared.joinServerUrl, apiStatus: ApiStatus.initial),
        ApiStatusItem(title: "FUOTA Service", healthResponse: {}, versionResponse: {}, key: "ql_fuota_svc",apiUrl: AppConfig.shared.foutaServerUrl, apiStatus: ApiStatus.initial),
        //ApiStatusItem(title: "BLE Service", healthResponse: {}, versionResponse: {}, key: "ql_ble_svc",apiUrl: AppConfig.shared.bleSvcUrl, apiStatus: ApiStatus.initial),
      ];
    }
  }

}

String divide1000(dynamic value){
  return (value/1000).toString();
}
double multiply1000(String value){
  return (double.parse(value)*1000);
}
