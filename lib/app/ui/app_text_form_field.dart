import 'package:quantumlink_node/app_import.dart';

class AppTextFormField extends StatelessWidget {
  final String? label;
  final String? errorText;
  final String? hintText;
  final Color? enabledBorderColor;
  final Color? focusedBorderColor;
  final Color? hintTextColor;
  final TextEditingController controller;
  final int maxLines;
  final TextInputType textInputType;
  final FormFieldValidator? validator;
  final ValueChanged<String>? onFieldSubmitted;
  final bool? readOnly;

  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final GestureTapCallback? onTap;
  final List<TextInputFormatter>? inputFormatters;
  final ValueChanged<String>? onChanged;
  final FocusNode? focusNode;
  final double? borderRadius;
  final double? hintFontSize;
  final EdgeInsetsGeometry? contentPadding;
  final String? fontFamily;
  final FontWeight? fontWeight;
  final bool obscureText;
  final TextStyle? textStyle;

  AppTextFormField(
      {super.key,
      this.label,
      this.errorText,
      required this.controller,
      required this.maxLines,
      required this.textInputType,
      this.validator,
      this.prefixIcon,
      this.inputFormatters,
      this.hintText,
      this.readOnly = false,
      this.enabledBorderColor,
      this.focusedBorderColor,
      this.onFieldSubmitted,
      this.suffixIcon,
      this.borderRadius,
      this.hintFontSize,
      this.fontFamily,
      this.contentPadding,
      this.onTap,
      this.hintTextColor,
      this.fontWeight,
      this.onChanged,
      this.focusNode,
      this.obscureText = false,
      this.textStyle = const TextStyle(
        color: Colors.black,
        fontSize: 15,
        fontWeight: FontWeight.w500,
      )});

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      onFieldSubmitted: onFieldSubmitted,
      cursorHeight: 16,
      decoration: decorationOfTextField(label,
          errorText: errorText,
          prefixIcon: prefixIcon,
          suffixIcon: suffixIcon,
          hintText: hintText,
          enabledBorderColor: enabledBorderColor,
          focusedBorderColor: focusedBorderColor,
          borderRadius: borderRadius,
          hintTextColor: hintTextColor,
          hintFontSize: hintFontSize,
          fontFamily: fontFamily,
          fontWeight: fontWeight,
          contentPadding: contentPadding).copyWith(errorMaxLines: 2),
      style: textStyle,
      validator: validator,
      controller: controller,
      keyboardType: textInputType,
      maxLines: maxLines,
      focusNode: focusNode,
      inputFormatters: inputFormatters,
      canRequestFocus: (readOnly == true) ? false : true,
      readOnly: (readOnly == true) ? true : false,
      onTap: onTap,
      onChanged: onChanged,
      obscureText: obscureText,
    );
  }
}

InputDecoration decorationOfTextField(labelText,
    {onTap,
    Widget? prefixIcon,
    Widget? suffixIcon,
    hintText,
    errorText,
    Color? enabledBorderColor,
    Color? focusedBorderColor,
    double? borderRadius,
    Color? hintTextColor,
    double? hintFontSize,
    String? fontFamily,
    FontWeight? fontWeight,
    EdgeInsetsGeometry? contentPadding}) {
  return InputDecoration(
      alignLabelWithHint: true,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      labelText: labelText,
      hintText: hintText,
      isDense: true,
      errorText: errorText,
      contentPadding:
          contentPadding ?? const EdgeInsets.fromLTRB(10, 10, 10, 10),
      floatingLabelStyle: TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 15,
          color: AppColorConstants.colorPrimary),
      labelStyle: TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: hintFontSize ?? 15,
          color: AppColorConstants.colorHintFormField,
          fontFamily: fontFamily),
      hintStyle: TextStyle(
          fontWeight: fontWeight ?? FontWeight.w400,
          fontSize: hintFontSize ?? 15,
          color: hintTextColor ?? AppColorConstants.colorBlack,
          fontFamily: fontFamily),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadius ?? 3),
        gapPadding: 0,
        borderSide: BorderSide(
          color: enabledBorderColor ?? AppColorConstants.colorHintFormField,
        ),
      ),
      errorStyle: TextStyle(
        color: AppColorConstants.colorRedAccent,
      ),
      errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius ?? 3),
          gapPadding: 0,
          borderSide: BorderSide(
            color: AppColorConstants.colorRedAccent,
          )),
      focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius ?? 3),
          gapPadding: 0,
          borderSide: const BorderSide(
            color: Colors.red,
          )),
      focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius ?? 3),
          gapPadding: 0,
          borderSide: BorderSide(
            color: focusedBorderColor ?? AppColorConstants.colorPrimary,
          )));
}

TextStyle get styleOfTextField => const TextStyle(
      color: Colors.black,
      fontSize: 15,
      fontWeight: FontWeight.w500,
    );
