import 'package:quantumlink_node/app_import.dart';

// Linear progress indicator
class CustomCardLinearPercentIndicator extends StatelessWidget {
  final double width;
  final double lineHeight;
  final double percent;
  final String labelText;
  final Color progressColor;
  final Color backgroundColor;
  final TextStyle? labelStyle;
  final Radius barRadius;
  final double elevation;
  final BorderRadiusGeometry borderRadius;

  const CustomCardLinearPercentIndicator({
    super.key,
    required this.width,
    required this.lineHeight,
    required this.percent,
    required this.labelText,
    required this.progressColor,
    this.backgroundColor = Colors.grey,
    this.labelStyle,
    this.barRadius = const Radius.circular(10),
    this.elevation = 5,
    this.borderRadius = const BorderRadius.all(Radius.circular(10)),
  })  : assert(percent >= 0 && percent <= 1, "Percent must be between 0 and 1");

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.centerLeft,
      children: [
        // Background bar
        Container(
          width: width+6,
          height: lineHeight+6,
          decoration: BoxDecoration(
            border:  Border.all(color:progressColor, width: 1.3,style: BorderStyle.none),
            color: progressColor,
            borderRadius: borderRadius,
          ),
          child:Container(
            width: width,
            height: lineHeight,
            decoration: BoxDecoration(
              color: AppColorConstants.colorWhite,
              borderRadius: borderRadius,
            ),
          ) ,
        ),
        // Progress bar
        Container(
          margin: const EdgeInsets.only(left: 3),
          width: width * percent,
          height: percent <= 0.01
              ? lineHeight - 7
              : percent <= 0.02
                  ? lineHeight - 3
                  : lineHeight,
          decoration: BoxDecoration(
            color: progressColor,
            borderRadius: BorderRadius.horizontal(
              left: barRadius,
              right: percent == 1.0 ? barRadius : Radius.zero,
            ),
          ),
        ),
        Container(
          width: width+6,
          alignment: Alignment.center,
          child: AppText(
            isSelectableText: false,
            labelText,
            style: labelStyle ??
                TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                  color: AppColorConstants.colorBlack,
                ),
          ),
        ),
      ],
    );
  }
}

// Circular progress indicator
class CustomCircularProgress extends StatelessWidget {
  final double radius;
  final double lineWidth;
  final double percent;
  final String centerText;
  final TextStyle textStyle;
  final Color progressColor;
  final Color backgroundColor;

  const CustomCircularProgress({
    super.key,
    required this.radius,
    required this.lineWidth,
    required this.percent,
    required this.centerText,
    required this.textStyle,
    required this.progressColor,
    this.backgroundColor = Colors.grey
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 5,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(50)),
      child: SizedBox(
        height: radius * 2,
        width: radius * 2,
        child: Stack(
          alignment: Alignment.center,
          children: [
            // Background Circle
            Container(
              width: radius * 2,
              height: radius * 2,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: backgroundColor,
              ),
            ),
            Transform.rotate(
              angle: -3.14 / 2,
              child: ShaderMask(
                shaderCallback: (Rect bounds) {
                  return SweepGradient(
                    startAngle: 0.0,
                    endAngle: 3.14 * 2,
                    stops: [percent, percent],
                    center: Alignment.center,
                    colors: [progressColor, AppColorConstants.colorLimeBlue],
                  ).createShader(bounds);
                },
                child: Container(
                  width: radius * 2,
                  height: radius * 2,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppColorConstants.colorWhite,
                  ),
                ),
              ),
            ),
            Container(
              width: radius * 1.6,
              height: radius * 1.6,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color:backgroundColor,
              ),
            ),
            AppText(
              isSelectableText: false,
              centerText,
              style: textStyle,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}