import 'package:quantumlink_node/app_import.dart';

class AppPaginationWidget extends StatelessWidget {
  final PaginationHelper paginationHelper;
  final Function() onLoadNext;
  final Function() onGoToFirstPage;
  final Function() onLoadPrevious;
  final Function() onGoToLastPage;
  final ValueChanged? onChanged;
  ApiStatus apiStatus = ApiStatus.initial;
  FocusNode ?focusNode;

   AppPaginationWidget({
    super.key,
    required this.paginationHelper,
    required this.onLoadNext,
    required this.onLoadPrevious,
    required this.onGoToFirstPage,
    required this.onGoToLastPage,
    this.itemsPerPage,
    this.onChanged,
    required this.apiStatus,
     this.focusNode
  });

  final int ? itemsPerPage;
  final List<String> perPageOptions = ["5", "10", "15", "20"];

  @override
  Widget build(BuildContext context) {
    return RawKeyboardListener(
      autofocus: true,
      focusNode: focusNode ?? FocusNode(),
      onKey: (RawKeyEvent event) async {
        if (event is RawKeyDownEvent && apiStatus != ApiStatus.loading) {
          if (paginationHelper.canGoToNextPage &&
              event.logicalKey == LogicalKeyboardKey.arrowRight) {
            onLoadNext();
          } else if (paginationHelper.canGoToPreviousPage &&
              event.logicalKey == LogicalKeyboardKey.arrowLeft) {
            onLoadPrevious();

          }
        }
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          if (itemsPerPage != null)
            SizedBox(
              width: 80,
              height: 30,
              child: CommonDropdownButton(
                iconColor: AppColorConstants.colorH3,
                selectedValue: itemsPerPage.toString(),
                menuHeight: 25,
                fontSize: 12,
                buttonHeight: getSize(35),
                hintText: '',
                items: perPageOptions,
                onChanged: onChanged,
              ),
            ),

          IconButton(
            icon: const Icon(Icons.skip_previous),
            onPressed: paginationHelper.canGoToPreviousPage
                ? () {
              paginationHelper.currentPage = 0;
              onGoToFirstPage();
            }
                : null,
          ),
          IconButton(
            icon: const Icon(
              Icons.arrow_back_ios_new,
              size: 14,
            ),
            onPressed: paginationHelper.canGoToPreviousPage
                ? () {
              onLoadPrevious();
            }
                : null,
          ),
          AppText('${paginationHelper.currentPage + 1}',
              style: TextStyle(
                  fontWeight: getMediumBoldFontWeight(),
                  fontSize: getSize(14),
                  fontFamily: AppAssetsConstants.openSans)),
          IconButton(
            icon: const Icon(
              Icons.arrow_forward_ios,
              size: 14,
            ),
            onPressed: paginationHelper.canGoToNextPage
                ? () {
              onLoadNext();
            }
                : null,
          ),
          IconButton(
            icon: const Icon(Icons.skip_next),
            onPressed: paginationHelper.canGoToNextPage &&
                    paginationHelper.currentPage != paginationHelper.totalPages - 1
                ? () {
                    paginationHelper.currentPage = paginationHelper.totalPages - 1;
                    onGoToLastPage();
                  }
                : null,
          ),
        ],
      ),
    );
  }
}