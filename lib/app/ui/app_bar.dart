import 'package:quantumlink_node/app_import.dart';


getDefaultAppBarWithAction(title, i,
    BuildContext context,
    {Widget? leading,double? leadingWidth,
    required Function notificationFunction,
    required Function helpFunction,
    required Function profileFunction,
    // required Function wifiConnectionFunction,
    required String ssidText,
    required String ipText,
    required Color wifiIconColor,Widget ? inventoryButtonView}) {
  return AppBar(leadingWidth: leadingWidth ?? 56 ,
    backgroundColor: AppColorConstants.colorPrimary,
    surfaceTintColor: AppColorConstants.colorTransparent,
    toolbarHeight: getSize(82),
    leading: leading,
    actions: [
      Padding(
        padding: const EdgeInsets.only(right: 16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            /*GestureDetector(onTap: () {
              notificationFunction.call();
            },
              child:  MouseRegion(
                cursor: SystemMouseCursors.click, // Change cursor type here
                child: CircleAvatar(maxRadius: 18,backgroundColor: AppColorConstants.colorWhite,
                  child: Padding(
                    padding:  EdgeInsets.all(getSize(5)),
                    child:  AppImageAsset(image: AppAssetsConstants.notificationIcon,color: AppColorConstants.colorGreen3),
                  ),
                ),
              ),
            ),
            SizedBox(width: getSize(20)),
            GestureDetector(onTap: () {
              helpFunction.call();
            },
              child:  MouseRegion(
                cursor: SystemMouseCursors.click, // Change cursor type here
                child: CircleAvatar(maxRadius: 18,backgroundColor: AppColorConstants.colorWhite,
                  child: Padding(
                    padding: EdgeInsets.all(getSize(5)),
                    child:  AppImageAsset(image: AppAssetsConstants.helpIcon,color: AppColorConstants.colorGreen3,),
                  ),
                ),
              ),
            ),
            SizedBox(width: getSize(20)),*/
            // if (!AppConfig.shared.isQLCentral) ...[
            //   GestureDetector(
            //     onTap: () => wifiConnectionFunction.call(),
            //     child: Tooltip(
            //       message: "${ssidText}\n${ipText}",
            //       textAlign: TextAlign.center,
            //       textStyle: TextStyle(
            //           fontFamily: AppAssetsConstants.openSans,
            //           fontWeight: FontWeight.w800,
            //           color: AppColorConstants.colorAppbar),
            //       decoration: BoxDecoration(
            //         color: AppColorConstants.colorWhite,
            //         borderRadius: BorderRadius.circular(10),
            //         border: Border.all(
            //           color: AppColorConstants.colorH2,
            //           width: 0.8,
            //         ),
            //         boxShadow: [
            //           BoxShadow(
            //             color: AppColorConstants.colorAppbar,
            //             spreadRadius: 1,
            //             blurRadius: 5,
            //             offset: const Offset(0, 3), // changes position of shadow
            //           ),
            //         ],
            //       ),
            //       child: MouseRegion(
            //         cursor: SystemMouseCursors.click,
            //         child: CircleAvatar(
            //           maxRadius: 18,
            //           backgroundColor: AppColorConstants.colorWhite,
            //           child: Padding(
            //             padding: EdgeInsets.all(getSize(5)),
            //             child: Icon(Icons.wifi, color: wifiIconColor),
            //           ),
            //         ),
            //       ),
            //     ),
            //   ),
            //   SizedBox(width: getSize(16)),
            // ],
            inventoryButtonView ?? SizedBox.shrink(),
            GestureDetector(onTap: () => profileFunction.call(),
              child:  MouseRegion(
                cursor: SystemMouseCursors.click, // Change cursor type here
                child: CircleAvatar(backgroundColor: AppColorConstants.colorWhite, child: i),
              ),
            ),
            SizedBox(width: getSize(5)),
          ],
        ),
      )
    ],centerTitle:false ,titleSpacing:0,
    title:AppText(
      (AppConfig.shared.isInventory && isInventoryViewByQuery()) ? AppStringConstants.appNameInventory : title,
      maxLines: 1,
      style: TextStyle(
          fontSize: getSize(28),
          color: AppColorConstants.colorWhite,
          fontFamily: AppAssetsConstants.openSans,
          fontWeight: FontWeight.w400,
      ),
    ),
    shape: Border(bottom: BorderSide(color: AppColorConstants.colorPrimary, width: 1.1)),
  );
}

getCustomAppBarWithClose(title){
  return Column(crossAxisAlignment: CrossAxisAlignment.center,
    children: [
      SizedBox(height: getSize(10)),
      Row(
        children: [
          SizedBox(width: getSize(2), height: getSize(8),),
          Expanded(child: AppText(title, style: TextStyle(fontWeight: getMediumBoldFontWeight(), fontSize: getSize(18) ,fontFamily: AppAssetsConstants.openSans),)),
          IconButton(onPressed: (){
            goBack();
          }, icon: const Icon(Icons.close)),
        ],
      ).paddingOnly(left: 20,right: 15),
      getAppDivider(),
    ],
  );
}

getPageTitleView( String pageTitle) {
  return Padding(
    padding: const EdgeInsets.symmetric(horizontal: 8),
    child: Column(crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if(!AppConfig.shared.isOpenFromBLE) SizedBox(height: getSize(30)) else SizedBox(height: getSize(15)),
        Wrap(
          children: [
            AppText(pageTitle, style: TextStyle(fontSize: 24, fontWeight: getBoldFontWeight(), color: AppColorConstants.colorChartLine),)
          ],
        ),
        SizedBox(height: getSize(5)),
      ],
    ),
  );
}