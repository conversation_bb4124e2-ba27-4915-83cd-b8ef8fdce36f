import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart' as latlong;

class CustomInfoWindowController {
  Function(Widget, latlong.LatLng, double, double, double)? addInfoWindow;

  VoidCallback? onCameraMove;

  VoidCallback? hideInfoWindow;

  VoidCallback? showInfoWindow;

  MapController? mapController;

  BuildContext? mapContext;

  void dispose() {
    addInfoWindow = null;
    onCameraMove = null;
    hideInfoWindow = null;
    showInfoWindow = null;
    mapController = null;
    mapContext = null;
  }
}

class CustomInfoWindow extends StatefulWidget {
  final CustomInfoWindowController controller;

  const CustomInfoWindow({super.key, required this.controller});

  @override
  CustomInfoWindowState createState() => CustomInfoWindowState();
}

class CustomInfoWindowState extends State<CustomInfoWindow> {
  bool _showNow = false;
  double _leftMargin = 0;
  double _topMargin = 0;
  Widget? _child;
  latlong.LatLng? _latLng;
  double? _offset;
  double? _height;
  double? _width;

  @override
  void initState() {
    super.initState();
    widget.controller.addInfoWindow = _addInfoWindow;
    widget.controller.onCameraMove = _onCameraMove;
    widget.controller.hideInfoWindow = _hideInfoWindow;
    widget.controller.showInfoWindow = _showInfoWindow;
  }

  void _updateInfoWindow() {
    if (_latLng == null ||
        _child == null ||
        _offset == null ||
        _height == null ||
        _width == null ||
        widget.controller.mapController == null ||
        !mounted) {
      return;
    }

    final screenSize = MediaQuery.of(context).size;
    
    // Convert LatLng to screen coordinates using the map's projection
    final camera = widget.controller.mapController!.camera;
    final screenPoint = camera.latLngToScreenPoint(_latLng!);
    if (screenPoint == null) return;

    double left = screenPoint.x - (_width! / 2);
    double top = screenPoint.y - (_offset! + _height!);

    // Ensure the info window stays within screen bounds
    left = left.clamp(0.0, screenSize.width - _width!);
    if (top < 0) {
      top = screenPoint.y + _offset!;
      if (top + _height! > screenSize.height) {
        top = screenSize.height - _height! - 10;
      }
    }
    top = top.clamp(0.0, screenSize.height - _height! - 20);

    setState(() {
      _showNow = true;
      _leftMargin = left;
      _topMargin = top;
    });
  }

  void _addInfoWindow(Widget child, latlong.LatLng latLng, double offset, double height, double width) {
    _child = child;
    _latLng = latLng;
    _offset = offset;
    _height = height;
    _width = width;
    _updateInfoWindow();
  }

  void _onCameraMove() {
    if (!_showNow) return;
    _updateInfoWindow();
  }

  void _hideInfoWindow() {
    setState(() {
      _showNow = false;
    });
  }

  void _showInfoWindow() {
    _updateInfoWindow();
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: _leftMargin,
      top: _topMargin,
      child: Visibility(
        visible: (_showNow == false ||
                (_leftMargin == 0 && _topMargin == 0) ||
                _child == null ||
                _latLng == null)
            ? false
            : true,
        child: SizedBox(
          height: _height,
          width: _width,
          child: _child,
        ),
      ),
    );
  }
}
