import '../../app_import.dart';
import 'dart:math' as math;
extension AppToast on String {
  void showSuccess(BuildContext context,{double fontSize = 15}) {
    final int maxLines = _calculateDynamicMaxLines();
    toastification.show(
      context: context,
      type: ToastificationType.success,
      style: ToastificationStyle.flat,
      title: AppText(this,style: TextStyle(fontSize: fontSize,fontFamily: AppAssetsConstants.notoSans),maxLines:maxLines),
      icon: const Icon(Icons.check_circle_rounded),
      alignment: Alignment.topRight,
      direction:TextDirection.ltr,
      autoCloseDuration: const Duration(seconds: 4),
      boxShadow: highModeShadow,
      showProgressBar: false,
    );
  }

  void showError(BuildContext context, {double fontSize = 15}) {
    final int maxLines = _calculateDynamicMaxLines();
    toastification.show(
      context: context,
      boxShadow: highModeShadow,
      type: ToastificationType.error,
      style: ToastificationStyle.flat,
      icon: const Icon(Icons.cancel_rounded),
      title: AppText(this,style:  TextStyle(fontSize: fontSize,fontFamily: AppAssetsConstants.notoSans),maxLines: maxLines,),
      alignment: Alignment.topRight,
      autoCloseDuration: const Duration(seconds: 4),
      borderRadius: BorderRadius.circular(12.0),
      showProgressBar: false,
    );
  }

  void showOffline(BuildContext context, {double fontSize = 15}) {
    toastification.show(
      context: context,
      boxShadow: highModeShadow,
      type: ToastificationType.info,
      style: ToastificationStyle.flat,
      icon:  Icon(Icons.info,color: AppColorConstants.colorPrimary,),
      title: AppText(this,style:  TextStyle(fontSize: fontSize,fontFamily: AppAssetsConstants.notoSans),),
      alignment: Alignment.topRight,
      autoCloseDuration: const Duration(seconds: 4),
      borderRadius: BorderRadius.circular(12.0),
      showProgressBar: false,
    );
  }
  void showSpectrumToast(BuildContext context, {double fontSize = 15}) {
    toastification.show(
      context: context,
      boxShadow: highModeShadow,
      type: ToastificationType.info,
      style: ToastificationStyle.flat,
      icon:  Icon(Icons.info,color: AppColorConstants.colorPrimary,),
      title: AppText(this,style:  TextStyle(fontSize: fontSize,fontFamily: AppAssetsConstants.notoSans),),
      alignment: Alignment.topRight,
       autoCloseDuration: const Duration(seconds: 6),
      borderRadius: BorderRadius.circular(12.0),
      showProgressBar: false,
    );
  }

  void showMessage({double fontSize = 15}) {
    final int maxLines = _calculateDynamicMaxLines();
    toastification.show(
      context: buildContext,
      boxShadow: highModeShadow,
      type: ToastificationType.info,
      style: ToastificationStyle.flat,
      icon:  Icon(Icons.info,color: AppColorConstants.colorPrimary,),
      title: AppText(this,style:  TextStyle(fontSize: fontSize,fontFamily: AppAssetsConstants.notoSans),maxLines: maxLines),
      alignment: Alignment.topRight,
      autoCloseDuration: const Duration(seconds: 4),
      borderRadius: BorderRadius.circular(12.0),
      showProgressBar: false,
    );
  }
  int _calculateDynamicMaxLines() {
    const int baseLength = 40;
    const int maxAllowedLines = 8;

    int linesNeeded = (length / baseLength).ceil();
    return math.min(linesNeeded, maxAllowedLines);
  }
}
