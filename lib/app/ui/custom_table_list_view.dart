import '../../app_import.dart';

class CustomExpansionTile extends StatelessWidget {
  final Widget titleWidget;
  final List<Widget>? rowChildren;
  final int index;
  final ExpansionTileController? controller;

  const CustomExpansionTile({
    super.key,
    required this.titleWidget,
    this.rowChildren,
    required this.index,
    this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return ExpansionTile(
      tilePadding: EdgeInsets.all(3),
      collapsedBackgroundColor:
          index.isEven ? AppColorConstants.colorWhite : AppColorConstants.colorBackgroundDark,
      shape: RoundedRectangleBorder(side: BorderSide(color: AppColorConstants.colorH2, width: 1)),
      collapsedShape:
          RoundedRectangleBorder(side: BorderSide(color: AppColorConstants.colorH2, width: 1)),
      title: titleWidget,
      controller: controller,
      enabled: false,
      showTrailingIcon: false,
      expandedCrossAxisAlignment: CrossAxisAlignment.start,
      expandedAlignment: Alignment.centerLeft,
      childrenPadding: const EdgeInsets.only(top: 5),
      children: rowChildren ?? [],
    );
  }
}
class CustomListTile extends StatelessWidget {
  final Widget titleWidget;
  final int index;
  final GestureTapCallback ? onTap;

  const CustomListTile({
    super.key,
    required this.titleWidget,
    required this.index,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(alignment: Alignment.center,
      decoration: BoxDecoration(
        color:  AppConfig.shared.isOpenFromBLE ? AppColorConstants.colorAppBackground : index.isEven ? AppColorConstants.colorWhite : AppColorConstants.colorBackgroundDark,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColorConstants.colorH2, width: 1),
      ),
      child: ListTile(
        hoverColor: AppColorConstants.colorBackgroundDark.withOpacity(0.2),
        onTap: onTap,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        title: titleWidget,
      ),
    );
  }
}

class CustomTableRow extends StatelessWidget {
  final String label;
  final DataCell cell;
  final bool isFlexible;

  const CustomTableRow(
      {super.key, required this.label, required this.cell, this.isFlexible = true});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 50,
      decoration: BoxDecoration(
        color: AppColorConstants.colorWhite,
        border: const Border(top: BorderSide(color: Colors.grey, width: 0.5)),
      ),
      padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 25),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
            alignment: Alignment.centerLeft,
            width: 120,
            child: AppText(
              label,
              style: TextStyle(fontSize: 14, color: AppColorConstants.colorBlack),
            ),
          ),
          if (isFlexible)
            Flexible(
              child: cell.child,
            )
          else
            cell.child
        ],
      ),
    );
  }
}
