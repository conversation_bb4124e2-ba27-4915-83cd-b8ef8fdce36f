// ignore_for_file: prefer_const_constructors, deprecated_member_use

import 'package:quantumlink_node/app_import.dart';


getAppDropDown(value, List<dynamic>? list, Function fun,
    {Color? dropdownButtonColor, Color? dropdownColor , Color? iconColor, Color? fontColor ,double? borderRadius}) {
  bool isExist = false;
  list?.forEach((element) {
    if (element.name == value!.name) {
      isExist = true;
    }
  });
  if (!isExist && value.name != null) {
    list!.add(value);
  }

  return DropdownButtonHideUnderline(
    child: GFDropdown(
      //padding: const EdgeInsets.only(top: 6, left: 16, bottom: 16, right: 16),
      itemHeight: 40,
      isExpanded: true,
      icon:  Icon(Icons.keyboard_arrow_down,color: iconColor ?? AppColorConstants.colorBlack,),
      borderRadius: BorderRadius.circular(borderRadius??8),
      iconSize: 24,
      border:   BorderSide(
          color: AppColorConstants.colorGray, width: 1),
      dropdownButtonColor:dropdownButtonColor?? AppColorConstants.colorPrimary,
      iconEnabledColor: AppColorConstants.colorBlack,
      dropdownColor:dropdownColor ?? AppColorConstants.colorPrimary,
      value: value!.name,
      hint: AppText(
        "Select",
        style: TextStyle(color: fontColor ?? Colors.white,fontSize: 15, fontFamily: AppAssetsConstants.openSans),
      ),
      onChanged: (newValue) {
        fun.call(newValue);
      },
      items: list!
          .map((value) => DropdownMenuItem(
                value: value.name,
                alignment: AlignmentDirectional.centerStart,
                child:Row(children: [
                  Padding(
                    padding: const EdgeInsets.only(left: 8.0),
                    child: Text(
                      value.name,
                      style: TextStyle(
                          color: fontColor ?? Colors.white,
                          fontWeight: FontWeight.w500,
                          fontFamily: AppAssetsConstants.openSans),
                    ),
                  ),
                ],)
              ))
          .toList(),
    ),
  );


}

class CommonDropdownButton extends StatelessWidget {
  final List<dynamic>? items;
  final dynamic selectedValue;
  final String hintText;
  final double? buttonHeight;
  final double? fontSize;
  final double? menuHeight;
  final Color ?borderColor;
  final Color ?iconColor;
  final ValueChanged? onChanged;
  final DropdownButtonBuilder? selectedItemBuilder;
  final List<DropdownMenuItem>? customDropDownItems;
  final bool isCustomItems;

  const CommonDropdownButton(
      {super.key,
        this.selectedValue,
        this.items,
        this.onChanged,
        this.borderColor,
        this.fontSize,
        this.iconColor,
        this.buttonHeight,
        this.menuHeight,
        required this.hintText,
        this.customDropDownItems,
  this.selectedItemBuilder,
        this.isCustomItems = false});

  @override
  Widget build(BuildContext context) {
    return DropdownButtonHideUnderline(
      child: DropdownButton2(
        isExpanded: true,
        hint: Text(
          hintText,
          style:  TextStyle(
            fontSize: fontSize??14,
            color: AppColorConstants.colorH2,
          ),
          overflow: TextOverflow.ellipsis,
        ),
        items: isCustomItems ?  customDropDownItems:items!
            .map(
              (items) => DropdownMenuItem<String>(
            value: items,
            child: AppText(isSelectableText: false,items,style:TextStyle(fontSize: fontSize??14,fontFamily: AppAssetsConstants.notoSans)),
          ),
        )
            .toList(),selectedItemBuilder:selectedItemBuilder,
        value: selectedValue,
        onChanged: onChanged,
        buttonStyleData: ButtonStyleData(padding:   EdgeInsets.only(right: 8),
          height: buttonHeight ?? 50,
          decoration: BoxDecoration(color: AppColorConstants.colorWhite,
            borderRadius: BorderRadius.circular(5),
            border: Border.all(
              color: borderColor ??AppColorConstants.colorH2.withOpacity(0.5),
            ),
          ),
        ),
        iconStyleData: IconStyleData(
          openMenuIcon: Icon(
            color: iconColor ?? AppColorConstants.colorWhite,
            Icons.keyboard_arrow_up_sharp,
          ),
          icon: Icon(
            color: iconColor ?? AppColorConstants.colorWhite,
            Icons.keyboard_arrow_down_outlined,
          ),
          iconSize: 20,
        ),
        dropdownStyleData: DropdownStyleData(openInterval: Interval(0.25 ,0.70),
          maxHeight: 200,
          decoration: BoxDecoration(
            color: AppColorConstants.colorWhite,
            borderRadius: BorderRadius.circular(8),
          ),
          scrollbarTheme: ScrollbarThemeData(
            radius: const Radius.circular(40),
            thickness: MaterialStateProperty.all<double>(6),
            thumbVisibility: MaterialStateProperty.all<bool>(true),
          ),
        ),
        menuItemStyleData: MenuItemStyleData(
          selectedMenuItemBuilder: (context, child) {
            return Padding(
              padding: const EdgeInsets.only(left: 4, right: 4),
              child: Container(padding:const EdgeInsets.only(left: 12) ,alignment: Alignment.centerLeft,
                  decoration: BoxDecoration(color: AppColorConstants.colorLimeBlue, borderRadius: BorderRadius.circular(5)),
                  height: menuHeight??32,
                  child: AppText( isSelectableText: false,
                    selectedValue,
                    style:  TextStyle(fontSize:fontSize?? 14,fontWeight: getBoldFontWeight(),fontFamily: AppAssetsConstants.notoSans),
                  )),
            );
          },
          height: menuHeight ?? 35,

        ),
      ),
    );
  }
}

