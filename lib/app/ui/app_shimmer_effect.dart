import 'package:flutter/material.dart';
import 'package:quantumlink_node/app/constants/app_constant.dart';
import 'package:shimmer/shimmer.dart';
import 'package:sizer/sizer.dart';

class AppShimmerEffectView extends StatelessWidget {
  final double? height;
  final double? width;
  final double? borderRadius;

  const AppShimmerEffectView({super.key, this.height, this.width, this.borderRadius});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: AppColorConstants.colorWhite200,
      highlightColor: AppColorConstants.colorWhite100,
      child: Container(
        height: height ?? 30.sp,
        width: width ?? 50.sp,
        decoration: BoxDecoration(
          color: AppColorConstants.colorWhite200,
          borderRadius: BorderRadius.circular(borderRadius ?? 4.sp),
        ),
      ),
    );
  }
}
