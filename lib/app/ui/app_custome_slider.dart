import 'package:quantumlink_node/app_import.dart';

class CustomRangeSlider extends StatefulWidget {
  final double minValue;
  final double maxValue;
  final double startValue;
  final double endValue;
  final ValueChanged<double> onStartChanged;
  final ValueChanged<double> onEndChanged;

  const CustomRangeSlider({super.key,
    required this.minValue,
    required this.maxValue,
    required this.startValue,
    required this.endValue,
    required this.onStartChanged,
    required this.onEndChanged,
  });

  @override
  State<CustomRangeSlider>  createState() => _CustomRangeSliderState();
}

class _CustomRangeSliderState extends State<CustomRangeSlider> {
  late double _startValue;
  late double _endValue;


  @override
  void initState() {
    super.initState();
    _startValue = widget.startValue;
    _endValue = widget.endValue;
  }

  @override
  Widget build(BuildContext context) {
    return SliderTheme(
      data: SliderTheme.of(context).copyWith(valueIndicatorTextStyle: TextStyle(fontSize: 10,color: AppColorConstants.colorWhite),
          showValueIndicator: ShowValueIndicator.always,
          activeTrackColor: AppColorConstants.colorChartBackGround1.withOpacity(0.3),
          trackHeight: 20,
          tickMarkShape: SliderTickMarkShape.noTickMark,
          overlayShape: const RoundSliderOverlayShape(overlayRadius: 5),
          rangeThumbShape: CircleThumbShape()),
      child: RangeSlider(
        values: RangeValues(_startValue, _endValue),
        min: widget.minValue,
        max: widget.maxValue,
        onChanged: (RangeValues values) {
          setState(() {
            _startValue = values.start;
            _endValue = values.end;
          });
          widget.onStartChanged(_startValue);
          widget.onEndChanged(_endValue);
        },
        labels: RangeLabels(
          '${_startValue.toInt()}',
          '${_endValue.toInt()}',
        ),

        inactiveColor: Colors.grey.withOpacity(0.3),
      ),
    );
  }
}

class CircleThumbShape extends RangeSliderThumbShape {
  @override
  Size getPreferredSize(bool isEnabled, bool isDiscrete) {
     return const Size(10, 10);
  }

  @override
  void paint(
      PaintingContext context,
      Offset center, {
        required Animation<double> activationAnimation,
        required Animation<double> enableAnimation,
        required SliderThemeData sliderTheme,
        bool? isDiscrete,
        bool? isEnabled,
        bool? isOnTop,
        TextDirection? textDirection,
        Thumb? thumb,
        bool? isPressed,
      }) {
    final Canvas canvas = context.canvas;
    canvas.drawShadow(
        Path()
          ..addRRect(RRect.fromRectAndRadius(
            Rect.fromCenter(center: center, width: 14, height: 32),
            const Radius.circular(4),
          )),
        Colors.black,
        5,
        false);

    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromCenter(center: center, width: 14.5, height: 32.5),
        const Radius.circular(4),
      ),
      Paint()..color = const Color(0xFF828282),
    );
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromCenter(center: center, width: 14, height: 32),
        const Radius.circular(4),
      ),
      Paint()..color = const Color(0xFFf5f5f5),
    );
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromCenter(center: center, width: 2, height: 11),
        const Radius.circular(4),
      ),
      Paint()..color = const Color(0xFF757575),
    );


  }
}

class CustomThumbShape extends RoundSliderThumbShape {
  @override
  void paint(
      PaintingContext context,
      Offset center, {
        required Animation<double> activationAnimation,
        required Animation<double> enableAnimation,
        required bool isDiscrete,
        required TextPainter labelPainter,
        required RenderBox parentBox,
        required SliderThemeData sliderTheme,
        required TextDirection textDirection,
        required double value,
        required double textScaleFactor,
        required Size sizeWithOverflow,
      }) {
    final Canvas canvas = context.canvas;

    // Draw main thumb
    final Paint paint = Paint()
      ..color = sliderTheme.thumbColor!
      ..style = PaintingStyle.fill;
    final Paint paint2 = Paint()
      ..color =AppColorConstants.colorWhite
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, 9, paint);
    canvas.drawCircle(center, 5, paint2);
  }
}