// ignore_for_file: deprecated_member_use

import 'package:quantumlink_node/app_import.dart';

class DataTableHelper {
  BoxDecoration tableBorderDeco() {
    return BoxDecoration(
      color: AppColorConstants.colorWhite,
      border: Border(
          bottom: BorderSide(color: AppColorConstants.colorH2, width: 1),
          left: BorderSide(color: AppColorConstants.colorH2, width: 1),
          right: BorderSide(color: AppColorConstants.colorH2, width: 1)),
    );
  }

  TextStyle headingTextStyle() {
    return TextStyle(
        color: AppColorConstants.colorWhite,
        fontWeight: FontWeight.w600,
        fontFamily: AppAssetsConstants.openSans,
        fontSize: 16);
  }

   TextStyle get dataRowTextStyle => TextStyle(
      fontWeight: getMediumBoldFontWeight(),
      fontFamily: AppAssetsConstants.openSans,
      color: AppColorConstants.colorBlack,
      fontSize: getSize(14),
    );


  TableBorder tableBorder() {
    return TableBorder(
      bottom: BorderSide(color: AppColorConstants.colorTableBroader),
      top: BorderSide(color: AppColorConstants.colorTableHeader),
    );
  }

  MaterialStateProperty<Color?>? headingRowColor() {
    return MaterialStateProperty.all(AppColorConstants.colorPrimary);
  }

  Widget getEmptyTableContent(BuildContext context) {
    return Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: AppColorConstants.colorWhite,
            border: Border(
                right: BorderSide(width: 1, color: Colors.grey[300]!),
                bottom: BorderSide(width: 1, color: Colors.grey[300]!),
                left: BorderSide(width: 1, color: Colors.grey[300]!))),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const AppImageAsset(
              image: AppAssetsConstants.emptyLogo,
              height: 50,
            ),
            Text(
              S.of(context).tableNoData,
              style: TextStyle(color: AppColorConstants.colorH2),
            ),
          ],
        ));
  }
  int getCurrentPageDataLength(List<dynamic> itemDataList, int currentPageIndex,
      {int perPageLimit =10}) {
    if(itemDataList.isNotEmpty) {
      int startIndex = currentPageIndex * perPageLimit ;
      int endIndex = (currentPageIndex + 1) * perPageLimit;
      if (endIndex > itemDataList.length) {
        endIndex = itemDataList.length;
      }
      return itemDataList.sublist(startIndex, endIndex).length;
    }
    return 0;
  }

  selectTableTypeButtonView({required bool isTableType, required VoidCallback onPressed}) {
    if(AppConfig.shared.isOpenFromBLE){
      return const SizedBox(height: 20,);
    }
    return Container(
      width: 50,
      padding: const EdgeInsets.only(left: 10, right: 8),
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColorConstants.colorAppbar,
          shape: const CircleBorder(),
          padding: const EdgeInsets.all(5),
        ),
        child: AppImageAsset(
          image: isTableType ? AppAssetsConstants.listTableIcon : AppAssetsConstants.tableIcon,
          width: isTableType ? 20 : 19,
          color: AppColorConstants.colorWhite,
        ),
      ),
    );
  }
}
