import 'dart:math';

import 'package:intl/intl.dart';
import 'package:latlong2/latlong.dart';

import '../../app_import.dart';

// --------------------------------- ISO6709 to LatLng -------------------------------------//

LatLng parseLatLng(String str) {
  try{
    String cleanedStr = str.replaceAll(RegExp(r'\s+'), ''); //Remove space
    return parseIso6709(cleanedStr.trim());
  }catch(ex){
    throw const FormatException("Invalid ISO 6709 format");
  }
}

// Function to parse ISO 6709 format
LatLng parseIso6709(String iso6709String) {
  debugLogs("parseLatLng -> "+iso6709String);
  // Regular expression to match ISO 6709 format
  final regex = RegExp(r'([+-]?\d+(\.\d+)?),?([+-]\d+(\.\d+)?)(/?)');
  final match = regex.firstMatch(iso6709String);

  if (match != null) {
    // Extract latitude and longitude from the matched groups
    String latStr = match.group(1)!;
    String lonStr = match.group(3)!;
    final latitude = parseCoordinate(latStr, isLatitude: true);
    final longitude = parseCoordinate(lonStr, isLatitude: false);

    LatLng latlng = LatLng(latitude, longitude);
    debugPrint("parseLatLng ISO 6709: $latlng");
    return latlng;
  } else {
    throw FormatException("Invalid ISO 6709 format");
  }
}

double parseCoordinate(String coordinate, {required bool isLatitude}) {
  // Check for decimal degrees format
  if (coordinate.contains('.') && (coordinate.length <= (isLatitude ? 3 : 4))) {
    return double.parse(coordinate);
  }
  if (isLatitude) {
    coordinate = formatWithSign(double.parse(coordinate), '00000.0####');
  } else {
    String longitude = coordinate.replaceAll('-', '').replaceAll('+', '').split('.')[0];
    coordinate = formatWithSign(
        double.parse(coordinate), longitude.length > 5 ? '0000000.0####' : '000000.0####');
  }
  // Handle DDMMSS.S or DDDMMSS.S formats
  final degreesLength = isLatitude
      ? 3
      : 4; // 2 digits for latitude degrees, 3 for longitude and 1 for sign
  final degrees = double.parse(coordinate.substring(0, degreesLength));
  final minutes =
      double.parse(coordinate.substring(degreesLength, degreesLength + 2));
  final seconds = double.parse(coordinate.substring(degreesLength + 2));

  if (degrees < 0) {
    return degrees * 1.0 - (minutes / 60.0) - (seconds / 3600.0);
  } else
    // Convert to decimal degrees
    return degrees * 1.0 + (minutes / 60.0) + (seconds / 3600.0);
}

String formatWithSign(double number, String pattern) {
  // Use NumberFormat to format the number with required decimal places
  final formattedNumber = NumberFormat(pattern).format(number);

  // Manually add "+" for positive numbers
  return number > 0 ? "+$formattedNumber" : formattedNumber;
}

// --------------------------------- LatLng to ISO6709 -------------------------------------//

String latLngToIso6709(double latitude, double longitude) {
  // print("ISO 6709 format: ----------- $latitude,$longitude");
  String latStr = formatCoordinate(latitude, isLatitude: true);
  String lonStr = formatCoordinate(longitude, isLatitude: false);
  // print("ISO 6709 format: 11 ----------- $latStr,$lonStr");
  return '$latStr,$lonStr';
}

String formatCoordinate(double coordinate, {required bool isLatitude}) {
  // Determine the degrees, minutes, and seconds
  int degrees = coordinate.truncate();
  double minutesDecimal = (coordinate.abs() - degrees.abs()) * 60;
  int minutes = minutesDecimal.truncate();
  double seconds = (minutesDecimal - minutes) * 60;

  // Format the output string
  String sign = (coordinate < 0) ? '-' : '+';
  String degreeString;

  if (isLatitude) {
    degreeString = degrees.abs().toString().padLeft(2, '0'); // Latitude has 2 digits
  } else {
    degreeString = degrees.abs().toString().padLeft(3, '0'); // Longitude has 3 digits
  }

  // Format minutes and seconds
  String minutesStr = minutes.toString().padLeft(2, '0');
  String secondsStr = seconds.toStringAsFixed(2).padLeft(5, '0');

  return '$sign$degreeString$minutesStr$secondsStr';
}