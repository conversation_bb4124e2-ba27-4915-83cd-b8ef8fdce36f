import 'package:quantumlink_node/app_import.dart';
import 'package:rxdart/rxdart.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/status.dart' as status;

class SocketHelper {
  // AMPLIFIERS
  late WebSocketChannel amplifiersChannel;

  final BehaviorSubject<dynamic> ampsListingStream = BehaviorSubject<dynamic>();
  String gatewayIdsKey = "gateway_ids";
  String deviceEuisKey = "device_euis";
  Stream<dynamic> get ampsListingStreamView => ampsListingStream.stream;

  connectAmplifiersChannelSocket() async {
    final dashboardUrl =
        Uri.parse(AppConfig.shared.wsUrl + RestConstants.instance.wsAmps);
    amplifiersChannel = WebSocketChannel.connect(dashboardUrl);
    await amplifiersChannel.ready;
  }

  Future<void> sendAmplifiersDeviceEUIList(
      List<AmplifierDeviceItem> listAmps, Function f) async {
    List deviceEUIList = listAmps.map((device) => device.deviceEui).toList();
    String formattedEuiList =
        deviceEUIList.map((e) => '"$e"').toList().toString();
    debugLogs("AMPLIFIERS Send EUI -> $formattedEuiList");
    amplifiersChannel.sink.add(formattedEuiList);
    amplifiersChannel.stream.listen((data) {
      debugLogs("Socket amplifiersChannel -> ");
      ampsListingStream.sink.add(data);
      f.call(data);
    });
  }

  void ampOnDispose() {
    debugLogs("Socket ampOnDispose -> ");
    amplifiersChannel.sink.close(status.normalClosure);
  }

  // DIAGNOSTICS
  late WebSocketChannel diagnosticsChannel;

  final BehaviorSubject<dynamic> diagnosticsStream = BehaviorSubject<dynamic>();

  Stream<dynamic> get diagnosticsStreamView => diagnosticsStream.stream;

  connectDiagnosticsSocket() async {
    final dashboardUrl = Uri.parse(
        AppConfig.shared.wsUrl + RestConstants.instance.wsDiagnostics);
    diagnosticsChannel = WebSocketChannel.connect(dashboardUrl);
    await diagnosticsChannel.ready;
  }

  setupConnectionAgain(){
    diagnosticsOnDispose();
    connectDiagnosticsSocket();
  }

  Future<void> sendDiagnosticsDeviceEUIList(
      List<String> formattedEuiList, Function f,{bool isDeviceEui=false}) async {
    setupConnectionAgain();
    Map<String, dynamic> data = {(isDeviceEui) ? deviceEuisKey : gatewayIdsKey: formattedEuiList};
    String jsonData = jsonEncode(data);
    debugLogs("VLGW Send EUI -> $jsonData");
    diagnosticsChannel.sink.add(jsonData);
    diagnosticsChannel.stream.listen((data) {
      diagnosticsStream.sink.add(data);
      f.call(data);
    });
  }

  Future<void> diagnosticsOnDispose() async {
    debugLogs("Socket diagnosticsOnDispose ->");
    await diagnosticsChannel.sink.close(status.normalClosure);
  }

// AMP DIAGNOSTICS
  late WebSocketChannel ampDiagnosticsChannel;

  final BehaviorSubject<dynamic> ampsDiagnosticsStream =
      BehaviorSubject<dynamic>();

  Stream<dynamic> get ampsDiagnosticsStreamView => ampsDiagnosticsStream.stream;

  connectAMPDiagnosticsSocket(String deviceEui, Function f) async {
    final dashboardUrl = Uri.parse(
        AppConfig.shared.wsUrl + RestConstants.instance.wsDiagnostics);
    ampDiagnosticsChannel = WebSocketChannel.connect(dashboardUrl);
    await ampDiagnosticsChannel.ready;
    ampDiagnosticsChannel.stream.listen((data) {
      ampsDiagnosticsStream.sink.add(data);
      f.call(data);
    });
    _sendDiagnosticsDeviceEUI(deviceEui);
  }

  Future<void> _sendDiagnosticsDeviceEUI(deviceEui) async {
    debugLogs("AMP Diagnostics Send EUI -> $deviceEui");
    ampDiagnosticsChannel.sink.add('["$deviceEui"]');
  }

  void ampDiagnosticsOnDispose() {
    debugLogs("Socket ampDiagnosticsOnDispose -> ");
    ampDiagnosticsChannel.sink.close(status.goingAway);
  }
}
