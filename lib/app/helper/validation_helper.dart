import 'package:flutter/material.dart';

class ValidationUtils {
  ValidationUtils._privateConstructor();

  static final ValidationUtils instance = ValidationUtils._privateConstructor();

  //     ======================= Regular Expressions =======================     //
  static const String phoneRegExp = r'^[+]?[(]?[0-9]{3}[)]?[-s.]?[0-9]{3}[-s.]?[0-9]{4,6}$';
  static const String emailRegExp = r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$';
  static const String nameRegExp = r'^[a-zA-Z ]+$';
  static const String passwordRegexp =
      r"^(?=^.{8,}$)(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{0,}$";
  static const String locationRegExp =r'[^0-9.,\-+ ]';
  static const String numberWithAlphaRegExp =r'^[a-zA-Z0-9]+$';
  static const String alphaNumericUnderscoreRegExp = r'^[A-Z0-9_]{1,10}$';


  //     ======================= Validation methods =======================     //
  bool validateEmptyController(TextEditingController textEditingController) {
    return textEditingController.text.trim().isEmpty;
  }

  bool lengthValidator(TextEditingController textEditingController, int length) {
    return textEditingController.text.trim().length < length;
  }

  bool regexValidator(TextEditingController textEditingController, String regex) {
    return RegExp(regex).hasMatch(textEditingController.text);
  }

  bool compareValidator(TextEditingController textEditingController, TextEditingController secondController) {
    return textEditingController.text != secondController.text;
  }
  bool locationValidator(String locationText) {
    return RegExp(locationRegExp).hasMatch(locationText);
  }

}
