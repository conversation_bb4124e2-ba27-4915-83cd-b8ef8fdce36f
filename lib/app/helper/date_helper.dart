import 'package:intl/intl.dart';
import 'package:quantumlink_node/app_import.dart';

String dateTime24HourFormat = 'dd MMM yyyy hh:mm a';
String paidOrReceiveDate = 'dd MMM yyyy';
String paidOrReceiveTime = 'hh:mm a';
String timeFormat12h = 'hh:mm a';
String lastSeenFormat="MM-dd-yyyy hh:mm:ss a";
String formatter='yyyy-MM-dd HH:mm:ss';
String formatDateTimeWithMilliseconds='yyyy-MM-dd HH:mm:ss.SSS';
String buildFormat="dd MMMM yyyy";
String lastUpdateDateFormat = "MM-dd-yyyy hh:mm:ss a";

String parseDate(DateTime date, {format}) {
  DateFormat dateFormat = DateFormat(format??"dd MMM yyyy");
  return dateFormat.format(date);
}
String rangeFormatDate(DateTime date) {
  return DateFormat.MMMd().format(date);
}

formatTimeOfDay(context, t){
  final localizations = MaterialLocalizations.of(context);
  final formattedTimeOfDay = localizations.formatTimeOfDay(t);
  return formattedTimeOfDay;
}
String formatIsoDate(String isoDate) {
  DateTime dateTime = DateTime.parse("$isoDate${isoDate.contains('Z') ? '' : 'Z'}");
  DateTime localDateTime = dateTime.toLocal();

  String formattedDate = DateFormat('MM/dd/yyyy hh:mm:ss a').format(localDateTime);

  return formattedDate;
}


String formatToLocal(String utcString) {
  DateTime utcDateTime = DateTime.parse(utcString);
  DateTime localDateTime = utcDateTime.toLocal();
  return DateFormat('yyyy-MM-dd HH:mm:ss').format(localDateTime);
}
 String getUtcTimeZone(double originalDateString ,{String ?dateFormat}) {
   DateTime dateTime = DateTime.fromMillisecondsSinceEpoch((originalDateString * 1000).toInt(), isUtc: true);
   String formattedTime = DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime);
  List<String> parts = formattedTime.split(' ');
  List<String> dateParts = parts[0].split('-');
  List<String> timeParts = parts[1].split(':');

  DateTime utcDateTime = DateTime.utc(
    int.parse(dateParts[0]),
    int.parse(dateParts[1]),
    int.parse(dateParts[2]),
    int.parse(timeParts[0]),
    int.parse(timeParts[1]),
    int.parse(timeParts[2]),
  );
 String formattedLocalDateTime =
      DateFormat(dateFormat??lastSeenFormat).format(utcDateTime.toLocal());
   if (originalDateString == 0) {
    return "";
  }
  return formattedLocalDateTime;
}
DateTime? getLastUpdateTime(String ?lastUpdateTime) { // From API
  if(lastUpdateTime == null) return null;
  int microseconds = (double.parse(lastUpdateTime) * 1000000).toInt();
  return DateTime.fromMicrosecondsSinceEpoch(microseconds);
}

String formatToMinutesAndSeconds(int value) {
  int minutes = value ~/ 60;
  int remainingSeconds = value % 60;
  String formattedTime = '${minutes.toString()}m ${remainingSeconds.toString().padLeft(2, '0')}s';
  return formattedTime;
}

String convertUpTimeFormat(int time) {
  int days = (time ~/ 86400);
  time = time % 86400;
  int hours = (time ~/ 3600);
  time = time % 3600;
  int minutes = (time ~/ 60);
  int seconds = time % 60;
  return '${days}d ${hours}h ${minutes}m ${seconds}s';
}

getDateTimeInUTC(){
  //DateTime utcDateTime = DateFormat('yyyy-MM-dd HH:mm:ss').parse(DateTime.now().toString());
  return DateTime.now();
}

getDateTimeStringForReference(DateTime d){
  //return parseDate(d, format: "ddMMMyyyyHH:mm:ss");
  return d.millisecondsSinceEpoch.toString();
}

class DateRangeFilterHelper {

  Future<DateTimeRange?> selectDateRange(BuildContext context, int startDateDifference) async {
    DateTimeRange initialDateRange = DateTimeRange(
      start:DateTime.now().subtract(const Duration(days: 7)),
      end: DateTime.now(),
    );

    DateTimeRange? picked = await showDateRangePicker(
        routeSettings: RouteSettings(arguments: initialDateRange),
        context: context,
        firstDate: DateTime.now().subtract(Duration(days: startDateDifference)),
        lastDate: DateTime.now(),
        initialEntryMode: DatePickerEntryMode.calendar,
        initialDateRange: initialDateRange,
        builder: (context, child) {
          return Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(padding:  const EdgeInsets.all(20),
                decoration: BoxDecoration(
                    color: Colors.transparent, borderRadius: BorderRadius.circular(10)),
                constraints: const BoxConstraints(maxWidth: 500, maxHeight: 500,minWidth: 300, minHeight: 100),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(20),
                  // Ensure the child is clipped to the border radius
                  child: child,
                ),
              )
            ],
          );
        });
    return picked;
  }

  Future<DateTime?> selectDate(BuildContext context) async {

    DateTime? picked = await showDatePicker(
        context: context,
        initialDate: DateTime.now().subtract(Duration(days: 7)),
        firstDate: DateTime.now().subtract(Duration(days: 90)),
        lastDate: DateTime.now(),
        initialEntryMode: DatePickerEntryMode.calendar,
        builder: (context, child) {
          return Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(padding:  const EdgeInsets.all(20),
                decoration: BoxDecoration(
                    color: Colors.transparent, borderRadius: BorderRadius.circular(10)),
                child: child,
              )
            ],
          );
        });
    return picked;
  }

  Future<TimeOfDay?> selectTime(BuildContext context) async {

    TimeOfDay? picked = await showTimePicker(
        initialTime: TimeOfDay.now(),
        context: context,
        initialEntryMode: TimePickerEntryMode.dial,
        builder: (context, child) {
          return Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(padding:  const EdgeInsets.all(20),
                decoration: BoxDecoration(
                    color: Colors.transparent, borderRadius: BorderRadius.circular(10)),
                child: child,
              )
            ],
          );
        });
    return picked;
  }

  void onDropdownChanged(String newValue, BuildContext context , Function f,{int startDateDifference = 90 , bool customDialogShownOnce=false}) async {

    DateTime now = DateTime.now();
    String selectedOption = newValue;
    DateTime startDate = DateTime(now.year, now.month, now.day);
    DateTime endDate = DateTime(now.year, now.month, now.day, 23, 59, 59);

    switch (newValue) {
      case 'Today':
        startDate = DateTime(now.year, now.month, now.day);
        endDate = DateTime.now();

        break;
      case 'Last Week (Sun - Sat)':
        final lastWeekStart = now.subtract(Duration(days: now.weekday + 7));
        final lastWeekEnd = lastWeekStart.add(const Duration(days: 6));

        startDate = DateTime(lastWeekStart.year, lastWeekStart.month, lastWeekStart.day);
        endDate = DateTime(lastWeekEnd.year, lastWeekEnd.month, lastWeekEnd.day, 23, 59, 59);
        break;
      case 'This Week (Sun - Today)':
        final startOfWeek = now.subtract(Duration(days: now.weekday));
        final endOfWeek =  DateTime.now();

          startDate = DateTime(startOfWeek.year, startOfWeek.month, startOfWeek.day);
          endDate = endOfWeek;

        break;
      case 'Custom':
        if(customDialogShownOnce){
          DateTimeRange? selectedDates = await selectDateRange(context,startDateDifference);
          if (selectedDates != null) {
            startDate = selectedDates.start;
            endDate = selectedDates.end.add(const Duration(hours: 23, minutes: 59, seconds: 59));
          }
        }
        break;
    }
    selectedOption = newValue;
    f.call(startDate, endDate ,selectedOption );
  }
  Widget commonDateRangeDropDown(
      {required String hintText,
        String? selectedValue,
        required List<dynamic> items,
        required ValueChanged onChanged,
        required DateTime startDate,
        required DateTime endDate}) {
    return CommonDropdownButton(iconColor:  AppColorConstants.colorH2,
      borderColor: AppColorConstants.colorWhite,
      selectedValue: selectedValue,
      buttonHeight: getSize(32),
      hintText: hintText,
      items: items,
      onChanged: onChanged,
      selectedItemBuilder: (context) {
        return items
            .map(
              (e) => Padding(
            padding: const EdgeInsets.symmetric(horizontal: 5),
            child: Align(
              alignment: Alignment.centerLeft,
              child: AppText(
                isSelectableText: false,
                e != S.of(context).today
                    ?"${rangeFormatDate(startDate)} - ${rangeFormatDate(endDate)}, ${endDate.year}"
                    : parseDate(endDate,format: buildFormat),
                maxLines: 1,
                style: TextStyle(
                  fontFamily: AppAssetsConstants.openSans,
                  fontWeight: getMediumBoldFontWeight(),
                  fontSize: getSize(14),
                ),
              ),
            ),
          ),
        )
            .toList();
      },
    );
  }

}

Widget getTimeDurationView({
  ApiStatus? refreshStatus,
  DateTime? updateTime,
  DateTime? onTapTime,
  Duration? difference,
String? differenceMessage,
String? waitingMessage,
  Alignment? alignment,
  Color? textColor
}) {
  return Align(alignment:alignment?? Alignment.centerRight,
    child: Padding(
      padding: const EdgeInsets.only(top: 6),
      child: AppText(
          refreshStatus == ApiStatus.loading && onTapTime != null
              ? waitingMessage ?? 'Please wait refreshing data...'
              : difference != null
                  ? '${differenceMessage ?? 'The refresh completed in '}${difference.inSeconds}.${difference.inMilliseconds ~/ 10}s'
                  : updateTime != null ? 'Last Updated: ${DateFormat(lastUpdateDateFormat).format(updateTime)}' : " ",
          style: TextStyle(
              color: refreshStatus == ApiStatus.loading || textColor == null || difference == null ? AppColorConstants.colorAppbar : textColor,
              fontWeight: FontWeight.w500,
              fontSize: getSize(13),
              fontFamily: AppAssetsConstants.openSans)),
    ),
  );
}

Widget getLastSeenView(DateTime ?lastUpdateTime, {Color? textColor, bool offline = false,Alignment? alignment}) {
  if (lastUpdateTime != null) {
    return Align(alignment: alignment??Alignment.centerRight,
      child: Padding(
        padding:  const EdgeInsets.only(right: 10,top:6),
        child: Text(
          "Last Updated: ${DateFormat(lastUpdateDateFormat).format(lastUpdateTime)}",
          style:  TextStyle(
              fontSize: 13,
              //color:textColor?? AppColorConstants.colorAppbar,
              color: offline? AppColorConstants.colorH1Grey : AppColorConstants.colorAppbar,
              fontFamily: AppAssetsConstants.openSans,fontWeight: FontWeight.w500),
        ),
      ),
    );
  }
  return Container();
}

/*
*
*
*
* From Header Request

1. AMP Device info screen
    - Placement and Identity
    - Amplifier Information
    - Transponder Information
    - Power Supply Information
2. Configuration Screen
    - DS Auto Alignment
    - US Auto Alignment
3. Diagnostic Screen
    - Test Point Config
    - Ingress Switch
4. Spectrum Chart


From Date Time Local

1. AMP Device Table screen
2. AMP Telemetry
3. AMP Alarm History
4. Down Stream AMP table
4. Dashboard
5. Site and Regions
6. VLGW
7. Provision Amplifier Table
8. Audit Logs
*
* */
