import 'package:quantumlink_node/app_import.dart';

class PaginationHelper {
  RestConstants restConstants = RestConstants.instance;
  int currentPage = 0;
  int perPageLimit = 0;
  int totalPages = 0;
  int pageOffset = 0;
  bool hasMoreData = false;

  void setPage(int page) {
    currentPage = page;
    if (currentPage < totalPages) {
      hasMoreData = true;
    }
  }

  bool get canGoToNextPage => hasMoreData;

  bool get canGoToPreviousPage => currentPage > 0;

  void updatePagination(int totalItems, {bool hasMore = true,required int pageLimit}) {
    perPageLimit= pageLimit;
    totalPages = (totalItems / perPageLimit).ceil();
    hasMoreData = hasMore;
  }

  void initializePagination() {
    currentPage = 0;
    totalPages = 0;
    pageOffset =0;
    perPageLimit = restConstants.limitVal10;
    hasMoreData = false;
  }
}


