
import 'package:http/http.dart' as http;
import 'package:http/http.dart';
import 'package:quantumlink_node/app/cache/app_shared_preference.dart';
import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/controller/dashboard_controller.dart';
import 'dart:js' as js;

class RestConstants {
  RestConstants._privateConstructor();

  static final RestConstants instance = RestConstants._privateConstructor();
  final String ipAddressURL="https://api.ipify.org?format=json";
  final String amps = "amps/";
  final String fm = "fm/";
  final String deployments = "deployments";
  final String auditLog = "audit_logs/";
  final String wsAmps = "/ws/dashboard"; // for amps
  final String wsDiagnostics = "/ws/frames/stream"; // for vlgw
  final String getAmplifierIdentification = "identification";
  final String setTestPointCfg = "set_test_point_config";
  final String getTestPointCfg = "get_test_point_config";
  final String setIngressSwitchControl = "set_ingress_switch_control";
  final String getIngressSwitchControl = "get_ingress_switch_control";
  final String getIngressSwitchControlS = "get_ingress_switch_controls";
  final String spectrum = "spectrum";
  final String sites = "sites";
  final String timeoutKey = "timeout";
  final String vendorCode = "vendor_code";
  final String getCallTimeout = "10";
  final String postCallTimeout = "15";
  final String postCallTimeout45 = "45";
  final String manualAlignmentCallTimeout = "30";
  final String getSensorData = "sensor_data";
  final String offsetKey="offset";
  final String offset="0";
  final String orderByKey="order_by";
  final String retriesKey="retries";
  final String getCallRetriesVal="3";
  final String postCallRetriesVal="1";
  final String directionKey="direction";
  final String showAutoDiscoveredKey="show_auto_discovered";
  final String durationInSeconds="duration_in_seconds";
  final String durationInSecondsVal= "00%3A05%3A00";//"00:02:00";
  final String cfgKey="cfg";
  final String cmdKey="cmd";
  final String downKey="down";
  final String upKey="up";
  final String alignmentKey="alignment";
  final String appKeyName = "app_key";
  final String deviceEuiKey = "device_eui";
  final String typeKey = "type";
  final String csvKey = "csv";
  final String csvFileKey = "csv_file";
  final String skipKey = "skip";
  final String telemetry = "telemetry";
  final String telemetryThresholds = "thresholds";
  final String dataKey = "data";
  final String limitKey = "limit";
  final String limitVal="50";
  final int limitVal10 = 10;
  final String refreshKey = "refresh";
  final String alarmsHistory = "alarms_history";
  final String vLGWs = "vlgws/";
  final String summary = "summary/";
  final String summaryWithoutSlash = "summary";
  final String exportAsKey = "export_as";
  final String exportCsvKey = "export_csv";
  final String fromDateKey = "from_date";
  final String toDateKey = "to_date";
  final String usAutoAlignment = "us_auto_alignment";
  final String dsAutoAlignment = "ds_auto_alignment";
  final String dsAutoAlignmentSpectrumData = "ds_auto_alignment_spectrum_data";
  final String dsManualAlignment = "get_ds_manual_alignment";
  final String setDsManualAlignment = "set_ds_manual_alignment";
  final String usManualAlignment = "get_us_manual_alignment";
  final String setUsManualAlignment = "set_us_manual_alignment";
  final String deviceSummary = "device_summary";
  final String transponderInfo = "transponder_info";
  final String alarms = "alarms";
  final String topology = "topology";
  final String device = "device";
  final String healthCheck = "healthcheck";
  final String version = "version";
  final String filesKey = "files";
  final String fileKey = "file";
  final String fileTypesKey = "file_types";
  final String applyKey = "apply";
  final String devices = "devices";
  final String devRebootTimeReq = "DevRebootTimeReq";
  final String fragKey = "frag";
  final String rebootTime = "0x00000000";
  final String fskStats = "fskstats";
  final String configKey = "config";
  final String versionKey = "version";
  final String provisionKey = "provision";
  final String uploadKey = "upload";
  final String overwriteKey = "overwrite";
  final String deleteKey = "delete";
  // final String wifiCheck = "wifi/check";
  // final String wifiDiscover = "wifi/discover";
  // final String wifiConfigure = "wifi/configure";
  final String firmwareId = "firmware_id";
  final String transponder = "transponder";
  final String firmwareImageInfo = "firmware_image_info";
  final String switchBankAndReboot = "switch_bank_and_reboot";
  final String authKey = "auth";
  final String tokenKey = "token";
  final String dsGainKey = "ds/gain";
  final String statsKey = "stats";
  final String nodeGwKey = "node-gw";
  final String setGainKey = "set-gain";
  final String gwEuiKey = "gw_eui";
  final String resetKey = "reset";
  final String rssiKey = "rssi";
  final String filterKey = "filter";
  final String gwId = "gwid";
  final String gwKey = "gw";
  final String gatewaysKey = "gateways";
  final String gatewayKey = "gateway";
  final String gatewayEuiKey = "gateway_eui";
  final String usersKey = "users";
  final String statusKey = "status";
  final String deviceEui = "dev_eui";
  final String getConfigFile = "get_config_file";
  final String setConfigFile = "set_config_file";
  final String searchQuery = "search_query";
  final String vendors = "vendors";
  final String code = "code";
  final String aoi = "AOI";
  final String keys = "keys";
  final String isEnabled = "is_enabled";
  final String status = "status";



  // NODE
  final String battery = "battery";
  final String idInfo = "idinfo";

}

class RestHelper {
  RestHelper._privateConstructor();

  static final RestHelper instance = RestHelper._privateConstructor();

  Map<String, String> headers = {'accept': 'application/json'};

  void showRequestLogs(path, Map<String, Object> requestData, String method) {
    debugLogs('•••••••••• Network debugLogs ••••••••••');
    debugLogs('Request $method --> $path');
    debugLogs('Request headers --> $requestData');
    debugLogs('••••••••••••••••••••••••••••••••••');
  }
  void showRequestAndResponsedebugLogs(http.Response? response, Map<String, Object> requestData) {
    debugLogs('•••••••••• Network debugLogs ••••••••••');
    debugLogs('Request code --> ${response!.statusCode} : ${response.request!.url}');
    debugLogs('Request headers --> $requestData');
    debugLogs('Response headers --> ${response.headers}');
    debugLogs('Response body --> ${response.body}');
    debugLogs('••••••••••••••••••••••••••••••••••');
  }

  Future<String?> getRestCall(
      {required String endpoint,
      String? addOns,
      bool addGuestToken = false,
      var customHeader,
      required BuildContext context,
      String? otherBaseUrl, bool isRetry = false}) async {
    String? responseData;
    try {
      String requestUrl = addOns != null
          ? '${AppConfig.shared.baseUrl}/$endpoint$addOns'
          : '${AppConfig.shared.baseUrl}/$endpoint';

      if(otherBaseUrl != null){
        requestUrl = addOns != null
            ? '$otherBaseUrl/$endpoint$addOns'
            : '$otherBaseUrl/$endpoint';
      }

      Uri? requestedUri = Uri.tryParse(requestUrl);
      await addAuthorizationToken();
      showRequestLogs(requestUrl, headers, 'GET');
      http.Response response = await http.get(requestedUri!, 
        headers: headers, 
        );

      showRequestAndResponsedebugLogs(response, headers);
      switch (response.statusCode) {
        case 200:
        case 201:
           responseData = response.body;
           break;
        case 400:
        case 422:
        case 504:
          responseData = response.body;
          break;
        case 401:
          String? responseData = response.body;
         return await handleTokenExpired(
            isRetry: isRetry,
            responseData: responseData,
            apiCallFunction: () => getRestCall(
              endpoint: endpoint,
              addOns: addOns,
              context: context,
              otherBaseUrl: otherBaseUrl,
              isRetry: true,
            ),
          );
        case 404:
        case 500:
        case 502:
          debugLogs('${response.statusCode}');
          responseData = response.body;
          break;
        case 409:
          if (context.mounted) jsonDecode(response.body)['error'].toString().showError(context);
          break;
        default:
          debugLogs('${response.statusCode} : ${response.body}');
          break;
      }
    } on PlatformException catch (e) {
      debugLogs('PlatformException in getRestCall --> ${e.message}');
    } on Exception catch (e) {
      debugLogs('Exception error: ${e.toString()}');
      // if (context.mounted) if(!isHealthCheck) e.toString().split(',')[0].showError(context);
    }
    if(endpoint != RestConstants.instance.healthCheck){
      apiHealthCheck(context, responseData);
    }
    return responseData;
  }

  Future<http.Response?> getRestCallWithResponse(
      {required String endpoint,
      String? addOns,
      bool addGuestToken = false,
      bool? isOtherBaseUrl,
      var customHeader,
      required BuildContext context,
      bool isOnlyForNode = false,bool isRetry = false}) async { // FOR RESPONSE HEADER
    http.Response? responseData;
    try {
      String requestUrl = addOns != null
          ? '${AppConfig.shared.baseUrl}/$endpoint$addOns'
          : '${AppConfig.shared.baseUrl}/$endpoint';

      if (isOnlyForNode) {
        requestUrl = addOns != null
            ? '${AppConfig.shared.nodeMgrUrl}/$endpoint$addOns'
            : '${AppConfig.shared.nodeMgrUrl}/$endpoint';
      }

      if (isOtherBaseUrl != null && isOtherBaseUrl) {
        requestUrl = endpoint;
        if (customHeader != null) {
          headers = customHeader;
        }
      }
      Uri? requestedUri = Uri.tryParse(requestUrl);
      await addAuthorizationToken();

      showRequestLogs(requestUrl, headers, 'GET');
      http.Response response = await http.get(
        requestedUri!,
        headers: headers,
      );

      showRequestAndResponsedebugLogs(response, headers);
      switch (response.statusCode) {
        case 200:
        case 201:
        case 400:
          responseData = response;
          break;
        case 401:
          String? responseData = response.body;
          return  await handleTokenExpired(
            isRetry: isRetry,
            responseData: responseData,
            apiCallFunction: () => getRestCallWithResponse(
              endpoint: endpoint,
              addOns: addOns,
              context: context,
              isOtherBaseUrl: isOtherBaseUrl,
              isOnlyForNode: isOnlyForNode,
              isRetry: true,
            ),
          );
        case 422:
          responseData = response;
          break;
        case 404:
        case 500:
        case 502:
          debugLogs('${response.statusCode}');
          break;
        case 409:
          if (context.mounted) jsonDecode(response.body)['error'].toString().showError(context);
          break;
        default:
          debugLogs('${response.statusCode} : ${response.body}');
          break;
      }
    } on PlatformException catch (e) {
      debugLogs('PlatformException in getRestCall --> ${e.message}');
    } on Exception catch (e) {
      debugLogs('Exception error: ${e.toString()}');
      // if (context.mounted) if(!isHealthCheck) e.toString().split(',')[0].showError(context);
    }
    apiHealthCheck(context, responseData);
    return responseData;
  }

  Future<String?>? postRestCall(
      {required String? endpoint,
      required Map<String, dynamic>? body,
      required BuildContext context,
      String? addOns, String? otherBaseUrl,bool isRetry = false}) async {
    String? responseData;

    try {
      String requestUrl = addOns != null
          ? '${AppConfig.shared.baseUrl}/$endpoint$addOns'
          : '${AppConfig.shared.baseUrl}/$endpoint';

      if(otherBaseUrl != null){
        requestUrl = addOns != null
            ? '$otherBaseUrl/$endpoint$addOns'
            : '$otherBaseUrl/$endpoint';
      }
      Uri? requestedUri = Uri.tryParse(requestUrl);
      debugLogs('Body map --> $body');
      headers['Content-Type'] = 'application/json';
      headers.addAll(headers);
      await addAuthorizationToken();
      showRequestLogs(requestUrl, headers, 'POST');
      debugLogs('Body map --> $body');
      http.Response response = await http.post(
        requestedUri!,
        body: jsonEncode(body),
        headers: headers,
      );
      showRequestAndResponsedebugLogs(response, headers);
      switch (response.statusCode) {
        case 200:
        case 500:
        case 404:
        case 201:
        responseData = response.body;
        break;
        case 401:
          String? responseData = response.body;
          return await handleTokenExpired(
            isRetry: isRetry,
            responseData: responseData,
            apiCallFunction: () => postRestCall(
              endpoint: endpoint,
              addOns: addOns,
              context: context,
              body: body,
              otherBaseUrl: otherBaseUrl,
              isRetry: true,
            ),
          );
          break;
        case 400:
          responseData = response.body;
          break;
        case 409:
          if (context.mounted) jsonDecode(response.body)['error'].toString().showError(context);
          break;
        case 502:
          debugLogs('${response.statusCode}');
          break;
        case 403:
          debugLogs('${response.statusCode}');
          break;
        default:
          debugLogs('${response.statusCode} : ${response.body}');
          break;
      }
    } on PlatformException catch (e) {
      debugLogs('PlatformException in postRestCall --> ${e.message}');
    } finally {
      apiHealthCheck(context, responseData);
    }
    return responseData;
  }

  Future<http.Response?>? postRestCallWithResponse(
      {required String? endpoint,
        required Map<String, dynamic>? body,
        required BuildContext context,
        String? otherBaseUrl,
        String? addOns,bool isRetry = false}) async { // FOR RESPONSE HEADER
    http.Response? responseData;

    try {
      String requestUrl = addOns != null
          ? '${AppConfig.shared.baseUrl}/$endpoint$addOns'
          : '${AppConfig.shared.baseUrl}/$endpoint';

      if (otherBaseUrl != null) {
        requestUrl = addOns != null ? '$otherBaseUrl/$endpoint$addOns' : '$otherBaseUrl/$endpoint';
      }

      Uri? requestedUri = Uri.tryParse(requestUrl);
      debugLogs('Body map --> $body');
      headers['Content-Type'] = 'application/json';
      headers.addAll(headers);
      await addAuthorizationToken();
      showRequestLogs(requestUrl, headers, 'POST');
      debugLogs('Body map --> $body');
      http.Response response = await http.post(
        requestedUri!,
        body: jsonEncode(body),
        headers: headers,
      );
      showRequestAndResponsedebugLogs(response, headers);
      switch (response.statusCode) {
        case 200:
        case 201:
        case 400:
        case 500:
        case 502:
        responseData = response;
        case 401:
            String? data = response.body;
            return await handleTokenExpired(
              isRetry: isRetry,
              responseData: data,
              apiCallFunction: () => postRestCallWithResponse(
                endpoint: endpoint,
                addOns: addOns,
                context: context,
                body: body,
                otherBaseUrl: otherBaseUrl,
                isRetry: true,
              ),
            );
        case 409:
          if (context.mounted) jsonDecode(response.body)['error'].toString().showError(context);
          break;
        case 404:
          debugLogs('${response.statusCode}');
          break;
        case 403:
          debugLogs('${response.statusCode}');
          break;
        default:
          debugLogs('${response.statusCode} : ${response.body}');
          break;
      }
    } on PlatformException catch (e) {
      debugLogs('PlatformException in postRestCall --> ${e.message}');
    } finally {
      apiHealthCheck(context, responseData);
    }
    return responseData;
  }

  Future<String?>? postRestCallWithoutBody(
      {required String? endpoint,
        required BuildContext context,
        String? addOns,bool isRetry = false}) async {
    String? responseData;

    try {
      String requestUrl = addOns != null
          ? '${AppConfig.shared.baseUrl}/$endpoint$addOns'
          : '${AppConfig.shared.baseUrl}/$endpoint';
      Uri? requestedUri = Uri.tryParse(requestUrl);

      http.Response response = await http.post(
        requestedUri!,
        headers: headers,
      );
      await addAuthorizationToken();
      showRequestAndResponsedebugLogs(response, headers);
      switch (response.statusCode) {
        case 200:
        case 500:
        case 201:
        case 400:
          responseData = response.body;
          break;
        case 401:
          String? responseData = response.body;
          return  await handleTokenExpired(
            isRetry: isRetry,
            responseData: responseData,
            apiCallFunction: () => postRestCallWithoutBody(
              endpoint: endpoint,
              addOns: addOns,
              context: context,
              isRetry: true,
            ),
          );
        case 409:
          if (context.mounted) jsonDecode(response.body)['error'].toString().showError(context);
          break;
        case 502:
        case 404:
          debugLogs('${response.statusCode}');
          break;
        case 403:
          debugLogs('${response.statusCode}');
          break;
        default:
          debugLogs('${response.statusCode} : ${response.body}');
          break;
      }
    } on PlatformException catch (e) {
      debugLogs('PlatformException in postRestCall --> ${e.message}');
    }
    return responseData;
  }

  Future<String?>? multiPartRestCall(
      {required String? endpoint,
      required String? keyName,
      required BuildContext context,
      required PlatformFile file,
      String? otherBaseUrl,
      bool isShowMessage = true,bool isRetry = false}) async {
    String? responseData;
    try {
      String requestUrl = '${AppConfig.shared.baseUrl}/$endpoint';
      if(otherBaseUrl != null){
        requestUrl = '$otherBaseUrl/$endpoint';
      }

      Uri? requestedUri = Uri.tryParse(requestUrl);
      MultipartRequest request = http.MultipartRequest('POST', requestedUri!);
      headers['Content-Type'] = 'multipart/form-data';
      request.headers.addAll(headers);
      await addAuthorizationToken();
      request.files.add(http.MultipartFile.fromBytes(keyName!, file.bytes!,
           filename: file.name));
      StreamedResponse responseStream = await request.send();
      final response = await http.Response.fromStream(responseStream);
      debugLogs("response $response");

      showRequestAndResponsedebugLogs(response, request.headers);
      debugLogs("request.headers ${request.headers}");

      switch (response.statusCode) {
        case 200:
        case 201:
          responseData = response.body;
          if (context.mounted && isShowMessage) S.of(context).uploadSuccess.showSuccess(context);
          break;
        case 500:
        case 502:
        case 400:
        case 401:
          String? responseData = response.body;
          return await handleTokenExpired(
            isRetry: isRetry,
            responseData: responseData,
            apiCallFunction: () => multiPartRestCall(
              endpoint: endpoint,
              keyName: keyName,
              context: context,
              file: file,
              otherBaseUrl: otherBaseUrl,
              isShowMessage: isShowMessage,
              isRetry: true,
            ),
          );
        case 404:
         if (context.mounted && isShowMessage) jsonDecode(response.body)['detail'].toString().showError(context);
          debugLogs('${response.statusCode}');
          break;
        default:
          debugLogs('${response.statusCode} : ${response.body}');
          break;
      }
    } on PlatformException catch (e) {
      debugLogs('PlatformException in multiPartRestCall --> ${e.message}');
    } catch (e) {
      if (e is http.ClientException) {
        debugLogs('ClientException in multiPartRestCall --> ${e.message}');
        responseData = e.message.toString();
        if (context.mounted)   e.message.toString().showError(context);
      } else {
        debugLogs('Error in multiPartRestCall --> $e');
      }
    }
    apiHealthCheck(context, responseData);
    return responseData;
  }

  Future<String?>? deleteRestCall(
      {required String? endpoint,
      Map<String, dynamic>? body,
      String? addOns,
      String? otherBaseUrl, bool isRetry = false}) async {
    String? responseData;

    try {
      String requestUrl = addOns != null
          ? '${AppConfig.shared.baseUrl}/$endpoint$addOns'
          : '${AppConfig.shared.baseUrl}/$endpoint';

      if(otherBaseUrl != null){
        requestUrl = addOns != null
            ? '$otherBaseUrl/$endpoint$addOns'
            : '$otherBaseUrl/$endpoint';
      }

      await addAuthorizationToken();
      Uri? requestedUri = Uri.tryParse(requestUrl);
      http.Response response = await http.delete(requestedUri!, headers: headers, body: body);

      showRequestAndResponsedebugLogs(response, headers);

      switch (response.statusCode) {
        case 200:
        case 422:
        case 201:
          responseData = response.body;
          break;
        case 500:
        case 502:
        case 400:
        case 401:
          String? responseData = response.body;
          return await handleTokenExpired(
            isRetry: isRetry,
            responseData: responseData,
            apiCallFunction: () => deleteRestCall(
              endpoint: endpoint,
              body: body,
              addOns: addOns,
              otherBaseUrl: otherBaseUrl,
              isRetry: true,
            ),
          );
        case 404:
          debugLogs('${response.statusCode}');
          break;

        default:
          debugLogs('${response.statusCode} : ${response.body}');
          break;
      }
    } on PlatformException catch (e) {
      debugLogs('PlatformException in deleteRestCall --> ${e.message}');
    }
    return responseData;
  }

  Future<http.Response>? putRestCallWithResponse(
      {required String? endpoint,
      required Map<String, dynamic>? body,
      required BuildContext context,
      String? addOns, bool isRetry = false}) async {

      String requestUrl = addOns != null
              ? '${AppConfig.shared.baseUrl}/$endpoint$addOns'
              : '${AppConfig.shared.baseUrl}/$endpoint';
      Uri? requestedUri = Uri.tryParse(requestUrl);

      headers['Content-Type'] = 'application/json';
      headers.addAll(headers);
      await addAuthorizationToken();
      http.Response response = await http.put(
        requestedUri!,
        headers:  headers,
        body:  jsonEncode(body),
      );

      showRequestAndResponsedebugLogs(response, headers);
      switch (response.statusCode) {
        case 200:
        case 201:
        debugLogs('${response.statusCode} : ${response.body}');
        return response;
        case 500:
        case 502:
        return response;
        case 400:
        case 401:
          String? responseData = response.body;
          return  await handleTokenExpired(
            isRetry: isRetry,
            responseData: responseData,
            apiCallFunction: () => putRestCallWithResponse(
              endpoint: endpoint,
              body: body,
              addOns: addOns,
              context: context,
              isRetry: true,
            ),
          );
        case 404:
          debugLogs('${response.statusCode}');
          break;

        default:
          debugLogs('${response.statusCode} : ${response.body}');
          break;
      }
      debugLogs('${response.statusCode} : ${response.body}');
      return response;
  }

  Future<String?>? putRestCall(
      {required String? endpoint,
      required Map<String, dynamic>? body,required BuildContext context,
      String? addOns,bool isRetry= false}) async {
    String? responseData;

    try {
      String requestUrl = addOns != null
              ? '${AppConfig.shared.baseUrl}/$endpoint$addOns'
              : '${AppConfig.shared.baseUrl}/$endpoint';
      Uri? requestedUri = Uri.tryParse(requestUrl);

      headers['Content-Type'] = 'application/json';
      headers.addAll(headers);
      await addAuthorizationToken();

      http.Response response = await http.put(
        requestedUri!,
        headers:  headers,
        body:  jsonEncode(body),
      );

      showRequestAndResponsedebugLogs(response, headers);

      switch (response.statusCode) {
        case 200:
        case 201:
          responseData = response.body;
          break;
        case 500:
        case 502:
        case 400:
        case 401:
          String? responseData = response.body;
          return await handleTokenExpired(
            isRetry: isRetry,
            responseData: responseData,
            apiCallFunction: () => putRestCall(
              endpoint: endpoint,
              body: body,
              addOns: addOns,
              context: context,
              isRetry: true,
            ),
          );
        case 404:
          responseData = response.body;
          debugLogs('${response.statusCode}');
          break;

        default:
          responseData = response.body;
          debugLogs('${response.statusCode} : ${response.body}');
          break;
      }
    } on PlatformException catch (e) {
      debugLogs('PlatformException in putRestCall --> ${e.message}');
    } on Exception catch (e) {
      debugLogs('Exception error: ${e.toString()}');
    } finally {
      apiHealthCheck(context, responseData);
    }
    return responseData;
  }

  apiHealthCheck(context, var responseData) async {
    debugLogs("apiHealthCheck ->");
    debugLogs(AppConfig.shared.apiHealthCheckStream.value);
    if(responseData==null){
      DashboardController dashboardController = Get.put(DashboardController());
      await dashboardController.checkHealth(context);
      debugLogs("apiHealthCheck after checkHealth->");
      debugLogs(AppConfig.shared.apiHealthCheckStream.value);
    }else{
      AppConfig.shared.apiHealthCheckStream.sink.add(true);
    }
  }

  Future<void> addAuthorizationToken() async {
    if(headers.containsKey("Authorization")){
      headers.remove("Authorization"); // to clear after sign out and get latest one
    }
      if (!AppConfig.shared.isQLCentral && AppConfig.shared.gw.isNotEmpty ) headers['X-QL-GW-EUI'] = AppConfig.shared.gw; // for ql node only and must not an empty
      String? accessToken = await getPrefStringValue(AppSharedPreference.accessToken) ?? '';
      if (accessToken.isNotEmpty && AppConfig.shared.aadAuth) headers['Authorization'] =  'Bearer $accessToken';
  }





  Future<dynamic> handleTokenExpired({
    required bool isRetry,
    required Future<dynamic>? Function() apiCallFunction,
    required String responseData,
  }) async {
    //"handleTokenExpired".showMessage();
    Map<String, dynamic> jsonData = jsonDecode(responseData);
    if (jsonData["error"] == "TokenExpired" && !isRetry) {
      if (!isRetry) {
        bool tokenRefreshed = await refreshToken();
        if (tokenRefreshed) {
          await Future.delayed(Duration(seconds: 2));
          //"recall".showMessage();
          return await apiCallFunction(); // Retry API call
        }
      }
    } else {
      // If token refresh fails, remove token and redirect to sign-in
      debugLogs("handleTokenExpired-->${jsonData["error"]}");
      AuthController authController = Get.put(AuthController());
      await authController.logOut(isTokenInvalid: true);
    }
    return null;
  }

  Future<bool> refreshToken() async {
    try {
      //"refreshToken".showMessage();
      AuthController authController = Get.put(AuthController());
      await authController.refreshToken();
      return true;
    } catch (e) {
      debugLogs("refreshToken-->${e}");
      await removeAT();
      goToSignInPage();
      return false;
    }
  }

  Future<void> removeAT() async {
    await removePrefValue(AppSharedPreference.accessToken);
    await removePrefValue(AppSharedPreference.refreshTokenId);
  }


}
