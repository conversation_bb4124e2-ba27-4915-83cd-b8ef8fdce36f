import "package:flutter/foundation.dart";
import "package:quantumlink_node/app/cache/app_shared_preference.dart";
import "package:quantumlink_node/app_import.dart";
import "package:universal_html/html.dart" as html;
import 'package:pub_semver/pub_semver.dart' as semver;
import 'dart:js' as js;

String protocol = "";
String? host = "";


readPathFromBrowser() async {
  // Get the current URL
  if(protocol.isEmpty){
    protocol = html.window.location.protocol;
    host = html.window.location.hostname;
    debugLogs("isQLCentral -> " + AppConfig.shared.isQLCentral.toString());

    if(!AppConfig.shared.isQLCentral){
      // For Node Header
      debugLogs("href -> " + html.window.location.href);
      Uri uri = Uri.parse(html.window.location.href); // or using Uri.base;

      var gw = uri.queryParameters['gw'] ?? "";
      debugLogs("gw -> " + gw);

      if(gw.isNotEmpty){
        await setPrefStringValue(AppSharedPreference.gw, gw);
      }

      var ip = uri.queryParameters['ip'] ?? "";
      debugLogs("ip -> " + ip);
      if(ip.isNotEmpty){
        await setPrefStringValue(AppSharedPreference.ip, ip);
      }

      var tFrmQ = uri.queryParameters['tkn'] ?? "";
      debugLogs("tFrmQ -> " + tFrmQ);
      if(tFrmQ.isNotEmpty){
        await setPrefStringValue(AppSharedPreference.accessToken, tFrmQ);
      }

      gw = await getPrefStringValue(AppSharedPreference.gw) ?? '';
      ip = await getPrefStringValue(AppSharedPreference.ip) ?? '';
      if(uri.queryParameters.containsKey("tkn")){
        AppConfig.shared.isOpenFromBLE=true;
      }

      AppConfig.shared.gw = gw;
      AppConfig.shared.ip = ip;
      //  For Node Header
    }
    debugLogs("gw -> ");
    debugLogs(AppConfig.shared.gw);
    if (kDebugMode) { // To test on secure protocol while debug
      protocol = "https:";
    }
  }


}


launchPath(p, n){
  if(p.toString().isEmpty){
    "Not found".showMessage();
    return;
  }
  html.window.open(p, n);
}

String getLastNChars(String input, int n) {
  if (input.length <= n) {
    return input; // Return the whole string if it's shorter than n
  }
  return input.substring(input.length - n);
}

getImageFromHex(product_id) {
  debugLogs("getImageFromHex -> $product_id");
  if(product_id!=null && product_id.toString().isNotEmpty){
    int decimalValue = int.parse(product_id);
    String hexValue = decimalValue.toRadixString(16);
    debugLogs("hexValue -> ");
    debugLogs("$hexValue");
    if(hexValue=="120"){
      return AppAssetsConstants.q18LineExtender;
    }
    else if(hexValue=="140"){
      return AppAssetsConstants.q18HGDExtender ;
    }
    else if(hexValue=="160"){
      return AppAssetsConstants.q18HGBT ;
    }
    else if(hexValue=="130"){
      return AppAssetsConstants.QB18_SYS_AMP_BLE ;
    }
    else if(hexValue=="150"){
      return AppAssetsConstants.QB18_SYS_AMP_MB ;
    }
    else{
      return "";
    }
  }
  return "";

}

isBetterAmpsFWVersion(versionString,{String ? verifyStableVersion}){
  debugLogs("isBetterAmpsFWVersion >");
  try {
    debugLogs("versionString -> " + versionString.toString());
    versionString = versionString.replaceAll(RegExp(r'[^0-9.]'), '');
    final semver.Version version = semver.Version.parse(versionString);
    debugLogs("version ->");
    debugLogs(version);
    final semver.Version stableVersion = semver.Version.parse(verifyStableVersion ?? AppStringConstants.betterFWVersion);
    if (version < stableVersion) { // if less than AppStringConstants.betterFWVersion then show only less tabs, hide switch bank and reboot, hide telemtry alsc pilots, spectrum single call
      return false;
    }
  } catch (e, s) {
    debugLogs('isBetterAmpsFWVersion -> ${e.toString()}');
  }
  return true;
}

