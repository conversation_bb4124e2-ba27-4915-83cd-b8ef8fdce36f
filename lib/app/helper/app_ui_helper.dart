import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:quantumlink_node/app/constants/app_constant.dart';
import 'package:intl/intl.dart';
import 'package:quantumlink_node/app_import.dart';

import '../../enum/api_status.dart';
import '../ui/app_text.dart';
import '../ui/custom_error_view.dart';
import 'date_helper.dart';


final value = NumberFormat("#,##,##0", "en_US");
writeAmount(num a){
  //return a.toString();
  return value.format(a);
}

getSize(double val){
  return val;
}

getVerySmallFontSize(){
  return 10.0;
}

getSmallFontSize(){
  return 12.0;
}

getMedFontSize(){
  return 14.0;
}
getMedLargeFontSize(){
  return 15.0;
}
getLargeFontSize(){
 return 16.0;
}

getVeryLargeFontSize(){
  return 18.0;
}

getLightFontWeight(){
  return FontWeight.w100;
}
getMediumFontWeight(){
  return FontWeight.w500;
}
  getMediumBoldFontWeight(){
  return FontWeight.w600;
}
getBoldFontWeight(){
  return FontWeight.w800;
}

debugLogs(r){
  debugPrint("$r");
}

showMessage(message, {isError, isDelayForBack}) async {
  //BotToast.showText(text: message, duration: const Duration(seconds: 5));
  if(isDelayForBack??false){
    await Future.delayed(const Duration(milliseconds: 500));
  }
  Get.snackbar(
      '',
      message,
      snackPosition: SnackPosition.TOP,
      backgroundColor: (isError!=null && isError) ? Colors.redAccent : AppColorConstants.colorPrimary,
      colorText: Colors.white
  );
}

getAppDivider({thickness}) {
  return Divider(color: AppColorConstants.colorDivider, thickness: thickness??getSize(5));
}
getHomeAppDivider() {
  return Container(color: AppColorConstants.colorPrimary, height: getSize(1.2));
}

BoxDecoration borderViewDecoration = BoxDecoration(
  color: AppColorConstants.colorWhite,
  border: Border.all(color: AppColorConstants.colorChart, width: 1.8),
  borderRadius: const BorderRadius.all(Radius.circular(8)),
);

Widget buildLastSeenView({ ApiStatus? apiStatus,DateTime? onTapTime, Duration? difference, bool isShow = true, DateTime? updateTime ,Color?textColor ,String? differenceMessage,bool isOffline = false}) {
  if (isShow) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: getTimeDurationView(
          differenceMessage: differenceMessage,
          refreshStatus: apiStatus,
          updateTime: updateTime,
          onTapTime: onTapTime,
          difference: difference,
          textColor: textColor),
    );
  } else {
    if (updateTime != null) {
      return Padding(
        padding: const EdgeInsets.only(bottom: 8.0),
        child: getLastSeenView(updateTime, textColor: textColor, offline: isOffline),
      );
    } else {
      return Container(height: 35);
    }
  }
}

errorMessageView({required String errorMessage,double ?padding}) {
  return Padding(
    padding: EdgeInsets.all(padding ?? 8.0),
    child: CustomPaint(
      painter: DottedBorderPainter(
        borderColor: AppColorConstants.colorRedLight.withOpacity(0.8),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 8.0),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.error_outline,
                color: AppColorConstants.colorRedLight, size: 15),
            const SizedBox(width: 5),
            Flexible(
              child: AppText(
                errorMessage,
                style: TextStyle(
                  color: AppColorConstants.colorDarkBlue,
                  fontSize: 12,
                  fontFamily: AppAssetsConstants.openSans,
                  fontWeight: getMediumFontWeight(),
                ),
              ),
            ),
          ],
        ),
      ),
    ),
  );
}

Widget errorRefreshView({
  required bool ifCondition,
  required bool elseCondition,
  required String errorMessage,
  required Widget refreshWidget,
  required ScreenLayoutType screenLayout,
}){
  return  Column(
    mainAxisAlignment: MainAxisAlignment.end,
    children: [
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          if (ifCondition)
            Flexible(flex : 5,child:  errorMessageView(
                errorMessage: errorMessage))
          else
            const Spacer(),
              if (elseCondition)
                Flexible(flex : 2,child: refreshWidget)
        ],
      ),
      if (screenLayout != ScreenLayoutType.desktop)
        refreshWidget,
    ],
  );
}

Color buildBackGroundColor() {
  return AppConfig.shared.isOpenFromBLE
      ? AppColorConstants.colorAppBackground
      : AppColorConstants.colorWhite;
}

Color buildTableAppbarColor() {
  return AppConfig.shared.isOpenFromBLE
      ? AppColorConstants.colorPrimaryLime
      : AppColorConstants.colorWhite;
}

Color buildTextFiledColor() {
  return AppConfig.shared.isOpenFromBLE
      ? AppColorConstants.colorWhite
      : AppColorConstants.colorWhite1;
}
ThemeData buildAppTheme({required bool isFormBLEApp}) {
  return ThemeData(
    textSelectionTheme: TextSelectionThemeData(
      selectionColor: AppColorConstants.colorSelectText,
      selectionHandleColor: AppColorConstants.colorSelectText,
    ),
    primaryColor: AppColorConstants.colorPrimary,
    fontFamily: AppAssetsConstants.openSans,
    scrollbarTheme: ScrollbarThemeData(
      thumbColor: isFormBLEApp ?  WidgetStatePropertyAll(AppColorConstants.colorTransparent) : null,
      interactive: true,
      thumbVisibility: WidgetStateProperty.all(true),
      thickness: WidgetStateProperty.all(8.0),
    ),
    dividerColor: AppColorConstants.dividerColorLight,
    scaffoldBackgroundColor: isFormBLEApp ?  AppColorConstants.colorAppBackground:  AppColorConstants.colorWhite,

    textTheme: TextTheme(
      bodySmall: const TextStyle(fontFamily: AppAssetsConstants.openSans),
      displayLarge: TextStyle(
        color: AppColorConstants.colorBlack,
        fontWeight: getBoldFontWeight(),
        fontSize: getLargeFontSize(),
      ),
      displayMedium: TextStyle(
        color: AppColorConstants.colorBlack,
        fontSize: getMedFontSize(),
        fontWeight: getMediumFontWeight(),
      ),
      displaySmall: TextStyle(
        fontSize: getSmallFontSize(),
        fontWeight: getLightFontWeight(),
        color: AppColorConstants.colorBlack,
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ButtonStyle(
        elevation: MaterialStateProperty.all(0),
      ),
    ),
  );
}
