import 'package:flutter/scheduler.dart';
import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/pages/software_update/software_update_page.dart';

class SoftwareUpdatePageHelper {
  late SoftwareUpdateState state;
  String versionNumber = "0.0.0";

  SoftwareUpdatePageHelper(this.state) {
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) async => await getVersionNumber());
  }

  Future<void> getVersionNumber() async {
    try {
      versionNumber = await rootBundle.loadString(AppAssetsConstants.versionPath);
    } catch (e) {
      debugPrint('catch exception in getVersionNumber ---> ${e.toString()}');
    }
    state.softwareUpdateController.update();
  }
}
