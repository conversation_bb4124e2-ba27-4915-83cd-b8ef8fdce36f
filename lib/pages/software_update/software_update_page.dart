import 'package:flutter/gestures.dart';
import 'package:quantumlink_node/app_import.dart';
import 'software_update_helper.dart';

class SoftwareUpdate extends StatefulWidget {
  const SoftwareUpdate({super.key});

  @override
  State<SoftwareUpdate> createState() => SoftwareUpdateState();
}

class SoftwareUpdateState extends State<SoftwareUpdate> {
  late ScreenLayoutType screenLayoutType;
  SoftwareUpdatePageHelper? softwareUpdatePageHelper;
  late SoftwareUpdateController softwareUpdateController;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    softwareUpdatePageHelper ?? (softwareUpdatePageHelper = SoftwareUpdatePageHelper(this));
    return GetBuilder<SoftwareUpdateController>(
      init: SoftwareUpdateController(),
      builder: (SoftwareUpdateController controller) {
        softwareUpdateController = controller;
        return ScreenLayoutTypeBuilder(builder: (context, screenType, constraints) {
          screenLayoutType = screenType;
          return Padding(
            padding: EdgeInsets.only(left: getSize(15), right: getSize(20)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                getPageTitleView(S.of(context).softwareUpdate),
                const SizedBox(height: 10),
                softwareUpdateView(),
              ],
            ),
          );
        });
      },
    );
  }

  Widget softwareUpdateView() {
    return Container(
      padding: const EdgeInsets.only(left: 10, right: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 15),
            child: RichText(
              text: TextSpan(
                style: const TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.normal),
                children: [
                  TextSpan(
                    text: "Please click ",
                  ),
                  TextSpan(
                    text: "here",
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      color: AppColorConstants.colorBlueLight,
                      decoration: TextDecoration.underline,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        launchPath(AppConfig.shared.softwareDownloadUrl, "");
                      },
                  ),
                  TextSpan(
                      text:
                          " to update the QuantumLink software."),
                ],
              ),
            ),
          ),
          RichText(text: TextSpan(
            style: const TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.normal),
            children: [
              TextSpan(
                text: "This will open a new page in your browser. After upgrade, close and reopen this page.",
              ),
            ]))
        ],
      ),
    );
  }
}
