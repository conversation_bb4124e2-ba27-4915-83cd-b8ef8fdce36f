import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/controller/audit_logs_controller.dart';
import 'package:quantumlink_node/pages/audit_logs/audit_log_devices/audit_logs_devices.dart';
import 'package:quantumlink_node/pages/audit_logs/audit_log_users/audit_logs_users.dart';

class AuditLogsPage extends StatefulWidget {
  const AuditLogsPage({super.key});

  @override
  State<StatefulWidget> createState() => AuditLogsPageState();
}

class AuditLogsPageState extends State<AuditLogsPage> with TickerProviderStateMixin {

  late AuditLogsController auditLogsController;
  late TabController tabController;
  List<DIGTabItem> auditLogsTabList = [
    DIGTabItem(title: "Devices", isCurrentOpen: true),
    DIGTabItem(title: "Users", isCurrentOpen: false)
  ];
  List<bool> isHovered = List.generate(2, (index) => false);

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    tabController = TabController(
        initialIndex: 0,
        length: 2,
        vsync: this,
        animationDuration: Duration.zero
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AuditLogsController>(
      init: AuditLogsController(),
      builder: (AuditLogsController controller) {
        auditLogsController = controller;
        return getBody();
      },
    );
  }

  Widget getBody() {
    return getAuditLogView();
  }

  Widget getAuditLogView() {
    return ScreenLayoutTypeBuilder(
      builder: (context, screenType, constraints) {
        return SizedBox(
          height: (double.infinity),
          child: ClipRRect(
            borderRadius: const BorderRadius.all(Radius.circular(5)),
            child: Container(
              color: AppColorConstants.colorWhite,
              width: double.infinity,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: NestedScrollView(
                        headerSliverBuilder: (context, innerBoxIsScrolled) => [
                              SliverToBoxAdapter(
                                  child: Padding(
                                      padding: const EdgeInsets.symmetric(horizontal: 10),
                                      child: getPageTitleView(S.of(context).auditLogs))),
                              SliverToBoxAdapter(child: SizedBox(height: getSize(10))),
                              SliverToBoxAdapter(child: getTabsOnAuditLogHeader()),
                            ],
                        body: getTabsContent()),
                  )
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget getTabsContent() {
    return TabBarView(
      physics: const NeverScrollableScrollPhysics(),
      controller: tabController,
      children: List.generate(auditLogsTabList.length, (index) {
        if (index == 0) {
          return  const AuditLogsDevicesPage();
        }
        return  AuditLogsUsersPage();
      }),
    );
  }

  Widget getTabsOnAuditLogHeader() {
    return TabBar(
      controller: tabController,
      dividerColor: AppColorConstants.colorWhite,
      labelPadding: EdgeInsets.zero,
      labelColor: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: getSize(10)),
      isScrollable: true,
      indicatorColor: AppColorConstants.colorWhite,
      tabAlignment: TabAlignment.start,
      // splashBorderRadius: BorderRadius.circular(12),
      onTap: (value) {
        for (var tabElement in auditLogsTabList) {
          tabElement.isCurrentOpen = false;
        }
        auditLogsTabList[value].isCurrentOpen = true;
        auditLogsController.update();
      },
      tabs: List.generate(auditLogsTabList.length, (index) {
        DIGTabItem digTabItem = auditLogsTabList[index];
        return MouseRegion(
          onEnter: (event) {
            isHovered[index] = true;
            auditLogsController.update();
          },
          onExit: (event) {
            isHovered[index] = false;
            auditLogsController.update();
          },
          child: Tab(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 5),
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: getSize(25)),
                height: getSize(42),
                alignment: Alignment.center,
                decoration: digTabItem.getDeco(isHovered[index]),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    AppText(
                      isSelectableText: false,
                      digTabItem.title,
                      style: TextStyle(
                        fontSize: getSize(16),
                        fontFamily: AppAssetsConstants.poppins,
                        fontWeight: FontWeight.w600,
                        color: isHovered[index] && !digTabItem.isCurrentOpen
                            ? AppColorConstants.colorBlackBlue
                            : digTabItem.isCurrentOpen
                                ? AppColorConstants.colorLightBlue
                                : AppColorConstants.colorH2,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      }),
    );
  }
}
