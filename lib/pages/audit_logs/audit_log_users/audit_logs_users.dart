import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/controller/audit_logs_controller.dart';
import 'package:quantumlink_node/pages/audit_logs/audit_log_users/audit_logs_users_page_helper.dart';
import 'package:quantumlink_node/pages/audit_logs/audit_log_users/auditlog_users_datasource.dart';



class AuditLogsUsersPage extends StatefulWidget {
  const AuditLogsUsersPage({super.key});

  @override
  State<AuditLogsUsersPage> createState() => AuditLogsUsersPageState();
}

class AuditLogsUsersPageState extends State<AuditLogsUsersPage> {
  AuditLogsUsersPageHelper? _helper;
  late AuditLogsController auditLogsController;
  late ScreenLayoutType screenLayoutType;

  @override
  Widget build(BuildContext context) {
    _helper ?? (_helper = AuditLogsUsersPageHelper(this));
    return GetBuilder<AuditLogsController>(
      init: AuditLogsController(),
      builder: (AuditLogsController controller) {
        auditLogsController = controller;
        return Stack(
          children: [
            getBodyView(),
          ],
        );
      },
    );
  }

  Widget getBodyView() {
    return ScreenLayoutTypeBuilder(
      builder: (context, screenType, constraints) {
        screenLayoutType = screenType;
        return getUserBoardView();
      },
    );
  }

  Widget getUserBoardView() {
    return Container(
      padding: EdgeInsets.only(
          bottom: getSize(10)),
      child: ClipRRect(
        borderRadius: const BorderRadius.all(Radius.circular(5)),
        child: Container(
          color: AppColorConstants.colorWhite,
          width: double.infinity,
          child: ListView(
            padding: const EdgeInsets.symmetric(horizontal: 15),
            children: [
              getPageAppBar(),
              auditLogsUserBoardView(),
              buildLastSeenView(),
            ],
          ),
        ),
      ),
    );
  }
  Widget buildLastSeenView() {
    if (_helper!.isShowText) {
      return getTimeDurationView(
        refreshStatus: _helper!.apiStatus,
        updateTime: _helper!.auditLogUpdateTime,
        onTapTime: _helper!.onTapTime,
        difference: _helper!.differenceTime,
      );
    } else {
      if (_helper!.auditLogUpdateTime != null) {
        return getLastSeenView(_helper!.auditLogUpdateTime);
      } else {
        return Container();
      }
    }
  }

  Widget getPageAppBar() {
    return Container(
      alignment: Alignment.centerLeft,
      decoration: BoxDecoration(
          border: Border.all(color: AppColorConstants.colorH2),
          color: AppColorConstants.colorWhite,
          borderRadius: BorderRadius.only(
              topRight: Radius.circular(getSize(7)),
              topLeft: Radius.circular(getSize(7)))),
      padding: EdgeInsets.only(left: getSize(18)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: getSize(10)),
              if (screenLayoutType == ScreenLayoutType.desktop) ...[
            Row(
              children: [
                const Spacer(flex: 2),
                Flexible(child:  _searchTextFieldView()),
                SizedBox(width: getSize(20)),
              ],
            )
              ],
          if (screenLayoutType != ScreenLayoutType.desktop) ...[
            SizedBox(height: getSize(10)),
            _searchTextFieldView(),
          ],
          SizedBox(height: getSize(10)),
        ],
      ),
    );
  }


  Widget _searchTextFieldView() {
    return Padding(
      padding: EdgeInsets.only(
          right: screenLayoutType == ScreenLayoutType.desktop ? 0 : 20),
      child: Container(
        decoration: BoxDecoration(
            color: AppColorConstants.colorWhite1,
            borderRadius: BorderRadius.circular(getSize(8))),
        child: AppTextFormField(
          onChanged: (value) {
            _helper!.paginationHelper.currentPage = 0;
            _helper!.auditPageController.goToFirstPage();
            _helper!.searchUserDataList = _helper!.listUserData
                .where((element) =>
                    element.userEmail.toLowerCase().contains(value.toLowerCase()) ||
                    element.operation.toLowerCase().contains(value.toLowerCase()) ||
                    element.application.toLowerCase().contains(value.toLowerCase()) ||
                    element.message.toLowerCase().contains(value.toLowerCase()) ||
                    element.ipAddress.toLowerCase().contains(value.toLowerCase()) ||
                    element.sourceService.toLowerCase().contains(value.toLowerCase()))
                .toList();
            _helper!.auditLogsUserDataSource = AuditLogsUsersDataSource(
              context,
              _helper!.searchUserDataList,
              (value) {},
            );
            _helper!.auditLogsUserDataSource.notifyListeners();
            auditLogsController.update();
          },
          focusedBorderColor: AppColorConstants.colorPrimary,
          controller: _helper!.searchController,
          enabledBorderColor: AppColorConstants.colorWhite1,
          hintText: S.of(context).quickSearch,
          maxLines: 1,
          textInputType: TextInputType.text,
          borderRadius: getSize(8),
          hintTextColor: AppColorConstants.colorDarkBlue,
          suffixIcon: const Padding(
            padding: EdgeInsets.all(12),
            child: AppImageAsset(image: AppAssetsConstants.searchIcon),
          ),
          hintFontSize: 17,
          fontFamily: AppAssetsConstants.openSans,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }

  Widget auditLogsUserBoardView() {
    if (_helper!.apiStatus == ApiStatus.loading) {
      return SizedBox(
          height: 400,
          width: 300,
          child: Align(
              alignment: Alignment.center,
              child: Container(
                  width: double.infinity,
                  decoration:
                      _helper!.tableHelper.tableBorderDeco(),
                  child: const AppLoader())));
    }
    int itemsPerPage = _helper!.tableHelper
        .getCurrentPageDataLength(_helper!.listUserData,
        _helper!.currentPageIndex,perPageLimit: AppStringConstants.auditLogsUserPerPageLimit);
    _helper!.recordsInPage =
    (_helper!.listUserData.length > AppStringConstants.auditLogsUserPerPageLimit)
        ? itemsPerPage
        : _helper!.listUserData.length;

    return Column(
      children: [
        Container(
            width: double.infinity,
            decoration: BoxDecoration(
              border: Border(
                  bottom:
                      BorderSide(color: AppColorConstants.colorH2, width: 0.8),
                  left:
                      BorderSide(color: AppColorConstants.colorH2, width: 0.8),
                  right:
                      BorderSide(color: AppColorConstants.colorH2, width: 0.8)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Table
                SizedBox(
                  height: (_helper!.listUserData.isNotEmpty)
                      ? (_helper!.recordsInPage *
                              _helper!.heightOfDataTableCell) +
                          (_helper!.recordsInPage * 0.1) +
                          100
                      : (_helper!.recordsInPage *
                              _helper!.heightOfDataTableCell) +
                          (_helper!.recordsInPage * 0.1) +
                          300,
                  child: getAuditLogsDataTableView(),
                ),
                AppPaginationWidget(
                  apiStatus: _helper!.apiStatus,
                  paginationHelper: _helper!.paginationHelper,
                  onLoadNext: () async {
                    await _helper!.loadNextLogs(context);
                  },
                  onLoadPrevious: () async {
                    await _helper!.loadPreviousLogs(context);
                    auditLogsController.update();
                  },
                  onGoToFirstPage: () {
                    _helper!.paginationHelper.setPage(0);
                    _helper!.auditPageController.goToFirstPage();
                  },
                    onGoToLastPage: () {
                      _helper!.auditPageController.goToLastPage();
                    },
                    itemsPerPage: AppStringConstants.auditLogsUserPerPageLimit,
                    onChanged: (value) {
                      AppStringConstants.auditLogsUserPerPageLimit = int.parse(value);
                      if (_helper!.apiStatus != ApiStatus.loading) {
                        _helper!.paginationHelper.setPage(0);
                        _helper!.auditPageController.goToFirstPage();
                        _helper!.auditLogsUserPageOffset = 0;
                        _helper!.getAuditLogsUsersData();
                        ();
                        auditLogsController.update();
                      }
                    }),
                const SizedBox(height: 10,),
              ],
            ))
      ],
    );
  }

  Widget getAuditLogsDataTableView() {
    return PaginatedDataTable2(
      columnSpacing: 0,
      rowsPerPage: AppStringConstants.auditLogsUserPerPageLimit,
      showCheckboxColumn: false,
      initialFirstRowIndex: _helper!.paginationHelper.currentPage *
          AppStringConstants.auditLogsUserPerPageLimit,
      headingTextStyle: _helper!.tableHelper.headingTextStyle(),
      wrapInCard: false,
      border: _helper!.tableHelper.tableBorder(),
      renderEmptyRowsInTheEnd: false,
      // ignore: deprecated_member_use
      headingRowColor: _helper!.tableHelper.headingRowColor(),
      columns: [
        DataColumn2(
          fixedWidth: 180,
          label: AppText(S.of(context).timestamp),
        ),
        DataColumn2(
          fixedWidth: 200,
          label: Center(child: AppText(S.of(context).email)),
        ),
        DataColumn2(
          fixedWidth: 180,
          label: Center(child: AppText(S.of(context).operation)),
        ),
        DataColumn2(
          fixedWidth: 180,
          label: Center(child: AppText(S.of(context).message)),
        ),
        DataColumn2(
          fixedWidth: 150,
          label: Center(child: AppText(S.of(context).source_service)),
        ),
        DataColumn2(
          fixedWidth: 120,
          label: Center(child: AppText(S.of(context).application)),
        ),
        DataColumn2(
          fixedWidth: 180,
          label: Center(child: AppText(S.of(context).ipAddress)),
        ),
        DataColumn(label: AppText(""))

      ],
      controller: _helper!.auditPageController,
      source: _helper!.auditLogsUserDataSource,
      minWidth: 1300,
      dataRowHeight: 51,
      hidePaginator: true,
      empty: _helper!.tableHelper.getEmptyTableContent(context),
    );
  }
}
