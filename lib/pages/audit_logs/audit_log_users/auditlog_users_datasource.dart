// ignore_for_file: deprecated_member_use

import 'package:quantumlink_node/app_import.dart';

import '../../../serialized/auditlogs/auditlog.dart';

class AuditLogsUsersDataSource extends DataTableSource {
  final int _selectedCount = 0;

  AuditLogsUsersDataSource.empty(this.context, this.userList, this.onTap);

  AuditLogsUsersDataSource(this.context, this.userList, this.onTap,
      [sortedByEUI = false,
        this.hasRowTaps = false,
        this.hasRowHeightOverrides = false,
        this.hasZebraStripes = false]) {
    if (sortedByEUI) {
      sort((d) => d.timestamp, true);
    }
  }

  final BuildContext context;
   List<AuditLogUserData> userList;
  final Function(AuditLogUserData) onTap;
  bool hasRowTaps = false;
  bool hasRowHeightOverrides = false;
  bool hasZebraStripes = false;
  DataTableHelper dataTableHelper = DataTableHelper();

  void sort<T>(Comparable<T> Function(AuditLogUserData d) getField, bool ascending) {
    userList.sort((a, b) {
      final aValue = getField(a);
      final bValue = getField(b);
      return ascending ? Comparable.compare(aValue, bValue) : Comparable.compare(bValue, aValue);
    });
    notifyListeners();
  }

  @override
  DataRow2 getRow(int index, [Color? color]) {
    assert(index >= 0);
    if (index >= userList.length) throw 'index > _desserts.length';
    final dessert = userList[index];
    String operation =dessert.operation ?? "";
    String userEmail =dessert.userEmail ?? "";
    String sourceService =dessert.sourceService ?? "";
    String application =dessert.application ?? "";
    String message =dessert.message ?? "";
    String ipAddress =dessert.ipAddress ?? "";
    String timestamp =(dessert.timestamp != null)? formatIsoDate(dessert.timestamp) : "" ;

    return DataRow2.byIndex(
      index: index,
      color:  (index.isEven
          ? MaterialStateProperty.all(AppColorConstants.colorWhite)
          : MaterialStateProperty.all(AppColorConstants.colorBackgroundDark)),
      onTap: () => onTap(dessert),
      cells: [
        DataCell(AppText(timestamp,
            style: dataTableHelper.dataRowTextStyle)),
        DataCell(AppText(userEmail,
            style: dataTableHelper.dataRowTextStyle)),
        DataCell(Center(
          child: AppText(operation,
              textAlign: TextAlign.center,
              style: dataTableHelper.dataRowTextStyle),
        )),
        DataCell(AppText(message,
            style: dataTableHelper.dataRowTextStyle)),
        DataCell(Center(
          child: AppText(sourceService,
              style: dataTableHelper.dataRowTextStyle),
        )),
        DataCell(Center(
          child: AppText(application,
              textAlign: TextAlign.center,
              style: dataTableHelper.dataRowTextStyle),
        )),
        DataCell(Center(
          child: AppText(ipAddress,
              style: dataTableHelper.dataRowTextStyle),
        )),
        const DataCell(SizedBox()),


      ],
    );

  }


  @override
  int get rowCount => userList.length;

  @override
  bool get isRowCountApproximate => false;

  @override
  int get selectedRowCount => _selectedCount;
}