import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/pages/audit_logs/audit_log_users/audit_logs_users.dart';
import 'package:quantumlink_node/serialized/auditlogs/auditlog.dart';
import 'auditlog_users_datasource.dart';

class AuditLogsUsersPageHelper {
  late AuditLogsUsersPageState state;

  ApiStatus apiStatus = ApiStatus.initial;
  late ScreenLayoutType screenLayoutType;
  final key = GlobalKey<PaginatedDataTableState>();
  final double heightOfDataTableCell = 48;
  int recordsInPage = 0; // For Set Fixed and Static Height Of Table
  int currentPageIndex = 0;
  bool isShowDiscovered = false;
  PaginatorController auditPageController = PaginatorController();
  TextEditingController searchController = TextEditingController();
  PaginationHelper paginationHelper = PaginationHelper();
  List<AuditLogUserData> searchUserDataList = [];
  List<AuditLogUserData> listUserData = [];
  late AuditLogsUsersDataSource auditLogsUserDataSource;
  DataTableHelper tableHelper = DataTableHelper();
  DateTime? onTapTime;
  Duration ? differenceTime;
  bool isShowText = true;
  Timer? refreshTimer;
  DateRangeFilterHelper dateHelper = DateRangeFilterHelper();
   DateTime ?auditLogUpdateTime ;
  AuditLogsUsersPageHelper(this.state) {
    apiStatus = ApiStatus.loading;
    Future.delayed(const Duration(milliseconds: 500)).then((value) async {
      getAuditLogsUsersData();
      getCurrantPageIndex();
    });
  }



  Future<void> loadPreviousLogs(BuildContext context) async {
    if (paginationHelper.canGoToPreviousPage) {
      paginationHelper.setPage(paginationHelper.currentPage - 1);
      auditPageController.goToPreviousPage();
    }
  }

  Future<void> loadNextLogs(BuildContext context) async {
    if (paginationHelper.canGoToNextPage) {
      paginationHelper.setPage(paginationHelper.currentPage + 1);
      if (paginationHelper.currentPage >= paginationHelper.totalPages) {
        auditLogsUserPageOffset = listUserData.length;
        await updateAuditLogsUsersData();
      } else {
        auditPageController.goToNextPage();
        state.auditLogsController.update();
      }
    }
  }

  updateAuditLogsUsersData() async {
    initializeTimer();
    apiStatus = ApiStatus.loading;
    state.auditLogsController.update();
    AuditLogUserResponse auditLogUserResponse = await state.auditLogsController.getAuditLogUserList(
        context: state.context,
        pageOffset: auditLogsUserPageOffset,perPageLimit: AppStringConstants.auditLogsUserPerPageLimit);
    List<AuditLogUserData> listAuditLogsUser = auditLogUserResponse.data ?? [];
    if (listAuditLogsUser.isNotEmpty) {
      listUserData.addAll(listAuditLogsUser);
      auditLogsUserDataSource = AuditLogsUsersDataSource(
        state.context,
        listUserData,
            (value) {},
      );
      bool hasMoreData = listAuditLogsUser.length == AppStringConstants.auditLogsUserPerPageLimit;
      paginationHelper.updatePagination(listUserData.length, hasMore: hasMoreData,pageLimit: AppStringConstants.auditLogsUserPerPageLimit);
      auditLogsUserDataSource.notifyListeners();
    } else {
      paginationHelper.setPage(paginationHelper.currentPage - 1);
      paginationHelper.updatePagination(listUserData.length, hasMore: false,pageLimit: AppStringConstants.auditLogsUserPerPageLimit);
      Future.delayed(const Duration(milliseconds: 100)).then((value) {
        auditPageController.goToLastPage();
        auditLogsUserDataSource.notifyListeners();
      });
    }
    getDifferenceTime();
    auditLogUpdateTime = DateTime.now();
    apiStatus = ApiStatus.success;
    state.auditLogsController.update();
  }
  int auditLogsUserPageOffset = 0;

  getAuditLogsUsersData() async {
    initializeTimer();
    apiStatus = ApiStatus.loading;
    state.auditLogsController.update();
    await state.auditLogsController
        .getAuditLogUserList(
            context: state.context,
            pageOffset: auditLogsUserPageOffset,
            perPageLimit: AppStringConstants.auditLogsUserPerPageLimit)
        .then(
      (value) {
        if (value != null) {
          AuditLogUserResponse auditLogUserResponse = value;
          listUserData = auditLogUserResponse.data ?? [];
          if (state.mounted) {
            auditLogsUserDataSource = AuditLogsUsersDataSource(
              state.context,
              listUserData,
              (value) {},
            );
            getDifferenceTime();
            auditLogUpdateTime = DateTime.now();
            apiStatus = ApiStatus.success;
            state.auditLogsController.update();
          }
          bool hasMoreData = listUserData.length == AppStringConstants.auditLogsUserPerPageLimit;
          paginationHelper.updatePagination(listUserData.length,
              hasMore: hasMoreData, pageLimit: AppStringConstants.auditLogsUserPerPageLimit);
        } else {
          auditLogsUserDataSource = AuditLogsUsersDataSource(
            state.context,
            listUserData,
            (value) {},
          );
          auditLogUpdateTime = DateTime.now();
          apiStatus = ApiStatus.failed;
          state.auditLogsController.update();
        }
      },
    );
  }

  void initializeTimer() {
    differenceTime = null;
    onTapTime = DateTime.now();
    isShowText = true;
    refreshTimer?.cancel();
  }
  getDifferenceTime() {
    differenceTime = DateTime.now().difference(onTapTime!);
    refreshTimer= Timer(const Duration(seconds: 2), () {
      isShowText = false;
      state.auditLogsController.update();
    });
  }

  //GET CURRANT PAGE INDEX
  getCurrantPageIndex() {
    paginationHelper.initializePagination();
    auditPageController.addListener(() {
      currentPageIndex =
          (auditPageController.currentRowIndex / AppStringConstants.auditLogsUserPerPageLimit).ceil();
      state.auditLogsController.update();
    });
  }
}
