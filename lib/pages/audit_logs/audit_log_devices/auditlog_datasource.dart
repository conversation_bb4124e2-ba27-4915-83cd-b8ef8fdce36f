// ignore_for_file: deprecated_member_use

import 'package:quantumlink_node/app_import.dart';

import '../../../serialized/auditlogs/auditlog.dart';

class AuditLogsDevicesDataSource extends DataTableSource {
  final int _selectedCount = 0;

  AuditLogsDevicesDataSource.empty(this.context, this.list, this.onTap);

  AuditLogsDevicesDataSource(this.context, this.list, this.onTap,
      [sortedByEUI = false,
        this.hasRowTaps = false,
        this.hasRowHeightOverrides = false,
        this.hasZebraStripes = false]) {
    if (sortedByEUI) {
      sort((d) => d.timestamp, true);
    }
  }

  final BuildContext context;
   List<AuditLogData> list;
  final Function(AuditLogData) onTap;
  bool hasRowTaps = false;
  bool hasRowHeightOverrides = false;
  bool hasZebraStripes = false;
  DataTableHelper dataTableHelper = DataTableHelper();

  void sort<T>(Comparable<T> Function(AuditLogData d) getField, bool ascending) {
    list.sort((a, b) {
      final aValue = getField(a);
      final bValue = getField(b);
      return ascending ? Comparable.compare(aValue, bValue) : Comparable.compare(bValue, aValue);
    });
    notifyListeners();
  }

  @override
  DataRow2 getRow(int index, [Color? color]) {
    assert(index >= 0);
    if (index >= list.length) throw 'index > _desserts.length';
    final dessert = list[index];

    String deviceEui = dessert.devEui ?? "";
    String deviceType =dessert.deviceType ?? "";
    String operation =dessert.operation ?? "";
    String userEmail =dessert.userEmail ?? "";
    String sourceService =dessert.sourceService ?? "";
    String application =dessert.application ?? "";
    String requestPath =dessert.requestPath ?? "";
    String requestMethod =dessert.requestMethod ?? "";
    String message =dessert.message ?? "";
    String timestamp =(dessert.timestamp != null)? formatIsoDate(dessert.timestamp) : "" ;

    return DataRow2.byIndex(
      index: index,
      color:  (index.isEven
          ? MaterialStateProperty.all(AppColorConstants.colorWhite)
          : MaterialStateProperty.all(AppColorConstants.colorBackgroundDark)),
      onTap: () => onTap(dessert),
      cells: [
        DataCell(AppText(timestamp,
            style: dataTableHelper.dataRowTextStyle)),
        DataCell(Center(
          child: AppText(deviceEui,
              style: dataTableHelper.dataRowTextStyle),
        )),
        DataCell(Center(
          child: AppText(deviceType,
              textAlign: TextAlign.center,
              style: dataTableHelper.dataRowTextStyle),
        )),
        DataCell(Center(
          child: AppText(operation,
              textAlign: TextAlign.center,
              style: dataTableHelper.dataRowTextStyle),
        )),
        DataCell(Center(
          child: AppText(userEmail,
              textAlign: TextAlign.center,
              style: dataTableHelper.dataRowTextStyle),
        )),
        DataCell(Center(
          child: AppText(sourceService,
              style: dataTableHelper.dataRowTextStyle),
        )),
        DataCell(Center(
          child: AppText(application,
              textAlign: TextAlign.center,
              style: dataTableHelper.dataRowTextStyle),
        )),
        DataCell(Center(
          child: AppText(requestPath,
              style: dataTableHelper.dataRowTextStyle),
        )),
        DataCell(Center(
          child: AppText(requestMethod,
              style: dataTableHelper.dataRowTextStyle),
        )),
        DataCell(AppText(message,
            style: dataTableHelper.dataRowTextStyle)),

      ],
    );

  }


  @override
  int get rowCount => list.length;

  @override
  bool get isRowCountApproximate => false;

  @override
  int get selectedRowCount => _selectedCount;
}