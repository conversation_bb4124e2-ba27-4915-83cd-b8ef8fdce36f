import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/pages/audit_logs/audit_log_devices/audit_logs_devices.dart';
import 'package:quantumlink_node/serialized/auditlogs/auditlog.dart';

import 'auditlog_datasource.dart';

class AuditLogsDevicesPageHelper {
  late AuditLogsDevicesPageState state;

  ApiStatus apiStatus = ApiStatus.initial;
  late ScreenLayoutType screenLayoutType;
  final key = GlobalKey<PaginatedDataTableState>();
  final double heightOfDataTableCell = 48;
  int recordsInPage = 0; // For Set Fixed and Static Height Of Table
  int currentPageIndex = 0;
  bool isShowDiscovered = false;
  PaginatorController auditPageController = PaginatorController();
  TextEditingController searchController = TextEditingController();
  PaginationHelper paginationHelper = PaginationHelper();
  List<AuditLogData> searchAuditLogsDataList = [];
  List<AuditLogData> listAuditLogsData = [];
  late AuditLogsDevicesDataSource auditLogsDataSource;
  DateTime startDate = DateTime.now(), endDate = DateTime.now();
  List listSelected = [];
  DataTableHelper tableHelper = DataTableHelper();
  String selectedOption = "";
  List dateRangeList = [];
  DateTime? onTapTime;
  Duration ? differenceTime;
  bool isShowText = true;
  Timer? refreshTimer;
  DateRangeFilterHelper dateHelper = DateRangeFilterHelper();
   DateTime ?auditLogUpdateTime ;
  AuditLogsDevicesPageHelper(this.state) {
    apiStatus = ApiStatus.loading;
    Future.delayed(const Duration(milliseconds: 500)).then((value) async {
      DateTime now = DateTime.now();
      startDate = DateTime(now.year, now.month, now.day);
      getAuditLogsData();
      getCurrantPageIndex();
    });
  }



  Future<void> loadPreviousLogs(BuildContext context) async {
    if (paginationHelper.canGoToPreviousPage) {
      //paginationHelper.pageOffset = (paginationHelper.pageOffset - paginationHelper.perPageLimit).clamp(0, paginationHelper.pageOffset);
      paginationHelper.setPage(paginationHelper.currentPage - 1);
      auditPageController.goToPreviousPage();
    }
  }

  Future<void> loadNextLogs(BuildContext context) async {
    if (paginationHelper.canGoToNextPage) {
      paginationHelper.setPage(paginationHelper.currentPage + 1);
      if (paginationHelper.currentPage >= paginationHelper.totalPages) {
        auditLogsPageOffset = listAuditLogsData.length;
        await updateAuditLogsData();
      } else {
        auditPageController.goToNextPage();
        state.auditLogsController.update();
      }
    }
  }

  updateAuditLogsData() async {
    initializeTimer();
    apiStatus = ApiStatus.loading;
    state.auditLogsController.update();
    AuditLogResponse auditLogResponse = await state.auditLogsController.getAuditLogDevicesList(
        context: state.context,
        pageOffset: auditLogsPageOffset,perPageLimit: AppStringConstants.auditLogsDevicePerPageLimit);
    List<AuditLogData> listAuditLogs = auditLogResponse.data ?? [];
    if (listAuditLogs.isNotEmpty) {
      listAuditLogsData.addAll(listAuditLogs);
      auditLogsDataSource = AuditLogsDevicesDataSource(
        state.context,
        listAuditLogsData,
        (value) {},
      );
      bool hasMoreData = listAuditLogs.length == AppStringConstants.auditLogsDevicePerPageLimit;
      paginationHelper.updatePagination(listAuditLogsData.length,
          hasMore: hasMoreData, pageLimit: AppStringConstants.auditLogsDevicePerPageLimit);
    } else {
      paginationHelper.setPage(paginationHelper.currentPage - 1);
      paginationHelper.updatePagination(listAuditLogsData.length,
          hasMore: false, pageLimit: AppStringConstants.auditLogsDevicePerPageLimit);
      Future.delayed(const Duration(milliseconds: 100)).then((value) {
        auditPageController.goToLastPage();
        auditLogsDataSource.notifyListeners();
      });
    }
    getDifferenceTime();
    auditLogUpdateTime = DateTime.now();
    apiStatus = ApiStatus.success;
    state.auditLogsController.update();
  }
  int auditLogsPageOffset = 0;

  getAuditLogsData() async {
    initializeTimer();
    apiStatus = ApiStatus.loading;
    state.auditLogsController.update();
    await state.auditLogsController
        .getAuditLogDevicesList(
            context: state.context,
            pageOffset: auditLogsPageOffset,
            perPageLimit: AppStringConstants.auditLogsDevicePerPageLimit)
        .then(
      (value) {
        if (value != null) {
          AuditLogResponse auditLogResponse = value;
          listAuditLogsData = auditLogResponse.data ?? [];
          if (state.mounted) {
            auditLogsDataSource = AuditLogsDevicesDataSource(
              state.context,
              listAuditLogsData,
              (value) {},
            );
            getDifferenceTime();
            auditLogUpdateTime = DateTime.now();
            apiStatus = ApiStatus.success;
            state.auditLogsController.update();
          }
          bool hasMoreData = listAuditLogsData.length == AppStringConstants.auditLogsDevicePerPageLimit;
          paginationHelper.updatePagination(listAuditLogsData.length,
              hasMore: hasMoreData, pageLimit: AppStringConstants.auditLogsDevicePerPageLimit);
        } else {
          auditLogsDataSource = AuditLogsDevicesDataSource(
            state.context,
            listAuditLogsData,
            (value) {},
          );
          auditLogUpdateTime = DateTime.now();
          apiStatus = ApiStatus.failed;
          state.auditLogsController.update();
        }
      },
    );
  }

  void initializeTimer() {
    differenceTime = null;
    onTapTime = DateTime.now();
    isShowText = true;
    refreshTimer?.cancel();
  }
  getDifferenceTime() {
    differenceTime = DateTime.now().difference(onTapTime!);
    refreshTimer= Timer(const Duration(seconds: 2), () {
      isShowText = false;
      state.auditLogsController.update();
    });
  }

  //GET CURRANT PAGE INDEX
  getCurrantPageIndex() {
    paginationHelper.initializePagination();
    auditPageController.addListener(() {
      currentPageIndex =
          (auditPageController.currentRowIndex / AppStringConstants.auditLogsDevicePerPageLimit).ceil();
      state.auditLogsController.update();
    });
  }
}
