import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/controller/audit_logs_controller.dart';
import 'package:quantumlink_node/pages/audit_logs/audit_log_devices/auditlog_datasource.dart';
import 'audit_logs_devices_page_helper.dart';

class AuditLogsDevicesPage extends StatefulWidget {
  const AuditLogsDevicesPage({super.key});

  @override
  State<AuditLogsDevicesPage> createState() => AuditLogsDevicesPageState();
}

class AuditLogsDevicesPageState extends State<AuditLogsDevicesPage> {
  AuditLogsDevicesPageHelper? auditLogsPageHelper;
  late AuditLogsController auditLogsController;
  late ScreenLayoutType screenLayoutType;

  @override
  Widget build(BuildContext context) {
    auditLogsPageHelper ?? (auditLogsPageHelper = AuditLogsDevicesPageHelper(this));
    if (auditLogsPageHelper!.dateRangeList.isEmpty) {
      auditLogsPageHelper!.dateRangeList = [
        S.of(context).today,
        S.of(context).thisWeek,
        S.of(context).lastWeek,
        S.of(context).custom
      ];
    }
    auditLogsPageHelper!.selectedOption = auditLogsPageHelper!.dateRangeList[0];
    return GetBuilder<AuditLogsController>(
      init: AuditLogsController(),
      builder: (AuditLogsController controller) {
        auditLogsController = controller;
        return Stack(
          children: [
            getBodyView(),
          ],
        );
      },
    );
  }

  Widget getBodyView() {
    return ScreenLayoutTypeBuilder(
      builder: (context, screenType, constraints) {
        screenLayoutType = screenType;
        return getAuditBoardView();
      },
    );
  }

  Widget getAuditBoardView() {
    return Container(
      padding: EdgeInsets.only(
          bottom: getSize(10)),
      child: ClipRRect(
        borderRadius: const BorderRadius.all(Radius.circular(5)),
        child: Container(
          color: AppColorConstants.colorWhite,
          width: double.infinity,
          child: ListView(
            padding: const EdgeInsets.symmetric(horizontal: 15),
            children: [
              getPageAppBar(),
              auditLogsBoardView(),
              buildLastSeenView(),
            ],
          ),
        ),
      ),
    );
  }
  Widget buildLastSeenView() {
    if (auditLogsPageHelper!.isShowText) {
      return getTimeDurationView(
        refreshStatus: auditLogsPageHelper!.apiStatus,
        updateTime: auditLogsPageHelper!.auditLogUpdateTime,
        onTapTime: auditLogsPageHelper!.onTapTime,
        difference: auditLogsPageHelper!.differenceTime,
      );
    } else {
      if (auditLogsPageHelper!.auditLogUpdateTime != null) {
        return getLastSeenView(auditLogsPageHelper!.auditLogUpdateTime);
      } else {
        return Container();
      }
    }
  }

  Widget getPageAppBar() {
    return Container(
      alignment: Alignment.centerLeft,
      decoration: BoxDecoration(
          border: Border.all(color: AppColorConstants.colorH2),
          color: AppColorConstants.colorWhite,
          borderRadius: BorderRadius.only(
              topRight: Radius.circular(getSize(7)),
              topLeft: Radius.circular(getSize(7)))),
      padding: EdgeInsets.only(left: getSize(18)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: getSize(10)),
              if (screenLayoutType == ScreenLayoutType.desktop) ...[
            Row(
              children: [
                const Spacer(flex: 2),
                Flexible(child:  _searchTextFieldView()),
                SizedBox(width: getSize(20)),
              ],
            )
              ],

          // Row(
          //   children: [
          //     Container(
          //       padding: const EdgeInsets.all(5),
          //       decoration: BoxDecoration(
          //           border: Border.all(
          //               color: AppColorConstants.colorH2.withOpacity(0.5)),
          //           borderRadius: const BorderRadius.all(Radius.circular(5))),
          //       height: 40,
          //       width: 330,
          //       child: Row(
          //         mainAxisAlignment: MainAxisAlignment.center,
          //         children: [
          //           Container(
          //             width: 100,
          //             alignment: Alignment.center,
          //             padding: EdgeInsets.only(
          //                 right: getSize(5), bottom: getSize(4)),
          //             child: AppText("${dropDownValueString(auditLogsPageHelper!.selectedOption)} :",
          //                 style: TextStyle(
          //                     fontFamily: AppAssetsConstants.openSans,
          //                     fontWeight: FontWeight.w600,
          //                     fontSize: getSize(15),
          //                     color: AppColorConstants.colorBlack)),
          //           ),
          //           Flexible(
          //             child: auditLogsPageHelper!.dateHelper.commonDateRangeDropDown(
          //                 onChanged: (value) => auditLogsPageHelper!.dateHelper.onDropdownChanged(
          //                         value, context, startDateDifference: 180, (startDateValue, endDateValue,
          //                             selectedOptionValue) {
          //                       auditLogsPageHelper!.startDate = startDateValue;
          //                       auditLogsPageHelper!.endDate = endDateValue;
          //                       auditLogsPageHelper!.selectedOption = selectedOptionValue;
          //                       auditLogsController.update();
          //                       auditLogsPageHelper!.paginationHelper.initializePagination();
          //                       auditLogsPageHelper!.auditPageController.goToFirstPage();
          //                       auditLogsPageHelper!.getAuditLogsData();
          //                     },customDialogShownOnce: true),
          //                 selectedValue: auditLogsPageHelper!.selectedOption,
          //                 hintText: S.of(context).selectDateRange,
          //                 items: auditLogsPageHelper!.dateRangeList,
          //                 startDate: auditLogsPageHelper!.startDate,
          //                 endDate: auditLogsPageHelper!.endDate),
          //           )
          //         ],
          //       ),
          //     ),
          //     SizedBox(width: getSize(10)),
          //     if (screenLayoutType == ScreenLayoutType.desktop) ...[
          //       const Spacer(flex: 2),
          //       Flexible(
          //         child: _searchTextFieldView(),
          //       ),
          //     ],
          //     SizedBox(width: getSize(20))
          //   ],
          // ),
          if (screenLayoutType != ScreenLayoutType.desktop) ...[
            SizedBox(height: getSize(10)),
            _searchTextFieldView(),
          ],
          SizedBox(height: getSize(10)),
        ],
      ),
    );
  }

  String dropDownValueString(String originalString) {
    int startIndex = originalString.indexOf('(');
    if (startIndex == -1) {
      return originalString;
    }
    return originalString.substring(0, startIndex).trim();
  }

  Widget _searchTextFieldView() {
    return Padding(
      padding: EdgeInsets.only(
          right: screenLayoutType == ScreenLayoutType.desktop ? 0 : 20),
      child: Container(
        decoration: BoxDecoration(
            color: AppColorConstants.colorWhite1,
            borderRadius: BorderRadius.circular(getSize(8))),
        child: AppTextFormField(
          onChanged: (value) {
            auditLogsPageHelper!.paginationHelper.currentPage = 0;
            auditLogsPageHelper!.auditPageController.goToFirstPage();
            auditLogsPageHelper!.searchAuditLogsDataList = auditLogsPageHelper!.listAuditLogsData
                .where((element) =>
                    element.devEui.toLowerCase().contains(value.toLowerCase()) ||
                    element.operation.toLowerCase().contains(value.toLowerCase()) ||
                    element.userEmail.toLowerCase().contains(value.toLowerCase()) ||
                    element.sourceService.toLowerCase().contains(value.toLowerCase()) ||
                    element.application.toLowerCase().contains(value.toLowerCase()) ||
                    element.requestPath.toLowerCase().contains(value.toLowerCase()) ||
                    element.requestMethod.toLowerCase().contains(value.toLowerCase()) ||
                    element.message.toLowerCase().contains(value.toLowerCase()) ||
                    element.deviceType.toLowerCase().contains(value.toLowerCase()))
                .toList();
            auditLogsPageHelper!.auditLogsDataSource = AuditLogsDevicesDataSource(
              context,
              auditLogsPageHelper!.searchAuditLogsDataList,
              (value) {},
            );
            auditLogsController.update();
          },
          focusedBorderColor: AppColorConstants.colorPrimary,
          controller: auditLogsPageHelper!.searchController,
          enabledBorderColor: AppColorConstants.colorWhite1,
          hintText: S.of(context).quickSearch,
          maxLines: 1,
          textInputType: TextInputType.text,
          borderRadius: getSize(8),
          hintTextColor: AppColorConstants.colorDarkBlue,
          suffixIcon: const Padding(
            padding: EdgeInsets.all(12),
            child: AppImageAsset(image: AppAssetsConstants.searchIcon),
          ),
          hintFontSize: 17,
          fontFamily: AppAssetsConstants.openSans,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }

  Widget auditLogsBoardView() {
    if (auditLogsPageHelper!.apiStatus == ApiStatus.loading) {
      return SizedBox(
          height: 400,
          width: 300,
          child: Align(
              alignment: Alignment.center,
              child: Container(
                  width: double.infinity,
                  decoration:
                      auditLogsPageHelper!.tableHelper.tableBorderDeco(),
                  child: const AppLoader())));
    }
    int itemsPerPage = auditLogsPageHelper!.tableHelper
        .getCurrentPageDataLength(auditLogsPageHelper!.listAuditLogsData,
        auditLogsPageHelper!.currentPageIndex,perPageLimit: AppStringConstants.auditLogsDevicePerPageLimit);
    auditLogsPageHelper!.recordsInPage =
    (auditLogsPageHelper!.listAuditLogsData.length > AppStringConstants.auditLogsDevicePerPageLimit)
        ? itemsPerPage
        : auditLogsPageHelper!.listAuditLogsData.length;

    return Column(
      children: [
        Container(
            width: double.infinity,
            decoration: BoxDecoration(
              border: Border(
                  bottom:
                      BorderSide(color: AppColorConstants.colorH2, width: 0.8),
                  left:
                      BorderSide(color: AppColorConstants.colorH2, width: 0.8),
                  right:
                      BorderSide(color: AppColorConstants.colorH2, width: 0.8)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Table
                SizedBox(
                  height: (auditLogsPageHelper!.listAuditLogsData.isNotEmpty)
                      ? (auditLogsPageHelper!.recordsInPage *
                              auditLogsPageHelper!.heightOfDataTableCell) +
                          (auditLogsPageHelper!.recordsInPage * 0.1) +
                          100
                      : (auditLogsPageHelper!.recordsInPage *
                              auditLogsPageHelper!.heightOfDataTableCell) +
                          (auditLogsPageHelper!.recordsInPage * 0.1) +
                          300,
                  child: getAuditLogsDataTableView(),
                ),
                AppPaginationWidget(
                  apiStatus: auditLogsPageHelper!.apiStatus,
                  paginationHelper: auditLogsPageHelper!.paginationHelper,
                  onLoadNext: () async {
                    await auditLogsPageHelper!.loadNextLogs(context);
                  },
                  onLoadPrevious: () async {
                    await auditLogsPageHelper!.loadPreviousLogs(context);
                    auditLogsController.update();
                  },
                  onGoToFirstPage: () {
                    auditLogsPageHelper!.paginationHelper.setPage(0);
                    auditLogsPageHelper!.auditPageController.goToFirstPage();
                  },
                    onGoToLastPage: () {
                      auditLogsPageHelper!.auditPageController.goToLastPage();
                    },
                    itemsPerPage: AppStringConstants.auditLogsDevicePerPageLimit,
                    onChanged: (value) {
                      AppStringConstants.auditLogsDevicePerPageLimit = int.parse(value);
                      if (auditLogsPageHelper!.apiStatus != ApiStatus.loading) {
                        auditLogsPageHelper!.paginationHelper.setPage(0);
                        auditLogsPageHelper!.auditPageController.goToFirstPage();
                        auditLogsPageHelper!.auditLogsPageOffset = 0;
                        auditLogsPageHelper!.getAuditLogsData();
                        ();
                        auditLogsController.update();
                      }
                    }),
                const SizedBox(height: 10,),
              ],
            ))
      ],
    );
  }

  Widget getAuditLogsDataTableView() {
    return PaginatedDataTable2(
      columnSpacing: 0,
      rowsPerPage: AppStringConstants.auditLogsDevicePerPageLimit,
      showCheckboxColumn: false,
      initialFirstRowIndex: auditLogsPageHelper!.paginationHelper.currentPage *
          AppStringConstants.auditLogsDevicePerPageLimit,
      headingTextStyle: auditLogsPageHelper!.tableHelper.headingTextStyle(),
      wrapInCard: false,
      border: auditLogsPageHelper!.tableHelper.tableBorder(),
      renderEmptyRowsInTheEnd: false,
      // ignore: deprecated_member_use
      headingRowColor: auditLogsPageHelper!.tableHelper.headingRowColor(),
      columns: [
        DataColumn2(
          fixedWidth: 180,
          label: AppText(S.of(context).timestamp),
        ),
        DataColumn2(
          size: ColumnSize.S,
          fixedWidth: 150,
          label: Center(child: SelectableText(S.of(context).deviceEUI)),
        ),
        DataColumn2(
          size: ColumnSize.S,
          fixedWidth: 150,
          label: Center(child: AppText(S.of(context).type)),
        ),
        DataColumn2(
          fixedWidth: 180,
          label: Center(child: AppText(S.of(context).operation)),
        ),
        DataColumn2(
          fixedWidth: 200,
          label: Center(child: AppText(S.of(context).email)),
        ),
        DataColumn2(
          fixedWidth: 180,
          label: Center(child: AppText(S.of(context).source_service)),
        ),
        DataColumn2(
          fixedWidth: 120,
          label: Center(child: AppText(S.of(context).application)),
        ),
        DataColumn2(
          fixedWidth: 180,
          label: Center(child: AppText(S.of(context).request_path)),
        ),   DataColumn2(
          fixedWidth: 100,
          label: Center(child: AppText(S.of(context).request_method,textAlign: TextAlign.center,)),
        ),

        DataColumn2(
          label: AppText(S.of(context).message),
        ),
      ],
      controller: auditLogsPageHelper!.auditPageController,
      source: auditLogsPageHelper!.auditLogsDataSource,
      minWidth: 1900,
      dataRowHeight: 51,
      hidePaginator: true,
      empty: auditLogsPageHelper!.tableHelper.getEmptyTableContent(context),
    );
  }
}
