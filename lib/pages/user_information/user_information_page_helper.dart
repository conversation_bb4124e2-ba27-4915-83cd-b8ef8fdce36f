import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/controller/user_information_controller.dart';

class UserInformationPageHelper {
  late UserInformationState state;

  late UserInformationController userInformationController;

  UserInformationPageHelper(this.state) {
    Future.delayed(const Duration(milliseconds: 100)).then((value) {
      userInformationController = Get.put(UserInformationController());
      setUserInformation();
    });
  }

  final sideMenuController = SideMenuController();
  int pageIndex = 0;

  void setUserInformation() async {
    await state.authController.getUserInfoFromToken();
    userInformationController.update();
  }
}
