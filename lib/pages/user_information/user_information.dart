import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/controller/user_information_controller.dart';

class UserInformation extends StatefulWidget {
  const UserInformation({super.key});

  @override
  State<UserInformation> createState() => UserInformationState();
}

class UserInformationState extends State<UserInformation> {
  late AuthController authController;
  UserInformationPageHelper? userInformationPageHelper;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    authController = Get.put(AuthController());
  }

  @override
  Widget build(BuildContext context) {
    userInformationPageHelper ?? (userInformationPageHelper = UserInformationPageHelper(this));
    return GetBuilder<UserInformationController>(
      init: UserInformationController(),
      builder: (UserInformationController controller) {
        buildContext = context;
        return getBody();
      },
    );
  }

  Widget getBody() {
    return getInfoView();
  }

  Widget getInfoView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Expanded(
          child: Stack(
            alignment: Alignment.topCenter,
            children: [
              Padding(
                padding: EdgeInsets.only(top: getSize(50), bottom: getSize(20), left: getSize(10)),
                child: Card(
                  elevation: getSize(10),
                  child: Container(
                    color: AppColorConstants.colorWhite,
                    constraints: BoxConstraints(maxWidth: getSize(350)),
                    child: Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(top: 50),
                            child: AppText(
                              authController.userInformation.name ?? "",
                              style: TextStyle(
                                fontSize: getSize(21),
                                fontWeight: getMediumBoldFontWeight(),
                                fontFamily: AppAssetsConstants.notoSans,
                              ),
                            ),
                          ),

                          SizedBox(height: getSize(21)),
                          Row(
                            children: [
                              const Icon(Icons.mail_outline),
                              SizedBox(width: getSize(10)),
                              Expanded(
                                child: Text(
                                  authController.userInformation.uniqueName ?? "",
                                  style: TextStyle(
                                    fontSize: getMedFontSize(),
                                    fontWeight: getMediumFontWeight(),
                                    fontFamily: AppAssetsConstants.notoSans,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            child: Divider(
                              height: 2,
                              color: AppColorConstants.colorH2,
                            ),
                          ),
                          SizedBox(height: getSize(30)),
                          const Spacer(),
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            child: Divider(
                              height: 2,
                              color: AppColorConstants.colorH2,
                            ),
                          ),
                       if(!AppConfig.shared.isOpenFromBLE) Align(
                            alignment: Alignment.bottomLeft,
                            child: InkWell(
                              onTap: () => logoutView(context, () async {
                                await authController.logOut();
                                goBack();
                              }),
                              child: AppText(isSelectableText: false,
                                S.of(context).signOut,
                                style: TextStyle(
                                  fontSize: getMedFontSize(),
                                  fontWeight: getMediumFontWeight(),
                                  fontFamily: AppAssetsConstants.notoSans,
                                  color: AppColorConstants.colorChartLine1,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              Positioned(
                  top: 10,
                  child: Container(
                      padding: const EdgeInsets.all(15),
                      decoration: BoxDecoration(
                          color: AppColorConstants.colorPrimaryLime,
                          borderRadius: BorderRadius.circular(55),
                          border:
                              Border.all(color: AppColorConstants.colorBackgroundDark, width: 5)),
                      child: Icon(
                        Icons.person,
                        size: getSize(70),
                      ))),
            ],
          ),
        ),
      ],
    );
  }
}
