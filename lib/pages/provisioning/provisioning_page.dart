// ignore_for_file: deprecated_member_use
import 'package:quantumlink_node/app_import.dart';

import 'mobile_provisioning.dart';

class ProvisioningPage extends StatefulWidget {
  const ProvisioningPage({super.key});

  @override
  State<ProvisioningPage> createState() => ProvisioningPageState();
}

class ProvisioningPageState extends State<ProvisioningPage> {
  Timer? _timer;

  @override
  void dispose() {
    provisioningPageHelper!.timer?.cancel(); // Cancel the timer if still active
    _stopTimer();
    super.dispose();
  }

  ProvisioningPageHelper? provisioningPageHelper;
  late ProvisionController provisionController;

  @override
  Widget build(BuildContext context) {
    provisioningPageHelper ??
        (provisioningPageHelper = ProvisioningPageHelper(this));
    return GetBuilder<ProvisionController>(
      init: ProvisionController(),
      builder: (ProvisionController controller) {
        provisionController = controller;
        return Stack(
          children: [
            getBody(),
          ],
        );
      },
    );
  }

  void _startTimer() {
    debugPrint("Start auto-discovered page timer");
    _timer = Timer(const Duration(seconds: 5), () {
      provisioningPageHelper!.listSelected = provisioningPageHelper!
          .listProvisionedDeviceItem!.result
          .where((element) => (element.selected ?? false == true))
          .toList();
      if (provisioningPageHelper!.listSelected.isEmpty) {
        provisioningPageHelper!.getProvisionData();
      } else {
        debugPrint(
            "provisioningPageHelper!.listSelected.isNotEmpty so not calling getProvisionData()");
      }
    });
  }

  void _stopTimer() {
    debugPrint("Stop auto-discovered page timer");
    _timer?.cancel();
    _timer = null;
  }

  Widget getBody() {
    return getProvisionBoardView();
  }

  Widget getPageAppBar() {
    return Container(
      decoration: BoxDecoration(
          border: Border.all(color: AppColorConstants.colorH2, width: 0.8),
          color: AppColorConstants.colorWhite,
          borderRadius: BorderRadius.only(
              topRight: Radius.circular(getSize(7)),
              topLeft: Radius.circular(getSize(7)))),
      alignment: Alignment.topCenter,
      padding: const EdgeInsets.all(10),
      child: Column(
        children: [
          Row(
            children: [
              const Spacer(),
              if (provisioningPageHelper!.screenLayoutType == ScreenLayoutType.desktop) ...[
                searchTextFieldView()
              ],
              Padding(
                padding: EdgeInsets.only(bottom: provisioningPageHelper!.screenLayoutType == ScreenLayoutType.desktop?0:10.0),
                child: MobileProvisioning().selectTableTypeButtonView(provisioningPageHelper),
              )
            ],
          ),
          if (provisioningPageHelper!.screenLayoutType !=
              ScreenLayoutType.desktop)
            ...[
              Row(
              children: [
                searchTextFieldView(),
              ],
            )]
        ],
      ),
    );
  }

  Widget getProvisionBoardView() {
    return ScreenLayoutTypeBuilder(
      builder: (context, screenType, constraints) {
        provisioningPageHelper!.screenLayoutType = screenType;
        MobileProvisioning().autoSelectTableType(provisioningPageHelper);
        return Container(
          child: ClipRRect(
            borderRadius: const BorderRadius.all(Radius.circular(5)),
            child: Container(
              color: AppColorConstants.colorWhite,
              width: double.infinity,
              height: double.infinity,
              child: ListView(
                shrinkWrap: true,
                padding: EdgeInsets.only(
                    left: getSize(15), right: getSize(20)),
                children: [
                  getPageTitleView(S.of(context).discoveredDevice),
                  SizedBox(height: getSize(10)),
                  getPageAppBar(),
                  buildDropDownViewForAutoJoinAndStatus(),
                  getTableBoardView(),
                  Row(mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      buildLastSeenView(),
                      getRefreshButtonView(),
                    ],
                  ),
                  if (provisioningPageHelper!.errorMessage != null)
                    CommonAPIErrorView(errorMessage: provisioningPageHelper!.errorMessage ?? "",rightPadding: 5),
                  SizedBox(height: getSize(20)),

                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget getTableBoardView() {
    if (provisioningPageHelper!.apiStatus == ApiStatus.loading) {
      return  SizedBox(
          height: 400,
          child: Align(
              alignment: Alignment.center,
              child: Container(
                  width: double.infinity,
                  decoration:
                  provisioningPageHelper!.dataTableHelper.tableBorderDeco(),
                  child: const AppLoader())));
    }
    final resultList = provisioningPageHelper?.listProvisionedDeviceItem?.result;
    if (resultList != null) {
      int itemsPerPage = provisioningPageHelper!.dataTableHelper.getCurrentPageDataLength(
        resultList,
        provisioningPageHelper!.currentPageIndex,
        perPageLimit: AppStringConstants.provisionPrePageLimit,
      );
      provisioningPageHelper!.recordsInPage =
      (resultList.length > AppStringConstants.provisionPrePageLimit)
          ? itemsPerPage
          : resultList.length;
    } else {
      provisioningPageHelper!.recordsInPage = 0;
    }
    return Column(
      children: [
        Container(
            width: double.infinity,
            decoration:
                provisioningPageHelper!.dataTableHelper.tableBorderDeco(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Table
              provisioningPageHelper!.isTableView?  SizedBox(
                height: ((provisioningPageHelper?.listProvisionedDeviceItem?.result.isNotEmpty ?? false)
                    ? 90
                    : 350) +
                    ((provisioningPageHelper?.recordsInPage ?? 0) *
                        (provisioningPageHelper?.heightOfDataTableCell ?? 40.0)) +
                    ((provisioningPageHelper?.recordsInPage ?? 0) * 0.1),
                  child: getDataTableWithPageBoardView(),
                ):MobileProvisioning().buildProvisionList(context, provisioningPageHelper!),
                SizedBox(height: getSize(20)),
                // Divider
                Container(
                  height: 1,
                  width: double.infinity,
                  color: AppColorConstants.colorBlack12,
                ),
                // Action Buttons
                Container(
                  padding: const EdgeInsets.only(left: 16, top: 10, bottom: 10),
                  width: double.infinity,
                  decoration: BoxDecoration(
                      color: AppColorConstants.colorBackgroundDark,
                      borderRadius: const BorderRadius.only(
                          bottomRight: Radius.circular(5), bottomLeft: Radius.circular(5))),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      AppPaginationWidget(
                        apiStatus: provisioningPageHelper!.apiStatus,
                        paginationHelper: provisioningPageHelper!.paginationHelper,
                        onLoadNext: () async {
                          await  provisioningPageHelper!.loadNextLogs(context);
                        },
                        onLoadPrevious: () async {
                          await  provisioningPageHelper!.loadPreviousLogs(context);
                          provisionController.update();
                        },
                        onGoToFirstPage: () {
                          provisioningPageHelper!.paginationHelper.setPage(0);
                          if(provisioningPageHelper!.isTableView) {
                            provisioningPageHelper!.paginatorController.goToFirstPage();
                          }
                          provisionController.update();
                        },
                        onGoToLastPage: () {
                          if(provisioningPageHelper!.isTableView) {
                            provisioningPageHelper!.paginatorController.goToLastPage();
                          }
                          provisionController.update();
                        },
                        itemsPerPage: AppStringConstants.provisionPrePageLimit,
                        onChanged: (value) {
                          if (provisioningPageHelper!.apiStatus != ApiStatus.loading) {
                            AppStringConstants.provisionPrePageLimit = int.parse(value);
                            provisioningPageHelper!.getProvisionData();
                            provisionController.update();
                          }
                        },
                      ),
                    ],
                  ),
                ),
              ],
            )),
      ],
    );
  }

  buildDropDownViewForAutoJoinAndStatus() {
    return Container(
      decoration: BoxDecoration(
          border: Border(
              bottom: BorderSide(color: AppColorConstants.colorH2, width: 0.8),
              left: BorderSide(color: AppColorConstants.colorH2, width: 0.8),
              right: BorderSide(color: AppColorConstants.colorH2, width: 0.8))),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0,horizontal: 5),
        child: Wrap(alignment: WrapAlignment.end,
          runSpacing: 8,
          spacing: 5,
          children: [
            SizedBox(
              width: 250,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                decoration: BoxDecoration(
                    border: Border.all(color: AppColorConstants.colorBlackBlue, width: 1.5),
                    borderRadius: const BorderRadius.all(Radius.circular(5))),
                child: Row(
                  children: [
                    SizedBox(width: 65,
                      child: AppText(
                        S.of(context).vendor + " :",
                        style: TextStyle(
                            fontSize: 14,
                            color: AppColorConstants.colorBlackBlue,
                            fontWeight: FontWeight.w600),
                      ),
                    ),
                    SizedBox(
                      height: 35,
                      width: 30,
                      child: provisioningPageHelper!.ampProviderStatus == ApiStatus.loading
                          ? const AppLoader()
                          : const SizedBox(),
                    ),
                    Expanded(
                      child: CommonDropdownButton(
                        iconColor: AppColorConstants.colorH3,
                        selectedValue: provisioningPageHelper!.selectedVendorCode,
                        fontSize: getSize(14),
                        borderColor: AppColorConstants.colorTransparent,
                        buttonHeight: getSize(35),
                        hintText: S.of(context).select,
                        items: provisioningPageHelper!.vendorCodeList ?? [],
                        onChanged: (value) async {
                          if (value == AppStringConstants.all) {
                            provisioningPageHelper!.selectedVendorCode = null;
                          } else {
                            provisioningPageHelper!.selectedVendorCode = value;
                          }
                          await provisioningPageHelper!.getProvisionData();
                        },
                      ),
                    ),

                  ],
                ),
              ),
            ),
            SizedBox(
              width: 250,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 8),
                decoration: BoxDecoration(
                    border: Border.all(color: AppColorConstants.colorBlackBlue, width: 1.5),
                    borderRadius: const BorderRadius.all(Radius.circular(5))),
                child: Row(
                  children: [
                    SizedBox(width: 95,
                      child: AppText(
                        S.of(context).autoJoin + " :",
                        style: TextStyle(
                            fontSize: 14,
                            color: AppColorConstants.colorBlackBlue,
                            fontWeight: FontWeight.w600),
                      ),
                    ),
                    Expanded(
                      child: CommonDropdownButton(
                        iconColor: AppColorConstants.colorH3,
                        selectedValue: provisioningPageHelper!.selectedAutoJoin,
                        fontSize: getSize(14),
                        buttonHeight: getSize(35),
                        borderColor: AppColorConstants.colorTransparent,
                        hintText: S.of(context).select,
                        items: provisioningPageHelper!.autoJoinList,
                        onChanged: (value) async {
                          if (value == AppStringConstants.all) {
                            provisioningPageHelper!.selectedAutoJoin = null;
                          } else {
                            provisioningPageHelper!.selectedAutoJoin = value;
                          }
                          await provisioningPageHelper!.getProvisionData();
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(
              width: 250,
              child: Container(

                padding: EdgeInsets.symmetric(horizontal: 8),
                decoration: BoxDecoration(
                    border: Border.all(color: AppColorConstants.colorBlackBlue, width: 1.5),
                    borderRadius: const BorderRadius.all(Radius.circular(5))),
                child: Row(
                  children: [
                    SizedBox(width: 60,
                      child: AppText(
                        S.of(context).status + " :",
                        style: TextStyle(
                            fontSize: 14,
                            color: AppColorConstants.colorBlackBlue,
                            fontWeight: FontWeight.w600),
                      ),
                    ),
                    Expanded(
                      child: CommonDropdownButton(
                        borderColor: AppColorConstants.colorWhite,
                        iconColor: AppColorConstants.colorH3,
                        selectedValue: provisioningPageHelper!.selectedStatus,
                        fontSize: getSize(14),
                        buttonHeight: getSize(35),
                        hintText: S.of(context).select,
                        items: provisioningPageHelper!.statusKeyList,
                        onChanged: (value) async {
                          if (value == AppStringConstants.all) {
                            provisioningPageHelper!.selectedStatus = null;
                          } else {
                            provisioningPageHelper!.selectedStatus = value;
                          }
                          await provisioningPageHelper!.getProvisionData();
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget getRefreshButtonView() {
    return Padding(
      padding: const EdgeInsets.only(right: 10.0),
      child: AppRefresh(
          onPressed: () {
            if (provisioningPageHelper!.apiStatus != ApiStatus.loading) {
              provisioningPageHelper!.getProvisionData();
            }
          },
          loadingStatus: provisioningPageHelper!.apiStatus),
    );
  }

  Widget buildLastSeenView() {
    if (provisioningPageHelper!.isShowText) {
      return getTimeDurationView(
        refreshStatus: provisioningPageHelper!.apiStatus,
        updateTime: provisioningPageHelper!.provisionUpdateTime,
        onTapTime: provisioningPageHelper!.onTapTime,
        difference: provisioningPageHelper!.differenceTime,
      );
    } else {
      return getLastSeenView(provisioningPageHelper!.provisionUpdateTime);
    }
  }

  Widget getDataTableWithPageBoardView() {
    return PaginatedDataTable2(
      rowsPerPage: AppStringConstants.provisionPrePageLimit,
      initialFirstRowIndex:
      provisioningPageHelper!.paginationHelper.currentPage * AppStringConstants.provisionPrePageLimit,
      headingCheckboxTheme:
          CheckboxThemeData(side: BorderSide(width: 2, color: AppColorConstants.colorWhite)),
      headingTextStyle: provisioningPageHelper!.dataTableHelper.headingTextStyle(),
      wrapInCard: false,
      datarowCheckboxTheme: const CheckboxThemeData(side: BorderSide(width: 2, color: Colors.grey)),
      border: provisioningPageHelper!.dataTableHelper.tableBorder(),
      renderEmptyRowsInTheEnd: false,
      headingRowColor: provisioningPageHelper!.dataTableHelper.headingRowColor(),
      columns: _getDataColumns(),
      controller: provisioningPageHelper!.paginatorController,
      source: provisioningPageHelper!.provisionedDeviceDataSource,
      onSelectAll: provisioningPageHelper!.provisionedDeviceDataSource.selectAll,
      minWidth: 1300,
      dataRowHeight: 51,
      // For progress indicator
      hidePaginator: true,
      empty: provisioningPageHelper!.dataTableHelper.getEmptyTableContent(context),
    );
  }

  List<DataColumn> _getDataColumns() {
    return [
      DataColumn2(
        fixedWidth: getSize(170),
        label: SelectableText(S.of(context).tableDeviceEUI),
      ),
      DataColumn2(
        fixedWidth: getSize(180),
        label: AppText(
          S.of(context).vendor,
          textAlign: TextAlign.center,
        ),
      ),
      DataColumn2(
        fixedWidth: getSize(300),
        label: AppText(
          S.of(context).tableType,
          textAlign: TextAlign.center,
        ),
      ),
      DataColumn2(
        fixedWidth: getSize(200),
        label: Center(
          child: AppText(
            S.of(context).status,
            textAlign: TextAlign.center,
          ),
        ),
      ),
      DataColumn2(fixedWidth: getSize(160),
        label: AppText(
          S.of(context).autoJoin,
          textAlign: TextAlign.center,
        ),
      ),
      DataColumn2(
        label: AppText(
          S.of(context).lastSeen,
          textAlign: TextAlign.center,
        ),
      ),

    ];
  }

  Widget getUploadButtonView() {
    return AppButton(
      buttonHeight: 33,
      fontSize: 14.5,
      buttonRadius: 9,
      buttonName: S.of(context).fileUpload,
      onPressed: () {
        provisioningPageHelper!.uploadCsvFiles();
      },
      fontFamily: AppAssetsConstants.openSans,
    );
  }

  void _showProvisioningAlertDialog(
      BuildContext context, List<ProvisioningDeviceItem> listSelected, Function callback) {
    // Create the AlertDialog
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          surfaceTintColor: AppColorConstants.colorWhite,
          backgroundColor: AppColorConstants.colorWhite,
          insetPadding: EdgeInsets.zero,
          contentPadding: EdgeInsets.zero,
          titlePadding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
          title: getCustomAppBarWithClose(S.of(context).provisionSelected),
          content: StatefulBuilder(builder: (context, snapshot) {
            return Container(
              width: MediaQuery.of(context).size.width * 0.19, // Adjust the width as needed
              height: MediaQuery.of(context).size.height * 0.28,
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(left: 8.0),
                    child: AppText(S.of(context).confirmProvision,
                        style: const TextStyle(
                          fontSize: 14,
                          fontFamily: AppAssetsConstants.openSans,
                          fontWeight: FontWeight.w400,
                        )),
                  ),
                  Expanded(
                    child: ListView.builder(
                      shrinkWrap: true,
                      // Allow the ListView to shrink-wrap its contents
                      itemCount: listSelected.length,
                      itemBuilder: (context, index) {
                        ProvisioningDeviceItem item = listSelected[index];
                        return ListTile(
                          title: AppText(
                            item.deviceEui,
                            style: const TextStyle(
                              fontSize: 15,
                              fontFamily: AppAssetsConstants.openSans,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          trailing: IconButton(
                            icon: Icon(
                              Icons.remove_circle_outline_sharp,
                              color: AppColorConstants.colorRedLight,
                            ),
                            onPressed: () {
                              listSelected.removeAt(index);
                              callback.call(listSelected);
                              if (listSelected.isEmpty) {
                                Navigator.of(context).pop();
                              }
                              snapshot(() {});
                            },
                          ),
                        );
                      },
                    ),
                  ),
                  getAppDivider(),
                ],
              ),
            );
          }),
          actions: <Widget>[
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                AppButton(
                  borderColor: Colors.grey.withOpacity(0.5),
                  buttonHeight: 20,
                  buttonColor: Colors.grey.withOpacity(0.5),
                  buttonName: S.of(context).cancel,
                  fontFamily: AppAssetsConstants.openSans,
                  onPressed: () {
                    goBack();
                  },
                ),
                const SizedBox(width: 16),
                AppButton(
                  fontFamily: AppAssetsConstants.openSans,
                  buttonHeight: 20,
                  buttonName: S.of(context).ok,
                  onPressed: () {
                    provisioningPageHelper!.provisionSelectedDevice();
                    Navigator.of(context).pop();
                  },
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  Widget getAddButtonView() {
    return AppButton(
      buttonHeight: 33,
      fontSize: 14.5,
      buttonRadius: 9,
      buttonName: S.of(context).addBtn,
      onPressed: () {
        bool isUpdate = false;
        provisioningPageHelper!.listSelected = provisioningPageHelper!
            .listProvisionedDeviceItem!.result
            .where((element) => (element.selected ?? false == true))
            .toList();
        if (provisioningPageHelper!.isShowDiscovered == true &&
            provisioningPageHelper!.listSelected.isNotEmpty) {
          _showProvisioningAlertDialog(context, provisioningPageHelper!.listSelected,
                  (List<ProvisioningDeviceItem> removeListSelected) {
            //Call the callback function with the updated list

            provisioningPageHelper!.listSelected = removeListSelected;
            for (var element in provisioningPageHelper!.listProvisionedDeviceItem!.result) {
              if (!provisioningPageHelper!.listSelected.contains(element)) {
                element.selected = false; // Unselect the item
              }
            }

            provisioningPageHelper!.provisionedDeviceDataSource.notifyListeners();
          });
        } else {
          addNewDeviceModalView(
              context,
              isUpdate,
              (ProvisioningDeviceItem item) =>
                  provisioningPageHelper!.addProvisionDevice(item, isUpdate));
        }
      },
      fontFamily: AppAssetsConstants.openSans,
    );
  }

  Widget getDeleteButtonView() {
    return AppButton(
      buttonRadius: 9,
      buttonHeight: 33,
      fontSize: 13,
      fontColor: provisioningPageHelper!.provisionedDeviceDataSource.selectedRowCount > 0
          ? AppColorConstants.colorRed
          : AppColorConstants.colorH1Grey,
      borderColor: provisioningPageHelper!.provisionedDeviceDataSource.selectedRowCount > 0
          ? AppColorConstants.colorRed.withOpacity(0.8)
          : AppColorConstants.colorH2.withOpacity(0.5),
      buttonName: S.of(context).deleteBtn,
      onPressed: () {
        if (provisioningPageHelper!.provisionedDeviceDataSource.selectedRowCount <= 0) {
          return;
        }
        provisioningPageHelper!.listSelected = provisioningPageHelper!
            .listProvisionedDeviceItem!.result
            .where((element) => (element.selected ?? false == true))
            .toList();
        deleteDeviceModalView(context, () => provisioningPageHelper!.deleteProvisionDevice(),
            provisioningPageHelper!.listSelected);
      },
      buttonColor: AppColorConstants.colorWhite,
      fontFamily: AppAssetsConstants.openSans,
    );
  }

  Widget searchTextFieldView() {
    return Flexible(
      child: Container(
        decoration: BoxDecoration(
            color: AppColorConstants.colorWhite1, borderRadius: BorderRadius.circular(getSize(8))),
        child: AppTextFormField(
          onChanged: (value) {
            if (provisioningPageHelper!.isTableView) {
              provisioningPageHelper!.paginatorController.goToFirstPage();
            }
            if (value.trim().isEmpty) {
              provisioningPageHelper!.searchController.clear();
              provisioningPageHelper!.getProvisionData();
            }
          },
          focusedBorderColor: AppColorConstants.colorPrimary,
          controller: provisioningPageHelper!.searchController,
          enabledBorderColor: AppColorConstants.colorWhite1,
          hintText: S.of(context).quickSearch,
          maxLines: 1,
          textInputType: TextInputType.emailAddress,
          validator: (value) {
            return null;
          },
          onFieldSubmitted: (value) {
            if (value.isNotEmpty) provisioningPageHelper!.getProvisionData();
          },
          borderRadius: getSize(8),
          hintTextColor: AppColorConstants.colorDarkBlue,
          suffixIcon: InkWell(
            onTap: () {
              provisionController.update();
              if (provisioningPageHelper!.searchController.text.isNotEmpty) {
                provisioningPageHelper!.getProvisionData();
              }
            },
            child: const Padding(
              padding: EdgeInsets.all(12),
              child: AppImageAsset(image: AppAssetsConstants.searchIcon),
            ),
          ),
          hintFontSize: 17,
          fontFamily: AppAssetsConstants.openSans,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }

  Widget fileUploadButtonView() {
    return AppButton(
        buttonHeight: 33,
        fontSize: 14.5,
        buttonRadius: 9,
        onPressed: () {
          provisioningPageHelper!.uploadJoinServerCsvFiles();
        },
        buttonName: S.of(context).uploadJoinServer,
        borderColor: AppColorConstants.colorLightBlue,
        fontFamily: AppAssetsConstants.openSans,
        buttonColor: AppColorConstants.colorLightBlue);
  }

  Widget showDiscoveredView() {
    return Padding(
      padding: const EdgeInsets.only(left: 7.0),
      child: Row(
        children: [
          Checkbox(
            splashRadius: 0,
            value: provisioningPageHelper!.isShowDiscovered,
            activeColor: AppColorConstants.colorGreen2,
            side: MaterialStateBorderSide.resolveWith(
              (Set<MaterialState> states) {
                return BorderSide(
                    width: 2,
                    color: provisioningPageHelper!.isShowDiscovered
                        ? AppColorConstants.colorGreen2
                        : AppColorConstants.colorBlack);
              },
            ),
            onChanged: (bool? value) async {
              provisioningPageHelper!.isShowDiscovered = value!;
              await provisioningPageHelper!.getProvisionData();
              if (provisioningPageHelper!.isShowDiscovered) {
                _startTimer();
              } else {
                _stopTimer();
              }
              provisioningPageHelper!.paginatorController.goToFirstPage();
              provisioningPageHelper!.provisionedDeviceDataSource.notifyListeners();
            },
          ),
          FittedBox(
            fit: BoxFit.cover,
            // Ensure the text fits within the available space
            child: AppText(S.of(context).showDiscovered,
                style: TextStyle(
                    fontFamily: AppAssetsConstants.openSans,
                    fontWeight: FontWeight.w500,
                    fontSize: getSize(16),
                    color: AppColorConstants.colorBlack)),
          ),
        ],
      ),
    );
  }

  Widget showNodeGWView() {
    return Padding(
      padding: const EdgeInsets.only(right: 20.0),
      child: Row(
        children: [
          Checkbox(
            splashRadius: 0,
            value: provisioningPageHelper!.isShowNodeGw,
            activeColor: AppColorConstants.colorGreen2,
            side: MaterialStateBorderSide.resolveWith(
              (Set<MaterialState> states) {
                return BorderSide(
                    width: 2,
                    color: provisioningPageHelper!.isShowNodeGw
                        ? AppColorConstants.colorGreen2
                        : AppColorConstants.colorBlack);
              },
            ),
            onChanged: (bool? value) async {
              provisioningPageHelper!.isShowNodeGw = value!;
              provisionController.update();
            },
          ),
          FittedBox(
            fit: BoxFit.cover,
            // Ensure the text fits within the available space
            child: AppText(S.of(context).nodeGW,
                style: TextStyle(
                    fontFamily: AppAssetsConstants.openSans,
                    fontWeight: FontWeight.w500,
                    fontSize: getSize(16),
                    color: AppColorConstants.colorBlack)),
          ),
        ],
      ),
    );
  }
}

addNewDeviceModalView(c, bool isUpdate, Function fun,
    {ProvisioningDeviceItem? item}) {
  {
    TextEditingController euiController = TextEditingController();
    RegExp deviceEuiRegExp = RegExp(
        ValidationUtils.numberWithAlphaRegExp);
    // TypeDDItem typeDDItem =
    //     TypeDDItem.fromName(AppStringConstants.selectTypeLE);
    // List<dynamic> listTypeDDItem = [
    //   TypeDDItem.fromName(AppStringConstants.selectTypeLE),
    //   TypeDDItem.fromName(AppStringConstants.selectTypeSE),
    // ];

    final GlobalKey<FormState> deviceFormKey = GlobalKey<FormState>();

    if (item != null) {
      euiController.text = item.deviceEui ?? "";
      //if (item.type != null) typeDDItem = TypeDDItem.fromName(item.type ?? "");
    }

    return showDialog(
      context: c,
      builder: (context) {
        return GetBuilder<ProvisionController>(
          init: ProvisionController(),
          builder: (ProvisionController controller) {
            return AlertDialog(
              surfaceTintColor: AppColorConstants.colorWhite,
              backgroundColor: AppColorConstants.colorWhite,
              insetPadding: EdgeInsets.zero,
              contentPadding: EdgeInsets.zero,
              titlePadding: EdgeInsets.zero,
              actionsPadding: EdgeInsets.zero,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(5)),
              title: getCustomAppBarWithClose(item != null
                  ? S.of(context).updateAmpDetails
                  : S.of(context).newAmpDetails),
              content: StatefulBuilder(builder: (context, snapshot) {
                return Padding(
                  padding: EdgeInsets.only(
                      bottom: MediaQuery.of(c).viewInsets.bottom),
                  //padding: const EdgeInsets.all(8.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Form(
                        key: deviceFormKey,
                        child: Container(
                          padding: const EdgeInsets.only(
                              left: 16, right: 16, top: 16),
                          child: Column(
                            children: [
                              const SizedBox(height: 8),
                              AppTextFormField(
                                label: S.of(context).deviceEUIHint,
                                controller: euiController,
                                maxLines: 1,
                                readOnly: isUpdate,
                                textInputType: TextInputType.text,
                                validator: (value) {
                                  if (value!.isEmpty) {
                                    return S.of(context).thisFieldIsRequired;
                                  } else if (!deviceEuiRegExp.hasMatch(value)) {
                                    return "Only alphanumeric values are allowed in this field.";
                                  } else if (value.length != 16) {
                                    return "Device EUI should be 16 characters";
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 16),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),
                      getAppDivider(),
                    ],
                  ),
                );
              }),
              actions: [
                Container(
                  padding: const EdgeInsets.only(
                      left: 16, right: 16, bottom: 16, top: 8),
                  child: AppButton(
                    fontSize: 14,
                    buttonWidth: 80,
                    buttonHeight: 35,
                    fontFamily: AppAssetsConstants.openSans,
                    buttonName: (item == null)
                        ? "+ ${S.of(context).addNewBtn}"
                        : S.of(context).update,
                    onPressed: () {
                      if (!deviceFormKey.currentState!.validate()) {
                        return;
                      }
                      if (item == null) {
                        // SAVE
                        ProvisioningDeviceItem item =
                            ProvisioningDeviceItem.empty();
                        item.deviceEui =
                            euiController.text.trim().toLowerCase();
                        item.appKey = "";
                        fun.call(item);
                      } else {
                        // UPDATE
                        ProvisioningDeviceItem updateItem =
                            ProvisioningDeviceItem.empty();
                        updateItem.deviceEui =
                            euiController.text.trim().toLowerCase();
                        updateItem.appKey = "";
                        /*updateItem.type = typeDDItem.name;
                        updateItem.status = item.status;*/
                        fun.call(updateItem);
                      }
                    },
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }
}

deleteDeviceModalView(c, Function fun, List l) {
  return showDialog(
    context: c,
    builder: (context) {
      return AlertDialog(
        surfaceTintColor: AppColorConstants.colorWhite,
        backgroundColor: AppColorConstants.colorWhite,
        insetPadding: EdgeInsets.zero,
        contentPadding: EdgeInsets.zero,
        titlePadding: EdgeInsets.zero,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
        title: getCustomAppBarWithClose("Delete ?"),
        content: StatefulBuilder(builder: (context, snapshot) {
          return Container(
            width: MediaQuery.of(context).size.width *
                0.2, // Adjust the width as needed
            height: MediaQuery.of(context).size.height * 0.3,
            padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Expanded(
                  child: ListView.builder(
                    shrinkWrap: true,
                    // Allow the ListView to shrink-wrap its contents
                    itemCount: l.length,
                    itemBuilder: (context, index) {
                      ProvisioningDeviceItem item = l[index];
                      return ListTile(
                        title: AppText(
                          ' ${index + 1}. ${item.deviceEui} ?',
                          style: const TextStyle(fontSize: 15,
                            fontFamily: AppAssetsConstants.openSans,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      );
                    },
                  ),
                ),
                getAppDivider(),
              ],
            ),
          );
        }),
        actions: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              AppButton(
                borderColor: Colors.grey.withOpacity(0.5),
                buttonHeight: 20,
                buttonColor: Colors.grey.withOpacity(0.5),
                buttonName: 'No',
                fontFamily: AppAssetsConstants.openSans,
                onPressed: () {
                  goBack();
                },
              ),
              const SizedBox(width: 16),
              AppButton(
                fontFamily: AppAssetsConstants.openSans,
                buttonHeight: 20,
                buttonName: 'Yes',
                onPressed: () {
                  goBack();
                  fun.call();
                },
              ),
            ],
          )
        ],
      );
    },
  );
}
