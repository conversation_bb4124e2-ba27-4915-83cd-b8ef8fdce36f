import 'package:quantumlink_node/app_import.dart';
import 'package:http/src/response.dart' as http;

class ProvisioningPageHelper {
  late ProvisioningPageState state;

  ApiStatus apiStatus = ApiStatus.initial;
  ApiStatus ampProviderStatus = ApiStatus.initial;
  late ScreenLayoutType screenLayoutType;
  final key = GlobalKey<PaginatedDataTableState>();
  final double heightOfDataTableCell = 48;
  int recordsInPage = 0; // For Set Fixed and Static Height Of Table
  int currentPageIndex = 0;
  bool isShowDiscovered = false;
  bool isAutoJoin = false;
  bool isShowNodeGw = false;
  PaginatorController paginatorController = PaginatorController();
  final FocusNode searchFocusNode = FocusNode();
  PaginationHelper paginationHelper = PaginationHelper();
  TextEditingController searchController = TextEditingController();
  List<String>? vendorCodeList=[];
  List<String> autoJoinList = [
    AppStringConstants.all,
    AppStringConstants.enable,
    AppStringConstants.disable
  ];
  List<String> statusKeyList = [
    AppStringConstants.all,
    AppStringConstants.pending,
    AppStringConstants.online,
    AppStringConstants.offline,
    AppStringConstants.missingKey,
    AppStringConstants.missingVendor
  ];
  String? selectedVendorCode;
  String? selectedAutoJoin;
  String? selectedStatus;
  ProvisioningDeviceList ? listProvisionedDeviceItem =
      ProvisioningDeviceList.empty();
  String? errorMessage;
  late ProvisionedDeviceDataSource provisionedDeviceDataSource;
  List<ProvisioningDeviceItem> listSelected = [];
  DataTableHelper dataTableHelper = DataTableHelper();
  DateTime provisionUpdateTime = DateTime.now();
  DateTime? onTapTime;
  Duration? differenceTime;
  bool isShowText = true;
  Timer? refreshTimer;
  bool isTableView = true;
  late ScreenLayoutType previousLayoutType = ScreenLayoutType.desktop;
  ProvisionedRequestParams provisionedRequestParams = ProvisionedRequestParams();
  ProvisioningPageHelper(this.state) {
    apiStatus = ApiStatus.loading;
    Future.delayed(const Duration(milliseconds: 500)).then((value) async {
      getProvisionData();
      getAmpProviderList();
      getCurrantPageIndex();
      FocusScope.of(state.context).requestFocus(searchFocusNode);
    });
  }

  Timer? timer;

  void startDelayedAction() {
    cancelDelayedAction();
    timer = Timer(const Duration(seconds: 60), () {
      getProvisionData();
    });
  }

  void cancelDelayedAction() {
    if (timer != null && timer!.isActive) {
      timer!.cancel();
      print('Delayed action canceled');
    }
  }

  Future<void> getProvisionData() async {
    apiStatus = ApiStatus.loading;
    initializeTimer();
    currentPageIndex=0;
    paginationHelper.initializePagination();
    state.provisionController.update();
    provisionedRequestParams = ProvisionedRequestParams(
        vendorCode: selectedVendorCode,
        pageOffset: paginationHelper.pageOffset,
        perPageLimit: AppStringConstants.provisionPrePageLimit,
        isEnabled: selectedAutoJoin,
        status: selectedStatus,
        search: searchController.text.isNotEmpty ? searchController.text : null);
    listProvisionedDeviceItem = await state.provisionController
        .getProvisionedDeviceList(context: state.context, requestParams: provisionedRequestParams);
    if (listProvisionedDeviceItem != null) {
      if (state.mounted) {
        provisionedDeviceDataSource =
            dataSource(listProvisionedDeviceItem!.result);
        bool hasMoreData = listProvisionedDeviceItem!.result.length == AppStringConstants.provisionPrePageLimit;
        paginationHelper.updatePagination(listProvisionedDeviceItem!.result.length, hasMore: hasMoreData,pageLimit: AppStringConstants.provisionPrePageLimit);
      }
    }else{
      errorMessage= S.of(state.context).somethingWentWrong;
      provisionedDeviceDataSource =
          dataSource(<ProvisioningDeviceItem>[]);
    }
    provisionUpdateTime = DateTime.now();
    apiStatus = ApiStatus.success;
    getDifferenceTime();
    state.provisionController.update();
  }
  getAmpProviderList() async {
    ampProviderStatus = ApiStatus.loading;
    state.provisionController.update();
    List<AmplifierProviderItem>? ampProviderList  = await state.provisionController.getAmpProviderList(context: state.context);
    vendorCodeList = [
     AppStringConstants.all, // Index 0, default label
      ...?ampProviderList
          ?.where((e) => e.code != null && e.code!.isNotEmpty)
          .map((e) => e.code!)
    ];
    ampProviderStatus = ApiStatus.success;
    state.provisionController.update();
  }

  //MANAGE PAGE DATA SOURCE
  ProvisionedDeviceDataSource dataSource(List<ProvisioningDeviceItem> result) {
    return ProvisionedDeviceDataSource(state.context, result, (value) {
      bool isUpdate = true;
      addNewDeviceModalView(state.context, isUpdate,
              (ProvisioningDeviceItem item) => addProvisionDevice(item, isUpdate),
          item: value);
    },(){
      listSelected = listProvisionedDeviceItem!.result
          .where((element) => (element.selected ?? false == true))
          .toList();
    },(ProvisioningDeviceItem itemData,bool isEnabled ) {
        updateAutoJoin(itemData,isEnabled);
    },);
  }

  //GET CURRANT PAGE INDEX
  void getCurrantPageIndex() {
    paginatorController.addListener(() {
      currentPageIndex = (paginatorController.currentRowIndex / AppStringConstants.provisionPrePageLimit).ceil();
      state.provisionController.update();
    });
  }

  Future<void> addProvisionDevice(
      ProvisioningDeviceItem item, bool isUpdate) async {
    goBack();
    apiStatus = ApiStatus.loading;
    searchController.clear();
    state.provisionController.update();
    Map<String, dynamic>? response = {};
    try {
      if (isUpdate) {
        var devEUI = item.deviceEui;
        item.deviceEui = null;
        response = await state.provisionController
            .updateProvisionDevice(itemData: item, context: state.context, devEUI: devEUI);
      } else {
        response = await state.provisionController
            .addProvisionDevice(itemData: item, context: state.context);
      }
      if (response != null && response.isNotEmpty && response['result'] != null) {
        if (state.mounted) {
          response['message'].toString().showSuccess(state.context);
          getProvisionData();
        }
      } else {
        String errorDetail = (response?['detail'] ?? 'Unknown error').toString();
        if (errorDetail.contains('in the join server')) {
          errorDetail = errorDetail.replaceAll(' in the join server', '');
        }
        errorDetail.showError(state.context);
      }
    } catch (e) {
      debugLogs("addProvisionDevice--->$e");
      S.of(state.context).somethingWentWrong.showError(state.context);
    } finally {
      apiStatus = ApiStatus.success;
      state.provisionController.update();
    }
  }

  Future<void> deleteProvisionDevice() async {
    apiStatus = ApiStatus.loading;
    state.provisionController.update();
    searchController.clear();
    for (var element in listSelected) {
      Map<String, dynamic>? success = await state.provisionController
          .deleteProvisionedDevice(deviceEui: element.deviceEui);
      if (success != null && success.isNotEmpty) {
        if (state.mounted) {
          success['message'].toString().showSuccess(state.context);
        }
        getProvisionData();
      }
    }

    apiStatus = ApiStatus.success;
    state.provisionController.update();
  }

  void initializeTimer() {
    differenceTime = null;
    onTapTime = DateTime.now();
    isShowText = true;
    refreshTimer?.cancel();
  }

  getDifferenceTime() {
    differenceTime = DateTime.now().difference(onTapTime!);
    refreshTimer = Timer(const Duration(seconds: 2), () {
      isShowText = false;
      state.provisionController.update();
    });
  }

  Future<void> uploadCsvFiles() async {
    try {
      FilePickerResult? result = (await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['csv'],
      ));
      if (result != null && result.files.isNotEmpty) {
        PlatformFile file = result.files.first;
        apiStatus = ApiStatus.loading;
        state.provisionController.update();
        if (state.mounted) {
          var responseData = await state.provisionController
              .addCsvProvisionedDevices(context: state.context, csvFile: file);
          if (responseData != null && responseData.isNotEmpty) {
            await getProvisionData();
            provisionedDeviceDataSource.notifyListeners();
          } else {
            if (state.mounted) {
              S.of(state.context).uploadFail.showError(state.context);
            }
          }
        }
        apiStatus = ApiStatus.success;
        state.provisionController.update();
      }
    } on PlatformException catch (e) {
      debugLogs('Unsupported operation$e');
    } catch (e) {
      debugLogs(e.toString());
    }
  }
  Future<void> uploadJoinServerCsvFiles() async {
    try {
      FilePickerResult? result = (await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['csv'],
      ));
      if (result != null && result.files.isNotEmpty) {
        PlatformFile file = result.files.first;
        apiStatus = ApiStatus.loading;
        state.provisionController.update();
        if (state.mounted) {
          final responseData = await state.provisionController
              .addCsvJoinServerDevices(context: state.context, csvFile: file);
          if (responseData != null && responseData.isNotEmpty) {
            await getProvisionData();
            provisionedDeviceDataSource.notifyListeners();
          } else {
            if (state.mounted) {
              S.of(state.context).uploadFail.showError(state.context);
            }
          }
        }
        apiStatus = ApiStatus.success;
        state.provisionController.update();
      }
    } on PlatformException catch (e) {
      debugLogs('Unsupported operation$e');
    } catch (e) {
      debugLogs(e.toString());
    }
  }

  Future<void> provisionSelectedDevice() async {
    listSelected.forEach((element) async {
      ProvisioningDeviceItem item = ProvisioningDeviceItem(
        deviceEui: element.deviceEui,
      );
      http.Response? response = await state.provisionController
          .updateProvisionDevice(
              itemData: item,
              context: state.context,
              devEUI: element.deviceEui);
      if (response != null) {
        if (response.statusCode == 200) {
          if (state.mounted) {
            //response['message'].toString().showSuccess(state.context);
            "Device ${element.deviceEui} provisioned".showSuccess(state.context);
            getProvisionData();
          }
        } else if (response.statusCode == 404) {
          if (state.mounted) {
            // show alert dialog that device key is not provisioned
            debugPrint("Device key is not provisioned");
            "Error AppKey missing: ${element.deviceEui}".showError(state.context);
          }
        } else {
          if (state.mounted) {
            // show alert dialog that device key is not provisioned
            debugPrint("response is null");
            "Error provisioning device: ${element.deviceEui}".showError(state.context);
          }
        }
      }
    });
    isShowDiscovered = false;
    getProvisionData();
    state.provisionController.update();
  }

  Future<void> updateAutoJoin(ProvisioningDeviceItem deviceItem, bool isEnabled) async {
    deviceItem.updateApiStatus = ApiStatus.loading;
    state.provisionController.update();
    http.Response? response = await state.provisionController.updateProvisionAutoJoinDevice(
        isEnabled: isEnabled, context: state.context, devEUI: deviceItem.deviceEui);
    try {
      if (response != null) {
        if (response.statusCode == 200) {
          if (state.mounted) {
            "Device ${deviceItem.deviceEui} update successfully".showSuccess(state.context);
            fetchAndUpdateProvisionedDevices();
          }
        } else {
          if (state.mounted) {
            final dynamic responseData = json.decode(response.body);
            String errorMessage;
            if (responseData is Map<String, dynamic> && responseData.containsKey('detail')) {
              errorMessage = responseData['detail'].toString();
            } else {
              errorMessage = responseData.toString();
            }
            errorMessage.showError(state.context);
          }
        }
      } else {
        S.of(state.context).somethingWentWrong.showError(state.context);
      }
    } catch (e) {
      response?.body.toString().showError(state.context);
    } finally {
      deviceItem.updateApiStatus = ApiStatus.success;
      provisionedDeviceDataSource.notifyListeners();
      state.provisionController.update();
    }
  }

  Future<void> fetchAndUpdateProvisionedDevices() async {
    try {
      apiStatus = ApiStatus.loading;
      initializeTimer();
      timer?.cancel();
      state.provisionController.update();
      ProvisioningDeviceList ? provisioningDeviceList = ProvisioningDeviceList.empty();
      provisionedRequestParams = ProvisionedRequestParams(
          vendorCode: selectedVendorCode,
          pageOffset: paginationHelper.pageOffset,
          perPageLimit: AppStringConstants.provisionPrePageLimit,
          isEnabled: selectedAutoJoin,
          status: selectedStatus,
          search: searchController.text.isNotEmpty ? searchController.text : null);
       provisioningDeviceList = await state.provisionController.getProvisionedDeviceList(
        context: state.context,
        requestParams: provisionedRequestParams);
      if (provisioningDeviceList == null) {
        errorMessage = S.of(state.context).somethingWentWrong;
      }
      ProvisioningDeviceList newItems = provisioningDeviceList  ?? ProvisioningDeviceList.empty();
      final startIndex = paginationHelper.pageOffset ;
      if (listProvisionedDeviceItem!.result.length < startIndex) {
        listProvisionedDeviceItem!.result.length = startIndex;
      }
      // Replace or add new items into the list at correct positions
      for (int i = 0; i < newItems.result.length; i++) {
        final currentIndex = startIndex + i;
        if (currentIndex < listProvisionedDeviceItem!.result.length) {
          listProvisionedDeviceItem!.result[currentIndex] = newItems.result[i];
        }
      }
      apiStatus = ApiStatus.success;
      state.provisionController.update();
    } catch (e) {
      apiStatus = ApiStatus.success;
      e.toString().showError(state.context);
      state.provisionController.update();
    }
  }

  Future<void> loadPreviousLogs(BuildContext context) async {
    if(paginationHelper.canGoToPreviousPage){
      paginationHelper.setPage(paginationHelper.currentPage - 1);
      if(isTableView) {
        paginatorController.goToPreviousPage();
      }
    }
   state.provisionController.update();
  }

  Future<void> loadNextLogs(BuildContext context) async {
    if (paginationHelper.canGoToNextPage) {
      paginationHelper.setPage(paginationHelper.currentPage + 1);
      if (paginationHelper.currentPage >= paginationHelper.totalPages) {
        paginationHelper.pageOffset = listProvisionedDeviceItem!.result.length;
        await updateProvisionPageData();
      } else {
        if(isTableView) {
          paginatorController.goToNextPage();
        }
       state.provisionController.update();
      }
    }
  }

  updateProvisionPageData() async {
    apiStatus = ApiStatus.loading;
    initializeTimer();
    timer?.cancel();
    state.provisionController.update();
    ProvisioningDeviceList? provisioningDeviceList = ProvisioningDeviceList.empty();
    provisionedRequestParams = ProvisionedRequestParams(
        vendorCode: selectedVendorCode,
        pageOffset: paginationHelper.pageOffset,
        perPageLimit: AppStringConstants.provisionPrePageLimit,
        isEnabled: selectedAutoJoin,
        status: selectedStatus,
        search: searchController.text.isNotEmpty ? searchController.text : null);
    provisioningDeviceList = await state.provisionController.getProvisionedDeviceList(
        context: state.context,
       requestParams: provisionedRequestParams);
    if (provisioningDeviceList != null  && provisioningDeviceList.result.isNotEmpty) {
      listProvisionedDeviceItem!.result.addAll(provisioningDeviceList.result);
      provisionedDeviceDataSource =
          dataSource(listProvisionedDeviceItem!.result);
      bool hasMoreData = provisioningDeviceList.result.length == AppStringConstants.provisionPrePageLimit;
      paginationHelper.updatePagination(listProvisionedDeviceItem!.result.length, hasMore: hasMoreData,pageLimit: AppStringConstants.provisionPrePageLimit);
    } else {
      if(provisioningDeviceList ==null){
        errorMessage = S.of(state.context).somethingWentWrong;
      }
      provisioningDeviceList ?? ProvisioningDeviceList.empty();
      paginationHelper.setPage(paginationHelper.currentPage - 1);
      paginationHelper.updatePagination(listProvisionedDeviceItem!.result.length, hasMore: false,pageLimit: AppStringConstants.provisionPrePageLimit);
      Future.delayed(const Duration(milliseconds: 100)).then((value) {
        if (isTableView) {
          if  (provisioningDeviceList != null && provisioningDeviceList.result.isNotEmpty  ) paginatorController.goToLastPage();
        }
        provisionedDeviceDataSource.notifyListeners();
      });
    }
    getDifferenceTime();
    provisionUpdateTime = DateTime.now();
    apiStatus = ApiStatus.success;
   state.provisionController.update();
  }
}
