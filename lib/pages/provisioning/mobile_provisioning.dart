import 'package:quantumlink_node/app_import.dart';

class MobileProvisioning {
  Widget buildProvisionList(BuildContext context, ProvisioningPageHelper provisioningPageHelper) {
    if (provisioningPageHelper.provisionedDeviceDataSource.rowCount == 0) {
      return SizedBox(
        height: 350,
        child: provisioningPageHelper.dataTableHelper.getEmptyTableContent(context),
      );
    }
    List<ProvisioningDeviceItem> fullList = provisioningPageHelper.provisionedDeviceDataSource.list;
    List<ProvisioningDeviceItem> paginatedList = fullList
        .skip(provisioningPageHelper.paginationHelper.currentPage *
        AppStringConstants.provisionPrePageLimit)
        .take(AppStringConstants.provisionPrePageLimit)
        .toList();
    return ListView.builder(
      shrinkWrap: true,
      itemCount: paginatedList.length,
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        ProvisioningDeviceItem provisioningDeviceItem = paginatedList[index];
        DataRow dataRow = provisioningPageHelper.provisionedDeviceDataSource.getRow(index,provisioningDeviceItem);
        String type = provisioningDeviceItem.type ?? "";
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 3, horizontal: 5),
          child: CustomListTile(
            onTap: () {
              provisioningPageHelper.provisionedDeviceDataSource.getRow(index).onTap!();
            },
            index: index,
            titleWidget: Wrap(
              alignment: WrapAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    dataRow.cells[0].child,
                    AppText(
                      isSelectableText: false,
                      type,
                      style: TextStyle(
                          fontFamily: AppAssetsConstants.roboto,
                          fontSize: getSize(13),
                          fontWeight: FontWeight.w400),
                    )
                  ],
                ),
                Row(mainAxisSize: MainAxisSize.min,
                  children: [
                    dataRow.cells[4].child,
                    dataRow.cells[3].child,
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget selectTableTypeButtonView(ProvisioningPageHelper? provisioningPageHelper) {
    return provisioningPageHelper?.dataTableHelper.selectTableTypeButtonView(
      isTableType: provisioningPageHelper.isTableView,
      onPressed: () {
        provisioningPageHelper.isTableView = !provisioningPageHelper.isTableView;
        provisioningPageHelper.state.provisionController.update();
      },
    );
  }

  void autoSelectTableType(ProvisioningPageHelper? provisioningPageHelper) {
    if (provisioningPageHelper?.previousLayoutType != null) {
      ScreenLayoutType currentLayoutType = provisioningPageHelper!.screenLayoutType;
      bool isMobile = currentLayoutType != ScreenLayoutType.desktop;
      if (provisioningPageHelper.previousLayoutType != currentLayoutType) {
        provisioningPageHelper.isTableView = !isMobile;
        provisioningPageHelper.previousLayoutType = currentLayoutType;
      }
    }
  }
}
