// ignore_for_file: deprecated_member_use

import 'package:flutter/cupertino.dart';
import 'package:quantumlink_node/app_import.dart';


class ProvisionedDeviceDataSource extends DataTableSource {
  int selectedCount = 0;
  ProvisionController provisionController = Get.find<ProvisionController>();
 Function onSelectChanged;
  ProvisionedDeviceDataSource.empty(this.context, this.list, this.onEdit, this.onSelectChanged,this.switchOnChanged);

  ProvisionedDeviceDataSource(this.context, this.list, this.onEdit,this.onSelectChanged,this.switchOnChanged,
      [sortedByEUI = false,
      this.hasRowHeightOverrides = false,
      this.hasZebraStripes = false]) {
    if (sortedByEUI) {
      sort((d) => d.deviceEui, true);
    }
  }

  final BuildContext context;
  final List<ProvisioningDeviceItem> list;
  final Function(ProvisioningDeviceItem) onEdit;
  final Function(ProvisioningDeviceItem , bool) switchOnChanged;
  // Override height values for certain rows
  bool hasRowHeightOverrides = false;


  // Color each Row by index's parity
  bool hasZebraStripes = false;

  void sort<T>(Comparable<T> Function(ProvisioningDeviceItem d) getField, bool ascending) {
    list.sort((a, b) {
      final aValue = getField(a);
      final bValue = getField(b);
      return ascending ? Comparable.compare(aValue, bValue) : Comparable.compare(bValue, aValue);
    });
    notifyListeners();
  }
  DataTableHelper dataTableHelper = DataTableHelper();

  @override
  DataRow2 getRow(int index, [ProvisioningDeviceItem? provisioningDeviceItem]) {
    assert(index >= 0);
    if (index >= list.length) throw 'index > _desserts.length';
    final dessert = provisioningDeviceItem ?? list[index];
    DetectedStatusType? detectedStatusType = getDetectedStatusType(dessert.status);
    String vendor= dessert.vendorCode ?? "";
    bool isAutoJoin= dessert.isEnabled ?? false;
    return DataRow2.byIndex(
      index: index,
      selected: dessert.selected ?? false,
      color:  index.isEven
          ? MaterialStateProperty.all(AppColorConstants.colorWhite)
          : MaterialStateProperty.all(AppColorConstants.colorBackgroundDark),
      cells: [
        DataCell(AppText(dessert.deviceEui ?? "",
            style: dataTableHelper.dataRowTextStyle)),
        DataCell(AppText(vendor,
            style: dataTableHelper.dataRowTextStyle)),
        DataCell(AppText(dessert.type ?? "",
            style: dataTableHelper.dataRowTextStyle)),
        DataCell(Center(
          child: Container(
            alignment: Alignment.center,
            height: getSize(30),
            width:  (detectedStatusType == DetectedStatusType.missingKey || detectedStatusType == DetectedStatusType.missingVendor) ?  getSize(120): getSize(70),
            decoration: BoxDecoration(
                color: detectedStatusType == DetectedStatusType.online
                    ? AppColorConstants.colorGreen2
                    : detectedStatusType == DetectedStatusType.offline
                    ? AppColorConstants.colorH2.withOpacity(0.4)
                    : (detectedStatusType == DetectedStatusType.pending)
                    ? AppColorConstants.colorOrange
                    : AppColorConstants.colorH2.withOpacity(0.5),
                borderRadius: BorderRadius.circular(getSize(8))),
            child: AppText(
              detectedStatusType == DetectedStatusType.online
                  ? S.of(context).online
                  : detectedStatusType == DetectedStatusType.offline
                  ? S.of(context).offline
                  : detectedStatusType == DetectedStatusType.pending
                  ? S.of(context).pending
                  : detectedStatusType == DetectedStatusType.missingKey
                  ? S.of(context).missingKey
                  : detectedStatusType == DetectedStatusType.missingVendor
                  ? S.of(context).missingVendor
                  : S.of(context).offline,
              style: TextStyle(
                  color: (detectedStatusType == DetectedStatusType.offline ||
                          detectedStatusType == DetectedStatusType.missingKey ||
                          detectedStatusType == DetectedStatusType.missingVendor ||
                          dessert.status == null)
                      ? AppColorConstants.colorH3
                      : AppColorConstants.colorWhite,
                  fontFamily: AppAssetsConstants.sourceSans,
                  fontSize: 13,
                  fontWeight: FontWeight.w600),
            ),
          ),
        )),
        // DataCell(Container(
        //   height: 15,width: 15,decoration: BoxDecoration(color:
        //   getStateColor(dessert.state ?? ""),shape: BoxShape.rectangle,borderRadius: BorderRadius.circular(3)),
        // )),
        DataCell(Row(
          children: [
            Transform.scale(
              scale: 0.8,
              child: CupertinoSwitch(
                  activeColor: AppColorConstants.colorPrimary,
                  trackColor: AppColorConstants.colorH2,
                  value: isAutoJoin,
                  onChanged: (onChangeValue) async {
                    if(dessert.updateApiStatus == ApiStatus.loading) return;
                    dessert.isEnabled = onChangeValue;
                    notifyListeners(); // Ensure DataTable gets updated
                    switchOnChanged(dessert,onChangeValue);
                  }),
            ),
            SizedBox(height: 50,width: 40,child: dessert.updateApiStatus == ApiStatus.loading ?const AppLoader() :const SizedBox(),)
          ],
        )),
        DataCell(AppText(
          dessert.lastSeen != null ? getUtcTimeZone(dessert.lastSeen) : '',
          style: dataTableHelper.dataRowTextStyle,
        )),

      ],
    );
  }

  @override
  int get rowCount => list.length;

  @override
  bool get isRowCountApproximate => false;

  @override
  int get selectedRowCount => selectedCount;

  void selectAll(bool? checked) {
    for (final dessert in list) {
      dessert.selected = checked ?? false;
    }
    selectedCount = (checked ?? false) ? list.length : 0;
    provisionController.update();
    notifyListeners();
  }
  Color getStateColor(String? state) {
    switch (state!.toUpperCase()) {
      case 'JOINED':
        return  AppColorConstants.colorJoined;
      case 'PROVISIONED':
        return  AppColorConstants.colorProvisioned;
      case 'NOT_YET_JOINED':
        return  AppColorConstants.colorNotYetJoined;
      case 'DISCOVERED':
        return AppColorConstants.colorDiscovered;
      default:
        return AppColorConstants.colorRedDark;
    }
  }
}
