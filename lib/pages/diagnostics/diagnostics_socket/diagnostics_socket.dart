import 'package:quantumlink_node/app_import.dart';
import '../diagnostics_page_helper.dart';
import 'mobile_diagnostics_socket.dart';

class DiagnosticsSocketPage extends StatefulWidget {
 final DiagnosticsPageHelper diagnosticsPageHelper;
  const DiagnosticsSocketPage(this.diagnosticsPageHelper ,{super.key});

  @override
  State<DiagnosticsSocketPage> createState() => DiagnosticsSocketPageState();
}

class DiagnosticsSocketPageState extends State<DiagnosticsSocketPage> {
  late DiagnosticsPageHelper diagnosticsPageHelper;
  late DiagnosticsController diagnosticsController;
  late ScreenLayoutType screenLayoutType;
  bool _isTimeout = false;
  Timer? _timeoutTimer;
  @override
  void initState() {
    super.initState();
    diagnosticsPageHelper = widget.diagnosticsPageHelper;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      diagnosticsPageHelper.getWayEuiIdList();
    });
    _startTimeoutWatcher();
  }

  @override
  void dispose() {
    diagnosticsPageHelper.socketHelper.diagnosticsOnDispose();
    super.dispose();
  }
  void _startTimeoutWatcher() {
    _timeoutTimer?.cancel();
    _timeoutTimer = Timer(const Duration(seconds: 10), () {
      _isTimeout = true;
      diagnosticsController.update();
    });
  }


  @override
  Widget build(BuildContext context) {
    return GetBuilder<DiagnosticsController>(
      init: DiagnosticsController(),
      builder: (DiagnosticsController controller) {
        diagnosticsController = controller;
        return Stack(
          children: [
            getBodyView(),
          ],
        );
      },
    );
  }

  getBodyView() {
    return ScreenLayoutTypeBuilder(
      builder: (context, screenType, constraints) {
        screenLayoutType = screenType;
        return getDiagnosticsBoardView();
      },
    );
  }

  getDiagnosticsBoardView() {
    return Container(
      color: AppColorConstants.colorWhite,
      width: double.infinity,
      child: ListView(
        physics: const ClampingScrollPhysics(),
        padding: EdgeInsets.symmetric(horizontal: 10),
        children: [
          getPageAppBar(),
          diagnosticsBoardView(),
          buildLastUpdateTimeWithRefreshView()
        ],
      ),
    );
  }

  getPageAppBar() {
    return Container(margin:const EdgeInsets.symmetric(horizontal: 5),
      alignment: Alignment.centerLeft,
      decoration: BoxDecoration(borderRadius: BorderRadius.only(
          topRight: Radius.circular(getSize(7)), topLeft: Radius.circular(getSize(7))),border:   Border.all(
        color: AppColorConstants.colorH2,
      ),
          color: AppColorConstants.colorWhite),
      padding: EdgeInsets.only(left: getSize(18)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: getSize(8)),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                flex: 3,
                child: Obx(() {
                  return SizedBox(
                          height: 40,
                          child: ListView.builder(
                              itemCount: diagnosticsPageHelper
                                  .selectedListDeviceEUI.length,
                              scrollDirection: Axis.horizontal,
                              shrinkWrap: true,
                              itemBuilder: (context, index) {
                                return Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 4.0),
                                  child: Chip(
                                    backgroundColor: Colors.white,
                                    label: Text(diagnosticsPageHelper
                                        .selectedListDeviceEUI[index]),
                                    deleteIcon: CircleAvatar(
                                        maxRadius: 10,
                                        backgroundColor:
                                            AppColorConstants.colorLightBlue,
                                        child: Icon(Icons.close,
                                            size: getSize(16),
                                            color:
                                                AppColorConstants.colorWhite)),
                                    onDeleted: () {
                                      if (!diagnosticsPageHelper.isTableView) {
                                        diagnosticsPageHelper.paginationHelper.currentPage = 0;
                                      } else {
                                        diagnosticsPageHelper.pageController.goToFirstPage();
                                      }
                                      diagnosticsPageHelper.selectDevEUI(
                                          diagnosticsPageHelper.selectedListDeviceEUI[index],
                                          false);
                                    },
                                  ),
                                );
                              }),
                        );
                }),
              ),
              if (screenLayoutType == ScreenLayoutType.desktop)
               SizedBox(width: MediaQuery.of(context).size.width*0.3,child:  searchAutoTextView())
            ],
          ),
          if (screenLayoutType != ScreenLayoutType.desktop)
            searchAutoTextView(),
          SizedBox(height: getSize(8)),
        ],
      ),
    );
  }

  searchAutoTextView() {
    TextEditingController textEditingController = TextEditingController();
    return Row(mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          height: 40,
          width: 50,
          child: StreamBuilder(
            stream: diagnosticsPageHelper.isStreamLoading,
            builder: (context, snapshot) {
              if (snapshot.data != null && snapshot.data == true) {
                return const AppLoader();
              } else {
                return Container();
              }
            },
          ),
        ),
        Expanded(
            child: Container(
          height: 40,
          child: AppTextFormField(
            controller: textEditingController,
            onFieldSubmitted: (value) {
              if (value.isEmpty) return;
              if (!diagnosticsPageHelper.isTableView) {
                diagnosticsPageHelper.paginationHelper.currentPage = 0;
              } else {
                diagnosticsPageHelper.pageController.goToFirstPage();
              }
              diagnosticsPageHelper.selectDevEUI(value, true);
              textEditingController.clear();
            },
            hintText: S.of(context).search,
            contentPadding: const EdgeInsets.symmetric(vertical: 0, horizontal: 20),
            focusedBorderColor: AppColorConstants.colorPrimary,
            enabledBorderColor: AppColorConstants.colorBlackBlue,
            maxLines: 1,
            textInputType: TextInputType.text,
            borderRadius: getSize(8),
            hintTextColor: AppColorConstants.colorDarkBlue,
            suffixIcon: const Padding(
              padding: EdgeInsets.all(10),
              child: AppImageAsset(image: AppAssetsConstants.searchIcon),
            ),
            hintFontSize: 17,
            fontFamily: AppAssetsConstants.openSans,
            fontWeight: FontWeight.w400,
          ),
        )),
        MobileDiagnosticsSocket().selectTableTypeButtonView(diagnosticsPageHelper),
        // Expanded(
        //   child: SizedBox(height: 40,
        //     child: Padding(
        //       padding: const EdgeInsets.only(right: 12),
        //       child: Autocomplete<String>(
        //         fieldViewBuilder:
        //             ((context, textController, focusNode, onFieldSubmitted) {
        //           textEditingController = textController;
        //           return Container(
        //             decoration: BoxDecoration(
        //                 border: Border.all(color: AppColorConstants.colorH2),
        //                 color: AppColorConstants.colorWhite,
        //                 borderRadius: BorderRadius.circular(getSize(8))),
        //             child: AppTextFormField(
        //               controller: textEditingController,
        //               focusNode: focusNode,
        //               onFieldSubmitted: (value) => onFieldSubmitted,
        //               hintText: S.of(context).search,
        //               contentPadding:
        //                   const EdgeInsets.symmetric(vertical: 0, horizontal: 20),
        //               focusedBorderColor: AppColorConstants.colorPrimary,
        //               enabledBorderColor: AppColorConstants.colorWhite1,
        //               maxLines: 1,
        //               textInputType: TextInputType.text,
        //               borderRadius: getSize(8),
        //               hintTextColor: AppColorConstants.colorDarkBlue,
        //               suffixIcon: const Padding(
        //                 padding: EdgeInsets.all(10),
        //                 child: AppImageAsset(image: AppAssetsConstants.searchIcon),
        //               ),
        //               hintFontSize: 17,
        //               fontFamily: AppAssetsConstants.openSans,
        //               fontWeight: FontWeight.w400,
        //             ),
        //           );
        //         }),
        //         optionsBuilder: (TextEditingValue textEditingValue) {
        //           if (textEditingValue.text == '') {
        //             return const Iterable<String>.empty();
        //           }
        //           final availableOptions = diagnosticsPageHelper.listDeviceEUI
        //               .where((option) => !diagnosticsPageHelper.selectedListDeviceEUI
        //                   .contains(option))
        //               .where((option) => option
        //                   .toLowerCase()
        //                   .contains(textEditingValue.text.toLowerCase()));
        //
        //           return availableOptions;
        //         },
        //         optionsViewBuilder: ((context, onSelected, options) => Align(
        //             alignment: Alignment.topLeft,
        //             child: ConstrainedBox(
        //               constraints: const BoxConstraints(maxWidth: 300),
        //               child: ListView.separated(
        //                 shrinkWrap: true,
        //                 padding: const EdgeInsets.all(8.0),
        //                 itemCount: options.length,
        //                 separatorBuilder: (context, i) {
        //                   return Divider(
        //                     color: AppColorConstants.colorDivider,
        //                     height: 1,
        //                   );
        //                 },
        //                 itemBuilder: (BuildContext context, int index) {
        //                   return InkWell(
        //                     onTap: () => onSelected(options.elementAt(index)),
        //                     child: Container(
        //                       decoration: BoxDecoration(
        //                           color: AppColorConstants.colorGreyWhite,
        //                           borderRadius: BorderRadius.circular(getSize(4)),
        //                           border: Border.all(
        //                               color: AppColorConstants.colorTableBroader)),
        //                       padding: const EdgeInsets.all(6),
        //                       child: Text(
        //                         options.elementAt(index),
        //                         style: TextStyle(
        //                           fontWeight: getMediumFontWeight(),
        //                           fontFamily: AppAssetsConstants.notoSans,
        //                           color: AppColorConstants.colorBlack,
        //                           fontSize: getSize(14),
        //                         ),
        //                       ),
        //                     ),
        //                   );
        //                 },
        //               ),
        //             ))),
        //         onSelected: (String selection) {
        //           print("onSelected");
        //           diagnosticsPageHelper.selectDevEUI(selection, true);
        //           textEditingController.clear();
        //         },
        //       ),
        //     ),
        //   ),
        // ),
      ],
    );
  }
  buildLastUpdateTimeWithRefreshView(){
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        getTimeDurationView(
          onTapTime: DateTime.now(),
          refreshStatus: diagnosticsPageHelper.apiStatus,
          updateTime: diagnosticsPageHelper.lastUpdateTime,),
      AppRefresh(onPressed: () {
        _isTimeout=false;
        _startTimeoutWatcher();
        diagnosticsPageHelper.getWayEuiIdList();
      },loadingStatus:diagnosticsPageHelper.apiStatus),
    ],);
  }

  diagnosticsBoardView() {
    if ((diagnosticsPageHelper.vlgWs.result.isEmpty && diagnosticsPageHelper.gwIDFormWebHost.isEmpty) &&
        diagnosticsPageHelper.apiStatus == ApiStatus.success) {
      return Container(
        margin:const EdgeInsets.symmetric(horizontal: 5),
        alignment: Alignment.center,
        height: 400,
        decoration: diagnosticsPageHelper.dataTableHelper.tableBorderDeco(),
        width: double.infinity,
        child: diagnosticsPageHelper.dataTableHelper.getEmptyTableContent(context),
      );
    }
    return StreamBuilder<dynamic>(
      stream: diagnosticsPageHelper.socketHelper.diagnosticsStreamView,
      builder: (context, snapshot) {
        if ((snapshot.connectionState == ConnectionState.waiting ||
            diagnosticsPageHelper.apiStatus == ApiStatus.loading )&&  !_isTimeout) {
          return Container(
              margin:const EdgeInsets.symmetric(horizontal: 5),
              alignment: Alignment.center,
              height: 400,
              decoration: BoxDecoration(
                border: Border.all(color: AppColorConstants.colorH2),
                color: AppColorConstants.colorWhite
              ),
              //decoration:diagnosticsPageHelper.dataTableHelper.tableBorderDeco() ,
              width: double.infinity,
              child: const AppLoader());
        } else if (snapshot.connectionState == ConnectionState.active) {
          if (snapshot.hasData) {
            diagnosticsPageHelper.isStreamLoading.add(true);
            // diagnosticsPageHelper.handleWebSocketMessage(snapshot.data);
          } else {
            diagnosticsPageHelper.isStreamLoading.add(false);
          }
        }else if (snapshot.connectionState == ConnectionState.waiting && _isTimeout) {
          return _buildTimeoutMessage();
        }
        return Container(
            margin:const EdgeInsets.symmetric(horizontal: 5),
            height: (!diagnosticsPageHelper.isTableView) ? null :diagnosticsPageHelper.tableHeight,
            width: double.infinity,
            decoration: diagnosticsPageHelper.dataTableHelper.tableBorderDeco(),
            child: getDiagnosticsDataTableView());
      },
    );
  }

  getDiagnosticsDataTableView() {
    if (!diagnosticsPageHelper.isTableView) {
      return  MobileDiagnosticsSocket()
          .buildDiagnosticsList(context, diagnosticsPageHelper);
    }
    return PaginatedDataTable2(
      isVerticalScrollBarVisible: false,
      columnSpacing: 8,
      showCheckboxColumn: false,
      headingTextStyle:
          diagnosticsPageHelper.dataTableHelper.headingTextStyle(),
      wrapInCard: false,
     border: diagnosticsPageHelper.dataTableHelper.tableBorder(),
      renderEmptyRowsInTheEnd: false,
      dividerThickness: 0.3,
      //ignore: deprecated_member_use
      headingRowColor: diagnosticsPageHelper.dataTableHelper.headingRowColor(),
      columns: [
        DataColumn2(
          fixedWidth: 180,
          label: AppText(S.of(context).timestamp),
        ),
        DataColumn2(
          fixedWidth: 250,
          label: AppText(S.of(context).description),
        ),
        DataColumn2(
          fixedWidth: 150,
          label: AppText(S.of(context).devAddress),
        ),
        DataColumn2(
          fixedWidth: 170,
          label: AppText(S.of(context).devEUI),
        ),
        DataColumn2(
          fixedWidth: 200,
          label: AppText(S.of(context).gatWayId),
        ),
        DataColumn2(fixedWidth: 100,
          label: AppText(S.of(context).rssi+"\n(${S.of(context).dBm})"),
        ),
        DataColumn2(fixedWidth: 100,
          label: AppText(S.of(context).fPort),
        ),
        DataColumn2(fixedWidth: 150,
          label: AppText(S.of(context).frequency),
        ),
        const DataColumn2(
          label: SizedBox(),
        ),
      ],
      controller: diagnosticsPageHelper.pageController,
      source: diagnosticsPageHelper.diagnosticsDataSource,
      minWidth: 1350,
      dataRowHeight: 55,
      // For progress indicator
      hidePaginator: false,
      empty:
          diagnosticsPageHelper.dataTableHelper.getEmptyTableContent(context),
    );
  }
  Widget _buildTimeoutMessage() {
    return Card(
      elevation: 10,
      margin: EdgeInsets.only(left: getSize(5), right: getSize(5),bottom: getSize(5)),
      child: Container(
        alignment: Alignment.center,
        height: 400,
        decoration: BoxDecoration(
            color:  AppColorConstants.colorWhite,
            border: Border.all(color: AppColorConstants.colorH2)
        ),
        width: double.infinity,
        child:  Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const AppImageAsset(
              image: AppAssetsConstants.emptyLogo,
              height: 50,
            ),
            AppText(
              S.of(context).socketTimeOutMessage,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 16, color: Colors.black87),
            ),
          ],
        ),
      ),
    );
  }
}
