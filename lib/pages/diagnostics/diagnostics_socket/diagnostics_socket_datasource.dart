// ignore_for_file: deprecated_member_use

import 'package:quantumlink_node/app_import.dart';

class DiagnosticsDataSource extends DataTableSource {
  DiagnosticsDataSource.empty(this.context, this.diagnosticsList ,this.onTap);

  DiagnosticsDataSource(this.context, this.diagnosticsList ,this.onTap,[sortedByEUI = false]) {
    if (sortedByEUI) {}
  }

  final BuildContext context;
  final List<DiagnosticsDataModel> diagnosticsList;
  final Function(DiagnosticsDataModel) onTap;
  DataTableHelper dataTableHelper = DataTableHelper();
  void sort<T>(Comparable<T> Function(DiagnosticsDataModel d) getField, bool ascending) {
    diagnosticsList.sort((a, b) {
      final aValue = getField(a);
      final bValue = getField(b);
      return ascending ? Comparable.compare(aValue, bValue) : Comparable.compare(bValue, aValue);
    });
    notifyListeners();
  }

  @override
  DataRow2 getRow(int index, [Color? color]) {
    assert(index >= 0);
    if (index >= diagnosticsList.length) throw 'index > _desserts.length';
    final dessert = diagnosticsList[index];
    dynamic fPort = (dessert.body?.phyPayload?.payload is Map<String, dynamic> &&
        dessert.body?.phyPayload?.payload.containsKey('f_port'))
        ? (dessert.body?.phyPayload?.payload['f_port'] ?? "").toString()
        : "";
    String rssi = (dessert.body?.rxInfo?.first.rssi ?? "").toString();
    String description = dessert.description ?? "";
    String devAddress = (dessert.properties?.devAddr ?? "").toString();
    String devEUI = (dessert.properties?.devEUI ?? "").toString();
    String gatewayID = (dessert.properties?.gatewayID ?? "").toString();
    num frequency = dessert.body?.txInfo?.frequency ?? 0;
    String frequencyMHZ = (frequency/1000000).toStringAsFixed(2);//Converts Hz to MHz.

    bool isDown = description.toLowerCase().contains("datadown");
    bool isUp = description.toLowerCase().contains("dataup");
    return DataRow2.byIndex(
      index: index,
      color: index.isEven
          ? MaterialStateProperty.all(AppColorConstants.colorWhite)
          : MaterialStateProperty.all(AppColorConstants.colorBackgroundDark),
      cells: [
        DataCell(AppText(dessert.time != null ? getUtcTimeZone(dessert.time!,dateFormat: formatter) : '',
            style: dataTableHelper.dataRowTextStyle)),
        DataCell(onTap: () => onTap(dessert),
          Padding(
            padding: const EdgeInsets.only(top: 12,bottom: 12),
            child: Card(elevation: 5,
              child: IntrinsicWidth(
                child: Container(
                  decoration: BoxDecoration(
                    color: isDown
                        ? AppColorConstants.colorLightBlue.withOpacity(0.2)
                        : isUp
                            ? AppColorConstants.colorShadowGreen
                            : AppColorConstants.colorBlueLight,
                    border: Border.all(
                        color: isDown
                            ? AppColorConstants.colorLightBlue
                            : isUp
                                ? AppColorConstants.colorGreen2
                                : AppColorConstants.colorBlueLight),
                    borderRadius: BorderRadius.circular(getSize(30)),
                  ),
                  alignment: Alignment.centerLeft,
                  child: Row(
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 7, vertical: 1),
                        child: AppImageAsset(
                          image: AppAssetsConstants.searchAddIcon,
                          color: isDown
                              ? AppColorConstants.colorBlueLight
                              : isUp
                                  ? AppColorConstants.colorGreen1
                                  : AppColorConstants.colorWhite,
                        ),
                      ),
                      Flexible(
                        child: Padding(
                          padding: const EdgeInsets.only(right: 10),
                          child: AppText(
                            isSelectableText: false,
                            dessert.description ?? "",
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontFamily: AppAssetsConstants.openSans,
                              color: isDown
                                  ? AppColorConstants.colorBlueLight
                                  : isUp
                                      ? AppColorConstants.colorGreen1
                                      : AppColorConstants.colorWhite,
                              fontSize: getSize(14),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
        DataCell(AppText(devAddress,
            style: dataTableHelper.dataRowTextStyle)),
        DataCell(AppText(devEUI,
            style: dataTableHelper.dataRowTextStyle)),
        DataCell(AppText(gatewayID,
            style: dataTableHelper.dataRowTextStyle)),
        DataCell(AppText(rssi,
            style: dataTableHelper.dataRowTextStyle)),
        DataCell(AppText(fPort.toString(),
            style: dataTableHelper.dataRowTextStyle)),
        DataCell(AppText(frequencyMHZ,
            style: dataTableHelper.dataRowTextStyle)),
        DataCell(Container()),
      ],
    );
  }
  commonIDContainerView({required String ? idValue}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: IntrinsicWidth(
        child: Container(
          alignment: Alignment.center,
          padding: EdgeInsets.symmetric(horizontal: getSize(9)),
          decoration: BoxDecoration(
              color: AppColorConstants.colorGreyWhite,
              borderRadius: BorderRadius.circular(getSize(4)),
              border: Border.all(color: AppColorConstants.colorTableBroader)),
          child: AppText(idValue ?? "",
              style: TextStyle(
                fontWeight: getMediumFontWeight(),
                fontFamily: AppAssetsConstants.notoSans,
                color: AppColorConstants.colorBlack,
                fontSize: getSize(14),
              )),
        ),
      ),
    );
  }


  @override
  int get rowCount => diagnosticsList.length;

  @override
  bool get isRowCountApproximate => false;

  @override
  int get selectedRowCount => 0;
}
