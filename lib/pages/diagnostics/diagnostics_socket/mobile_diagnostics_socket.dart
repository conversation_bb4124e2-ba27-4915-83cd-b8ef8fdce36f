import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/pages/diagnostics/diagnostics_page_helper.dart';

class MobileDiagnosticsSocket {
  DataTableHelper dataTableHelper = DataTableHelper();
  int itemPerPage=10;
  Widget buildDiagnosticsList(BuildContext context, DiagnosticsPageHelper diagnosticsPageHelper) {
    if (diagnosticsPageHelper.diagnosticsDataSource.rowCount == 0) {
      return SizedBox(
        height: 350,
        child: diagnosticsPageHelper.dataTableHelper.getEmptyTableContent(context),
      );
    }
    return StreamBuilder<dynamic>(
      stream: diagnosticsPageHelper.socketHelper.diagnosticsStreamView,
      builder: (context, snapshot) {
        List<DiagnosticsDataModel> fullList =
            diagnosticsPageHelper.diagnosticsDataSource.diagnosticsList;

        int startIndex = diagnosticsPageHelper.paginationHelper.currentPage * itemPerPage;
        int endIndex = startIndex + itemPerPage;
        endIndex = endIndex > fullList.length ? fullList.length : endIndex;
        List<DiagnosticsDataModel> currentPageItems = fullList.sublist(startIndex, endIndex);
        diagnosticsPageHelper.paginationHelper.updatePagination(fullList.length,
            hasMore: currentPageItems.length >= itemPerPage && fullList.length > itemPerPage, pageLimit: itemPerPage);
        return Column(
          children: [
            ListView.builder(
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              itemCount: currentPageItems.length,
              itemBuilder: (context, index) {
              DiagnosticsDataModel diagnosticsDataModel = currentPageItems[index];
              dynamic fPort = (diagnosticsDataModel.body?.phyPayload?.payload is Map<String, dynamic> &&
                  diagnosticsDataModel.body?.phyPayload?.payload.containsKey('f_port'))
                  ? (diagnosticsDataModel.body?.phyPayload?.payload['f_port'] ?? "").toString()
                  : "";
              String rssi = (diagnosticsDataModel.body?.rxInfo?.first.rssi ?? "").toString();
              bool isDown = diagnosticsDataModel.description.toLowerCase().contains("datadown");
              bool isUp = diagnosticsDataModel.description.toLowerCase().contains("dataup");
              String gatewayID = diagnosticsDataModel.properties?.gatewayID ?? '';
              String devAddress = diagnosticsDataModel.properties?.devAddr ?? '';
              num frequency = diagnosticsDataModel.body?.txInfo?.frequency ?? 0;
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 3, horizontal: 5),
                child: CustomListTile(
                  onTap: () {},
                  index: index,
                  titleWidget: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          IntrinsicWidth(
                            child: InkWell(
                              onTap: () {
                                diagnosticsPageHelper.diagnosticsDataSource.onTap(
                                    diagnosticsPageHelper.diagnosticsDataSource.diagnosticsList[index]);
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                  color: isDown
                                      ? AppColorConstants.colorLightBlue.withOpacity(0.2)
                                      : isUp
                                      ? AppColorConstants.colorShadowGreen
                                      : AppColorConstants.colorBlueLight,
                                  border: Border.all(
                                    color: isDown
                                        ? AppColorConstants.colorLightBlue
                                        : isUp
                                        ? AppColorConstants.colorGreen2
                                        : AppColorConstants.colorBlueLight,
                                  ),
                                  borderRadius: BorderRadius.circular(getSize(30)),
                                ),
                                alignment: Alignment.centerLeft,
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.symmetric(horizontal: 7, vertical: 1),
                                      child: AppImageAsset(
                                        image: AppAssetsConstants.searchAddIcon,
                                        width: 20,
                                        height: 20,
                                        color: isDown
                                            ? AppColorConstants.colorBlueLight
                                            : isUp
                                            ? AppColorConstants.colorGreen1
                                            : AppColorConstants.colorWhite,
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.only(right: 10),
                                      child: AppText(
                                        isSelectableText: false,
                                        diagnosticsDataModel.description ?? "",
                                        style: TextStyle(
                                          fontWeight: FontWeight.w500,
                                          fontFamily: AppAssetsConstants.openSans,
                                          color: isDown
                                              ? AppColorConstants.colorBlueLight
                                              : isUp
                                              ? AppColorConstants.colorGreen1
                                              : AppColorConstants.colorWhite,
                                          fontSize: getSize(14),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          const Spacer(),
                          if (rssi.isNotEmpty) titleAndValueView(context, "${S.of(context).rssi} (${S.of(context).dBm})", rssi)
                        ],
                      ),
                      SizedBox(height: getSize(3)),
                      Row(
                        children: [
                          if (diagnosticsDataModel.properties?.devEUI.isNotEmpty ?? false)
                            Padding(
                              padding: const EdgeInsets.only(right: 10.0),
                              child: AppText(diagnosticsDataModel.properties?.devEUI,
                                  style: dataTableHelper.dataRowTextStyle),
                            ),
                          const Spacer(),
                          if (fPort.isNotEmpty) titleAndValueView(context, S.of(context).fPort, fPort)
                        ],
                      ),
                      if (diagnosticsDataModel.properties?.devEUI.isNotEmpty ?? false || rssi.isNotEmpty)
                        SizedBox(height: getSize(3)),
                      Row(
                        children: [
                          if(devAddress.isNotEmpty)
                            AppText(
                                devAddress,
                                style: dataTableHelper.dataRowTextStyle),
                          if (diagnosticsDataModel.time != null) const Spacer(),
                          GestureDetector(
                            onTap: () {
                              _showDialog(
                                context,
                                diagnosticsDataModel.properties?.devEUI ?? '',
                                dialogContent(
                                  context,
                                  fPort: fPort,
                                  frequency: frequency.toString(),
                                  gatWayId: gatewayID,
                                  timeStamp: diagnosticsDataModel.time != null
                                      ? getUtcTimeZone(diagnosticsDataModel.time!, dateFormat: formatter)
                                      : '',
                                  devAddress: devAddress,
                                  rssi: rssi,
                                  description: diagnosticsDataModel.description,
                                ),
                              );
                            },
                            child: const Icon(Icons.info),
                          ),
                        ],
                      ),
                      if(diagnosticsDataModel.time != null)
                        Padding(
                          padding: const EdgeInsets.only(top: 3.0),
                          child: AppText(
                              diagnosticsDataModel.time != null
                                  ? getUtcTimeZone(diagnosticsDataModel.time!, dateFormat: formatter)
                                  : '',
                              style: dataTableHelper.dataRowTextStyle),
                        ),
                    ],
                  ),
                ),
              );
              },
            ),
            customPaginationView(diagnosticsPageHelper)
          ],
        );
      },
    );
  }

  Widget selectTableTypeButtonView(DiagnosticsPageHelper? diagnosticsPageHelper) {
    return diagnosticsPageHelper?.dataTableHelper.selectTableTypeButtonView(
      isTableType: diagnosticsPageHelper.isTableView,
      onPressed: () {
        diagnosticsPageHelper.isTableView = !diagnosticsPageHelper.isTableView;
        diagnosticsPageHelper.state.diagnosticsController.update();
      },
    );
  }

  void autoSelectTableType(DiagnosticsPageHelper? diagnosticsPageHelper) {
    if (diagnosticsPageHelper?.previousLayoutType != null) {
      ScreenLayoutType currentLayoutType = diagnosticsPageHelper!.screenLayoutType;
      bool isMobile = currentLayoutType != ScreenLayoutType.desktop;
      if (diagnosticsPageHelper.previousLayoutType != currentLayoutType) {
        diagnosticsPageHelper.isTableView = !isMobile;
        diagnosticsPageHelper.previousLayoutType = currentLayoutType;
      }
    }
  }

  Widget dialogContent(
    BuildContext context, {
    required String timeStamp,
    required String description,
    required String devAddress,
    required String gatWayId,
    required String rssi,
    required String fPort,
    required String frequency,
  }) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(15.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          mainAxisSize: MainAxisSize.min,
          children: [
            AppText(description, style: dataTableHelper.dataRowTextStyle),
            SizedBox(height: getSize(7),),
            dialogTitleAndValueView(context, S.of(context).timestamp, timeStamp),
            SizedBox(height: getSize(7)),
            dialogTitleAndValueView(context, S.of(context).devAddress, devAddress),
            SizedBox(height: getSize(7)),
            dialogTitleAndValueView(context, S.of(context).gatWayId, gatWayId),
            SizedBox(height: getSize(7)),
            dialogTitleAndValueView(context, S.of(context).rssi+" (${S.of(context).dBm})", rssi),
            SizedBox(height: getSize(7)),
            dialogTitleAndValueView(context, S.of(context).fPort, fPort),
            SizedBox(height: getSize(7)),
            dialogTitleAndValueView(context, S.of(context).frequency, frequency),
          ],
        ),
      ),
    );
  }
  Widget customPaginationView(DiagnosticsPageHelper? provisioningPageHelper){
    if(provisioningPageHelper!.isTableView){
      return SizedBox();
    }
    return  AppPaginationWidget(
      apiStatus: ApiStatus.success,
      paginationHelper: provisioningPageHelper.paginationHelper,
      onLoadNext: () async {
        provisioningPageHelper.paginationHelper.currentPage++;
        provisioningPageHelper.state.diagnosticsController.update();
      },
      onLoadPrevious: () async {
        provisioningPageHelper.paginationHelper.currentPage--;
        provisioningPageHelper.state.diagnosticsController.update();
      },
      onGoToFirstPage: () {
        provisioningPageHelper.paginationHelper.currentPage=0;
        provisioningPageHelper.state.diagnosticsController.update();
      },
      onGoToLastPage: () {
        List<DiagnosticsDataModel> fullList = provisioningPageHelper.diagnosticsDataSource.diagnosticsList;
        int totalPages = (fullList.length / itemPerPage).ceil();
        provisioningPageHelper.paginationHelper.currentPage=totalPages-1;
        provisioningPageHelper.state.diagnosticsController.update();
      },
      onChanged: (value) {

      },
    );
  }

  Widget titleAndValueView(BuildContext context, String title, String value) {
    return Row(
      children: [
        AppText(
          title,
          style: TextStyle(
            fontWeight: FontWeight.w800,
            fontFamily: AppAssetsConstants.openSans,
            color: AppColorConstants.colorBlack,
            fontSize: getSize(14),
          ),
        ),
        AppText(value, style: dataTableHelper.dataRowTextStyle)
      ],
    );
  }

  Widget dialogTitleAndValueView(BuildContext context, String title, String value) {
    return Row(
      children: [
        AppText(
          title,
          style: TextStyle(
            fontWeight: FontWeight.w800,
            fontFamily: AppAssetsConstants.openSans,
            color: AppColorConstants.colorBlack,
            fontSize: getSize(14),
          ),
        ),
        const Spacer(),
        AppText(value, style: dataTableHelper.dataRowTextStyle)
      ],
    );
  }

  void _showDialog(BuildContext context, String deviceEui, Widget contentView) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          surfaceTintColor: AppColorConstants.colorWhite,
          backgroundColor: AppColorConstants.colorWhite,
          insetPadding: const EdgeInsets.symmetric(horizontal: 8.0),
          contentPadding: EdgeInsets.zero,
          titlePadding: EdgeInsets.zero,
          actionsPadding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
          title: getCustomAppBarWithClose(deviceEui),
          content: contentView,
        );
      },
    );
  }
}
