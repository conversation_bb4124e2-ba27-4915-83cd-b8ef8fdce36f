import 'package:quantumlink_node/app_import.dart';

import 'software_download_helper.dart';


class SoftwareDownload extends StatefulWidget {
  const SoftwareDownload({super.key});

  @override
  State<SoftwareDownload> createState() => SoftwareDownloadState();
}

class SoftwareDownloadState extends State<SoftwareDownload> with TickerProviderStateMixin {
  late ScreenLayoutType screenLayoutType;
  SoftwareDownloadPageHelper? setupPageHelper;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    setupPageHelper ?? (setupPageHelper = SoftwareDownloadPageHelper(this));
    return GetBuilder<DiagnosticsController>(
      init: DiagnosticsController(),
      builder: (DiagnosticsController controller) {
        return ScreenLayoutTypeBuilder(builder: (context, screenType, constraints) {
          screenLayoutType = screenType;
          return SingleChildScrollView(
            padding: EdgeInsets.only(bottom: getSize(40)),
            child: Padding(
              padding: const EdgeInsets.only(top: 13,left: 5),
              child: Column(crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  softwareDownloadView(),
                ],
              ),
            ),
          );
        });
      },
    );
  }
  Widget softwareDownloadView() {
    return Row(
      children: [
        Container(
          height: 35,
          alignment: Alignment.center,
          decoration: BoxDecoration(
              border: Border.all(color: AppColorConstants.colorH2.withOpacity(0.5)),
              borderRadius: const BorderRadius.all(Radius.circular(5))),
          child: IconButton(
            style: ButtonStyle(
                padding: MaterialStateProperty.all<EdgeInsets>(
                    const EdgeInsets.symmetric(horizontal: 8, vertical: 3)),
                shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(5.0),
                    side: const BorderSide(color: Colors.transparent)))),
            onPressed: () {
              launchPath(AppConfig.shared.softwareDownloadUrl, "");
            },
            icon: Row(
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 8.0),
                  child: AppText(
                    isSelectableText: false,
                    S.of(context).downloadSoftware,
                    style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        fontFamily: AppAssetsConstants.notoSans),
                  ),
                ),
                const SizedBox(width: 5),
                AppImageAsset(
                  image: AppAssetsConstants.softwareDownloadIcon,
                  width: 21,
                  height: 18,
                  color: AppColorConstants.colorBlackBlue,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

}
