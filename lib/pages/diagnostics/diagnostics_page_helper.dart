import 'package:quantumlink_node/app/cache/app_shared_preference.dart';
import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/controller/dashboard_controller.dart';
import 'package:quantumlink_node/controller/vlgw_controller.dart';
import 'package:quantumlink_node/pages/diagnostics/diagnostics_socket/diagnostics_socket_datasource.dart';
import 'package:quantumlink_node/serialized/dashboard/dashboard.dart';
import 'package:rxdart/rxdart.dart';


class DiagnosticsPageHelper {
  late DiagnosticsPageState state;

  ApiStatus apiStatus = ApiStatus.initial;
  late ScreenLayoutType screenLayoutType;
  final key = GlobalKey<PaginatedDataTableState>();
  final double heightOfDataTableCell = 48;
  int recordsInPage = 0; // For Set Fixed and Static Height Of Table
  int currentPageIndex = 0;
  int perPageLimit=10;
  PaginatorController pageController = PaginatorController();
  SocketHelper socketHelper = SocketHelper();
  BehaviorSubject<bool> isStreamLoading = BehaviorSubject();
  List<DiagnosticsDataModel> listDiagnosticsData = [];
  late DiagnosticsDataSource diagnosticsDataSource;
  DataTableHelper dataTableHelper = DataTableHelper();
  AmplifierController amplifierController = AmplifierController();
  DashboardController dashboardController = DashboardController();
  VLGWController vlgwController = VLGWController();
  double tableHeight = 0;
  AmplifierDeviceList amplifierDeviceList = AmplifierDeviceList.empty();
  AmplifierDeviceList searchAmplifierDeviceList = AmplifierDeviceList.empty();
  PaginationHelper paginationHelper = PaginationHelper();
  late HomeController homeController ;
  late TabController tabController;

  List<DIGTabItem> diagnosticsTabList = [
    //DIGTabItem(title: "Software Download", isCurrentOpen: true, icon: AppAssetsConstants.settingIcon),
    DIGTabItem(title: "API Status", isCurrentOpen: true, icon: AppAssetsConstants.apiStatusIcon),
  if(AppConfig.shared.isQLCentral)  DIGTabItem(title: "Diagnostics", isCurrentOpen: false, icon: AppAssetsConstants.diagnosticsIcon)
  ];
  List<bool> isHovered = List.generate(2, (index) => false);
  List<String> listDeviceEUI = [];
  RxList<String> selectedListDeviceEUI = <String>[].obs;
  VLGWs vlgWs = VLGWs.empty();
  String gwIDFormWebHost = "";
  bool isTableView = true;
  DateTime ?lastUpdateTime;
  late ScreenLayoutType previousLayoutType = ScreenLayoutType.desktop;
  DiagnosticsPageHelper(this.state) {
    socketHelper.connectDiagnosticsSocket();
    tabController = TabController(
        initialIndex: 0,
        length: AppConfig.shared.isQLCentral ? 2 : 1,
        vsync: state,
        animationDuration: Duration.zero);
    Future.delayed(const Duration(milliseconds: 500)).then((value) async {
      homeController = Get.find<HomeController>();
    });
    debugLogs("Socket URL -> ");
    debugLogs(Uri.parse(
        AppConfig.shared.wsUrl + RestConstants.instance.wsDiagnostics));
    MockData.listForNodeMgr();
  }

  getWayEuiIdList() async {
    selectedListDeviceEUI.clear();
    apiStatus = ApiStatus.loading;
    state.diagnosticsController.update();
    if (AppConfig.shared.isQLCentral) {
      vlgWs = await vlgwController.getVlGwList(state.context);
      List<String> vlgwEUIList = vlgWs.result
          .map<String>((element) => element.eui)
          .toList();
      if (vlgwEUIList.isNotEmpty) {
        gwIDFormWebHost = vlgwEUIList.first;
        getSocketData(vlgwEUIList);
      }
    } else {
      gwIDFormWebHost = await getPrefStringValue(AppSharedPreference.gw) ?? '';
      if(gwIDFormWebHost.isNotEmpty) getSocketData([gwIDFormWebHost]);

    }
    lastUpdateTime=DateTime.now();
    apiStatus = ApiStatus.success;
    state.diagnosticsController.update();
  }

  selectDevEUI(String devEUI,bool isAdd) {
    if (isAdd) {
      if (!selectedListDeviceEUI.contains(devEUI)) {
        selectedListDeviceEUI.add(devEUI);
      }
    } else {
      selectedListDeviceEUI.remove(devEUI);
    }
    if (selectedListDeviceEUI.isEmpty) {
      if (AppConfig.shared.isQLCentral) {
        List<String> vlgwEUIList = vlgWs.result
            .map<String>((element) => element.eui)
            .toList();
         getSocketData(vlgwEUIList);
      }else{
       if(gwIDFormWebHost.isNotEmpty) getSocketData([gwIDFormWebHost]);
      }
    } else {
      getSocketData(selectedListDeviceEUI, isDeviceEuiSend: true);
    }
  }

  getSocketData(List<String> formattedEuiList , {bool isDeviceEuiSend=false}) {
    listDiagnosticsData.clear();
    socketHelper.sendDiagnosticsDeviceEUIList(isDeviceEui: isDeviceEuiSend,formattedEuiList, (message) {
      handleWebSocketMessage(message);
    });
  }

  String getListToString(List<AmplifierDeviceItem> listAmps) {
    List deviceEUIList = listAmps.map((device) => device.deviceEui).toList();
    return deviceEUIList.map((e) => '"$e"').toList().toString();
  }

  String getSearchString(List<String> listAmps) {
    return listAmps.map((e) => '"$e"').toList().toString();
  }

  handleWebSocketMessage(String message) {
    debugLogs("VLGW Socket Msg ->");
    final data = jsonDecode(message) as Map<String, dynamic>;
    debugLogs(jsonEncode(data));
    DiagnosticsDataModel diagnosticsDataItem =
        DiagnosticsDataModel.fromJson(data);
    bool isUnique =
        listDiagnosticsData.every((item) => item.id != diagnosticsDataItem.id);
    if (isUnique) {
      listDiagnosticsData.add(diagnosticsDataItem);
      listDiagnosticsData.sort((a, b) => a.time.compareTo(b.time));
      diagnosticsDataSource = DiagnosticsDataSource(
        state.context, listDiagnosticsData.reversed.toList(), (dataMap) {
        jsonTreeDataHandling(dataMap);
      });
      handleTableHeightChange();
    }

  }

  jsonTreeDataHandling(DiagnosticsDataModel dataMap) {
    String newJsonData = jsonEncode(dataMap);
    Map<String, dynamic> jsonData = jsonDecode(newJsonData);
    homeController.updateJsonData(jsonData);
    scaffoldKey.currentState?.openEndDrawer();
  }

  handleTableHeightChange() {
    int itemsPerPage = dataTableHelper.getCurrentPageDataLength(
        listDiagnosticsData, currentPageIndex);
    int recordsInPage = (listDiagnosticsData.length > 10)
        ? itemsPerPage
        : listDiagnosticsData.length;
    double height = (listDiagnosticsData.isNotEmpty)
        ? (recordsInPage * heightOfDataTableCell) + (recordsInPage * 0.1) + 190
        : (recordsInPage * heightOfDataTableCell) + (recordsInPage * 0.1) + 300;

    tableHeight = height;
  }
  tabDigHeaderOnTap(int value) {
    for (var tabElement in diagnosticsTabList) {
      tabElement.isCurrentOpen = false;
    }
    diagnosticsTabList[value].isCurrentOpen = true;
    state.diagnosticsController.update();
  }

}
