import 'package:quantumlink_node/app_import.dart';
import 'diagnostics_page_helper.dart';
import 'diagnostics_socket/mobile_diagnostics_socket.dart';


class DiagnosticsPage extends StatefulWidget {
  const DiagnosticsPage({super.key});

  @override
  State<DiagnosticsPage> createState() => DiagnosticsPageState();
}

class DiagnosticsPageState extends State<DiagnosticsPage> with TickerProviderStateMixin {
  DiagnosticsPageHelper? diagnosticsPageHelper;
  late DiagnosticsController diagnosticsController;
  @override
  Widget build(BuildContext context) {
    diagnosticsPageHelper ?? (diagnosticsPageHelper = DiagnosticsPageHelper(this));
    return GetBuilder<DiagnosticsController>(
      init: DiagnosticsController(),
      builder: (DiagnosticsController controller) {
        diagnosticsController = controller;
        return getBody();
      },
    );
  }

  Widget getBody() {
    return getVLGWView();
  }

  Widget getVLGWView() {
    return ScreenLayoutTypeBuilder(
      builder: (context, screenType, constraints) {
        diagnosticsPageHelper!.screenLayoutType = screenType;
        MobileDiagnosticsSocket().autoSelectTableType(diagnosticsPageHelper!);
        return Container(
          height: (double.infinity),
          child: ClipRRect(
            borderRadius: const BorderRadius.all(Radius.circular(5)),
            child: Container(
              color: AppColorConstants.colorWhite,
              width: double.infinity,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: NestedScrollView(
                        headerSliverBuilder: (context, innerBoxIsScrolled) => [
                          SliverToBoxAdapter(
                                  child: Padding(
                                      padding: const EdgeInsets.symmetric(horizontal: 10),
                                      child: getPageTitleView(AppConfig.shared.isQLCentral ? S.of(context).diagnostics:S.of(context).apis))),
                              SliverToBoxAdapter(child: SizedBox(height: getSize(10))),
                             if(AppConfig.shared.isQLCentral) SliverToBoxAdapter(child:  getTabsOnDiagnosticsHeader()),
                        ],
                        body: getTabsContent()),
                  )
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget getTabsContent() {
    return TabBarView(
      physics: const NeverScrollableScrollPhysics(),
      controller: diagnosticsPageHelper!.tabController,
      children: List.generate(diagnosticsPageHelper!.diagnosticsTabList.length, (index) {
        if (index == 0) {
        //   return const SoftwareDownload();
        // } else if (index == 1) {
          return const ApiStatusView();
        }
        return DiagnosticsSocketPage(diagnosticsPageHelper!);
      }),
    );
  }


  Widget getTabsOnDiagnosticsHeader() {
    return TabBar(
      /*overlayColor: MaterialStateProperty.all(Colors.transparent),*/
      /*indicator: BoxDecoration(
        color: AppColorConstants.colorLightBlue1.withOpacity(0.7),
      ),*/
      controller: diagnosticsPageHelper!.tabController,
      dividerColor: AppColorConstants.colorWhite,
      labelPadding: EdgeInsets.zero,
      labelColor: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: getSize(10)),
      isScrollable: true,
      indicatorColor: AppColorConstants.colorWhite,
      tabAlignment: TabAlignment.start,
      // splashBorderRadius: BorderRadius.circular(12),
      onTap: (value) => diagnosticsPageHelper!.tabDigHeaderOnTap(value),
      tabs: List.generate(diagnosticsPageHelper!.diagnosticsTabList.length, (index) {
        DIGTabItem digTabItem = diagnosticsPageHelper!.diagnosticsTabList[index];
        return MouseRegion(
          onEnter: (event) {
            diagnosticsPageHelper!.isHovered[index] = true;
            diagnosticsController.update();
          },
          onExit: (event) {
            diagnosticsPageHelper!.isHovered[index] = false;
            diagnosticsController.update();
          },
          child: Tab(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 5),
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: getSize(25)),
                height: getSize(42),
                alignment: Alignment.center,
                decoration: digTabItem.getDeco(diagnosticsPageHelper!.isHovered[index]),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(5),
                      child: AppImageAsset(
                        height: 20,
                        width: 20,
                        image: digTabItem.icon ?? "",
                        color: diagnosticsPageHelper!.isHovered[index] && !digTabItem.isCurrentOpen
                            ? AppColorConstants.colorBlackBlue
                            : digTabItem.isCurrentOpen
                            ? AppColorConstants.colorLightBlue
                            : AppColorConstants.colorH2,
                      ),
                    ),
                    AppText(
                      isSelectableText: false,
                      digTabItem.title,
                      style: TextStyle(
                        fontSize: getSize(16),
                        fontFamily: AppAssetsConstants.poppins,
                        fontWeight: FontWeight.w400,
                        color: diagnosticsPageHelper!.isHovered[index] && !digTabItem.isCurrentOpen
                            ? AppColorConstants.colorBlackBlue
                            : digTabItem.isCurrentOpen
                            ? AppColorConstants.colorLightBlue
                            : AppColorConstants.colorH2,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      }),
    );
  }

}