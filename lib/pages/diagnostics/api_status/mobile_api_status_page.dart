import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/pages/diagnostics/api_status/api_status_halper.dart';

class MobileApiStatusPage {
  DataTableHelper dataTableHelper = DataTableHelper();

  Widget buildApiStatusView(BuildContext context, ApiStatusPageHelper apiStatusPageHelper) {
    int index = 0;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: MockData.listApiStatus.map(
        (element) {
          index++;
          ApiStatusItem service = element;
          String title = service.title;
          String apiUrl = "${service.apiUrl}/docs";
          String version = "";
          if (service.versionResponse.isNotEmpty) {
            String input = service.versionResponse.toString();
            if (service.key == 'ql_fuota_svc') {
              version = service.versionResponse['version'].toString();
            } else {
              version = input.replaceAll("{", "").replaceAll("}", "");
            }
          }
          String status = "";
          Widget statusIcon = Container();
          if (service.healthResponse.isNotEmpty) {
            status = service.healthResponse["status"] ?? "";
            statusIcon = const Icon(
              Icons.check_circle_outline_rounded,
              color: Colors.green,
            );
          }

          if (service.apiStatus == ApiStatus.failed) {
            status = "Not Found";
            version = "Not Found";
            statusIcon = const Icon(
              Icons.error_outline_rounded,
              color: Colors.red,
            );
          }
          return Container(
            margin: const EdgeInsets.symmetric(vertical: 3, horizontal: 5),
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
            decoration: BoxDecoration(
              color: index.isEven
                  ? AppColorConstants.colorWhite
                  : AppColorConstants.colorBackgroundDark,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColorConstants.colorH2, width: 1),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                InkWell(
                  hoverColor: Colors.transparent,
                  onTap: service.title == AppStringConstants.joinServer
                      ? null :() {
                    launchPath(apiUrl, "");
                  },
                  child: Row(
                    children: [
                      AppText(title,
                          isSelectableText: false,
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontFamily: AppAssetsConstants.openSans,
                            color: AppColorConstants.colorBlack ,
                            fontSize: getSize(14),
                          )),
                      if (service.title != AppStringConstants.joinServer)
                        Icon(Icons.insert_link, color: AppColorConstants.colorBlueLight)
                            .paddingOnly(left: 5),
                    ],
                  ),
                ),
                SizedBox(height: getSize(7)),
                if (version.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 7.0),
                    child: AppText(
                      version,
                      style: apiStatusPageHelper.dataTableHelper.dataRowTextStyle,
                    ),
                  ),
                Row(
                  children: [
                    AppText(
                      status,
                      style: apiStatusPageHelper.dataTableHelper.dataRowTextStyle,
                    ),
                    const SizedBox(
                      width: 5,
                    ),
                    if (service.apiStatus == ApiStatus.loading)
                      const SizedBox(
                        height: 50,
                        width: 50,
                        child: AppLoader(),
                      )
                    else
                      statusIcon,
                  ],
                )
              ],
            ),
          );
        },
      ).toList(),
    );
  }

  Widget selectTableTypeButtonView(ApiStatusPageHelper? apiStatusPageHelper) {
    return apiStatusPageHelper?.dataTableHelper.selectTableTypeButtonView(
      isTableType: apiStatusPageHelper.isTableView,
      onPressed: () {
        apiStatusPageHelper.isTableView = !apiStatusPageHelper.isTableView;
        apiStatusPageHelper.diagnosticsController.update();
      },
    );
  }

  void autoSelectTableType(ApiStatusPageHelper? apiStatusPageHelper) {
    if (apiStatusPageHelper?.previousLayoutType != null) {
      ScreenLayoutType currentLayoutType = apiStatusPageHelper!
    .state.screenLayoutType;
      bool isMobile = currentLayoutType != ScreenLayoutType.desktop;
    if (apiStatusPageHelper.previousLayoutType != currentLayoutType) {
    apiStatusPageHelper.isTableView = !isMobile;
    apiStatusPageHelper.previousLayoutType = currentLayoutType;
    }
  }
  }
}
