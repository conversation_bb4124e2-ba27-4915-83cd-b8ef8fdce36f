import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/pages/diagnostics/api_status/api_status_halper.dart';
import 'package:quantumlink_node/pages/diagnostics/api_status/mobile_api_status_page.dart';
import 'package:quantumlink_node/pages/diagnostics/diagnostics_socket/mobile_diagnostics_socket.dart';


class ApiStatusView extends StatefulWidget {
  const ApiStatusView({super.key});

  @override
  State<ApiStatusView> createState() => ApiStatusViewState();
}

class ApiStatusViewState extends State<ApiStatusView>
    with TickerProviderStateMixin {
  late ScreenLayoutType screenLayoutType;
  ApiStatusPageHelper? apiStatusPageHelper;

  @override
  void initState() {
// TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    apiStatusPageHelper ?? (apiStatusPageHelper = ApiStatusPageHelper(this));
    return GetBuilder<DiagnosticsController>(
      init: DiagnosticsController(),
      builder: (DiagnosticsController controller) {
        return ScreenLayoutTypeBuilder(
            builder: (context, screenType, constraints) {
          screenLayoutType = screenType;
          apiStatusPageHelper?.screenWidth = constraints.maxWidth;
          MobileApiStatusPage().autoSelectTableType(apiStatusPageHelper);
          return Container(
                margin: const EdgeInsets.symmetric(horizontal: 10),
                color: AppColorConstants.colorWhite,
                width: double.infinity,
                child: ListView(
                  physics: const ClampingScrollPhysics(),
                  children: [
                    getPageAppBar(),
                    apiBoardView(),                  ],
                ),
              );
            });
      },
    );
  }


  apiBoardView(){
    return Column(
      children: [
        Container(
          width: double.infinity,
          decoration: apiStatusPageHelper!.dataTableHelper.tableBorderDeco(),
          child: getApiStatusDataTableView(),
        ).paddingSymmetric(horizontal: 5),
        Row(mainAxisAlignment: MainAxisAlignment.end,
          children: [
            getTimeDurationView(
              onTapTime: DateTime.now(),
                refreshStatus: apiStatusPageHelper!.isLoading ? ApiStatus.loading : ApiStatus.success,
                updateTime: apiStatusPageHelper!.lastUpdateTime,),
            apiStatusRefreshButton(),
          ],
        )
      ],
    );
  }

 Widget getApiStatusDataTableView() {
    if (!apiStatusPageHelper!.isTableView) {
      return MobileApiStatusPage().buildApiStatusView(context, apiStatusPageHelper!);
    }

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        headingRowHeight: 50,
        dataRowHeight: 55,
        headingRowColor: MaterialStateProperty.all(AppColorConstants.colorPrimary),
        columns: <DataColumn>[
          DataColumn(
            label: SizedBox(
              width: 180,
              child: AppText(
                S.of(context).services,
                style: apiStatusPageHelper!.dataTableHelper.headingTextStyle(),
              ),
            ),
          ),
          DataColumn(
            label: SizedBox(
              width: 180,
              child: AppText(
                S.of(context).version,
                style: apiStatusPageHelper!.dataTableHelper.headingTextStyle(),
              ),
            ),
          ),
          DataColumn(
            label: SizedBox(
              width: (apiStatusPageHelper!.screenWidth - 500)
                  .clamp(120.0, apiStatusPageHelper!.screenWidth),
              child: AppText(
                S.of(context).status,
                style: apiStatusPageHelper!.dataTableHelper.headingTextStyle(),
              ),
            ),
          ),
        ],
        rows: MockData.listApiStatus.map((service) {
          String title = service.title;
          String apiUrl = service.apiUrl + "/docs";
          String version = service.versionResponse.isNotEmpty
              ? (service.key == 'ql_fuota_svc'
              ? service.versionResponse['version'].toString()
              : service.versionResponse.toString().replaceAll("{", "").replaceAll("}", ""))
              : "";

          String status = "";
          Widget statusIcon = Container();
          if (service.healthResponse.isNotEmpty) {
            status = service.healthResponse["status"] ?? "";
            statusIcon = const Icon(
              Icons.check_circle_outline_rounded,
              color: Colors.green,
            );
          }

          if (service.apiStatus == ApiStatus.failed) {
            status = "Not Found";
            version = "Not Found";
            statusIcon = const Icon(
              Icons.error_outline_rounded,
              color: Colors.red,
            );
          }

          return DataRow(
            cells: <DataCell>[
              DataCell(SizedBox(
                width: 180,
                child: InkWell(
                  hoverColor: Colors.transparent,
                  onTap: service.title == AppStringConstants.joinServer
                      ? null
                      : () {
                          launchPath(apiUrl, "");
                        },
                  child: Row(
                    children: [

                      AppText(
                        title,
                        isSelectableText: false,
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontFamily: AppAssetsConstants.openSans,
                          color:  AppColorConstants.colorBlack ,
                          fontSize: getSize(14),
                        ),
                      ),
                      if (service.title != AppStringConstants.joinServer)
                        Icon(Icons.insert_link, color: AppColorConstants.colorBlueLight)
                            .paddingOnly(left: 5),
                    ],
                  ),
                ),
              )),
              DataCell(SizedBox(
                width: 180,
                child: AppText(
                  version,
                  style: apiStatusPageHelper!.dataTableHelper.dataRowTextStyle,
                ),
              )),
              DataCell(Row(
                children: [
                  AppText(
                    status,
                    style: apiStatusPageHelper!.dataTableHelper.dataRowTextStyle,
                  ),
                  const SizedBox(width: 5),
                  if (service.apiStatus == ApiStatus.loading)
                    const SizedBox(
                      height: 50,
                      width: 50,
                      child: AppLoader(),
                    )
                  else
                    statusIcon,
                ],
              )),
            ],
          );
        }).toList(),
      ),
    )
    ;
  }

  Widget getPageAppBar() {
    return Padding(
      padding: EdgeInsets.only(bottom: getSize(0), left: getSize(5), right: getSize(5)),
      child: Container(
        height: 55,
        alignment: Alignment.centerLeft,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
                topRight: Radius.circular(getSize(7)), topLeft: Radius.circular(getSize(7))),
            border: Border.all(
              color: AppColorConstants.colorH2,
            ),
            color: AppColorConstants.colorWhite),
        padding: EdgeInsets.only(left: getSize(18)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
          const Spacer(),MobileApiStatusPage().selectTableTypeButtonView(apiStatusPageHelper)],
        ),
      ),
    );
  }

  Widget apiStatusRefreshButton() {
    return Padding(
      padding: const EdgeInsets.only(right: 8.0),
      child: AppRefresh(loadingStatus: apiStatusPageHelper!.isLoading ? ApiStatus.loading : ApiStatus.success,
        onPressed: () => apiStatusPageHelper!.apiStatusRefreshOnTap(),
      ),
    );
  }
}
