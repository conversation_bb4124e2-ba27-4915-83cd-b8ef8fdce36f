import 'package:quantumlink_node/app_import.dart';


class ApiStatusPageHelper {



  late ApiStatusViewState state;
  DataTableHelper dataTableHelper = DataTableHelper();
  bool isTableView = true;
  late ScreenLayoutType previousLayoutType = ScreenLayoutType.desktop;
  double screenWidth = 0.0;
  DiagnosticsController diagnosticsController = Get.put(DiagnosticsController());
  DateTime ?lastUpdateTime ;
  ApiStatusPageHelper(this.state) {
    Future.delayed(const Duration(milliseconds: 500)).then((value) async {
        await getServices();
    });
  }
  bool isLoading = false;
  getServices() async {
    isLoading = true;
    List<Future<void>> futures = [];
    for (var item in MockData.listApiStatus) {
      if (item.apiStatus != ApiStatus.success) {

        if (item.apiUrl.isEmpty) {
          item.apiStatus = ApiStatus.failed;
          continue;
        }

        item.apiStatus = ApiStatus.loading;
        diagnosticsController.update();
        futures.add(_checkApiStatus(item));
      }
    }
    await Future.wait(futures);
    lastUpdateTime= DateTime.now();
    diagnosticsController.update();
    isLoading = false;
  }

// Function to handle API status check
  Future<void> _checkApiStatus(ApiStatusItem item) async {
    try {

      var results = await Future.wait([
        diagnosticsController.checkHealth(state.context, otherBaseUrl: item.apiUrl),
        diagnosticsController.getBackendVersions(state.context, otherBaseUrl: item.apiUrl),
      ]);

      var statusData = results[0]; // Result from checkHealth()
      var versionData = results[1]; // Result from getBackendVersions()

      if (statusData != null && versionData != {}) {
        String? statusDetail = statusData["detail"] as String?;
        String? versionDetail = versionData["detail"] as String?;

        if ((statusDetail?.isEmpty ?? true) || (versionDetail?.isEmpty ?? true)) {
          item.apiStatus = ApiStatus.success;
          item.healthResponse = statusData;
          item.versionResponse = versionData;
          diagnosticsController.update();
        } else {
          item.apiStatus = ApiStatus.failed;
        }
      } else {
        item.apiStatus = ApiStatus.failed;
      }
      diagnosticsController.update();
    } catch (e) {
      item.apiStatus = ApiStatus.failed;
    }
  }

  apiStatusRefreshOnTap() async {
    if (isLoading) return;
    isLoading = true;
    try {
      for (var element in MockData.listApiStatus) {
        element.apiStatus = ApiStatus.initial;
      }
      await getServices();
    } finally {
      isLoading = false;
    }
  }





}