import 'package:quantumlink_node/app_import.dart';

class MobileAmplifierPage {
  Widget buildAmplifierList(BuildContext context, AmplifierPageHelper amplifierPageHelper) {
    if (amplifierPageHelper.amplifierDataSource.rowCount == 0) {
      return SizedBox(
        height: 350,
        child: amplifierPageHelper.dataTableHelper.getEmptyTableContent(context),
      );
    }
    List<AmplifierDeviceItem> fullList = amplifierPageHelper.amplifierDataSource.list;
    List<AmplifierDeviceItem> paginatedList = fullList
        .skip(amplifierPageHelper.ampPaginationHelper.currentPage *
            AppStringConstants.ampPrePageLimit)
        .take(AppStringConstants.ampPrePageLimit)
        .toList();
    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: paginatedList.length,
      itemBuilder: (context, index) {
        AmplifierDeviceItem amplifierDeviceItem =
            paginatedList[index];// amplifierPageHelper.amplifierDataSource.list[index];
        DataRow dataRow = amplifierPageHelper.amplifierDataSource.getRow(index,amplifierDeviceItem);
        String type = amplifierDeviceItem.type ?? "";
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 3, horizontal: 5),
          child: CustomListTile(onTap: () {
            amplifierPageHelper.amplifierDataSource.getRow(index,amplifierDeviceItem).onTap!();
          },
            index: index,
            titleWidget: Row(crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                dataRow.cells[0].child,
                SizedBox(width: getSize(10)),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    dataRow.cells[1].child,
                    AppText(
                      isSelectableText: false,
                      type,
                      style: TextStyle(
                          fontFamily: AppAssetsConstants.roboto,
                          fontSize: getSize(13),
                          fontWeight: FontWeight.w400),
                    )
                  ],
                ),
                const Spacer(),
                dataRow.cells[3].child,
              ],
            ),
          ),
        );
      },
    );
  }

  Widget selectTableTypeButtonView(AmplifierPageHelper? amplifierPageHelper) {
    return amplifierPageHelper?.dataTableHelper.selectTableTypeButtonView(
      isTableType: amplifierPageHelper.isTableView,
      onPressed: () {
        amplifierPageHelper.isTableView = !amplifierPageHelper.isTableView;
        amplifierPageHelper.isExpandAll = amplifierPageHelper.isTableView;
        amplifierPageHelper.state.amplifierController.update();
      },
    );
  }

  void autoSelectTableType(AmplifierPageHelper? amplifierPageHelper) {
    if (amplifierPageHelper?.previousLayoutType != null) {
      ScreenLayoutType currentLayoutType = amplifierPageHelper!.screenLayoutType;
      bool isMobile = currentLayoutType != ScreenLayoutType.desktop;
      if (amplifierPageHelper.previousLayoutType != currentLayoutType) {
        amplifierPageHelper.isTableView = !isMobile;
        amplifierPageHelper.isExpandAll = amplifierPageHelper.isTableView;
        amplifierPageHelper.previousLayoutType = currentLayoutType;
      }
    }
  }
}
