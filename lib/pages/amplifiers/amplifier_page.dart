import 'package:quantumlink_node/app_import.dart';
import 'mobile_amplifier_page.dart';
class AmplifierPage extends StatefulWidget {
  final Map<String, dynamic>? mapWithTabIndex;

  const AmplifierPage({super.key, required this.mapWithTabIndex});

  @override
  State<AmplifierPage> createState() => AmplifierPageState();
}

class AmplifierPageState extends State<AmplifierPage> with TickerProviderStateMixin {
  AmplifierPageHelper? ampPageHelper;
  late AmplifierController amplifierController;

  @override
  void dispose() {
    ampPageHelper!.stopSocketTimer();
    ampPageHelper!.socketHelper.ampOnDispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    ampPageHelper ?? (ampPageHelper = AmplifierPageHelper(this));
    ampPageHelper!.initializeTabs(context);
    debugLogs(ampPageHelper!.deviceEui);
    return GetBuilder<AmplifierController>(
      init: AmplifierController(),
      builder: (AmplifierController controller) {
        amplifierController = controller;
        return Stack(
          children: [getBody()],
        );
      },
    );
  }

  Widget getBody() {
    return getAmplifierPageView();
  }

  Widget getAmplifierPageView() {
    return ScreenLayoutTypeBuilder(builder: (context, screenType, constraints) {
      ampPageHelper!.screenLayoutType = screenType;
      MobileAmplifierPage().autoSelectTableType(ampPageHelper!);
      return MergeSemantics(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Scrollbar(
                thumbVisibility: true,
                child: NestedScrollView(
                    key: ValueKey(ampPageHelper!.isMapTab),
                    physics: ampPageHelper!.isMapTab
                        ? const NeverScrollableScrollPhysics()
                        : const BouncingScrollPhysics(),
                    headerSliverBuilder: (context, innerBoxIsScrolled) => [
                          SliverToBoxAdapter(child: _buildHeader()),
                          SliverToBoxAdapter(child: SizedBox(height: getSize(10))),
                          SliverToBoxAdapter(child: getTabsOnAmpsHeader()),
                        ],
                    body: getTabsContent()),
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildHeader() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(width: 15),
        getPageTitleView(S.of(context).amplifiers),
        const Spacer(),
      ],
    );
  }

  Widget getTabsOnAmpsHeader() {
    return SelectionArea(
      child: TabBar(
          controller: ampPageHelper!.tabController,
          dividerColor: AppColorConstants.colorWhite,
          labelPadding: EdgeInsets.zero,
          labelColor: Colors.white,
          padding: EdgeInsets.zero,
          isScrollable: true,
          indicatorColor: AppColorConstants.colorWhite,
          tabAlignment: TabAlignment.start,
          onTap: (value) async{
            if(ampPageHelper!.tabController.previousIndex == value){
              return;
            }
            String currentTitle = ampPageHelper!.listTabs[ampPageHelper!.tabController.previousIndex].title;
            if(value<2){
              currentTitle = ampPageHelper!.listTabs.singleWhere((element) => element.isCurrentOpen).title;
            }
            int deviceListIndex = ampPageHelper!.amplifierDeviceList.result
                .indexWhere((tab) => tab.deviceEui == currentTitle);
            if(deviceListIndex==-1){
              ampPageHelper!.tabAmpsHeaderOnTap(value);
              return;
            }
            if (ampPageHelper!.getConfigurationMap(deviceListIndex) != null) {
              return;
            } else {
              ampPageHelper!.tabAmpsHeaderOnTap(value);
            }
          },
          tabs: List.generate(ampPageHelper!.listTabs.length, (index) {
            AmpTabItem ampTabItem = ampPageHelper!.listTabs[index];
            if (AppConfig.shared.isOpenFromBLE && index >= 2 ) {
              return const SizedBox(); // or return SizedBox.shrink();
            }
            return MouseRegion(
              onEnter: (event) {
                ampPageHelper!.isHovered[index] = true;
                amplifierController.update();
              },
              onExit: (event) {
                ampPageHelper!.isHovered[index] = false;
                amplifierController.update();
              },
              child: Tab(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal:5).copyWith(left: index == 0 ? 25 : 5),
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: getSize(25)),
                    height: getSize(42),
                    alignment: Alignment.center,
                    decoration: ampTabItem.getDeco(ampPageHelper!.isHovered[index]),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (index < 2)
                          Padding(
                            padding: EdgeInsets.all((index == AmpTabConstants.ampList
                                ? 9.5
                                : index == AmpTabConstants.ampMap
                                    ? 8.0
                                    : 0.0)),
                            child: AppImageAsset(width: index == 0 ? 25 :null,
                                image: ampTabItem.icon ?? "",
                                color: ampPageHelper!.isHovered[index] && !ampTabItem.isCurrentOpen
                                    ? AppColorConstants.colorBlackBlue
                                    : ampTabItem.isCurrentOpen
                                        ? AppColorConstants.colorLightBlue
                                        : AppColorConstants.colorH2),
                          ),
                        if (ampPageHelper?.screenLayoutType != ScreenLayoutType.mobile ||
                            index > AmpTabConstants.ampMap)
                          AppText(
                          isSelectableText: false,
                          ampTabItem.title,
                          style: TextStyle(
                              fontSize: getSize(16),
                              fontFamily: AppAssetsConstants.poppins,
                              fontWeight: FontWeight.w600,
                              color: ampPageHelper!.isHovered[index] && !ampTabItem.isCurrentOpen
                                  ? AppColorConstants.colorBlackBlue
                                  : ampTabItem.isCurrentOpen
                                      ? AppColorConstants.colorLightBlue
                                      : AppColorConstants.colorH2),
                        ),
                        if (ampPageHelper!.listTabs.length > AmpTabConstants.ampMap &&
                            index > AmpTabConstants.ampList &&
                            index > AmpTabConstants.ampMap) ...[
                          SizedBox(width: getSize(10)),
                          GestureDetector(
                              onTap: () async{
                                int deviceListIndex = ampPageHelper!.amplifierDeviceList.result
                                    .indexWhere((tab) => tab.deviceEui == ampTabItem.title);
                                if (ampPageHelper!.getConfigurationMap(deviceListIndex) != null) {
                                  bool isSuccess = await ampPageHelper!.checkConfigurationMap(
                                      context, deviceListIndex);
                                  if (isSuccess) {
                                    ampPageHelper!.removeAmpTab(index);
                                  }else{
                                    return;
                                  }
                                } else {
                                  ampPageHelper!.removeAmpTab(index);
                                }
                              },
                              child: CircleAvatar(
                                  maxRadius: 10,
                                  backgroundColor: ampTabItem.isCurrentOpen
                                      ? AppColorConstants.colorLightBlue
                                      : AppColorConstants.colorH2.withOpacity(0.3),
                                  child: Icon(Icons.close,
                                      size: getSize(16),
                                      color: ampTabItem.isCurrentOpen
                                          ? AppColorConstants.colorWhite
                                          : AppColorConstants.colorH3))),
                        ]
                      ],
                    ),
                  ),
                ),
              ),
            );
          })),
    );
  }

  Widget provisionButtonView() {
    return Padding(
      padding: const EdgeInsets.only(top: 20, right: 15),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AppButton(
              buttonHeight: getSize(35),
              buttonRadius: 9,
              fontSize: 14,
              buttonWidth: 80,
              onPressed: () {
                GoRouter router = GoRouter.of(context);
                router.go(RouteHelper.routeProvisionPage);
              },
              buttonName: S.of(context).provision,
              borderColor: AppColorConstants.colorLightBlue,
              fontFamily: AppAssetsConstants.openSans,
              buttonColor: AppColorConstants.colorLightBlue),
        ],
      ),
    );
  }

  Widget getRefreshButtonView() {
    return AppRefresh(
        onPressed: () {
          if (ampPageHelper!.apiStatus != ApiStatus.loading) {
            ampPageHelper!.searchController.clear();
            ampPageHelper!.getAmplifierList();
          }
        },
        loadingStatus: ampPageHelper!.apiStatus);
  }

  Widget getTableBoardView() {
    if (amplifierRepository.amplifierDeviceList == null || ampPageHelper!.apiStatus == ApiStatus.loading) {
      return Align(
          alignment: Alignment.center,
          child: ListView(
            physics: const ClampingScrollPhysics(),
            children: [
              getPageAppBar(),
              Container(
                  height: 400,
                  width: double.infinity,
                  decoration:
                  ampPageHelper!.dataTableHelper.tableBorderDeco(),
                  child: const AppLoader()),
            ],
          ).paddingSymmetric(horizontal: getSize(15)));
    } else {
      int itemsPerPage = ampPageHelper!.dataTableHelper.getCurrentPageDataLength(
          ampPageHelper!.amplifierDeviceList.result, ampPageHelper!.currentPageIndex,
          perPageLimit: AppStringConstants.ampPrePageLimit);
      ampPageHelper!.recordsInPage = (ampPageHelper!.amplifierDeviceList.result.length > AppStringConstants.ampPrePageLimit)
          ? itemsPerPage
          : ampPageHelper!.amplifierDeviceList.result.length;
      return SingleChildScrollView(
        physics: const ClampingScrollPhysics(),
        child: Column(
          children: [
            getPageAppBar(),
            Container(
              height:(!ampPageHelper!.isTableView)
                  ? null
                  :ampPageHelper!.amplifierDeviceList.result.isNotEmpty
                  ? (ampPageHelper!.recordsInPage * ampPageHelper!.heightOfDataTableCell) +
                      (ampPageHelper!.recordsInPage * 0.1) +
                      170
                  : (ampPageHelper!.recordsInPage * ampPageHelper!.heightOfDataTableCell) +
                      (ampPageHelper!.recordsInPage * 0.1) +
                      350,
              decoration: ampPageHelper!.dataTableHelper.tableBorderDeco(),
              child: Column(
                children: [
                  if (ampPageHelper!.isTableView)
                    Expanded(
                      child: StreamBuilder<dynamic>(
                        stream: ampPageHelper!.socketHelper.ampsListingStreamView,
                        builder: (context, snapshot) {
                          return getDataTableWithPageBoardView();
                        },
                      ),
                    )
                  else
                    StreamBuilder<dynamic>(
                      stream: ampPageHelper!.socketHelper.ampsListingStreamView,
                      builder: (context, snapshot) {
                        return getDataTableWithPageBoardView();
                      },
                    ),
                  AppPaginationWidget(
                    apiStatus: ampPageHelper!.apiStatus,
                    paginationHelper: ampPageHelper!.ampPaginationHelper,
                    onLoadNext: () async {
                      await  ampPageHelper!.loadNextLogs(context);
                    },
                    onLoadPrevious: () async {
                      await  ampPageHelper!.loadPreviousLogs(context);
                      amplifierController.update();
                    },
                    onGoToFirstPage: () {
                      ampPageHelper!.ampPaginationHelper.setPage(0);
                      if(ampPageHelper!.isTableView) {
                        ampPageHelper!.pageController.goToFirstPage();
                      }
                      amplifierController.update();
                    },
                    onGoToLastPage: () {
                      if(ampPageHelper!.isTableView) {
                        ampPageHelper!.pageController.goToLastPage();
                      }
                      amplifierController.update();
                    },
                    itemsPerPage: AppStringConstants.ampPrePageLimit,
                    onChanged: (value) {
                      if (ampPageHelper!.apiStatus != ApiStatus.loading) {
                        AppStringConstants.ampPrePageLimit = int.parse(value);
                        ampPageHelper!.getAmplifierList();
                        amplifierController.update();
                      }
                    },
                  ),
                ],
              ),
            ),
            Row(mainAxisAlignment:  MainAxisAlignment.end,
              children: [
                StreamBuilder<String>(
                  stream: ampPageHelper!.updateStreamView,
                  builder: (context, snapshot) {
                    return Align(alignment: Alignment.centerRight, child: buildLastSeenView());
                  },
                ),
                getRefreshButtonView(),
              ],),
            if (ampPageHelper!.ampErrorMessage != null)
              CommonAPIErrorView(errorMessage: ampPageHelper!.ampErrorMessage ?? "",rightPadding: 5),
            const SizedBox(
              height: 20,
            ),
          ],
        ).paddingSymmetric(horizontal: getSize(15)),
      );
    }
  }
  Widget buildLastSeenView() {
    if (ampPageHelper!.isShowText) {
      return getTimeDurationView(
        refreshStatus: ampPageHelper!.apiStatus,
        updateTime: ampPageHelper!.lastUpdateTime,
        onTapTime: ampPageHelper!.onTapTime,
        difference: ampPageHelper!.differenceTime,
      );
    } else {
      if (ampPageHelper!.lastUpdateTime != null) {
        return getLastSeenView(ampPageHelper!.lastUpdateTime);
      } else {
        return Container();
      }
    }
  }


  Widget getTabsContent() {
    return TabBarView(
      physics: const NeverScrollableScrollPhysics(),
      controller: ampPageHelper!.tabController,
      children: List.generate(ampPageHelper!.listTabs.length, (index) {
        //print("tabController getTabsContent index: $index == ${ampPageHelper!.tabController.index}");
        return (index == AmpTabConstants.ampList)
            ? getTableBoardView()
            : (index == AmpTabConstants.ampMap)
                ? LocationScreen(
                    ampPageHelper: ampPageHelper!,
                  )
                : AmplifierDetail(
                    amplifierItem: ampPageHelper!.listTabs[index].ampDeviceItem,
                    ampPageHelper: ampPageHelper!,
        );
      }),
    );
  }

  Widget getDataTableWithPageBoardView() {
    if(!ampPageHelper!.isTableView){
      return MobileAmplifierPage().buildAmplifierList(context,ampPageHelper!);
    }
    return PaginatedDataTable2(
      columnSpacing: 20,
      rowsPerPage: AppStringConstants.ampPrePageLimit,
      headingTextStyle: ampPageHelper!.dataTableHelper.headingTextStyle(),
      wrapInCard: false,
      initialFirstRowIndex:
          ampPageHelper!.ampPaginationHelper.currentPage * AppStringConstants.ampPrePageLimit,
      controller: ampPageHelper!.pageController,
      border:  ampPageHelper!.dataTableHelper.tableBorder(),
      // ignore: deprecated_member_use
      headingRowColor: ampPageHelper!.dataTableHelper.headingRowColor(),
      renderEmptyRowsInTheEnd: false,
      fixedColumnsColor: Colors.red,
      columns: _getDataColumns(),
      source: ampPageHelper!.amplifierDataSource,
      onSelectAll: ampPageHelper!.amplifierDataSource.selectAll,
      minWidth: 1200,
      dataRowHeight: 51,
      // For progress indicator
      hidePaginator: true,
      showCheckboxColumn: false,
      empty: (ampPageHelper!.amplifierDataSource.list.isEmpty &&
              ampPageHelper!.apiStatus != ApiStatus.loading)
          ? ampPageHelper!.dataTableHelper.getEmptyTableContent(context)
          : ampPageHelper!.amplifierDataSource.list.isEmpty && ampPageHelper!.searchController.text.isNotEmpty
              ? ampPageHelper!.dataTableHelper.getEmptyTableContent(context)
              : const Align(alignment: Alignment.center, child: AppLoader()),
    );
  }

  List<DataColumn> _getDataColumns() {
    return [
      DataColumn2(fixedWidth: 90, label: AppText(S.of(context).alarmStatus)),
      DataColumn2(fixedWidth: getSize(220),label: AppText(S.of(context).deviceEUI)),
      DataColumn2(fixedWidth: getSize(200), label: Center(child: AppText(S.of(context).type))),
      DataColumn2(fixedWidth: getSize(150), label: Center(child: AppText(S.of(context).status))),
      DataColumn2(fixedWidth: getSize(180), label: Center(child: AppText(S.of(context).assetID))),
      if (AppConfig.shared.isQLCentral)
        DataColumn2(fixedWidth: getSize(120), label: Center(child: AppText(S.of(context).site)))
      else
        DataColumn2(fixedWidth: getSize(140), label: Center(child: AppText(S.of(context).gatWayName,textAlign:TextAlign.center,))),
      DataColumn2(label: AppText(S.of(context).lastSeen)),
    ];
  }
  Widget getPageAppBar() {
    return Container(
      decoration: BoxDecoration(
          border: Border.all(color: AppColorConstants.colorH2, width: 0.8),
          color:  buildTableAppbarColor(),
          borderRadius: BorderRadius.only(
              topRight: Radius.circular(getSize(7)), topLeft: Radius.circular(getSize(7)))),
      alignment: Alignment.topCenter,
      padding: const EdgeInsets.all(10),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (ampPageHelper!.screenLayoutType == ScreenLayoutType.desktop)
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(child: searchTextFieldView()),
                MobileAmplifierPage().selectTableTypeButtonView(ampPageHelper),
                const Spacer()
              ],
            )else ...[
            Row(
              children: [
                Flexible(child: searchTextFieldView()),
                MobileAmplifierPage().selectTableTypeButtonView(ampPageHelper),
              ],
            ),

          ],
        ],
      ),
    );
  }

  Widget searchTextFieldView() {
    return Container(height: 40,
      decoration: BoxDecoration(
          color: buildTextFiledColor(), borderRadius: BorderRadius.circular(getSize(8))),
      child: AppTextFormField(
        onChanged: (value) {
          if (value.trim().isEmpty) {
            ampPageHelper!.searchController.clear();
            ampPageHelper!.getAmplifierList();
          }
        },
        focusedBorderColor: AppColorConstants.colorPrimary,
        controller: ampPageHelper!.searchController,
        enabledBorderColor: AppColorConstants.colorWhite1,
        hintText: S.of(context).quickSearch,
        maxLines: 1,
        textInputType: TextInputType.emailAddress,
        validator: (value) {
          return null;
        },onFieldSubmitted: (value) {
        if(value.isNotEmpty)ampPageHelper!.getAmplifierList() ;
        },
        borderRadius: getSize(8),
        hintTextColor: AppColorConstants.colorDarkBlue,
        suffixIcon: InkWell(
                onTap: () {
                  amplifierController.update();
                  if (ampPageHelper!.searchController.text.isNotEmpty) {
                    ampPageHelper!.getAmplifierList();
                  }
                },
                child: const Padding(
                  padding: EdgeInsets.all(12),
                  child: AppImageAsset(image: AppAssetsConstants.searchIcon),
                ),
              ),
        hintFontSize: 17,
        fontFamily: AppAssetsConstants.openSans,
        fontWeight: FontWeight.w400,
      ),
    );
  }
}
