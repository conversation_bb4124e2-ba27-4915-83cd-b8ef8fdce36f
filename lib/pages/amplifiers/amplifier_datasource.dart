import 'package:dotted_border/dotted_border.dart';
import 'package:quantumlink_node/app_import.dart';

class AmplifierDataSource extends DataTableSource {
  int _selectedCount = 0;
  BuildContext? context;

  AmplifierDataSource.empty(this.list, this.onTap) ;

  AmplifierDataSource(this.list, this.onTap, this.context, this.locationFun,
      [sortedByEUI = false,
      this.hasRowTaps = false,
      this.hasRowHeightOverrides = false,
      this.hasZebraStripes = false]) {
      _hoveredIndices = List.generate(list.length, (_) => false);
    if (sortedByEUI) {
      sort((d) => d.deviceEui, true);
    }
  }

  final List<AmplifierDeviceItem> list;
  final Function(AmplifierDeviceItem) onTap;
  Function(String)? locationFun;

  bool hasRowTaps = false;

  bool hasRowHeightOverrides = false;

  bool hasZebraStripes = false;

  void sort<T>(
      Comparable<T> Function(AmplifierDeviceItem d) getField, bool ascending) {
    list.sort((a, b) {
      final aValue = getField(a);
      final bValue = getField(b);
      return ascending
          ? Comparable.compare(aValue, bValue)
          : Comparable.compare(bValue, aValue);
    });
    notifyListeners();
  }
  RegExp locationRegExp =  RegExp(ValidationUtils.locationRegExp);

  DataTableHelper dataTableHelper = DataTableHelper();
  List<bool> _hoveredIndices = [];

  @override
  DataRow2 getRow(int index, [AmplifierDeviceItem ? amplifierDeviceItem]) {
    assert(index >= 0);
    if (index >= list.length) throw 'index > _desserts.length';
    final dessert = amplifierDeviceItem ?? list[index];
    DetectedStatusType? detectedStatusType =
        getDetectedStatusType(dessert.status);

    String beID = dessert.assetId != null ? dessert.assetId.toString().trim() :"-";
    String gatWayName =
        (dessert.gateway != null && dessert.gateway!.isNotEmpty) ? dessert.gateway?.first.name ?? "" : "";

    String siteName= (dessert.gateway != null && dessert.gateway!.isNotEmpty) ? dessert.gateway?.first.site?.name ?? "" :"";

    return DataRow2.byIndex(
      index: index,
      selected: dessert.selected ?? false,
      // ignore: deprecated_member_use
      color: index.isEven
          ? MaterialStateProperty.all(AppColorConstants.colorWhite)
          : MaterialStateProperty.all(AppColorConstants.colorBackgroundDark),
      onSelectChanged: (value) {
        if (dessert.selected != value) {
          _selectedCount += value! ? 1 : -1;
          assert(_selectedCount >= 0);
          dessert.selected = value;
          notifyListeners();
        }
      },
      onTap: () => detectedStatusType == DetectedStatusType.pending ? S.of(context!).amplifierIsOffline.showOffline(context!) : onTap(dessert),
      onDoubleTap: hasRowTaps
          ? () => showMessage('Double Tapped on row ${dessert.deviceEui}')
          : null,
      onLongPress: hasRowTaps
          ? () => showMessage('Long pressed on row ${dessert.deviceEui}')
          : null,
      onSecondaryTap: hasRowTaps
          ? () => showMessage('Right clicked on row ${dessert.deviceEui}')
          : null,
      onSecondaryTapDown: hasRowTaps
          ? (d) => showMessage('Right button down on row ${dessert.deviceEui}')
          : null,
      cells: [
        DataCell((detectedStatusType == DetectedStatusType.offline)
            ? const AppImageAsset(image: AppAssetsConstants.deviceOffline)
            : (((dessert.alarm == null)
                ? Icon(
                    Icons.check_circle_outline,
                    color:(detectedStatusType == DetectedStatusType.pending) ?AppColorConstants.colorH2 : AppColorConstants.colorGreen2,
                  )
                : Icon(
                    Icons.error_outline,
                    color: getSeverityColor(dessert.alarm, false),
                  )))),
        DataCell(Wrap(
          alignment: WrapAlignment.end,
          runSpacing: 5,
          children: [
            MouseRegion(
              onEnter: (_) {
                _hoveredIndices[index] = true;
                notifyListeners();
              },
              onExit: (_) {
                _hoveredIndices[index] = false;
                notifyListeners();
              },
              child: AppText(
                "${dessert.deviceEui ?? ""}",
                maxLines: 1,
                isSelectableText: false,
                style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontFamily: AppAssetsConstants.openSans,
                    color: AppColorConstants.colorLightBlue,
                    fontSize: getSize(14),
                    decoration:
                        _hoveredIndices[index] ? TextDecoration.underline : TextDecoration.none,
                    decorationThickness: 4,
                    decorationColor: AppColorConstants.colorLightBlue),
              ),
            ),
            SizedBox(width: getSize(10)),
            if (dessert.location != null && dessert.location.isNotEmpty && dessert.location.toString().trim() !=",")
              DottedBorder(
                padding: EdgeInsets.symmetric(
                    vertical: getSize(2.5), horizontal: getSize(2.5)),
                radius: Radius.circular(getSize(4)),
                strokeWidth: 0.5,
                borderType: BorderType.RRect,
                color: AppColorConstants.colorH1Grey,
                child: InkWell(
                  onTap: () {
                    locationFun!(dessert.location);
                  },
                  child: Icon(
                    Icons.location_on,
                    size: getSize(18),
                    color: AppColorConstants.colorPrimary,
                  ),
                ),
              )
            else
              DottedBorder(
                padding: EdgeInsets.symmetric(
                    vertical: getSize(2.5), horizontal: getSize(2.5)),
                radius: Radius.circular(getSize(4)),
                strokeWidth: 0.5,
                borderType: BorderType.RRect,
                color: AppColorConstants.colorH1Grey,
                child: InkWell(
                  onTap: null,
                  child: Icon(
                    Icons.location_on,
                    size: getSize(18),
                    color: AppColorConstants.colorH2,
                  ),
                ),
              ),
          ],
        )),
        DataCell(AppText(dessert.type ?? "-",
            style: dataTableHelper.dataRowTextStyle,)),
        DataCell(AppText(
          dessert.ampFwVersion ?? "",
          style: dataTableHelper.dataRowTextStyle,
        )),
        DataCell(AppText(
          dessert.xponderFwVersion ?? "",
          style: dataTableHelper.dataRowTextStyle,
        )),
        DataCell(Container(
          alignment: Alignment.center,
          height: getSize(30),
          width: (detectedStatusType == DetectedStatusType.missingKey ||
                  detectedStatusType == DetectedStatusType.missingVendor )
              ? getSize(120)
              : getSize(70),
          decoration: BoxDecoration(
              color: detectedStatusType == DetectedStatusType.online
                  ? AppColorConstants.colorGreen2
                  : detectedStatusType == DetectedStatusType.offline
                      ? AppColorConstants.colorH2.withOpacity(0.4)
                      : (detectedStatusType == DetectedStatusType.pending)
                          ? AppColorConstants.colorOrange
                          : (detectedStatusType == DetectedStatusType.fwDownload)
                              ? AppColorConstants.colorPrimary
                              : AppColorConstants.colorH2.withOpacity(0.4),
              borderRadius: BorderRadius.circular(getSize(8))),
          child: AppText(
            detectedStatusType == DetectedStatusType.online
                ? S.of(context!).online
                : detectedStatusType == DetectedStatusType.offline
                    ? S.of(context!).offline
                    : detectedStatusType == DetectedStatusType.pending
                        ? S.of(context!).pending
                        : detectedStatusType == DetectedStatusType.fwDownload
                            ? S.of(context!).fwdnld
                            : detectedStatusType == DetectedStatusType.missingKey
                            ? S.of(context!).missingKey
                            : detectedStatusType == DetectedStatusType.missingVendor
                                ? S.of(context!).missingVendor
                                : S.of(context!).offline,
            style: TextStyle(
                color: (detectedStatusType == DetectedStatusType.offline ||
                        detectedStatusType == DetectedStatusType.missingKey ||
                        detectedStatusType == DetectedStatusType.missingVendor ||
                        dessert.status == null)
                    ? AppColorConstants.colorH3
                    : AppColorConstants.colorWhite,
                fontFamily: AppAssetsConstants.sourceSans,
                fontSize: 13,
                fontWeight: FontWeight.w600),
          ),
        )),
        DataCell(AppText(beID,
            style: dataTableHelper.dataRowTextStyle)),
        if (AppConfig.shared.isQLCentral)
          DataCell(AppText(siteName,
              style: dataTableHelper.dataRowTextStyle))else
          DataCell(AppText(gatWayName,
              style: dataTableHelper.dataRowTextStyle)),
        DataCell(AppText(
          dessert.lastSeen != null ? getUtcTimeZone(dessert.lastSeen) : '',
          style: dataTableHelper.dataRowTextStyle,
        )),
      ],
    );
  }

  @override
  int get rowCount => list.length;

  @override
  bool get isRowCountApproximate => false;

  @override
  int get selectedRowCount => _selectedCount;

  void selectAll(bool? checked) {
    for (final dessert in list) {
      dessert.selected = checked ?? false;
    }
    _selectedCount = (checked ?? false) ? list.length : 0;
    notifyListeners();
  }
}
