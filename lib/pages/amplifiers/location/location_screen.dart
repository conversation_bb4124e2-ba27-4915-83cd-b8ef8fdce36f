import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:latlong2/latlong.dart';
import 'package:quantumlink_node/app_import.dart';
import "package:universal_html/html.dart" as html;
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart' as latlong;

import '../../../app/helper/location_helper.dart';
class LocationScreen extends StatefulWidget {
  final AmplifierPageHelper ampPageHelper;

  const LocationScreen({super.key, required this.ampPageHelper});

  @override
  State<LocationScreen> createState() => LocationScreenState();
}

class LocationScreenState extends State<LocationScreen> {
  final MapController mapController = MapController();
   latlong.LatLng initialLocation = const latlong.LatLng(33.46186, -84.23398);
  List<Marker> markers = [];
  AmplifierController amplifierController =Get.put<AmplifierController>(AmplifierController());
  List<latlong.LatLng> locations = [];
  bool _isBlinkingMarkerVisible = true;
  Timer? _blinkingTimer;
   double _initialZoom = 14.0;
  latlong.LatLng? _initialLocationParsed;
  bool isOffline = false;
  String ? _initialLocationIcon;
  @override
  void initState() {
    super.initState();
    getInitialCameraPosition(widget.ampPageHelper.initialLocationValue);
    getLocationCamaraPosition();
    isOffline = html.window.navigator.onLine!;
  }

  @override
  void dispose() {
    mapController.dispose();
    widget.ampPageHelper.locationDeviceStreamController.close();
    _blinkingTimer?.cancel();
    super.dispose();
  }

  getInitialCameraPosition(String initialLocationValue) async {

    if (initialLocationValue.isNotEmpty) {
      try {
        if(isValidLatLng(parseLatLng(initialLocationValue))){
          final latLng = parseLatLng(initialLocationValue) ;
          _initialLocationParsed = latlong.LatLng(latLng.latitude, latLng.longitude);
        }

      } catch (e) {
        debugPrint("Invalid initialLocationValue: $e");
      }
    }
  }

  getLocationCamaraPosition() async {
    final googleLocations = parseLocations(widget.ampPageHelper.amplifierDataSource.list
        .map((e) => e.location.toString())
        .toList());
    locations = googleLocations.map((loc) => latlong.LatLng(loc.latitude, loc.longitude)).toList();
    await requestLocationData();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AmplifierController>(
      init: AmplifierController(),
      builder: (AmplifierController controller) {
        return AnnotatedRegion<SystemUiOverlayStyle>(
          value: SystemUiOverlayStyle.light,
          child: Scaffold(
            resizeToAvoidBottomInset: false,
            body: Stack(
              children: [
                if (!isOffline)
                  Center(
                    child: AppText(
                      "Internet connection is required to load the map",
                      style: TextStyle(
                        fontFamily: AppAssetsConstants.openSans,
                        fontSize: 18,
                        fontWeight: getBoldFontWeight(),
                        color: AppColorConstants.colorH2,
                      ),
                    ),
                  )
                else
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
                    child: SizedBox(
                      width: double.infinity,
                      height: 600,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(5),
                        child: FlutterMap(
                          mapController: mapController,
                          options: MapOptions(
                            center: _initialLocationParsed ?? initialLocation,
                            zoom:_initialZoom,
                            minZoom: 4,
                            maxZoom: 18,
                            onPointerHover: (event, point) {
                              _checkMarkerHover(event.localPosition, point);
                            },
                            onMapEvent: (event) {
                              if (event is MapEventMove) {
                                customInfoWindowController.onCameraMove?.call();
                              }
                            },
                          ),
                          children: [
                            TileLayer(
                              urlTemplate: AppAssetsConstants.openStreetMapUrl,
                            ),
                            MarkerLayer(
                              markers: markers,
                            ),
                            Builder(
                              builder: (context) {
                                customInfoWindowController.mapContext = context;
                                customInfoWindowController.mapController = mapController;
                                return const SizedBox.shrink();
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                CustomInfoWindow(controller: customInfoWindowController),

                Positioned(
                  bottom: 23.0,
                  right: 30,
                  child: zoomInAndOutWidget(),
                ),
                openStreetMapCopyRightLinkView(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget openStreetMapCopyRightLinkView() {
    return Positioned(
      bottom: 15,
      left: 20,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.8),
          borderRadius: BorderRadius.circular(4),
        ),
        child: RichText(
          text: TextSpan(
            style: TextStyle(
              fontSize: 12,
              color: AppColorConstants.colorBlackBlue,
            ),
            children: [
              TextSpan(text: '${S.of(context).openStreetMap}',style: const TextStyle(
                color: Colors.blue,
                decoration: TextDecoration.underline,
              ),
                recognizer: TapGestureRecognizer()
                  ..onTap = () {
                    html.window.open(AppAssetsConstants.openStreetMapCopyRightURL, "");
                  },),
              TextSpan(text: ' ${S.of(context).contributors}',),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> requestLocationData() async {
    String ic_offline = AppAssetsConstants.starIcon;
    String ic_star_online = AppAssetsConstants.ic_star_online;
    String ic_star_pending = AppAssetsConstants.ic_star_pending;
    markers = [];
    if (widget.ampPageHelper.initialLocationValue.isNotEmpty) {
      try {
        final latLng = parseLatLng(widget.ampPageHelper.initialLocationValue);
        _initialLocationParsed = latlong.LatLng(latLng.latitude, latLng.longitude);
      } catch (e) {
        debugPrint("Invalid initialLocationValue: $e");
      }
    }
     if(locations.isNotEmpty) initialLocation = latlong.LatLng(locations.first.latitude, locations.first.longitude);
        widget.ampPageHelper.amplifierDataSource.list.forEach((AmplifierDeviceItem e) {
      if (e.location != null && e.location.toString().isNotEmpty && e.location.toString().trim() != ",") {
        try {
          final latLng = parseLatLng(e.location);
          final parsedLocation = latlong.LatLng(latLng.latitude, latLng.longitude);
          final icon = (getDetectedStatusType(e.status) == DetectedStatusType.online)
              ? ic_star_online
              : ((getDetectedStatusType(e.status) == DetectedStatusType.pending)
              ? ic_star_pending
              : ic_offline);

          if (widget.ampPageHelper.initialLocationValue.isNotEmpty) {
            _initialZoom=18;
            if (parsedLocation != _initialLocationParsed) {
              markers.add(
                Marker(
                  point: parsedLocation,
                  child: AppImageAsset(image:icon),
                ),
              );
            }else{
              _initialLocationIcon = icon;
            }
          } else {

            markers.add(
              Marker(
                point: parsedLocation,
                child: AppImageAsset(image:icon),
              ),
            );
          }
        } catch (e) {
          debugPrint("Invalid location: $e");
        }
      }
    });
    void _listenToDeviceStream(String ic_offline, String ic_star_online, String ic_star_pending) {
      widget.ampPageHelper.locationDeviceStreamController.stream.listen((List<AmplifierDeviceItem> onData) {
        for (final element in onData) {
          final latLng = parseLatLng(element.location);
          final parsedLocation = latlong.LatLng(latLng.latitude, latLng.longitude);
          Widget icon = (getDetectedStatusType(element.status) == DetectedStatusType.online)
              ? AppImageAsset(image: ic_star_online)
              : ((getDetectedStatusType(element.status) == DetectedStatusType.pending)
              ? AppImageAsset(image: ic_star_pending)
              : AppImageAsset(image: ic_offline));

          // Find the index of the marker with the same location
          final int markerIndex = markers.indexWhere((marker) => marker.point == parsedLocation);
          if (markerIndex != -1) {
            final Marker existingMarker = markers[markerIndex];
            // Create a new marker with the updated icon
            final bool iconChanged =
                (existingMarker.child is AppImageAsset && (existingMarker.child as AppImageAsset).image != (icon as AppImageAsset).image);
            final bool positionChanged = existingMarker.point != parsedLocation;
            if (iconChanged || positionChanged) {
              // Replace the old marker with the new one
              markers[markerIndex] = Marker(
                point: parsedLocation,
                child: icon,
              );
            }
          } else {
            // If marker doesn't exist, add it
            markers.add(Marker(
              point: parsedLocation,
              child: icon,
            ));
          }
        }
        // Notify the UI to update
        setState(() {});
        amplifierController.update();
      });
    }
    _listenToDeviceStream(ic_offline,ic_star_online, ic_star_pending);
    if (widget.ampPageHelper.initialLocationValue.isNotEmpty && _initialLocationParsed != null) {
      _startBlinkingMarker(_initialLocationIcon);
    }

    setState(() {});
  }

  void _startBlinkingMarker(String? initialLocationIcon) {
    var counter = 8;
    _blinkingTimer?.cancel();
    _blinkingTimer = Timer.periodic(const Duration(milliseconds: 300), (timer) {
      counter--;
      if (counter == 0) {
        timer.cancel();
      }
      _isBlinkingMarkerVisible = !_isBlinkingMarkerVisible;
      if (_isBlinkingMarkerVisible && _initialLocationParsed != null) {
        markers.add(
          Marker(
            point: _initialLocationParsed!,
            child:  AppImageAsset(image:initialLocationIcon!),
          ),
        );
      } else {
        markers.removeWhere((marker) => marker.point == _initialLocationParsed);
      }
      setState(() {});
    });
  }

  CustomInfoWindowController customInfoWindowController = CustomInfoWindowController();

  void _checkMarkerHover(Offset localPosition, latlong.LatLng point) {
    bool isHovering = false;
    for (final marker in markers) {
      // Convert marker coordinates to screen coordinates
      final screenPoint = mapController.camera.latLngToScreenPoint(marker.point);
      if (screenPoint == null) continue;

      final markerRect = Rect.fromCenter(
        center: Offset(screenPoint.x, screenPoint.y),
        width: 35,
        height: 70,
      );

      if (markerRect.contains(localPosition)) {
        isHovering = true;
        final ampDeviceItem = widget.ampPageHelper.amplifierDataSource.list.firstWhere(
          (element) {
            if (element.location != null && element.location.toString().isNotEmpty) {
              try {
                final latLng = parseLatLng(element.location);
                return latLng.latitude == marker.point.latitude && latLng.longitude == marker.point.longitude;
              } catch (e) {
                return false;
              }
            }
            return false;
          },
          orElse: () => AmplifierDeviceItem.empty(),
        );

        if (ampDeviceItem.deviceEui != null) {
          customInfoWindowController.addInfoWindow!(
            ampInfoView(ampDeviceItem, customInfoWindowController.hideInfoWindow!),
            marker.point,
            20,
            260,
            280,
          );
        }
        break;
      }
    }

    if (!isHovering) {
      customInfoWindowController.hideInfoWindow!();
    }
  }

  Widget ampInfoView(AmplifierDeviceItem ampDeviceItem, VoidCallback? onPressed) {
    DetectedStatusType? detectedStatusType = getDetectedStatusType(ampDeviceItem.status);
    String ampDeviceMode = ampDeviceItem.bwMode == null
        ? ""
        : (ampDeviceItem.bwMode == 0
            ? "1.2"
            : (ampDeviceItem.bwMode == 1 ? "1.8" : ""));

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      width: 250,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 8.0, right: 8.0, top: 8.0),
            child: Align(
              alignment: Alignment.topRight,
              child: InkWell(
                onTap: onPressed,
                child: const Icon(
                  Icons.close,
                  size: 14,
                ),
              ),
            ),
          ),
          infoTitleView(
            title: S.of(context).deviceEUI,
            value: ampDeviceItem.deviceEui,
          ),
          infoTitleView(
            title: S.of(context).type,
            value: ampDeviceItem.type ?? "",
          ),
          infoTitleView(
            title: S.of(context).status,
            value: detectedStatusType == DetectedStatusType.online
                ? S.of(context).online
                : detectedStatusType == DetectedStatusType.offline
                    ? S.of(context).offline
                    : detectedStatusType == DetectedStatusType.pending
                        ? S.of(context).pending
                        : S.of(context).offline,
          ),
          infoTitleView(
            title: S.of(context).location,
            value: ampDeviceItem.location ?? "",
          ),
          infoTitleView(
            title: S.of(context).deviceAlias,
            value: ampDeviceItem.deviceAlias ?? "",
          ),
          infoTitleView(
            title: S.of(context).amplifierMode,
            value: "$ampDeviceMode ${S.of(context).ghZ}",
          ),
          infoTitleView(
            title: S.of(context).placement,
            value: ampDeviceItem.placement ?? "",
          ),
          infoTitleView(
            title: S.of(context).lastSeen,
            value: ampDeviceItem.lastSeen != null
                ? getUtcTimeZone(ampDeviceItem.lastSeen)
                : S.of(context).unknown,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 13),
            child: AppButton(
              fontSize: 14.5,
              buttonName: S.of(context).open,
              onPressed: () {
                if (detectedStatusType == DetectedStatusType.pending) {
                  S.of(context).amplifierIsOffline.showOffline(context);
                } else {
                  if(AppConfig.shared.isOpenFromBLE) {
                    widget.ampPageHelper.updateTabAndNavigateToDetail(ampDeviceItem) ;
                  }else{
                    widget.ampPageHelper.addTabTableOnTap(ampDeviceItem);
                  }

                }
              },
            ),
          ),
          const SizedBox(height: 6),
        ],
      ),
    );
  }

  Widget infoTitleView({required dynamic title, required dynamic value}) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 1),
      child: SelectionArea(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            AppText(
              isSelectableText: false,
              "$title: ",
              style: TextStyle(
                fontFamily: AppAssetsConstants.openSans,
                fontWeight: getMediumBoldFontWeight(),
                fontSize: 14,
              ),
            ),
            Expanded(
              child: Text(
                "$value",
                style: TextStyle(
                  fontFamily: AppAssetsConstants.openSans,
                  fontWeight: getMediumBoldFontWeight(),
                  fontSize: 14,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  bool isValidLatLng(LatLng latLng) {
    return (latLng.latitude >= -90 && latLng.latitude <= 90) &&
        (latLng.longitude >= -180 && latLng.longitude <= 180);
  }

  List<LatLng> parseLocations(List<dynamic> rawLocations) {
    final Set<LatLng> uniqueLocations = {};
    for (var location in rawLocations) {
      if (location != "null" && location is String && location.isNotEmpty && location.toString().trim() !=",") {
        try {
          final parsedLocation = parseLatLng(location);
          uniqueLocations.add(parsedLocation);
        } catch (e) {
          debugPrint(e.toString());
        }
      }
    }
    return uniqueLocations.toList();
  }

  String convertLatLngToCustomString(LatLng latLng) {
    String latString = (latLng.latitude * 10000).toStringAsFixed(2);
    String lngString = (latLng.longitude * 10000).toStringAsFixed(2);
    return '$latString,$lngString';
  }

  bool isLocationMatch(String elementLocation, String formattedLocation) {
    if (elementLocation.isEmpty || formattedLocation.isEmpty) {
      return false;
    }
    return elementLocation == formattedLocation;
  }

  List<AmplifierDeviceItem> findAmplifierDeviceItem(LatLng location) {
    String formattedLocation = convertLatLngToCustomString(location);
    List<AmplifierDeviceItem> ampList = widget.ampPageHelper.amplifierDeviceList.result
        .where((element) => isLocationMatch(
            element.location.toString().isNotEmpty &&
                    element.location != null &&
                    element.location?.trim() != ","
                ? convertLatLngToCustomString(parseLatLng(element.location!))
                : "",
            formattedLocation))
        .toList();
    return ampList;
  }

  zoomInAndOutWidget(){
    return Column(
      children: [
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            maximumSize: const Size(40, 40),
            minimumSize: const Size(24, 32),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            backgroundColor: Colors.white,
            padding: EdgeInsets.zero,
          ),
          child: const Center(
            child: Icon(Icons.add),
          ),
          onPressed: () async {
            final currentZoom = mapController.zoom;
            final center = mapController.center;
            mapController.move(center, currentZoom + 1);
          },
        ),
        const SizedBox(
          height: 16,
        ),
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            maximumSize: const Size(40, 40),
            minimumSize: const Size(24, 32),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            backgroundColor: Colors.white,
            padding: EdgeInsets.zero,
          ),
          child: const Center(
            child: Icon(Icons.remove),
          ),
          onPressed: () async {
            final currentZoom = mapController.zoom;
            final center = mapController.center;
            mapController.move(center, currentZoom - 1);
          },
        ),
      ],
    );
  }
}


class NoScrollBehavior extends ScrollBehavior {
  @override
  ScrollPhysics getScrollPhysics(BuildContext context) {
    return const NeverScrollableScrollPhysics();
  }
}