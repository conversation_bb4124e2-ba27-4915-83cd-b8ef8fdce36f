import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:latlong2/latlong.dart';
import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/controller/map_topology_controller.dart';
import "package:universal_html/html.dart" as html;
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart' as latlong;

import '../../../app/helper/location_helper.dart';
import '../../../serialized/map_topology/map_layers_response.dart';

class LocationScreen extends StatefulWidget {
  final AmplifierPageHelper ampPageHelper;

  const LocationScreen({super.key, required this.ampPageHelper});

  @override
  State<LocationScreen> createState() => LocationScreenState();
}

class LocationScreenState extends State<LocationScreen> {
  final MapController mapController = MapController();
   latlong.LatLng initialLocation = const latlong.LatLng(37.068851, -88.669345);
  List<Marker> markers = [];
  AmplifierController amplifierController =Get.put<AmplifierController>(AmplifierController());
  bool _isBlinkingMarkerVisible = true;
  Timer? _blinkingTimer;
   final double _initialZoom = 9;
  latlong.LatLng? _initialLocationParsed;
  bool isOffline = false;
  String ? _initialLocationIcon;
  
  // Map topology integration
  MapTopologyController mapTopologyController = Get.put<MapTopologyController>(MapTopologyController());
  List<MapDevice> _currentDevices = [];
  List<MapDeviceLink> _currentConnections = [];
  bool _isLoading = false;
  String? _errorMessage;
  @override
  void initState() {
    super.initState();
    getInitialCameraPosition(widget.ampPageHelper.initialLocationValue);
    getLocationCamaraPosition();
    isOffline = html.window.navigator.onLine!;
  }

  @override
  void dispose() {
    mapController.dispose();
    widget.ampPageHelper.locationDeviceStreamController.close();
    _blinkingTimer?.cancel();
    super.dispose();
  }

  getInitialCameraPosition(String initialLocationValue) async {

    if (initialLocationValue.isNotEmpty) {
      try {
        if(isValidLatLng(parseLatLng(initialLocationValue))){
          final latLng = parseLatLng(initialLocationValue) ;
          _initialLocationParsed = latlong.LatLng(latLng.latitude, latLng.longitude);
        }

      } catch (e) {
        debugPrint("Invalid initialLocationValue: $e");
      }
    }
  }

  getLocationCamaraPosition() async {
    await _fetchMapData();
  }

  /// Fetch map data from API using map topology helper
  Future<void> _fetchMapData() async {
    _setLoading(true);
    _clearError();
    
    try {
      // Build layers based on filter states
      final layers = _buildLayersArray();
      final request = MapLayersRequest(
        layers: layers,
        filters: {},
        bbox: [0],
        context: {},
        autoFit: false,
      );
      
      final result = await mapTopologyController.getMapLayersData(
        context: context, 
        request: request
      );

      if (result is ParsedMapLayersData) {
        final devices = _convertMapDevices(result.devices);
        final connections = result.deviceLinks;
        _updateDevices(devices);
        _updateConnections(connections);

        await requestLocationData();
      }
    } catch (e) {
      _handleError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  /// Build layers array based on filter states
  List<String> _buildLayersArray() {
    final layers = <String>[];
    layers.add(AppStringConstants.devices);
    layers.add(AppStringConstants.deviceLinks);
    return layers;
  }

  /// Convert MapDevice list
  List<MapDevice> _convertMapDevices(List<MapDevice> mapDevices) {
    return mapDevices
        .map((mapDevice) => MapDevice(
              deviceEui: mapDevice.deviceEui,
              deviceAlias: mapDevice.deviceAlias,
              position: mapDevice.position,
              status: mapDevice.status,
              lastSeen: mapDevice.lastSeen,
              vendorCode: mapDevice.vendorCode,
              type: mapDevice.type,
              description: mapDevice.description,
              bwMode: mapDevice.bwMode,
              placement: mapDevice.placement,
            ))
        .toList();
  }

  void _updateDevices(List<MapDevice> devices) {
    _currentDevices = devices;
    _clearError();
  }

  void _updateConnections(List<MapDeviceLink> connections) {
    _currentConnections = connections;
  }

  /// Build polylines for device connections
  List<Polyline> _buildPolylines() {
    List<Polyline> polylines = [];

    // Filter connections to only include those with valid devices
    final deviceMap = {for (var d in _currentDevices) d.deviceEui: d};
    final filteredConnections = _currentConnections.where((conn) {
      final fromDevice = deviceMap[conn.sourceId];
      final toDevice = deviceMap[conn.targetId];
      return fromDevice != null && toDevice != null;
    }).toList();

    // Create polylines for each connection
    for (final conn in filteredConnections) {
      final points = _getPolylinePoints(conn);
      if (points != null) {
        polylines.add(
          Polyline(
            points: points.map((point) => latlong.LatLng(point.latitude, point.longitude)).toList(),
            color: AppColorConstants.colorPolyLine ?? Colors.blue,
            strokeWidth: 2,
          ),
        );
      }
    }

    return polylines;
  }

  /// Get polyline points for a connection
  List<LatLng>? _getPolylinePoints(MapDeviceLink conn) {
    if (conn.polylinePoints.isNotEmpty) {
      return conn.polylinePoints;
    } else {
      try {
        final sourceDevice = _currentDevices.firstWhere((d) => d.deviceEui == conn.sourceId);
        final targetDevice = _currentDevices.firstWhere((d) => d.deviceEui == conn.targetId);
        return [sourceDevice.position, targetDevice.position];
      } catch (e) {
        return null;
      }
    }
  }
  void _handleError(String error) {
    debugLogs("Error fetching devices: $error");
    _errorMessage ="Failed to fetch map data: $error";
  }


  void _setLoading(bool loading) {
    _isLoading = loading;
    setState(() {});
  }


  void _clearError() {
    _errorMessage = null;
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AmplifierController>(
      init: AmplifierController(),
      builder: (AmplifierController controller) {
        return AnnotatedRegion<SystemUiOverlayStyle>(
          value: SystemUiOverlayStyle.light,
          child: Scaffold(
            resizeToAvoidBottomInset: false,
            body: Stack(
              children: [
                if (!isOffline)
                  Center(
                    child: AppText(
                      "Internet connection is required to load the map",
                      style: TextStyle(
                        fontFamily: AppAssetsConstants.openSans,
                        fontSize: 18,
                        fontWeight: getBoldFontWeight(),
                        color: AppColorConstants.colorH2,
                      ),
                    ),
                  )
                else
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
                    child: SizedBox(
                      width: double.infinity,
                      height: 600,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(5),
                        child: FlutterMap(
                          mapController: mapController,
                          options: MapOptions(
                            center: _initialLocationParsed ?? initialLocation,
                            zoom:_initialZoom,
                            minZoom: 4,
                            maxZoom: 18,
                            onPointerHover: (event, point) {
                              _checkMarkerHover(event.localPosition, point);
                            },
                            onMapEvent: (event) {
                              if (event is MapEventMove) {
                                customInfoWindowController.onCameraMove?.call();
                              }
                            },
                          ),
                          children: [
                            TileLayer(
                              urlTemplate: AppAssetsConstants.openStreetMapUrl,
                            ),
                            PolylineLayer(
                              polylines: _buildPolylines(),
                            ),
                            MarkerLayer(
                              markers: markers,
                            ),
                            Builder(
                              builder: (context) {
                                customInfoWindowController.mapContext = context;
                                customInfoWindowController.mapController = mapController;
                                return const SizedBox.shrink();
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                CustomInfoWindow(controller: customInfoWindowController),

                Positioned(
                  bottom: 23.0,
                  right: 30,
                  child: zoomInAndOutWidget(),
                ),
                _buildZoomInfoBar(),
                openStreetMapCopyRightLinkView(),
                if (_buildErrorOverlay() != null) _buildErrorOverlay()!,
              ],
            ),
          ),
        );
      },
    );
  }
  Widget _buildZoomInfoBar() {
    if (_isLoading) {
      return Container(
      margin: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: AppColorConstants.colorBackgroundDark,
        border: Border(
          top: BorderSide(color: AppColorConstants.colorDivider.withOpacity(0.3)),
          bottom: BorderSide(color: AppColorConstants.colorDivider.withOpacity(0.3)),
        ),
      ),
      child: Row(
        children: [
          if (_isLoading) ...[
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
            const SizedBox(width: 8),
            Text(S.of(context).loadingDevices),
          ]
        ],
      ),
    );
    }
    return const SizedBox();
  }

  Widget openStreetMapCopyRightLinkView() {
    return Positioned(
      bottom: 15,
      left: 20,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.8),
          borderRadius: BorderRadius.circular(4),
        ),
        child: RichText(
          text: TextSpan(
            style: TextStyle(
              fontSize: 12,
              color: AppColorConstants.colorBlackBlue,
            ),
            children: [
              TextSpan(text: '${S.of(context).openStreetMap}',style: const TextStyle(
                color: Colors.blue,
                decoration: TextDecoration.underline,
              ),
                recognizer: TapGestureRecognizer()
                  ..onTap = () {
                    html.window.open(AppAssetsConstants.openStreetMapCopyRightURL, "");
                  },),
              TextSpan(text: ' ${S.of(context).contributors}',),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> requestLocationData() async {
    String ic_offline = AppAssetsConstants.starIcon;
    String ic_star_online = AppAssetsConstants.ic_star_online;
    String ic_star_pending = AppAssetsConstants.ic_star_pending;
    markers = [];
    
    if (widget.ampPageHelper.initialLocationValue.isNotEmpty) {
      try {
        final latLng = parseLatLng(widget.ampPageHelper.initialLocationValue);
        _initialLocationParsed = latlong.LatLng(latLng.latitude, latLng.longitude);
      } catch (e) {
        debugPrint("Invalid initialLocationValue: $e");
      }
    }

     if(_currentDevices.isNotEmpty) initialLocation =_currentDevices.first.position;
     mapController.move(_initialLocationParsed != null ? _initialLocationParsed! : initialLocation,
        _initialLocationParsed != null ? 16 : 12);
    // Use API data instead of local amplifier data
    _currentDevices.forEach((MapDevice device) {
      if (device.position != null) {
        try {
          final parsedLocation = latlong.LatLng(device.position!.latitude, device.position!.longitude);
          final icon = (device.status == DetectedStatusType.online)
              ? ic_star_online
              : ((device.status == DetectedStatusType.pending)
              ? ic_star_pending
              : ic_offline);

          if (widget.ampPageHelper.initialLocationValue.isNotEmpty) {
            if (parsedLocation != _initialLocationParsed) {
              markers.add(
                Marker(
                  point: parsedLocation,
                  child: AppImageAsset(image:icon),
                ),
              );
            } else {
              _initialLocationIcon = icon;
            }
          } else {
            markers.add(
              Marker(
                point: parsedLocation,
                child: AppImageAsset(image:icon),
              ),
            );
          }
        } catch (e) {
          debugPrint("Invalid location: $e");
        }
      }
    });
    
    if (widget.ampPageHelper.initialLocationValue.isNotEmpty && _initialLocationParsed != null) {
      _startBlinkingMarker(_initialLocationIcon);
    }

    setState(() {});
  }

  void _startBlinkingMarker(String? initialLocationIcon) {
    var counter = 8;
    _blinkingTimer?.cancel();
    _blinkingTimer = Timer.periodic(const Duration(milliseconds: 300), (timer) {
      counter--;
      if (counter == 0) {
        timer.cancel();
      }
      _isBlinkingMarkerVisible = !_isBlinkingMarkerVisible;
      if (_isBlinkingMarkerVisible && _initialLocationParsed != null) {
        markers.add(
          Marker(
            point: _initialLocationParsed!,
            child:  AppImageAsset(image:initialLocationIcon!),
          ),
        );
      } else {
        markers.removeWhere((marker) => marker.point == _initialLocationParsed);
      }
      setState(() {});
    });
  }

  CustomInfoWindowController customInfoWindowController = CustomInfoWindowController();

  void _checkMarkerHover(Offset localPosition, latlong.LatLng point) {
    bool isHovering = false;
    for (final marker in markers) {
      // Convert marker coordinates to screen coordinates
      final screenPoint = mapController.camera.latLngToScreenPoint(marker.point);
      if (screenPoint == null) continue;

      final markerRect = Rect.fromCenter(
        center: Offset(screenPoint.x, screenPoint.y),
        width: 35,
        height: 35,
      );

      if (markerRect.contains(localPosition)) {
        isHovering = true;
        final mapDevice = _currentDevices.firstWhere(
          (element) {
            return element.position.latitude == marker.point.latitude &&
                   element.position.longitude == marker.point.longitude;
                      return false;
          },
        );

        if (mapDevice.deviceEui.isNotEmpty) {
          customInfoWindowController.addInfoWindow!(
            ampInfoViewFromMapDevice(mapDevice, customInfoWindowController.hideInfoWindow!),
            marker.point,
            5,
            255,
            280,
          );
        }
        break;
      }
    }

    if (!isHovering) {
      customInfoWindowController.hideInfoWindow!();
    }
  }

  Widget ampInfoView(AmplifierDeviceItem ampDeviceItem, VoidCallback? onPressed) {
    DetectedStatusType? detectedStatusType = getDetectedStatusType(ampDeviceItem.status);
    String ampDeviceMode = ampDeviceItem.bwMode == null
        ? ""
        : (ampDeviceItem.bwMode == 0
            ? "1.2"
            : (ampDeviceItem.bwMode == 1 ? "1.8" : ""));

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      width: 250,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 8.0, right: 8.0, top: 8.0),
            child: Align(
              alignment: Alignment.topRight,
              child: InkWell(
                onTap: onPressed,
                child: const Icon(
                  Icons.close,
                  size: 14,
                ),
              ),
            ),
          ),
          infoTitleView(
            title: S.of(context).deviceEUI,
            value: ampDeviceItem.deviceEui,
          ),
          infoTitleView(
            title: S.of(context).type,
            value: ampDeviceItem.type ?? "",
          ),
          infoTitleView(
            title: S.of(context).status,
            value: detectedStatusType == DetectedStatusType.online
                ? S.of(context).online
                : detectedStatusType == DetectedStatusType.offline
                    ? S.of(context).offline
                    : detectedStatusType == DetectedStatusType.pending
                        ? S.of(context).pending
                        : S.of(context).offline,
          ),
          infoTitleView(
            title: S.of(context).location,
            value: ampDeviceItem.location ?? "",
          ),
          infoTitleView(
            title: S.of(context).deviceAlias,
            value: ampDeviceItem.deviceAlias ?? "",
          ),
          infoTitleView(
            title: S.of(context).amplifierMode,
            value: "$ampDeviceMode ${S.of(context).ghZ}",
          ),
          infoTitleView(
            title: S.of(context).placement,
            value: ampDeviceItem.placement ?? "",
          ),
          infoTitleView(
            title: S.of(context).lastSeen,
            value: ampDeviceItem.lastSeen != null
                ? getUtcTimeZone(ampDeviceItem.lastSeen)
                : S.of(context).unknown,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 13),
            child: AppButton(
              fontSize: 14.5,
              buttonName: S.of(context).open,
              onPressed: () {
                if (detectedStatusType == DetectedStatusType.pending) {
                  S.of(context).amplifierIsOffline.showOffline(context);
                } else {
                  if(AppConfig.shared.isOpenFromBLE) {
                    widget.ampPageHelper.updateTabAndNavigateToDetail(ampDeviceItem) ;
                  }else{
                    widget.ampPageHelper.addTabTableOnTap(ampDeviceItem);
                  }

                }
              },
            ),
          ),
          const SizedBox(height: 6),
        ],
      ),
    );
  }

  /// Create info view from MapDevice (API data)
  Widget ampInfoViewFromMapDevice(MapDevice mapDevice, VoidCallback? onPressed) {
    String ampDeviceMode = mapDevice.bwMode == null
        ? ""
        : (mapDevice.bwMode == 0
            ? "1.2"
            : (mapDevice.bwMode == 1 ? "1.8" : ""));

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      width: 250,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 8.0, right: 8.0, top: 8.0),
            child: Align(
              alignment: Alignment.topRight,
              child: InkWell(
                onTap: onPressed,
                child: const Icon(
                  Icons.close,
                  size: 14,
                ),
              ),
            ),
          ),
          infoTitleView(
            title: S.of(context).deviceEUI,
            value: mapDevice.deviceEui,
          ),
          infoTitleView(
            title: S.of(context).type,
            value: mapDevice.type,
          ),
          infoTitleView(
            title: S.of(context).status,
            value: mapDevice.status == DetectedStatusType.online
                ? S.of(context).online
                : mapDevice.status == DetectedStatusType.pending
                    ? S.of(context).pending
                    : mapDevice.status == DetectedStatusType.offline
                        ? S.of(context).offline
                        : S.of(context).offline,
          ),
          infoTitleView(
            title: S.of(context).location,
            value: mapDevice.position != null
                ? '${mapDevice.position.latitude.toStringAsFixed(6)}, ${mapDevice.position.longitude.toStringAsFixed(6)}'
                : "",
          ),
          infoTitleView(
            title: S.of(context).deviceAlias,
            value: mapDevice.deviceAlias,
          ),
          infoTitleView(
            title: S.of(context).amplifierMode,
            value: "$ampDeviceMode ${S.of(context).ghZ}",
          ),
          infoTitleView(
            title: S.of(context).placement,
            value: mapDevice.placement,
          ),
          infoTitleView(
            title: S.of(context).lastSeen,
            value: mapDevice.lastSeen != null
                ? getUtcTimeZone(mapDevice.lastSeen)
                : S.of(context).unknown,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 13),
            child: AppButton(
              fontSize: 14.5,
              buttonName: S.of(context).open,
              buttonColor: _deviceExistsInAmplifierList(mapDevice.deviceEui)
                  ? AppColorConstants.colorLightBlue
                  : AppColorConstants.colorH2.withOpacity(0.5),
              borderColor: _deviceExistsInAmplifierList(mapDevice.deviceEui)
                  ? AppColorConstants.colorLightBlue
                  : AppColorConstants.colorH2.withOpacity(0.5),
              onPressed: _deviceExistsInAmplifierList(mapDevice.deviceEui)
                  ? () {
                      if (mapDevice.status == DetectedStatusType.pending) {
                  S.of(context).amplifierIsOffline.showOffline(context);
                } else {
                  // Convert MapDevice to AmplifierDeviceItem for navigation
                  final ampDeviceItem = widget.ampPageHelper.amplifierDeviceList.result.firstWhere(
                        (item) => item.deviceEui == mapDevice.deviceEui,
                  );
                  if(AppConfig.shared.isOpenFromBLE) {
                    widget.ampPageHelper.updateTabAndNavigateToDetail(ampDeviceItem);
                  } else {
                    widget.ampPageHelper.addTabTableOnTap(ampDeviceItem);
                  }
                }
              } : null,
            ),
          ),
          const SizedBox(height: 6),
        ],
      ),
    );
  }

  bool _deviceExistsInAmplifierList(String deviceEui) {
    return widget.ampPageHelper.amplifierDeviceList.result.any(
      (item) => item.deviceEui == deviceEui
    );
  }
  Widget? _buildErrorOverlay() {
    if (_errorMessage == null) return null;

    return Positioned(
      bottom: 40,
      left: 20,
      right: 90,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.red[100],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.red),
        ),
        child: Row(
          children: [
            const Icon(Icons.error, color: Colors.red),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                _errorMessage??"",
                style: const TextStyle(color: Colors.red),
              ),
            ),
            IconButton(
              icon: const Icon(Icons.close, color: Colors.red),
              onPressed: () => _clearError(),
            ),
          ],
        ),
      ),
    );
  }
  Widget infoTitleView({required dynamic title, required dynamic value}) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 1),
      child: SelectionArea(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            AppText(
              isSelectableText: false,
              "$title: ",
              style: TextStyle(
                fontFamily: AppAssetsConstants.openSans,
                fontWeight: getMediumBoldFontWeight(),
                fontSize: 14,
              ),
            ),
            Expanded(
              child: Text(
                "$value",
                style: TextStyle(
                  fontFamily: AppAssetsConstants.openSans,
                  fontWeight: getMediumBoldFontWeight(),
                  fontSize: 14,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  bool isValidLatLng(LatLng latLng) {
    return (latLng.latitude >= -90 && latLng.latitude <= 90) &&
        (latLng.longitude >= -180 && latLng.longitude <= 180);
  }

  List<LatLng> parseLocations(List<dynamic> rawLocations) {
    final Set<LatLng> uniqueLocations = {};
    for (var location in rawLocations) {
      if (location != "null" && location is String && location.isNotEmpty && location.toString().trim() !=",") {
        try {
          final parsedLocation = parseLatLng(location);
          uniqueLocations.add(parsedLocation);
        } catch (e) {
          debugPrint(e.toString());
        }
      }
    }
    return uniqueLocations.toList();
  }

  String convertLatLngToCustomString(LatLng latLng) {
    String latString = (latLng.latitude * 10000).toStringAsFixed(2);
    String lngString = (latLng.longitude * 10000).toStringAsFixed(2);
    return '$latString,$lngString';
  }

  bool isLocationMatch(String elementLocation, String formattedLocation) {
    if (elementLocation.isEmpty || formattedLocation.isEmpty) {
      return false;
    }
    return elementLocation == formattedLocation;
  }

  List<AmplifierDeviceItem> findAmplifierDeviceItem(LatLng location) {
    String formattedLocation = convertLatLngToCustomString(location);
    List<AmplifierDeviceItem> ampList = widget.ampPageHelper.amplifierDeviceList.result
        .where((element) => isLocationMatch(
            element.location.toString().isNotEmpty &&
                    element.location != null &&
                    element.location?.trim() != ","
                ? convertLatLngToCustomString(parseLatLng(element.location!))
                : "",
            formattedLocation))
        .toList();
    return ampList;
  }

  zoomInAndOutWidget(){
    return Column(
      children: [
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            maximumSize: const Size(40, 40),
            minimumSize: const Size(24, 32),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            backgroundColor: Colors.white,
            padding: EdgeInsets.zero,
          ),
          child: const Center(
            child: Icon(Icons.add),
          ),
          onPressed: () async {
            final currentZoom = mapController.zoom;
            final center = mapController.center;
            mapController.move(center, currentZoom + 1);
          },
        ),
        const SizedBox(
          height: 16,
        ),
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            maximumSize: const Size(40, 40),
            minimumSize: const Size(24, 32),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            backgroundColor: Colors.white,
            padding: EdgeInsets.zero,
          ),
          child: const Center(
            child: Icon(Icons.remove),
          ),
          onPressed: () async {
            final currentZoom = mapController.zoom;
            final center = mapController.center;
            mapController.move(center, currentZoom - 1);
          },
        ),
      ],
    );
  }
}


class NoScrollBehavior extends ScrollBehavior {
  @override
  ScrollPhysics getScrollPhysics(BuildContext context) {
    return const NeverScrollableScrollPhysics();
  }
}