import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/pages/amplifiers/amplifier_details/alarms_history/alarms_history.dart';

class MobileAlarmsHistoryPage  {
  int itemPerPage=10;
  Widget buildAmplifierList(BuildContext context, AmplifierPageHelper amplifierPageHelper, AlarmHistoryPageState alarmHistoryPageState) {
    if (amplifierPageHelper.alarmsHistoryDataSource.rowCount == 0) {
      return SizedBox(
        height: 350,
        child: amplifierPageHelper.dataTableHelper.getEmptyTableContent(context),
      );
    }
    List<AlarmHistoryData> fullList = amplifierPageHelper.alarmsHistoryDataSource.alarmHistoryList;
    int startIndex = alarmHistoryPageState.paginationHelper.currentPage * itemPerPage;
    int endIndex = startIndex + itemPerPage;
    endIndex = endIndex > fullList.length ? fullList.length : endIndex;
    List<AlarmHistoryData> currentPageItems = fullList.sublist(startIndex, endIndex);
    int currentPage=alarmHistoryPageState.paginationHelper.currentPage+1;
    bool hasMore = (currentPage * itemPerPage) < fullList.length;
    alarmHistoryPageState.paginationHelper
        .updatePagination(fullList.length, hasMore: hasMore, pageLimit: itemPerPage);
   

    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: currentPageItems.length,
      itemBuilder: (context, index) {
        AlarmHistoryData alarmHistoryData = currentPageItems[index];
        DataRow dataRow = amplifierPageHelper.alarmsHistoryDataSource.getRow(index,alarmHistoryData);
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 3, horizontal: 5),
          child: CustomListTile(onTap: () {
            amplifierPageHelper.amplifierDataSource.getRow(index).onTap!();
          },
            index: index,
            titleWidget: Row(crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                dataRow.cells[0].child,
                SizedBox(width: getSize(10)),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    dataRow.cells[3].child,
                    dataRow.cells[1].child,
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget selectTableTypeButtonView(AlarmHistoryPageState pageState) {
    return pageState.widget.ampPageHelper.dataTableHelper.selectTableTypeButtonView(
      isTableType: pageState.isTableView,
      onPressed: () {
        pageState.isTableView = !pageState.isTableView;
        pageState.amplifierController.update();
      },
    );
  }

  void autoSelectTableType(AlarmHistoryPageState pageState) {
      ScreenLayoutType currentLayoutType = pageState.screenLayoutType;
      bool isMobile = currentLayoutType != ScreenLayoutType.desktop;
      if (pageState.previousLayoutType != currentLayoutType) {
        pageState.isTableView = !isMobile;
        pageState.previousLayoutType = currentLayoutType;
      }
  }

  Widget customPaginationView(List<AlarmHistoryData> fullList,
      AmplifierController amplifierController, PaginationHelper paginationHelper) {
    return AppPaginationWidget(
      apiStatus: ApiStatus.success,
      paginationHelper: paginationHelper,
      onLoadNext: () async {
        paginationHelper.currentPage++;
        amplifierController.update();
      },
      onLoadPrevious: () async {
        paginationHelper.currentPage--;
        amplifierController.update();
      },
      onGoToFirstPage: () {
        paginationHelper.currentPage = 0;
        amplifierController.update();
      },
      onGoToLastPage: () {
        int totalPages = (fullList.length / itemPerPage).ceil();
        paginationHelper.currentPage = totalPages - 1;
        amplifierController.update();
      },
      onChanged: (value) {},
    );
  }
}
