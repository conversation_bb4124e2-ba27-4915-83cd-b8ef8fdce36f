import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/pages/amplifiers/amplifier_details/alarms_history/alarms_history_datasource.dart';

import 'mobile_alarms_history_page.dart';

class AlarmHistoryPage extends StatefulWidget {
  final AmplifierDeviceItem amplifierItem;
  final AmplifierPageHelper ampPageHelper;

  const AlarmHistoryPage({super.key, required this.amplifierItem, required this.ampPageHelper});

  @override
  State<AlarmHistoryPage> createState() => AlarmHistoryPageState();
}

class AlarmHistoryPageState extends State<AlarmHistoryPage> {
  AmplifierController amplifierController =Get.put<AmplifierController>(AmplifierController());
  late AmplifierDeviceItem ampItem;
  final double heightOfDataTableCell = 48;
  int recordsInPage = 0; // For Set Fixed and Static Height Of Table
  int currentPageIndex = 0;
  TextEditingController searchController = TextEditingController();
  late ScreenLayoutType screenLayoutType;
  String selectedOption = "";
  DateTime startDate = DateTime.now();
  DateTime endDate =DateTime.now();
  List dateRangeList =[];
  List<AlarmHistoryData> searchAlarmsHistoryDataList = [];
  DateRangeFilterHelper dateHelper = DateRangeFilterHelper();
  num previousLastAlarmAt=0;
  num previousLastNotificationAt=0;
  bool isTableView = true;
   ScreenLayoutType previousLayoutType = ScreenLayoutType.desktop;
  PaginationHelper paginationHelper = PaginationHelper();
  initializeListValue() {
    dateRangeList = [
      S.of(context).today,
      S.of(context).thisWeek,
      S.of(context).lastWeek,
      S.of(context).custom
    ];
    selectedOption = dateRangeList[0];
  }
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    ampItem = widget.amplifierItem;
    initLastAlarmData();
    initCall();
  }
  initCall() async {
    int refreshIndex =
    widget.ampPageHelper.listTabs.indexWhere((tab) => tab.title == ampItem.deviceEui);
    widget.amplifierItem.alarmStatusModel = [];
    DateTime now = DateTime.now();
    int startDate =
        DateTime(now.year, now.month, now.day).millisecondsSinceEpoch ~/
            1000;
    int endDate = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    await widget.ampPageHelper.getAlarmsHistoryData(
        ampItem.deviceEui, refreshIndex, startDate, endDate);
  }
  initLastAlarmData() {
    initializeTimer();
    if(widget.ampPageHelper.amplifierDataSource.list.isEmpty) {
      return;
    }
    previousLastAlarmAt=widget.ampPageHelper.amplifierDataSource.list
        .firstWhere((element) => element.deviceEui == widget.amplifierItem.deviceEui)
        .lastAlarmAt ?? 0;
    previousLastNotificationAt=widget.ampPageHelper.amplifierDataSource.list
        .firstWhere((element) => element.deviceEui == widget.amplifierItem.deviceEui)
        .lastNotificationAt ?? 0;
  }
  @override
  Widget build(BuildContext context) {
    if(dateRangeList.isEmpty) {
      initializeListValue();
    }
    return GetBuilder<AmplifierController>(
      init: AmplifierController(),
      builder: (AmplifierController controller) {
        return ScreenLayoutTypeBuilder(
          builder: (context, screenType, constraints) {
            return Padding(
              padding: EdgeInsets.symmetric(vertical: getSize(20)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  getBodyView()
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget getBodyView() {
    return ScreenLayoutTypeBuilder(
      builder: (context, screenType, constraints) {
        screenLayoutType = screenType;
        MobileAlarmsHistoryPage().autoSelectTableType(this);
        return getAlarmHistoryBoardView();
      },
    );
  }

  Widget getAlarmHistoryBoardView() {
    return ClipRRect(
      // borderRadius: const BorderRadius.all(Radius.circular(5)),
      child: Container(
        width: double.infinity,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (ampItem.ampDeviceSummaryError != null) ...[
              if (screenLayoutType != ScreenLayoutType.mobile)
                Row(
                  children: [
                    Expanded(child: alarmRefreshButton(),),
                  ],
                )
              else ...[
                alarmRefreshButton(),
              ],
            ] else ...[
              alarmRefreshButton(),
            ],
            buildAlarmsStreaming(),
            AppText(
              S.of(context).alarmsNotifications,
              style: TextStyle(
                  fontFamily: AppAssetsConstants.openSans,
                  fontWeight: getMediumBoldFontWeight(),
                  letterSpacing: 0.32,
                  color: AppColorConstants.colorLightBlue,
                  fontSize: getSize(20)),
            ),
            SizedBox(height: getSize(20)),
            getPageAppBar(),
            alarmHistoryBoardView(),
           // getLastSeenView(ampItem.alarmUpdateTime)

          ],
        ),
      ),
    );
  }

  Widget getPageAppBar() {
    print("selectedOption :$selectedOption");
    return Container(
      alignment: Alignment.centerLeft,
      decoration: BoxDecoration(
          border: Border.all(color: AppColorConstants.colorH2),
          color: buildTableAppbarColor(),
          borderRadius: BorderRadius.only(
              topRight: Radius.circular(getSize(7)), topLeft: Radius.circular(getSize(7)))),
      padding: EdgeInsets.only(left: getSize(18)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: getSize(10)),
          Row(
            children: [
              Container(
                decoration: BoxDecoration(
                    color:  AppColorConstants.colorWhite,
                    border: Border.all(color:  AppColorConstants.colorH2.withOpacity(0.5)),
                    borderRadius: const BorderRadius.all(Radius.circular(5))),
                height: (screenLayoutType == ScreenLayoutType.mobile) ? 70 : 42,
                width: screenLayoutType == ScreenLayoutType.mobile ? 200 : 330,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (screenLayoutType == ScreenLayoutType.mobile)
                      Container(
                        width: 100,
                        alignment: Alignment.center,
                        padding: EdgeInsets.only(right: getSize(5), bottom: getSize(4),left: getSize(5)),
                        child: AppText("${dropDownValueString(selectedOption)} :",
                            style: TextStyle(
                                fontFamily: AppAssetsConstants.openSans,
                                fontWeight: FontWeight.w600,
                                fontSize: getSize(15),
                                color: AppColorConstants.colorBlack)),
                      ),
                    Row(mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (screenLayoutType != ScreenLayoutType.mobile)
                        Container(
                          width: 100,
                          alignment: Alignment.center,
                          padding: EdgeInsets.only(right: getSize(5),bottom: getSize(4)),
                          child: AppText("${dropDownValueString(selectedOption)} :",
                              style: TextStyle(
                                  fontFamily: AppAssetsConstants.openSans,
                                  fontWeight: FontWeight.w600,
                                  fontSize: getSize(15),
                                  color: AppColorConstants.colorBlack)),
                        ),
                        Flexible(child:  dateHelper.commonDateRangeDropDown(
                            onChanged: (value) => dateHelper.onDropdownChanged(value, context, (startDateValue, endDateValue, selectedOptionValue) async {
                              initializeTimer();
                              paginationHelper.initializePagination();
                              startDate = startDateValue;
                              endDate = endDateValue;
                              selectedOption = selectedOptionValue;
                              int fromDate = startDate.millisecondsSinceEpoch ~/ 1000;
                              int toDate = endDate.millisecondsSinceEpoch ~/ 1000;
                              int refreshIndex = widget.ampPageHelper.listTabs.indexWhere((tab) => tab.title == ampItem.deviceEui);
                             await widget.ampPageHelper
                                  .getAlarmsHistoryData(ampItem.deviceEui, refreshIndex, fromDate, toDate);
                              widget.ampPageHelper.getAlarmHistoryDifferenceTime();
                              amplifierController.update();
                            },customDialogShownOnce: true),
                            selectedValue: selectedOption,
                            hintText: S.of(context).selectDateRange,
                            items: dateRangeList,
                            startDate: startDate,
                            endDate: endDate),)
                      ],
                    ),
                  ],
                ),
              ),
              SizedBox(width: getSize(10)),
              if (screenLayoutType == ScreenLayoutType.desktop) ...[
                Flexible(
                  child: _searchTextFieldView(),
                ),
              ],
              if(screenLayoutType!=ScreenLayoutType.desktop) const Spacer(),
              MobileAlarmsHistoryPage().selectTableTypeButtonView(this),
            ],
          ),
          if (screenLayoutType != ScreenLayoutType.desktop) ...[
            SizedBox(height: getSize(10)),
            _searchTextFieldView(),
          ],
          SizedBox(height: getSize(10)),
        ],
      ),
    );
  }

  String dropDownValueString(String originalString) {
    int startIndex = originalString.indexOf('(');
    if(startIndex == -1) {
      return originalString;
    }
    return originalString.substring(0, startIndex).trim();
  }

  Widget buildAlarmsStreaming() {
    return StreamBuilder<String>(
      stream: widget.ampPageHelper.updateStreamView,
      builder: (context, snapshot) {
        debugLogs("updateStreamView -> Alarms : 1->");
        if(widget.ampPageHelper.amplifierDataSource.list.isEmpty) {
          return getAlarmsView();
        }
        debugLogs("updateStreamView -> Alarms : 2->");
        ampItem.lastAlarmAt = widget.ampPageHelper.amplifierDataSource.list
                .firstWhere((element) => element.deviceEui == widget.amplifierItem.deviceEui)
                .lastAlarmAt ??
            0;
        ampItem.lastNotificationAt = widget.ampPageHelper.amplifierDataSource.list
                .firstWhere((element) => element.deviceEui == widget.amplifierItem.deviceEui)
                .lastNotificationAt ??
            0;
        debugLogs("updateStreamView -> Alarms : 3->");

        if (previousLastAlarmAt != ampItem.lastAlarmAt ||
            previousLastNotificationAt != ampItem.lastNotificationAt) {

          previousLastAlarmAt = ampItem.lastAlarmAt;
          previousLastNotificationAt = ampItem.lastNotificationAt;

          Future.delayed(const Duration(seconds: 2), () {
            dateHelper.onDropdownChanged(S.of(context).today, context,
                    (startDateValue, endDateValue, selectedOptionValue) async {
                  debugLogs("updateStreamView -> Alarms : 4->");
                  initializeTimer();
                  startDate = startDateValue;
                  endDate = endDateValue;
                  selectedOption = selectedOptionValue;
                  int fromDate = startDate.millisecondsSinceEpoch ~/ 1000;
                  int toDate = endDate.millisecondsSinceEpoch ~/ 1000;
                  int refreshIndex =
                  widget.ampPageHelper.listTabs.indexWhere((tab) => tab.title == ampItem.deviceEui);
                  await widget.ampPageHelper
                      .getAlarmsHistoryData(ampItem.deviceEui, refreshIndex, fromDate, toDate);
                  widget.ampPageHelper.getAlarmHistoryDifferenceTime();
                });
          });

        }
        ampItem.alarmFlagsSeverity = widget.ampPageHelper.amplifierDataSource.list
            .firstWhere(
                (element) => element.deviceEui == widget.amplifierItem.deviceEui).alarmFlagsSeverity;

        return getAlarmsView();
      },
    );
  }

  Widget getAlarmsView() {
    return Row(
      children: [
        Expanded(
          child: Padding(
            padding: EdgeInsets.only(top: getSize(5), bottom: getSize(20)),
            child: Container(padding: EdgeInsets.only(top: getSize(15), bottom: getSize(25)),
              decoration: BoxDecoration(
                color: AppColorConstants.colorWhite,
                border: Border.all(color: AppColorConstants.colorDotLine, width: 1.5),
                borderRadius: BorderRadius.circular(
                  getSize(10),
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  AppText(
                    S.of(context).activeAlarms,
                    style: TextStyle(
                        fontFamily: AppAssetsConstants.openSans,
                        fontWeight: getMediumBoldFontWeight(),
                        letterSpacing: 0.32,
                        color: AppColorConstants.colorLightBlue,
                        fontSize: getSize(20)),
                  ),
                  SizedBox(height: getSize(20)),
                  if (ampItem.alarmFlagsSeverity.isEmpty) ...[
                    Padding(
                      padding: const EdgeInsets.all(40),
                      child: Container(
                          height: 65,
                          color: AppColorConstants.colorWhite.withOpacity(0.8),
                          width: double.infinity,
                          child:Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const AppImageAsset(
                                image: AppAssetsConstants.emptyLogo,
                                height: 30,
                              ),
                              SizedBox(height: getSize(10)),

                              Text(
                                  (ampItem.ampDeviceSummaryError == null)?S.of(context).alarmCleared : S.of(context).alarmError,
                                style: TextStyle(
                                    color: AppColorConstants.colorH2,fontFamily: AppAssetsConstants.openSans,),
                              ),
                            ],
                          )),
                    ),
                  ] else ...[
                    ListView(
                        shrinkWrap: true,
                        children: ampItem.alarmFlagsSeverity
                            .map((e) => Padding(
                                  padding: const EdgeInsets.only(top: 5, left: 25, right: 25, bottom: 5),
                                  child: Row(
                                    children: [
                                      Icon(
                                        size: 20,
                                        Icons.error_outline,
                                        color: getSeverityColor(e.severity, false),
                                      ),
                                      const SizedBox(width: 10),
                                      Flexible(
                                        child: Row(
                                          children: [
                                            AppText(
                                              e.flag,
                                              style: TextStyle(
                                                  color: getSeverityColor(e.severity, false),
                                                  fontFamily: AppAssetsConstants.openSans,
                                                  fontWeight: FontWeight.w500,
                                                  fontSize: getSize(14)),
                                            ),
                                            Expanded(
                                              child: Padding(
                                                padding: const EdgeInsets.symmetric(horizontal: 12),
                                                child: DottedLine(color: getSeverityColor(e.severity, true)),
                                              ),
                                            ),
                                            AppText(
                                              e.severity.toUpperCase(),
                                              style: TextStyle(
                                                  fontFamily: AppAssetsConstants.openSans,
                                                  fontWeight: FontWeight.w500,
                                                  color: getSeverityColor(e.severity, false),
                                                  fontSize: getSize(14)),
                                            ),
                                          ],
                                        ),
                                      )
                                    ],
                                  ),
                                ))
                            .toList()),
                  ],
                ],
              ),
            ),
          ),
        ),
       if(screenLayoutType != ScreenLayoutType.mobile) const Spacer(),
      ],
    );
  }

  errorMessageView({required String errorMessage}) {
    return Padding(
      padding: const EdgeInsets.only(left: 10, bottom: 10, top: 5),
      child: CustomPaint(
        painter: DottedBorderPainter(
          borderColor: AppColorConstants.colorRedLight.withOpacity(0.8),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 8.0),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.error_outline,
                  color: AppColorConstants.colorRedLight, size: 15),
              const SizedBox(width: 5),
              Flexible(
                child: AppText(
                  errorMessage,
                  style: TextStyle(
                    color: AppColorConstants.colorDarkBlue,
                    fontSize: 12,
                    fontFamily: AppAssetsConstants.openSans,
                    fontWeight: getMediumFontWeight(),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _searchTextFieldView() {
    return Padding(
      padding: EdgeInsets.only(right: screenLayoutType == ScreenLayoutType.desktop ? 0 : 20),
      child: Container(
        decoration: BoxDecoration(
            color: buildTextFiledColor(), borderRadius: BorderRadius.circular(getSize(8))),
        child: AppTextFormField(
          onChanged: (value) {
            if(isTableView){
              widget.ampPageHelper.alarmsTableController.goToFirstPage();
            }
            searchAlarmsHistoryDataList = widget.ampPageHelper.alarmsHistoryDataList
                .where((element) => element.message.toLowerCase().contains(value.toLowerCase()) ||
                element.type.toLowerCase().contains(value.toLowerCase()))
                .toList();
            widget.ampPageHelper.alarmsHistoryDataSource = AlarmsHistoryDataSource(
              context,
              searchAlarmsHistoryDataList,
              (value) {},
            );
            amplifierController.update();
          },
          focusedBorderColor: AppColorConstants.colorPrimary,
          controller:searchController,
          enabledBorderColor: AppColorConstants.colorWhite1,
          hintText: S.of(context).quickSearch,
          maxLines: 1,
          textInputType: TextInputType.text,
          borderRadius: getSize(8),
          hintTextColor: AppColorConstants.colorDarkBlue,
          suffixIcon: const Padding(
            padding: EdgeInsets.all(12),
            child: AppImageAsset(image: AppAssetsConstants.searchIcon),
          ),
          hintFontSize: 17,
          fontFamily: AppAssetsConstants.openSans,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }

  Widget alarmHistoryBoardView() {
    if (ampItem.alarmHistoryStatus == ApiStatus.loading) {
      return  Container(
          decoration: widget.ampPageHelper.dataTableHelper.tableBorderDeco(),
          height: 300,
          child: const Align(alignment: Alignment.center, child: AppLoader()));
    }
    int itemsPerPage =
      widget.ampPageHelper.dataTableHelper.getCurrentPageDataLength(widget.ampPageHelper.alarmsHistoryDataList, currentPageIndex);
    recordsInPage = (widget.ampPageHelper.alarmsHistoryDataList.length > 10)
        ? itemsPerPage
        : widget.ampPageHelper.alarmsHistoryDataList.length;
    return Column(
      children: [
        Container(
            width: double.infinity,
            decoration: widget.ampPageHelper.dataTableHelper.tableBorderDeco(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Table
                SizedBox(
                  height: !isTableView
                      ? null
                      : (widget.ampPageHelper.alarmsHistoryDataList.isNotEmpty)
                          ? (recordsInPage * heightOfDataTableCell) + (recordsInPage * 0.1) + 150
                          : (recordsInPage * heightOfDataTableCell) + (recordsInPage * 0.1) + 300,
                  child: getAlarmHistoryDataTableView(),
                ),
                SizedBox(height: getSize(20)),
                // Divider
                Container(
                  height: 1,
                  width: double.infinity,
                  color: AppColorConstants.colorBlack12,
                ),
                if(!isTableView)MobileAlarmsHistoryPage().customPaginationView(
                    widget.ampPageHelper.alarmsHistoryDataSource.alarmHistoryList,
                    amplifierController,paginationHelper)
                // Action Button
              ],
            ))
      ],
    );
  }

  Widget alarmRefreshButton() {
    return Row(mainAxisAlignment: MainAxisAlignment.end,
      children: [
        if (widget.ampPageHelper.isShowTextOfAlarmsHistory)
          getTimeDurationView(
              refreshStatus: ampItem.alarmHistoryStatus,
              updateTime: ampItem.alarmUpdateTime,
              onTapTime: widget.ampPageHelper.onTapTimeOfAlarmsHistory,
              difference: widget.ampPageHelper.differenceTimeOfAlarmsHistory,
              textColor: ampItem.alarmHistoryStatus == ApiStatus.loading
                  ? AppColorConstants.colorAppbar
                  : AppColorConstants.colorGreen)
        else ...[
          if (ampItem.alarmUpdateTime != null)
            getLastSeenView(ampItem.alarmUpdateTime,
                textColor: ampItem.alarmHistoryStatus == ApiStatus.loading
                    ? AppColorConstants.colorAppbar
                    : AppColorConstants.colorGreen)
        ],
        AppRefresh(enabled: (getDetectedStatusType(ampItem.status) == DetectedStatusType.online),
          loadingStatus: ampItem.alarmHistoryStatus,
          buttonColor: getDetectedStatusType(ampItem.status) == DetectedStatusType.online
                      ? AppColorConstants.colorPrimary
                      : AppColorConstants.colorH1Grey,
          onPressed: getDetectedStatusType(ampItem.status) == DetectedStatusType.online ? () async {
            initializeTimer();
            if (ampItem.alarmHistoryStatus != ApiStatus.loading ) {
              ampItem.alarmHistoryStatus = ApiStatus.loading;
              amplifierController.update();
                    try {
                      final vIValue =
                          await widget.ampPageHelper.state.amplifierController.getDeviceSummary(
                        deviceEui: ampItem.deviceEui,
                        context: context,
                        isRefresh: true,
                        bitMask: 8,
                      );
                      if (vIValue['body'] != null && vIValue['body'] is AmpDeviceSummary) {
                        AmpDeviceSummary ampDeviceSummary = vIValue['body'];
                        ampItem.ampDeviceSummary.result.alarms.alarmFlags =
                            ampDeviceSummary.result.alarms.alarmFlags;
                      } else {
                        ampItem.ampDeviceSummaryError = vIValue['body'];
                      }
                    } catch (e) {
                      debugLogs(
                        "getDeviceSummary in alarm history-->${e.toString()}",
                      );
                    }
                    paginationHelper.initializePagination();
                    dateHelper.onDropdownChanged(selectedOption, context,
                      (startDateValue, endDateValue, selectedOptionValue) async {
                    selectedOption = selectedOptionValue;
                    int fromDate = startDate.millisecondsSinceEpoch ~/ 1000;
                    int toDate = endDate.millisecondsSinceEpoch ~/ 1000;
                    int refreshIndex =
                    widget.ampPageHelper.listTabs.indexWhere((tab) => tab.title == ampItem.deviceEui);
                    await widget.ampPageHelper
                        .getAlarmsHistoryData(ampItem.deviceEui, refreshIndex, fromDate, toDate);
                    //widget.ampPageHelper.getAlarmHistoryDifferenceTime();
                  });

              widget.ampPageHelper.alarmsTableController.goToFirstPage();
              amplifierController.update();
            }
          } : null,
        ),
      ],
    );
  }
  void initializeTimer() {
    widget.ampPageHelper.differenceTimeOfAlarmsHistory = null;
    widget.ampPageHelper.onTapTimeOfAlarmsHistory = DateTime.now();
    widget.ampPageHelper.isShowTextOfAlarmsHistory = true;
    widget.ampPageHelper.refreshTimerOfAlarmsHistory?.cancel();
  }
  Widget getAlarmHistoryDataTableView() {
    if(!isTableView) {
      return MobileAlarmsHistoryPage().buildAmplifierList(context, widget.ampPageHelper,this);
    }
    return PaginatedDataTable2(
      showCheckboxColumn: false,
      headingTextStyle:widget.ampPageHelper.dataTableHelper.headingTextStyle(),
      wrapInCard: false,
      border: widget.ampPageHelper.dataTableHelper.tableBorder(),
      renderEmptyRowsInTheEnd: false,
      // ignore: deprecated_member_use
      headingRowColor: widget.ampPageHelper.dataTableHelper.headingRowColor(),
      columns: [
        DataColumn2(
          fixedWidth: 90,
          label: SelectableText(S.of(context).status),
        ),
        DataColumn2(
          fixedWidth: 230,
          label: AppText(S.of(context).timestamp),
        ),
        DataColumn2(
          fixedWidth: 150,
          label: AppText(S.of(context).type),
        ),
        DataColumn(
          label: AppText(S.of(context).message),
        ),
      ],
      controller:widget.ampPageHelper.alarmsTableController,
      source:widget.ampPageHelper.alarmsHistoryDataSource,
      minWidth: 1200,
      dataRowHeight: 55,
      onPageChanged: (rowIndex) {
        currentPageIndex = (rowIndex / 10).ceil();
        amplifierController.update();
      },
      // For progress indicator
      hidePaginator: false,
      empty: widget.ampPageHelper.dataTableHelper.getEmptyTableContent(context),
    );
  }


}
