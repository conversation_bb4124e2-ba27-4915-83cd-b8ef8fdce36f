// ignore_for_file: deprecated_member_use

import 'package:quantumlink_node/app_import.dart';

class AlarmsHistoryDataSource extends DataTableSource {
  AlarmsHistoryDataSource.empty(this.context, this.alarmHistoryList, this.onTap);

  AlarmsHistoryDataSource(this.context, this.alarmHistoryList, this.onTap, [sortedByEUI = false]) {
    if (sortedByEUI) {
      sort((d) => d.deviceEUI, true);
    }
  }

  final BuildContext context;
  final List<AlarmHistoryData> alarmHistoryList;
  final Function(AlarmHistoryData) onTap;

  void sort<T>(
      Comparable<T> Function(AlarmHistoryData d) getField, bool ascending) {
    alarmHistoryList.sort((a, b) {
      final aValue = getField(a);
      final bValue = getField(b);
      return ascending
          ? Comparable.compare(aValue, bValue)
          : Comparable.compare(bValue, aValue);
    });
    notifyListeners();
  }

  @override
  DataRow2 getRow(int index, [AlarmHistoryData? alarmHistory]) {
    assert(index >= 0);
    if (index >= alarmHistoryList.length) throw 'index > _desserts.length';
    final dessert = alarmHistory ?? alarmHistoryList[index];
    return DataRow2.byIndex(
      index: index,
      color: MaterialStateProperty.all(AppColorConstants.colorWhite),
      onTap: () => onTap(dessert),
      cells: [
        DataCell(Container(
          alignment: Alignment.center,
          child: (dessert.type == "notification")
              ? Icon(
                  Icons.notifications_none_outlined,
                  color: AppColorConstants.colorLightBlue3,
                )
              : Icon(
                  Icons.error_outline,
                  color: getSeverityColor(dessert.alarm, false),
                ),
        )),
        DataCell(AppText(
            dessert.timestamp != null ? getUtcTimeZone(dessert.timestamp) : '',
            style: TextStyle(
              fontWeight: getMediumFontWeight(),
              fontFamily: AppAssetsConstants.roboto,
              color: AppColorConstants.colorBlack,
              fontSize: getSize(14),
            ))),
        DataCell(AppText("${dessert.type ?? ""}",
            style: TextStyle(
              fontWeight: getMediumFontWeight(),
              fontFamily: AppAssetsConstants.roboto,
              color: AppColorConstants.colorBlack,
              fontSize: getSize(14),
            ))),
        DataCell(AppText("${dessert.message ?? ""}",
            style: TextStyle(
              fontWeight: getMediumFontWeight(),
              fontFamily: AppAssetsConstants.roboto,
              color: AppColorConstants.colorBlack,
              fontSize: getSize(14),
            ))),
      ],
    );
  }

  @override
  int get rowCount => alarmHistoryList.length;

  @override
  bool get isRowCountApproximate => false;

  @override
  int get selectedRowCount => 0;
}
