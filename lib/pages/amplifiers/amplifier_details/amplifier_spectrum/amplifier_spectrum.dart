import 'package:quantumlink_node/app_import.dart';
import 'package:pub_semver/pub_semver.dart' as semver;
class AmplifierSpectrum extends StatefulWidget {
  final AmplifierDeviceItem amplifierItem;
  final AmplifierPageHelper ampPageHelper;

  const AmplifierSpectrum({super.key, required this.amplifierItem, required this.ampPageHelper});

  @override
  State<AmplifierSpectrum> createState() => _AmplifierSpectrumState();
}

class _AmplifierSpectrumState extends State<AmplifierSpectrum> {
  AmplifierController amplifierController =Get.put<AmplifierController>(AmplifierController());
  double baselineX = AppStringConstants.spectrumStartFrequency.toDouble();
  bool isLevelChart = true;
  bool isRefChart = true;
  double progressValue = 0.0;
  late AmplifierDeviceItem  ampItem;
  bool isRefRefresh = false;
  List<PointData> mainLevelPointsData = [];
  List<PointData> mainRefPointsData = [];
  ApiStatus frequencyStatus = ApiStatus.initial;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    ampItem = widget.amplifierItem;
    if (ampItem.spectrumStatus != ApiStatus.loading && ampItem.refreshSpectrumStatus != ApiStatus.loading) {
      initializeFrequency().then((value) {
        if (ampItem.bwModeError == null) {
          initSpectrumData();
        }
      });
    }

    //initSpectrumData();
  }


  @override
  void dispose() {
    super.dispose();
  }
  void spectrumDataInitialization() {
    ampItem.totalCalls = 0;
    ampItem.completedCalls = 0;
    widget.ampPageHelper.differenceTimeOfSpectrum = null;
    widget.ampPageHelper.onTapTimeOfSpectrum = DateTime.now();
    widget.ampPageHelper.isShowTextOfSpectrum = true;
    widget.ampPageHelper.refreshTimerOfSpectrum?.cancel();
    ampItem.spectrumError = null;
    if (isRefRefresh) {
      ampItem.isRefSuccessCalls = 0;
      mainRefPointsData = [];
      ampItem.refPoints = [];
      if(ampItem.isShowSingleSpectrumCapture==false) ampItem.refIndicatorColors = List.filled(4, AppColorConstants.colorWhite);

    } else {
      ampItem.isLevelSuccessCalls = 0;
      if(ampItem.isShowSingleSpectrumCapture==false) ampItem.levelIndicatorColors = List.filled(4, AppColorConstants.colorWhite);
      mainLevelPointsData = [];
      ampItem.levelPoints = [];
    }
    amplifierController.update();
  }

  Future<void> initSpectrumData() async {
    int refreshIndex =
    widget.ampPageHelper.listTabs.indexWhere((tab) => tab.title == ampItem.deviceEui);
    //await initializeFrequency();
    if (ampItem.levelPoints.isEmpty &&
        ampItem.refPoints.isEmpty) {
      mainRefPointsData = [];
      ampItem.refPoints = [];
      if(ampItem.isShowSingleSpectrumCapture==false)[ampItem.refIndicatorColors = List.filled(4, AppColorConstants.colorWhite),ampItem.levelIndicatorColors = List.filled(4, AppColorConstants.colorWhite)];
      mainLevelPointsData = [];
      ampItem.levelPoints = [];

      ampItem.isLevelSuccessCalls = 0;
      ampItem.isRefSuccessCalls = 0;
      ampItem.totalCalls = 0;
      ampItem.completedCalls = 0;
      ampItem.spectrumError = null;
      widget.ampPageHelper.differenceTimeOfSpectrum = null;
      widget.ampPageHelper.onTapTimeOfSpectrum= DateTime.now();
      widget.ampPageHelper.isShowTextOfSpectrum = true;
      widget.ampPageHelper.refreshTimerOfSpectrum?.cancel();
      WidgetsBinding.instance.addPostFrameCallback((_) {
        getSpectrumOnButtonTap(refreshIndex);
      });
    }
  }

  Future<void> initializeFrequency() async {
    await Future.delayed(Duration(milliseconds: 300));
      ampItem.bwModeError = null;
      if(ampItem.bwMode == null){
        ampItem.spectrumStatus = ApiStatus.loading;
      }
      frequencyStatus = ApiStatus.loading;
      amplifierController.update();
    try {
      final value = await amplifierController.getDeviceSummary(
        deviceEui: ampItem.deviceEui,
        context: context,
        isRefresh: true,
        bitMask: 1,
      );
      if (value['body'] != null && value['body'] is AmpDeviceSummary) {
        AmpDeviceSummary ampDeviceSummary = value['body'];
        int bwMode = ampDeviceSummary.result.bwMode ?? -1;
        String ampVersion= ampDeviceSummary.result.versionInfo.fwVersion ?? "";
        ampItem.isShowSingleSpectrumCapture = isBetterAmpsFWVersion(ampVersion);
        _updateSpectrumBWMode(bwMode);
        if (bwMode == 0) {
          // 1.2 GHz
          ampItem.spectrumEndFrequency = AppStringConstants.spectrumEndFrequency1215;
        } else {
          // 1.8 Ghz
          ampItem.spectrumEndFrequency = AppStringConstants.spectrumEndFrequency1719;
        }
      }else{
        ampItem.bwModeError = "Failed To Retrieve Data Of BW Mode";
        ampItem.spectrumStatus = ApiStatus.success;
      }
    } catch (e) {
      debugLogs("initializeFrequency error--> ${e.toString()}");
      ampItem.bwModeError = "Failed To Retrieve Data Of BW Mode";
      ampItem.spectrumStatus = ApiStatus.success;
    } finally {
      frequencyStatus = ApiStatus.success;
      amplifierController.update();
    }
  }

  bool isRefresh = false;
  _updateSpectrumBWMode(int bwMode) {
    if(ampItem.bwMode == null){
      ampItem.bwMode = bwMode;
    }else{
      if (ampItem.bwMode != bwMode) {
        isRefresh = true;
        ampItem.bwMode = bwMode;
        ampItem.refPoints.clear();
        ampItem.levelPoints.clear();
        amplifierController.update();
      }
    }
  }

  Future<void> getSpectrumOnButtonTap(int index) async {
    if(isRefRefresh){
      ampItem.refreshSpectrumStatus = ApiStatus.loading;
      amplifierController.update();
    }else{
      ampItem.spectrumStatus = ApiStatus.loading;
      amplifierController.update();
    }
    SpectrumRequestItem levelSpectrumItem = SpectrumRequestItem(
      config: SpectrumConfig(
        spectrumCaptureModeE: 1,
        spectrumCaptureDataTypeE: 2,
        scanMode: 1,
      ),
      ranges: [
        SpectrumRange(
          startFreq:  AppStringConstants.spectrumStartFrequency,
          stepSize: (ampItem.isShowSingleSpectrumCapture==false) ? AppStringConstants.spectrumStepSize24 :AppStringConstants.spectrumStepSize6 ,
          endFreq: ampItem.spectrumEndFrequency,
        )
      ],
    );

    SpectrumRequestItem refSpectrumItem = SpectrumRequestItem(
      config: SpectrumConfig(
        spectrumCaptureModeE: 1,
        spectrumCaptureDataTypeE: 1,
        scanMode: 1,
      ),
      ranges: [
        SpectrumRange(
          startFreq: AppStringConstants.spectrumStartFrequency,
          stepSize:  (ampItem.isShowSingleSpectrumCapture==false) ? AppStringConstants.spectrumStepSize24 :AppStringConstants.spectrumStepSize6 ,
          endFreq: ampItem.spectrumEndFrequency,
        )
      ],
    );
    if(ampItem.isShowSingleSpectrumCapture==false) {
      await initiateParallelCalls(levelSpectrumItem, refSpectrumItem, index, 4, isRefRefresh) ;
    } else {
      await initiateParallelCalls(levelSpectrumItem, refSpectrumItem, index, 1, isRefRefresh) ;
    }
  }
  /// Manage Four API Call Base
  Future<void> initiateParallelCalls(
      SpectrumRequestItem levelSpectrumItem,
      SpectrumRequestItem refSpectrumItem,
    int index,
    int tapCount, bool isRefRefresh
  ) async {
    if (ampItem.levelPoints.isEmpty &&
        ampItem.refPoints.isEmpty &&
        ampItem.isRefSuccessCalls == 0 &&
        ampItem.isLevelSuccessCalls == 0) {
      ampItem.totalCalls = tapCount * 2;
    } else {
      ampItem.totalCalls = tapCount;
    }
    Future<void> incrementCompletedCalls(bool isLevel, isSuccess) async {
      if (isLevel) {
        ampItem.isLevelSuccessCalls++;
        int index = ampItem.isLevelSuccessCalls - 1;
        if (index >= 0 && index < ampItem.levelIndicatorColors.length) {
          ampItem.levelIndicatorColors[index] = isSuccess
              ? AppColorConstants.colorLevelChartBackGround
              : AppColorConstants.colorRedLight;
        }
      } else {
        ampItem.isRefSuccessCalls++;
        int index = ampItem.isRefSuccessCalls - 1;
        if (index >= 0  && index < ampItem.refIndicatorColors.length) {
          ampItem.refIndicatorColors[index] = isSuccess
              ? AppColorConstants.colorRefChartBackGround
              : AppColorConstants.colorRedLight;
        }
      }
      ampItem.completedCalls++;
      if (ampItem.completedCalls == ampItem.totalCalls) {
        await Future.delayed(const Duration(milliseconds: 800));
        _updateAmpItemStatus();
      }
    }

    if (ampItem.levelPoints.isEmpty &&
        ampItem.refPoints.isEmpty &&
        ampItem.isRefSuccessCalls == 0 &&
        ampItem.isLevelSuccessCalls == 0) {
      Future.wait([
        createRecursiveFunction(levelSpectrumItem, tapCount, index,
            isLevel: true, onComplete: incrementCompletedCalls ,isRefresh: isRefresh),
        createRecursiveFunction(refSpectrumItem, tapCount, index,
            isLevel: false, onComplete: incrementCompletedCalls,isRefresh: isRefresh),
      ]).catchError((error) {
        _updateAmpItemStatus();
      });
    }else{
      Future.wait([
        if (isRefRefresh)
          createRecursiveFunction(refSpectrumItem, tapCount, index,
              isLevel: false, onComplete: incrementCompletedCalls,isRefresh: true)
        else
          createRecursiveFunction(levelSpectrumItem, tapCount, index,
              isLevel: true, onComplete: incrementCompletedCalls,isRefresh: true),
      ]).catchError((error) {
        _updateAmpItemStatus();
      });
    }

  }


  Future<dynamic> sendPostRequest(SpectrumRequestItem spectrumDataItem, int index, bool isRefresh) async {
    try{
      dynamic simulatedResponse = await widget.ampPageHelper.state.amplifierController
          .getSpectrumData(
          deviceEui: ampItem.deviceEui,
          sendSpectrumItem: spectrumDataItem,
          context: widget.ampPageHelper.state.context,
          isRefresh: isRefresh);
      if (simulatedResponse['body'] is SpectrumModel) {
        return simulatedResponse;
      } else {
        ampItem.spectrumError = simulatedResponse['body'];
        return;
      }
    }catch(e){
      ampItem.spectrumTimeValue ??= null;
      ampItem.spectrumError = S.of(context).socketExceptionMessage ;
      amplifierController.update();
    }
  }

  Future<void> createRecursiveFunction(SpectrumRequestItem spectrumData, int tapCount, int index,
      {required bool isLevel, required Function onComplete, required bool isRefresh}) async {
    if (tapCount <= 0) {
      return;
    }
    if (tapCount < 4) {
      spectrumData.ranges[0].startFreq += 6;
    }
    try {
      Map<String, dynamic> spectrumResponse = await sendPostRequest(spectrumData, index ,isRefresh);
      SpectrumModel spectrumModels = spectrumResponse['body'];
      ampItem.spectrumData = spectrumModels.result?.response ?? SpectrumData.empty() ;
      if(isLevel){
        ampItem.spectrumTimeValue = spectrumResponse['headers']['updated_at'];
      }
      if (spectrumModels.result != null) {
        ampItem.spectrumEndPoint = ampItem.spectrumEndFrequency;
        var result = spectrumModels.result!;
        num startLevelFreq = result.response!.values!.first.startFreq;
        num starRefFreq = result.response!.values!.first.startFreq;
        var values = result.response!.values![0].values;
        for (int i = 0; i < values!.length; i++) {
          double value = values[i] / 10;
          if (isLevel) {
            if (i > 0) {
              startLevelFreq = startLevelFreq += (ampItem.isShowSingleSpectrumCapture == false)
                  ? AppStringConstants.spectrumStepSize24
                  : AppStringConstants.spectrumStepSize6;
            }
            if (value == -0.1) continue;
            mainLevelPointsData.add(PointData(freq: startLevelFreq, value: value));
            ampItem.totalCompositePowerValue= spectrumModels.result?.response?.totalCompositePower?? "-" ;
          } else {
            if (i > 0) {
              starRefFreq = starRefFreq += (ampItem.isShowSingleSpectrumCapture == false)
                  ? AppStringConstants.spectrumStepSize24
                  : AppStringConstants.spectrumStepSize6;
            }
            if (value == -0.1) continue;
              mainRefPointsData.add(PointData(freq: starRefFreq, value: value));

          }
        }
        if(isLevel){
          mainLevelPointsData.sort((point1, point2) => point1.freq.compareTo(point2.freq));
          ampItem.levelPoints = List<PointData>.from(mainLevelPointsData);
        }else{
          mainRefPointsData.sort((point1, point2) => point1.freq.compareTo(point2.freq));
          ampItem.refPoints = List<PointData>.from(mainRefPointsData);
        }
        if(isLevel){
          onComplete.call(isLevel , true);
        }else{
          onComplete.call(isLevel , true);
        }
        amplifierController.update();
      }else{
        if(isLevel){
          onComplete.call(isLevel , false);
        }else{
          onComplete.call(isLevel , false);
        }
        amplifierController.update();
      }
      await createRecursiveFunction(spectrumData, tapCount - 1, index,
          isLevel: isLevel, onComplete: onComplete, isRefresh: isRefresh);
    } catch (e) {
      ampItem.spectrumUpdateTime = null;
      if(isLevel){
        onComplete.call(isLevel , false);
      }else{
        onComplete.call(isLevel , false);
      }
      ampItem.spectrumError = S.of(context).socketExceptionMessage;
      amplifierController.update();
      await createRecursiveFunction(spectrumData, tapCount - 1, index,
          isLevel: isLevel, onComplete: onComplete, isRefresh: isRefresh);
      debugLogs('Error sending request: $e');
    }finally{
      amplifierController.update();
    }
  }

  void _updateAmpItemStatus() {
    ampItem.spectrumUpdateTime = getLastUpdateTime(ampItem.spectrumTimeValue) ?? DateTime.now();
    ampItem.spectrumStatus = ApiStatus.success;
    ampItem.refreshSpectrumStatus = ApiStatus.success;
    getDifferenceTime();
    amplifierController.update();
  }

  getDifferenceTime() {
    widget.ampPageHelper.differenceTimeOfSpectrum =
        DateTime.now().difference(widget.ampPageHelper.onTapTimeOfSpectrum!);
    widget.ampPageHelper.refreshTimerOfSpectrum = Timer(const Duration(seconds: 3), () {
      widget.ampPageHelper.isShowTextOfSpectrum = false;
      amplifierController.update();
    });
  }
  late ScreenLayoutType screenLayoutType;
  double screenWidth = 0.0;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AmplifierController>(
      init: AmplifierController(),
      builder: (AmplifierController controller) {
        return ScreenLayoutTypeBuilder(
          builder: (context, screenType, constraints) {
            screenLayoutType = screenType;
            screenWidth = MediaQuery.of(context).size.width;
            return Stack(
              children: [
                if(ampItem.levelPoints.isNotEmpty || ampItem.refPoints.isNotEmpty)  Padding(
                  padding:  EdgeInsets.only(left: screenLayoutType == ScreenLayoutType.mobile ? 0 : getSize(35),right: screenLayoutType == ScreenLayoutType.mobile ? 10 : getSize(35)),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                    if(screenLayoutType != ScreenLayoutType.mobile)  selectedGraphButtonView(controller) else SizedBox(height: getSize(70)),
                      SizedBox(height: getSize(20)),
                      chartView(ampItem.levelPoints, ampItem.refPoints, controller),
                 //     getLastSeenView(ampItem.spectrumUpdateTime)
                    ],
                  ),
                ),
                if (ampItem.levelPoints.isEmpty && ampItem.refPoints.isEmpty)
                  Container(
                      constraints: BoxConstraints(maxWidth: screenLayoutType == ScreenLayoutType.desktop ? 925 : screenWidth),
                      color: AppColorConstants.colorWhite.withOpacity(0.8),
                      height: getSize(400),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Spacer(),
                          Center(
                            child: Wrap(
                              crossAxisAlignment: WrapCrossAlignment.center,
                              alignment: WrapAlignment.center,
                              children: [
                                if (frequencyStatus == ApiStatus.loading ||ampItem.spectrumStatus == ApiStatus.loading || ampItem.refreshSpectrumStatus == ApiStatus.loading ) ...[
                                  const SizedBox(
                                    height: 100,
                                    width: 100,
                                    child: AppLoader(loaderSize: 50),
                                  ),
                                  SizedBox(
                                    width: getSize(20),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(top: 10),
                                    child: AppText(
                                      S.of(context).fetchSpectrum,
                                      style: TextStyle(
                                          fontSize: getSize(25),
                                          fontFamily: AppAssetsConstants.notoSans,
                                          fontWeight: getBoldFontWeight()),
                                    ),
                                  )
                                ]
                              ],
                            ),
                          ),
                          const Spacer(),
                          (ampItem.spectrumError != null)
                              ? CommonAPIErrorView(errorMessage: ampItem.spectrumError ?? "")
                              : Container(),
                          Row(mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                            bwErrorHandlingView()
                          ],),
                          Align(
                            alignment: Alignment.bottomCenter,
                            child: Row(children: [
                              const AppText("0"),
                              SizedBox(
                                width: getSize(5),
                              ),
                              Expanded(
                                  child: Divider(
                                    height: 1,
                                    color: AppColorConstants.colorH2,
                                  ))
                            ]),
                          )
                        ],
                      ))
                else if (frequencyStatus == ApiStatus.loading)
                   Center(
                    child: Container(
                      height: getSize(450),
                      width: 100,
                      alignment:  Alignment.center,
                      child: AppLoader(loaderSize: 50),
                    ),
                  ),
                spectrumRefreshButtonView(),
                if (screenLayoutType == ScreenLayoutType.mobile&& (ampItem.levelPoints.isNotEmpty || ampItem.refPoints.isNotEmpty))
                  Padding(
                    padding: EdgeInsets.only(top: getSize(50)),
                    child: selectedGraphButtonView(controller),),
              ],
            );
          },
        );
      },
    );
  }

  selectedGraphButtonView(AmplifierController controller) {
    return Container(
      constraints: BoxConstraints(maxWidth: screenLayoutType == ScreenLayoutType.desktop ? 900 : screenWidth),
      alignment: Alignment.topLeft,
      height: getSize(110),
      child: Column(mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              InkWell(
                onTap: () {
                  isLevelChart = !isLevelChart;
                  controller.update();
                },
                child: Row(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Container(
                        height: getSize(11),
                        width: getSize(12),
                        decoration: BoxDecoration(
                          color: isLevelChart
                              ? AppColorConstants.colorLevelChartBackGround
                              : AppColorConstants.colorH2.withOpacity(0.8),
                          border: Border.all(
                            color: isLevelChart
                                ? AppColorConstants.colorLevelChartBorder
                                : AppColorConstants.colorH2.withOpacity(0.8),
                          )
                        ),
                      ),
                    ),
                    AppText(
                      S.of(context).level,
                      style: TextStyle(
                          color: isLevelChart ? AppColorConstants.colorBlack : AppColorConstants.colorH2.withOpacity(0.5),
                          fontSize: getSize(13),
                          fontFamily: AppAssetsConstants.notoSans,
                          fontWeight: getMediumBoldFontWeight()),
                    ),
                  ],
                ),
              ),
              if (!isLevelChart)
                IconButton(
                  // ignore: deprecated_member_use
                  mouseCursor: MaterialStateMouseCursor.clickable,
                  onPressed: () {
                    isLevelChart = true;
                    controller.update();
                  },
                  icon: Icon(
                    Icons.location_searching,
                    size: getSize(14),
                  ),
                ),
              SizedBox(
                width: getSize(20),
              ),
              InkWell(
                onTap: () {
                  isRefChart = !isRefChart;
                  controller.update();
                },
                child: Row(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Container(
                          height: getSize(11),
                          width: getSize(12),
                        decoration: BoxDecoration(
                          color: isRefChart
                              ? AppColorConstants.colorRefChartBackGround
                              : AppColorConstants.colorH2.withOpacity(0.8),
                          border: Border.all(
                              color: isRefChart
                                  ? AppColorConstants.colorRefChartBorder
                                  : AppColorConstants.colorH2.withOpacity(0.8)),
                        ),
                      ),
                    ),
                    AppText(S.of(context).ref,
                        style: TextStyle(
                            color: isRefChart ? AppColorConstants.colorBlack : AppColorConstants.colorH2.withOpacity(0.5),
                            fontSize: getSize(13),
                            fontFamily: AppAssetsConstants.notoSans,
                            fontWeight: getMediumBoldFontWeight())),
                  ],
                ),
              ),
              if (!isRefChart)
                IconButton(
                  // ignore: deprecated_member_use
                  mouseCursor: MaterialStateMouseCursor.clickable,
                  onPressed: () {
                    isRefChart = true;
                    controller.update();
                  },
                  icon: Icon(
                    Icons.location_searching,
                    size: getSize(14),
                  ),
                ),
              SizedBox(width: getSize(10)),
              Spacer(),
              (ampItem.totalCompositePowerValue != null && ampItem.isShowSingleSpectrumCapture)
                  ? AppText(
                    "${S.of(context).tcp} : ${ampItem.totalCompositePowerValue?.toStringAsFixed(2) ?? ""}",
                    style: const TextStyle(fontWeight: FontWeight.w600),
                  ):const SizedBox(),
              Spacer(flex: 2,),
            ],
          ),
          bwErrorHandlingView()
        ],
      ),
    );
  }

  Widget spectrumRefreshButtonView() {
    return Container(
      constraints: BoxConstraints(
          maxWidth: screenLayoutType == ScreenLayoutType.desktop ? 925 : screenWidth),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Align(alignment: Alignment.centerRight,
            child: Wrap(
              children: [
                Padding(
                  padding: const EdgeInsets.only(top: 10.0),
                  child: AppButton(
                    buttonRadius: 9,
                    loadingStatus: ampItem.levelPoints.isNotEmpty || ampItem.refPoints.isNotEmpty
                        ? ampItem.refreshSpectrumStatus
                        : ApiStatus.initial,
                    buttonHeight: getSize(35),
                    buttonWidth: getSize(138),
                    fontColor: ampItem.spectrumStatus != ApiStatus.loading
                        ? (getDetectedStatusType(ampItem.status) == DetectedStatusType.online
                            ? AppColorConstants.colorWhite
                            : AppColorConstants.colorH2)
                        : AppColorConstants.colorH2,
                    borderColor: ampItem.spectrumStatus != ApiStatus.loading
                        ? (getDetectedStatusType(ampItem.status) == DetectedStatusType.online
                            ? AppColorConstants.colorLightBlue.withOpacity(0.5)
                            : AppColorConstants.colorH2)
                        : AppColorConstants.colorH2,
                    buttonName: S.of(context).refreshRef,
                    onPressed: getDetectedStatusType(ampItem.status) == DetectedStatusType.online
                        ? () async {
                            int refreshIndex = widget.ampPageHelper.listTabs
                                .indexWhere((tab) => tab.title == ampItem.deviceEui);
                            if (ampItem.spectrumStatus != ApiStatus.loading &&
                                ampItem.refreshSpectrumStatus != ApiStatus.loading) {
                              isRefresh = false;
                              await initializeFrequency();
                              if(ampItem.bwModeError != null){
                                return;
                              }
                              if(isRefresh){
                                initSpectrumData();
                                return;
                              }
                              isRefRefresh = true;
                              spectrumDataInitialization();
                              S.of(context).initiatedSpectrum.showSpectrumToast(context);
                              getSpectrumOnButtonTap(refreshIndex);
                            }
                          }
                        : null,
                    buttonColor: ampItem.spectrumStatus != ApiStatus.loading ||
                            getDetectedStatusType(ampItem.status) != DetectedStatusType.online
                        ? (getDetectedStatusType(ampItem.status) == DetectedStatusType.online
                            ? AppColorConstants.colorLightBlue
                            : AppColorConstants.colorChart.withOpacity(0.5))
                        : AppColorConstants.colorChart.withOpacity(0.5),
                    fontSize: getSize(15),
                  ),
                ),
                SizedBox(width: getSize(10)),
                Padding(
                  padding: const EdgeInsets.only(top: 10.0, right: 5),
                  child: AppButton(
                    buttonRadius: 9,
                    loadingStatus: ampItem.levelPoints.isNotEmpty || ampItem.refPoints.isNotEmpty
                        ? ampItem.spectrumStatus
                        : ApiStatus.initial,
                    buttonHeight: getSize(35),
                    buttonWidth: getSize(80),
                    fontColor: ampItem.refreshSpectrumStatus != ApiStatus.loading
                        ? (getDetectedStatusType(ampItem.status) == DetectedStatusType.online
                            ? AppColorConstants.colorWhite
                            : AppColorConstants.colorH2)
                        : AppColorConstants.colorH2,
                    borderColor: ampItem.refreshSpectrumStatus != ApiStatus.loading
                        ? (getDetectedStatusType(ampItem.status) == DetectedStatusType.online
                            ? AppColorConstants.colorLightBlue.withOpacity(0.5)
                            : AppColorConstants.colorH2)
                        : AppColorConstants.colorH2,
                    buttonName: S.of(context).capture,
                    onPressed: getDetectedStatusType(ampItem.status) == DetectedStatusType.online
                        ? () async {
                            int refreshIndex = widget.ampPageHelper.listTabs
                                .indexWhere((tab) => tab.title == ampItem.deviceEui);
                            if (ampItem.spectrumStatus != ApiStatus.loading &&
                                ampItem.refreshSpectrumStatus != ApiStatus.loading) {
                              isRefresh = false;
                              await initializeFrequency();
                              if(ampItem.bwModeError != null){
                                return;
                              }
                              if(isRefresh){
                                initSpectrumData();
                                return;
                              }
                              S.of(context).initiatedSpectrum.showSpectrumToast(context);
                              isRefRefresh = false;
                              spectrumDataInitialization();
                              getSpectrumOnButtonTap(refreshIndex);
                            }
                          }
                        : null,
                    buttonColor: ampItem.refreshSpectrumStatus != ApiStatus.loading
                        ? (getDetectedStatusType(ampItem.status) == DetectedStatusType.online
                            ? AppColorConstants.colorLightBlue
                            : AppColorConstants.colorChart.withOpacity(0.5))
                        : AppColorConstants.colorChart.withOpacity(0.5),
                    fontSize: getSize(15),
                  ),
                ),
              ],
            ),
          ),
          if ( widget.ampPageHelper.isShowTextOfSpectrum) ...[
            if ( ampItem.refreshSpectrumStatus != ApiStatus.loading &&
                 ampItem.spectrumStatus != ApiStatus.loading)
              getTimeDurationView(
                  textColor: ampItem.spectrumError == null
                      ? AppColorConstants.colorGreen
                      : AppColorConstants.colorAppbar,
                  refreshStatus: ampItem.refreshSpectrumStatus == ApiStatus.loading
                      ? ampItem.refreshSpectrumStatus
                      : ampItem.spectrumStatus == ApiStatus.loading
                          ? ampItem.spectrumStatus
                          : ApiStatus.initial,
                  updateTime: ampItem.spectrumUpdateTime,
                  onTapTime:  widget.ampPageHelper.onTapTimeOfSpectrum,
                  waitingMessage: "",
                  difference:  widget.ampPageHelper.differenceTimeOfSpectrum)
            else
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  spectrumIndicatorView(ampItem.refIndicatorColors),
                  SizedBox(width: 65,),
                  spectrumIndicatorView(ampItem.levelIndicatorColors),
                ],
              ),
          ] else ...[
            if (ampItem.spectrumUpdateTime != null)
              getLastSeenView(ampItem.spectrumUpdateTime,
                  textColor: ampItem.spectrumError == null
                      ? AppColorConstants.colorGreen
                      : AppColorConstants.colorAppbar),
          ],
        ],
      ),
    );
  }

  spectrumIndicatorView(List<dynamic> indicatorColorsList) {
    return Container(
      height: 20,
      padding: const EdgeInsets.all(5),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: indicatorColorsList.map((color) {
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 2.5), // Spacing between circles
            width: 12, // Width of each circle
            height: 12, // Height of each circle
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: color,
              border: Border.all(color:AppColorConstants.colorBlack, width: 1), // Border color and width
            ),
          );
        }).toList(),
      ),
    ) ;
  }
  bwErrorHandlingView(){
    return (ampItem.bwModeError !=   null)
        ? CustomPaint(
          painter: DottedBorderPainter(
            borderColor: AppColorConstants.colorRedLight.withOpacity(0.8),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 8.0),
            child: Row(mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, color: AppColorConstants.colorRedLight, size: 15),
                const SizedBox(width: 5),
                Flexible(
                  child: AppText(
                    "${ampItem.bwModeError}",
                    style: TextStyle(
                      color: AppColorConstants.colorDarkBlue,
                      fontSize: 12,
                      fontFamily: AppAssetsConstants.openSans,
                      fontWeight: getMediumFontWeight(),
                    ),
                  ),
                ),
                InkWell(
                    onTap: () {
                      ampItem.bwMode = null;
                      ampItem.bwModeError = null;
                      ampItem.spectrumError = null;
                      ampItem.refPoints.clear();
                      ampItem.levelPoints.clear();
                      amplifierController.update();
                      if(ampItem.isShowSingleSpectrumCapture==false){
                        ampItem.refIndicatorColors = List.filled(4, AppColorConstants.colorWhite);
                        ampItem.levelIndicatorColors = List.filled(4, AppColorConstants.colorWhite);
                      }

                      initializeFrequency().then((value) {
                        if (ampItem.bwModeError == null) {
                          initSpectrumData();
                        }
                      });
                    },
                    child: const Icon(
                      Icons.refresh,
                      color: AppColorConstants.colorDarkBlue,
                      size: 20,
                    ),
                  ),
                ],
              ),
          ),
        )
        : Container();
  }

  chartView(List<PointData> levelPoints, List<PointData> refPoints, AmplifierController controller) {
    return SingleChildScrollView(
      padding: EdgeInsets.zero,
      child: Padding(
        padding:  EdgeInsets.symmetric(vertical: 20, horizontal: widget.ampPageHelper.screenLayoutType==ScreenLayoutType.mobile?0:20).copyWith(left: 0),
        child: Column(
          children: [
            Container(
              height: getSize(500),
              constraints: BoxConstraints(maxWidth: screenLayoutType == ScreenLayoutType.desktop ? 900 : screenWidth),
              child: LineChart(LineChartData(
                borderData: FlBorderData(
                    show: true,
                    border: Border(
                        top: BorderSide(color: AppColorConstants.colorH1, width: 0.5),
                        bottom: BorderSide(color: AppColorConstants.colorH1))),
                lineTouchData: LineTouchData(
                  getTouchedSpotIndicator: (LineChartBarData barData, List<int> spotIndexes) {
                    return spotIndexes.map((spotIndex) {
                      return TouchedSpotIndicatorData(
                        FlLine(color: AppColorConstants.colorH1.withOpacity(0.5), strokeWidth: 1),
                        const FlDotData(),
                      );
                    }).toList();
                  },
                  touchTooltipData: LineTouchTooltipData(
                    maxContentWidth: getSize(200),
                    getTooltipColor: (touchedSpot) => Colors.white,
                    getTooltipItems: (touchedSpots) {
                      if (touchedSpots.isEmpty) return [];

                      LineBarSpot? byIndex(int index) {
                        for (final s in touchedSpots) {
                          if (s.barIndex == index) return s;
                        }
                        return null;
                      }

                      final spot0 = byIndex(0);
                      final spot1 = byIndex(1);

                      final anchor = spot0 ?? spot1;
                      if (anchor == null) return [];

                      final baseStyle = TextStyle(
                        fontFamily: AppAssetsConstants.openSans,
                        color: AppColorConstants.colorH1,
                        fontSize: getSize(14),
                        fontWeight: FontWeight.w600,
                      );

                      final valueStyle = TextStyle(height: 2,
                        fontWeight: FontWeight.w500,
                        color: AppColorConstants.colorH1,
                        fontSize: getSize(14),
                      );

                      final titleStyle0 = TextStyle(height: 2,
                        fontWeight: FontWeight.w500,
                        fontFamily: AppAssetsConstants.openSans,
                        color: spot0?.bar.gradient?.colors.first ??
                            spot0?.bar.color ??
                            AppColorConstants.colorRefChartBorder,
                        fontSize: getSize(14),
                      );

                      final titleStyle1 = TextStyle(height: 2,
                        fontWeight: FontWeight.w500,
                        fontFamily: AppAssetsConstants.openSans,
                        color: spot1?.bar.gradient?.colors.first ??
                            spot1?.bar.color ??
                            AppColorConstants.colorLevelChartBorder,
                        fontSize: getSize(14),
                      );

                      final xStr = anchor.x.toStringAsFixed(0);

                      final combined = LineTooltipItem(
                        // Y on top? put it here instead of xStr if you want; currently X first, then two series:
                        '$xStr\n',
                        baseStyle,
                        children: [
                          if (spot0 != null) ...[
                            TextSpan(text: "\u{25C9} ${S.of(context).level}: ", style: titleStyle0),
                            TextSpan(text: '${spot0.y.toStringAsFixed(2)}\n', style: valueStyle),
                          ],
                          if (spot1 != null) ...[
                            TextSpan(text: "\u{25C9} ${S.of(context).ref}: ", style: titleStyle1),
                            TextSpan(text: spot1.y.toStringAsFixed(2), style: valueStyle),
                          ],
                        ],
                        textAlign: TextAlign.start,
                      );

                      return touchedSpots.map((s) {
                        final isAnchor = (s.barIndex == anchor.barIndex) && (s.x == anchor.x);
                        return isAnchor ? combined : LineTooltipItem('', baseStyle);
                        // If your fl_chart version allows nulls, you can do: return isAnchor ? combined : null;
                      }).toList();
                    },
                  ),
                  handleBuiltInTouches: true,
                ),
                lineBarsData: [
                  if (isLevelChart)
                    LineChartBarData(
                      color: AppColorConstants.colorLevelChartBorder,
                      spots: levelPoints
                          .where((point) {
                        return point.freq >= baselineX && point.freq <= ampItem.spectrumEndPoint;
                      })
                          .toList()
                          .map((point) => FlSpot(point.freq, point.value))
                          .toList(),
                      isCurved: false,
                      isStrokeCapRound: true,
                      isStrokeJoinRound: true,
                      barWidth: 1,
                      belowBarData: BarAreaData(show: true, color: AppColorConstants.colorLevelChartBackGround),
                      dotData: const FlDotData(
                        show: false,
                      ),
                    ),
                  if (isRefChart)
                    LineChartBarData(
                      color: AppColorConstants.colorRefChartBorder,
                      spots: refPoints
                          .where((point) {
                        return point.freq >= baselineX && point.freq <= ampItem.spectrumEndPoint;
                      })
                          .toList()
                          .map((point) => FlSpot(point.freq, point.value))
                          .toList(),
                      isCurved: false,
                      isStrokeCapRound: true,
                      isStrokeJoinRound: true,
                      barWidth: 1,
                      belowBarData: BarAreaData(show: true, color: AppColorConstants.colorRefChartBackGround),
                      dotData: const FlDotData(show: false),
                    ),
                ],
                minY: 0,
                maxY: 55,
                baselineX: baselineX,
                maxX: ampItem.spectrumEndPoint,
                gridData: FlGridData(
                  show: true,
                  checkToShowVerticalLine: (value) => false,
                  horizontalInterval: 10,
                  getDrawingHorizontalLine: (value) {
                    return const FlLine(
                      color: Colors.grey, // Color of horizontal grid lines
                      strokeWidth: 0.5, // Width of horizontal grid lines
                    );
                  },
                ),
                titlesData: FlTitlesData(
                  bottomTitles: AxisTitles(axisNameSize: 25,
                      axisNameWidget: AppText(S.of(context).frequency,
                          style:
                          TextStyle(
                            color: AppColorConstants.colorH1,
                          )),
                      drawBelowEverything: true,
                      sideTitles:
                      SideTitles(
                        interval: screenLayoutType == ScreenLayoutType.mobile ? 200 : 96,
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          final isLast = (value - meta.max).abs() < 1;
                          int maxValue =screenLayoutType == ScreenLayoutType.mobile ? 70 : 50;
                          if (!isLast && (meta.max - value) < maxValue) {
                            return const SizedBox.shrink();
                          }
                          return Column(
                            children: [
                              Container(
                                height: getSize(5),
                                width: getSize(1),
                                color: AppColorConstants.colorH1,
                              ),
                              Text(
                                value.toStringAsFixed(0),
                                style: TextStyle(fontSize: 11, color: AppColorConstants.colorH1),
                              ),
                            ],
                          );
                        },
                        reservedSize: getSize(40),
                      )
                  ),
                  rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  leftTitles: AxisTitles(axisNameSize: 25,
                    axisNameWidget: Row( mainAxisAlignment:  MainAxisAlignment.spaceEvenly,
                      children: [
                        SizedBox(width: getSize(10)),
                        AppText(S.of(context).dBmV,
                            style: TextStyle(fontSize: 13,
                              color: AppColorConstants.colorH1,
                            )),
                      ],
                    ),
                    sideTitles: SideTitles(
                      interval: 5,
                      showTitles: true,
                      getTitlesWidget: (value, meta) => AppText(
                        value.toStringAsFixed(1),
                        style: TextStyle(fontSize: 12, color: AppColorConstants.colorH1),
                      ),
                      reservedSize: getSize(30),
                    ),
                  ),
                ),
              )),
            ),
            customSlider(controller, ampItem.levelPoints,ampItem.refPoints),
            SizedBox(height: getSize(20)),
          ],
        ),
      ),
    );
  }

  Widget customSlider(AmplifierController controller, List<PointData> levelPoints, List<PointData> refPoints) {
    return Container(
      padding: const EdgeInsets.only(left: 24, top: 16),
      constraints: BoxConstraints(maxWidth: screenLayoutType == ScreenLayoutType.desktop ? 900 : screenWidth),
      child: Stack(

        children: [
          if(levelPoints.isNotEmpty && refPoints.isNotEmpty)  Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: SizedBox(
              height: getSize(20),
              child: LineChart(
                LineChartData(
                    borderData: FlBorderData(show: false),
                    lineBarsData: [
                      if (isLevelChart || isRefChart)  LineChartBarData(
                        color: AppColorConstants.colorH2.withOpacity(0.8),
                        spots: isLevelChart
                            ? levelPoints.map((point) => FlSpot(point.freq, point.value)).toList()
                            : refPoints.map((point) => FlSpot(point.freq, point.value)).toList(),
                        isCurved: false,
                        isStrokeCapRound: true,
                        barWidth: 1,
                        belowBarData: BarAreaData(show: true, color: AppColorConstants.colorH2.withOpacity(0.4)),
                        dotData: const FlDotData(show: false),
                      ),
                    ],
                    titlesData: const FlTitlesData(
                      bottomTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                      rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                      topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                      leftTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                    ),
                    gridData: const FlGridData(
                      show: false,
                    )),
              ),
            ),
          ),
          if (levelPoints.isNotEmpty && refPoints.isNotEmpty)
            CustomRangeSlider(
              minValue: AppStringConstants.spectrumStartFrequency.toDouble(),
              maxValue: ampItem.spectrumEndFrequency.toDouble(),
              startValue: AppStringConstants.spectrumStartFrequency.toDouble(),
              endValue: ampItem.spectrumEndFrequency.toDouble(),
              onStartChanged: (start) {
                baselineX = start;
                controller.update();
              },
              onEndChanged: (end) {
                ampItem.spectrumEndPoint = end;
                controller.update();
              },
            ),
        ],
      ),
    );
  }
}
