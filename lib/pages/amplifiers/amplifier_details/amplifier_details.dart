import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/pages/amplifiers/amplifier_details/amp_configuration/amp_configuration.dart';
import 'package:quantumlink_node/pages/amplifiers/amplifier_details/audit_log_history/audit_log_history.dart';
import 'package:quantumlink_node/utils/dialog_utils.dart';
import 'alarms_history/alarms_history.dart';
import 'amp_diagnostics/amp_diagnostics.dart';
import 'amp_telemetry/amp_telemetry.dart';
import 'amplifier_dashboard/amplifier_dashboard.dart';
import 'amplifier_spectrum/amplifier_spectrum.dart';

class AmplifierDetail extends StatefulWidget {
  final AmplifierDeviceItem amplifierItem;
  final AmplifierPageHelper ampPageHelper;

  const AmplifierDetail(
      {super.key, required this.amplifierItem, required this.ampPageHelper});

  @override
  State<AmplifierDetail> createState() => _AmplifierDetailState();
}

class _AmplifierDetailState extends State<AmplifierDetail>
    with TickerProviderStateMixin {
  int currentSubTab = AmpSubTabConstants.dashboard;
  String previousDeviceEui = "";
  late AmplifierDeviceItem ampItem;

  late ScreenLayoutType screenLayoutType;
  int alarmsFlag = 0;
  String alarmsSeverity = "";
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    ampItem = widget.amplifierItem;
    getAlarmsFlagCount();
    if (ampItem.initialAmpDetailTabValue != null) {
      currentSubTab = ampItem.initialAmpDetailTabValue;
    //  onTapChangeSubTab(currentSubTab);
    }
    manageSubTab();
  }

  manageSubTab(){
    widget.ampPageHelper.subTabController = TabController(
        initialIndex: currentSubTab,
        length: ampItem.isShowConfigurationAndDiagnostics ? 7 : 4,
        vsync: widget.ampPageHelper.state,
        animationDuration: Duration.zero);
    widget.ampPageHelper.subTabList = [
      AppStringConstants.deviceInfo,
      AppStringConstants.telemetry,
      AppStringConstants.alarms,
      AppStringConstants.spectrum,
      if (ampItem.isShowConfigurationAndDiagnostics) ...[
        AppStringConstants.auditHistory,
        AppStringConstants.diagnostics,
        AppStringConstants.configuration,
      ]
    ];
  }

  getAlarmsFlagCount() async {
    if (widget.ampPageHelper.amplifierDataSource.list.isEmpty) {
      return;
    }
    ampItem.alarmFlagsSeverity = widget.ampPageHelper.amplifierDataSource.list
        .firstWhere(
            (element) => element.deviceEui == widget.amplifierItem.deviceEui)
        .alarmFlagsSeverity;

    ampItem.alarm = widget.ampPageHelper.amplifierDataSource.list
        .firstWhere(
            (element) => element.deviceEui == widget.amplifierItem.deviceEui)
        .alarm;

    alarmsFlag = ampItem.alarmFlagsSeverity.length;
    alarmsSeverity = ampItem.alarm ?? "";
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AmplifierController>(
      init: AmplifierController(),
      builder: (AmplifierController controller) {
        return ScreenLayoutTypeBuilder(
            builder: (context, screenType, constraints) {
              screenLayoutType = screenType;
              return MergeSemantics(
                child: Column(crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                Divider(
                  height: getSize(1),
                  color: AppColorConstants.colorDotLine,
                ).paddingSymmetric(
                    horizontal: screenType == ScreenLayoutType.mobile ? getSize(0) : getSize(20)),
                getTabsHeaderView(controller, screenType).paddingSymmetric(
                    horizontal: screenType == ScreenLayoutType.mobile ? getSize(0) : getSize(15)),
                    Divider(
                  height: getSize(1),
                  color: AppColorConstants.colorDotLine,
                ).paddingSymmetric(
                    horizontal: screenType == ScreenLayoutType.mobile ? getSize(0) : getSize(20)),
                dashboardInfoViews()
                  ],
                ),
              );
            });
      },
    );
  }

 Widget dashboardInfoViews() {
    if (AppConfig.shared.isQLCentral) {
      return qlCentralDashboardViews();
    } else {
      return qlNodeDashboardViews();
    }
  }

  Widget qlCentralDashboardViews() {
    return Expanded(
      child: ListView(
          physics: const ClampingScrollPhysics(),
          padding: EdgeInsets.symmetric(horizontal: getSize(15)),
          shrinkWrap: true, children: [
        if (currentSubTab == AmpSubTabConstants.dashboard)
          AmplifierDashboard(
              amplifierItem: ampItem, ampPageHelper: widget.ampPageHelper),
        if (currentSubTab == AmpSubTabConstants.telemetry)
          AmpTelemetry(
              amplifierItem: ampItem, ampPageHelper: widget.ampPageHelper),
        if (currentSubTab == AmpSubTabConstants.alarmHistory)
          AlarmHistoryPage(
              amplifierItem: ampItem, ampPageHelper: widget.ampPageHelper),
        if (currentSubTab == AmpSubTabConstants.spectrum)
          AmplifierSpectrum(
              amplifierItem: ampItem, ampPageHelper: widget.ampPageHelper),
        if (currentSubTab == AmpSubTabConstants.auditLog)
          AuditLogHistoryPage(amplifierItem: ampItem),
        if (currentSubTab == AmpSubTabConstants.diagnostics)
          AmpDiagnostics(
              amplifierItem: ampItem, ampPageHelper: widget.ampPageHelper),
        if (currentSubTab == AmpSubTabConstants.configuration)
          AmpConfiguration(
              amplifierItem: ampItem, ampPageHelper: widget.ampPageHelper),
      ]),
    );
  }

  Widget qlNodeDashboardViews() {
    return Expanded(
      child: ListView(
        physics: const ClampingScrollPhysics(),
        padding: EdgeInsets.symmetric(horizontal: getSize(15)),
        shrinkWrap: true,
        children: [
          if (currentSubTab == AmpSubTabConstants.dashboard)
            AmplifierDashboard(
                amplifierItem: ampItem, ampPageHelper: widget.ampPageHelper),
          if (currentSubTab == AmpSubTabConstants.telemetry)
            AmpTelemetry(
                amplifierItem: ampItem, ampPageHelper: widget.ampPageHelper),
          if (currentSubTab == AmpSubTabConstants.alarmHistory)
            AlarmHistoryPage(
                amplifierItem: ampItem, ampPageHelper: widget.ampPageHelper),
          if (currentSubTab == AmpSubTabConstants.spectrum)
            AmplifierSpectrum(
                amplifierItem: ampItem, ampPageHelper: widget.ampPageHelper),
          if (currentSubTab == AmpSubTabConstants.auditLog)
             AuditLogHistoryPage(amplifierItem: ampItem),
          if (currentSubTab == AmpSubTabConstants.diagnostics)
            AmpDiagnostics(
                amplifierItem: ampItem, ampPageHelper: widget.ampPageHelper),
          if (currentSubTab == AmpSubTabConstants.configuration)
            AmpConfiguration(
                amplifierItem: ampItem, ampPageHelper: widget.ampPageHelper),
        ],
      ),
    );
  }

  Widget getTabsHeaderView(
      AmplifierController controller, ScreenLayoutType screenType) {
    if (screenType == ScreenLayoutType.mobile) {
      return buildMultiLineTabBar(controller).paddingSymmetric(horizontal: getSize(15));
    }
    return Row(
      children: [
        Flexible(
          child: TabBar(indicatorPadding:   EdgeInsets.symmetric(horizontal: getSize(15)),
              controller: widget.ampPageHelper.subTabController,
              dividerColor: AppColorConstants.colorWhite,
              labelPadding: EdgeInsets.only(left: getSize(5)),
              labelColor: Colors.white,
              padding: EdgeInsets.zero,
              isScrollable: true,
              indicatorColor: getDetectedStatusType(ampItem.status) ==
                  DetectedStatusType.online
                  ? AppColorConstants.colorLightBlue
                  : AppColorConstants.colorH1Grey,
              tabAlignment: TabAlignment.start,
              onTap:  (value) => handleTabTap(value,controller),
              tabs: List.generate(widget.ampPageHelper.subTabList.length, (index) {
                String ampTabItem = widget.ampPageHelper.subTabList[index];
                return Tab(
                  height: 40,
                  child: Padding(
                    padding: EdgeInsets.only(
                        left: getSize(30), right: getSize(30), top: getSize(8)),
                    child: commonTabContainView(ampTabItem,index),
                  ),
                );
              })),
        ),
      ],
    );
  }

  Widget buildMultiLineTabBar(AmplifierController controller) {
    return Column(
      children: [
        Wrap(
          alignment: WrapAlignment.start,
          crossAxisAlignment: WrapCrossAlignment.start,
          children: List.generate(widget.ampPageHelper.subTabList.length, (index) {
            String ampTabItem = widget.ampPageHelper.subTabList[index];
            return InkWell(
              onTap: () {
                widget.ampPageHelper.subTabController.animateTo(index);
                handleTabTap(index, controller);
              },
              child: Container(
                margin: const EdgeInsets.all(4),
                padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                decoration: BoxDecoration(
                  color: AppColorConstants.colorWhite,
                  border: Border.all(
                    color: currentSubTab == index
                        ? AppColorConstants.colorLightBlue
                        : AppColorConstants.colorH2,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child:commonTabContainView(ampTabItem,index),
              ),
            );
          }),
        ),
      ],
    );
  }

  Widget commonTabContainView(String ampTabItem, int index){
    return  Row(
      mainAxisSize: MainAxisSize.min, // Prevents unnecessary expansion
      children: [
        AppText(
          isSelectableText: false,
          ampTabItem,
          style: TextStyle(
            fontFamily: AppAssetsConstants.openSans,
            fontWeight: currentSubTab == index
                ? getMediumBoldFontWeight()
                : getMediumFontWeight(),
            fontSize: 14,
            color: currentSubTab == index
                ? AppColorConstants.colorLightBlue
                : AppColorConstants.colorBlack,
          ),
        ),
        StreamBuilder<String>(
          stream: widget.ampPageHelper.updateStreamView,
          builder: (context, snapshot) {
            if (index == AmpSubTabConstants.dashboard) {
              return getDeviceInitializationCountWidget();
            } else if (index == AmpSubTabConstants.alarmHistory) {
              getAlarmsFlagCount();
              return getAlarmCountWidget();
            } else {
              return Container();
            }
          },
        ),
      ],
    );
  }

  Future<void> handleTabTap(int value ,AmplifierController controller) async {
    bool isThatTab = false;
    if (widget.ampPageHelper.subTabList.length == 7) {
      isThatTab = (widget.ampPageHelper.subTabList.singleWhere(
              (element) => element == S.of(context).configuration,
          orElse: null) !=
          null)
          ? true
          : false;
    }

    if (currentSubTab == value) {
      return; // Do nothing if the same tab is clicked
    }

    if (isThatTab &&
        (ampItem.mapWrittenCtrlDS.isNotEmpty ||
            ampItem.mapWrittenCtrlUS.isNotEmpty ||
            ampItem.mapWrittenCtrlDSSpectrum.isNotEmpty)) {
      widget.ampPageHelper.subTabController.animateTo(currentSubTab);
    }

    int index = widget.ampPageHelper.amplifierDeviceList.result
        .indexWhere((tab) => tab.deviceEui == ampItem.deviceEui);
    if (widget.ampPageHelper.getConfigurationMap(index) != null && value != 6  && currentSubTab == 6 ) {
      bool isSuccess = await widget.ampPageHelper.checkConfigurationMap(context, index);
      if (isSuccess) {
        currentSubTab = value;
        //onTapChangeSubTab(value);
        widget.ampPageHelper.subTabController.animateTo(value);
        controller.update();
      }
    } else {
      currentSubTab = value;
      widget.ampPageHelper.subTabController.animateTo(value);
      ampItem.initialAmpDetailTabValue = value;

     // onTapChangeSubTab(value);
      controller.update();
    }
  }

  Widget getDeviceInitializationCountWidget() {
    if (ampItem.config?.isEmpty ?? true) {
      return Container();
    } else {
      // count number of strings which have NOT in them
      int count = 0;
      ampItem.config?.forEach((value) {
        if (value.contains("NOT")) {
          count++;
        }
      });
      if (count == 0) {
        return Container();
      } else {
        return Container(
          height: 20,
          width: 19,
          margin: const EdgeInsets.only(left: 5),
          decoration: BoxDecoration(
              color: AppColorConstants.colorCriticalLite,
              borderRadius: BorderRadius.circular(2)),
          alignment: Alignment.center,
          child: Center(
            child: Text("$count",
                style: TextStyle(
                  fontFamily: AppAssetsConstants.openSans,
                  fontWeight: getMediumBoldFontWeight(),
                  fontSize: 14,
                  color: AppColorConstants.colorRedLight,
                )),
          ),
        );
      }
    }
  }

  Widget getAlarmCountWidget() {
    if (alarmsSeverity.isEmpty) {
      return Container();
    }

    return Container(
      height: 20,
      width: 19,
      margin: const EdgeInsets.only(left: 5),
      decoration: BoxDecoration(
          color: getSeverityColor(alarmsSeverity, true),
          borderRadius: BorderRadius.circular(2)),
      alignment: Alignment.center,
      child: Center(
        child: Text("$alarmsFlag",
            style: TextStyle(
              fontFamily: AppAssetsConstants.openSans,
              fontWeight: getMediumBoldFontWeight(),
              fontSize: 14,
              color: getSeverityColor(alarmsSeverity, false),
            )),
      ),
    );
  }


}
