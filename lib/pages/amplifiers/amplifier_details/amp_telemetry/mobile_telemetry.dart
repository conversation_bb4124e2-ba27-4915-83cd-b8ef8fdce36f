import 'package:quantumlink_node/app_import.dart';

class MobileAmpTelemetry {
  TextStyle textStyle = TextStyle(
      fontFamily: AppAssetsConstants.roboto,
      fontSize: getSize(14),
      fontWeight: getMediumFontWeight());
  DataTableHelper dataTableHelper = DataTableHelper();
  Widget buildTelemetryList(BuildContext context, AmplifierPageHelper amplifierPageHelper,bool isShowTelemetryALSCPilots) {

    List<TelemetryItem> fullList = amplifierPageHelper.telemetryDataSource?.telemetryList ?? [];
    List<TelemetryItem> paginatedList = fullList
        .skip(amplifierPageHelper.telemetryPaginationHelper.currentPage *
            AppStringConstants.telemetryPrePageLimit)
        .take(AppStringConstants.telemetryPrePageLimit)
        .toList();
    if (amplifierPageHelper.telemetryDataSource?.rowCount == 0 ||(paginatedList.isEmpty)) {
      return SizedBox(
        height: 350,
        child: amplifierPageHelper.dataTableHelper.getEmptyTableContent(context),
      );
    }
    return ListView.builder(
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      itemCount: paginatedList.length,
      itemBuilder: (context, index) {
        TelemetryItem telemetryItem = paginatedList[index];
        checkPilots(dynamic value, {bool isDivide100 = false}) {
          if (isDivide100) {
            return telemetryItem.divideMeasuredBy100(value);
          }
          return telemetryItem.checkFrequency(value);
        }

        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 3, horizontal: 5),
          child: CustomListTile(
            onTap: () {},
            index: index,
            titleWidget: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          AppText(
                              telemetryItem.timestamp != null
                                  ? getUtcTimeZone(telemetryItem.timestamp)
                                  : '',
                              style: dataTableHelper.dataRowTextStyle),
                          const Spacer(),
                          GestureDetector(
                              onTap: () {
                                _showDialog(
                                  context,
                                  amplifierPageHelper.amplifierItem.deviceEui,
                                  dialogContent(context,
                                      isShowTelemetryALSCPilots: isShowTelemetryALSCPilots,
                                      selectTemUnit:amplifierPageHelper.selectedUnit,
                                      timeStamp: telemetryItem.timestamp != null
                                          ? getUtcTimeZone(telemetryItem.timestamp)
                                          : '',
                                      acVoltage: amplifierPageHelper.telemetryDataSource
                                          ?.changeDouble(amplifierPageHelper.telemetryDataSource!
                                          .checkValue(telemetryItem.acVoltage))
                                          .toString() ??
                                          '00',
                                      twentyFourV: amplifierPageHelper.telemetryDataSource
                                          ?.changeDouble(amplifierPageHelper.telemetryDataSource!
                                          .checkValue(telemetryItem.dc24v))
                                          .toString() ??
                                          '00',
                                      eightV: amplifierPageHelper.telemetryDataSource
                                          ?.changeDouble(
                                          amplifierPageHelper.telemetryDataSource!.checkValue(telemetryItem.dc8v))
                                          .toString() ??
                                          '00',
                                      fiveV: amplifierPageHelper.telemetryDataSource?.changeDouble(amplifierPageHelper.telemetryDataSource!.checkValue(telemetryItem.dc5v)).toString() ?? '00',
                                      threeV: amplifierPageHelper.telemetryDataSource?.changeDouble(amplifierPageHelper.telemetryDataSource!.checkValue(telemetryItem.vdd33v)).toString() ?? '00',
                                       alarm: "" ,//"${telemetryItem.alarmFlags ?? ''}",
                                       configBitmask: "" ,//"${telemetryItem.configBitmask ?? ''}",
                                      pilotBackup: "${checkPilots(telemetryItem.pilotBackup0)} / ${checkPilots(telemetryItem.pilotBackupLevel0,isDivide100: true)}\n${checkPilots(telemetryItem.pilotBackup1)} / ${checkPilots(telemetryItem.pilotBackupLevel1,isDivide100: true)}" ,
                                      pilotPrimary: "${checkPilots(telemetryItem.pilotPrimary0)} / ${checkPilots(telemetryItem.pilotPrimaryLevel0, isDivide100: true)}\n${checkPilots(telemetryItem.pilotPrimary1)} / ${checkPilots(telemetryItem.pilotPrimaryLevel1, isDivide100: true)}",
                                      pilotMarker: "${checkPilots(telemetryItem.pilotMarker0)} / ${checkPilots(telemetryItem.pilotMarkerLevel0, isDivide100: true)} , ${checkPilots(telemetryItem.pilotMarker1)} / ${checkPilots(telemetryItem.pilotMarkerLevel1, isDivide100: true)}\n${checkPilots(telemetryItem.pilotMarker2)} / ${checkPilots(telemetryItem.pilotMarkerLevel2, isDivide100: true)} , ${checkPilots(telemetryItem.pilotMarker3)} / ${checkPilots(telemetryItem.pilotMarkerLevel3, isDivide100: true)}",
                                      temperature: telemetryItem.changeTemp(amplifierPageHelper.telemetryDataSource!.checkValue(telemetryItem.temperature), isCelsius: amplifierPageHelper.selectedUnit == AppStringConstants.celsius).toString() ?? '00',
                                      dsAttn: amplifierPageHelper.telemetryDataSource!.checkValue(telemetryItem.divideBy10(telemetryItem.dsGainAdjust)).toString(),
                                      dsEQ: amplifierPageHelper.telemetryDataSource!.checkValue(telemetryItem.divideBy10(telemetryItem.dsSlopAdjust)).toString(),
                                      usAttn: amplifierPageHelper.telemetryDataSource!.checkValue(telemetryItem.divideBy10(telemetryItem.usGainAdjust)).toString(),
                                      usEQ: amplifierPageHelper.telemetryDataSource!.checkValue(telemetryItem.divideBy10(telemetryItem.usSlopAdjust)).toString()),
                                );
                              },
                              child: const Icon(Icons.info))
                        ],
                      ),
                      Row(
                        children: [
                          Row(
                            children: [
                              AppText(S.of(context).dSAttn+" (${S.of(context).dB})",
                                  style: const TextStyle(fontWeight: FontWeight.w700)),
                              AppText(
                                  "${amplifierPageHelper.telemetryDataSource?.checkValue(telemetryItem.divideBy10(telemetryItem.dsGainAdjust)) ?? ""}",
                                  style: amplifierPageHelper.telemetryDataSource?.getTextStyle(
                                      amplifierPageHelper.telemetryDataSource
                                          ?.checkValue(telemetryItem.dsGainAdjust),
                                      amplifierPageHelper.currentTelemetryThreshold.dsGainAdjust))
                            ],
                          ),
                          const Spacer(),

                          SizedBox(height: getSize(7)),
                          Row(
                            children: [
                              AppText(
                                S.of(context).dSEQ+" (${S.of(context).dB})",
                                style: const TextStyle(fontWeight: FontWeight.w700),
                              ),
                              AppText(
                                  "${amplifierPageHelper.telemetryDataSource?.checkValue(telemetryItem.divideBy10(telemetryItem.dsSlopAdjust)) ?? ""}",
                                  style: amplifierPageHelper.telemetryDataSource?.getTextStyle(
                                      amplifierPageHelper.telemetryDataSource
                                          ?.checkValue(telemetryItem.dsSlopAdjust),
                                      amplifierPageHelper.currentTelemetryThreshold.dsSlopeAdjust))
                            ],
                          ),
                        ],
                      ),

                      Row(
                        children: [
                          Row(
                            children: [
                              AppText(
                                S.of(context).uSAttn+" (${S.of(context).dB})",
                                style: const TextStyle(fontWeight: FontWeight.w700),
                              ),
                              AppText(
                                  "${amplifierPageHelper.telemetryDataSource?.checkValue(telemetryItem.divideBy10(telemetryItem.usGainAdjust)) ?? ""}",
                                  style: amplifierPageHelper.telemetryDataSource?.getTextStyle(
                                      amplifierPageHelper.telemetryDataSource
                                          ?.checkValue(telemetryItem.usGainAdjust),
                                      amplifierPageHelper.currentTelemetryThreshold.usGainAdjust))
                            ],
                          ),
                          const Spacer(),
                          Row(
                            children: [
                              AppText(
                                S.of(context).uSEQ+" (${S.of(context).dB})",
                                style: const TextStyle(fontWeight: FontWeight.w700),
                              ),
                              AppText(
                                  "${amplifierPageHelper.telemetryDataSource?.checkValue(telemetryItem.divideBy10(telemetryItem.usSlopAdjust)) ?? ""}",
                                  style: amplifierPageHelper.telemetryDataSource?.getTextStyle(
                                      amplifierPageHelper.telemetryDataSource
                                          ?.checkValue(telemetryItem.usSlopAdjust),
                                      amplifierPageHelper.currentTelemetryThreshold.usSlopeAdjust))
                            ],
                          ),
                        ],
                      ),

                    ],
                  ),
                ),

              ],
            ),
          ),
        );
      },
    );

  }


  Widget selectTableTypeButtonView(AmplifierPageHelper? amplifierPageHelper) {
    return amplifierPageHelper?.dataTableHelper.selectTableTypeButtonView(
      isTableType: amplifierPageHelper.isTableView,
      onPressed: () {
        amplifierPageHelper.isTableView = !amplifierPageHelper.isTableView;
        amplifierPageHelper.isExpandAll = amplifierPageHelper.isTableView;
        amplifierPageHelper.state.amplifierController.update();
      },
    );
  }

  void autoSelectTableType(AmplifierPageHelper? amplifierPageHelper) {
    if (amplifierPageHelper?.previousLayoutType != null) {
      ScreenLayoutType currentLayoutType = amplifierPageHelper!.screenLayoutType;
      bool isMobile = currentLayoutType != ScreenLayoutType.desktop;
      if (amplifierPageHelper.previousLayoutType != currentLayoutType) {
        amplifierPageHelper.isTableView = !isMobile;
        amplifierPageHelper.isExpandAll = amplifierPageHelper.isTableView;
        amplifierPageHelper.previousLayoutType = currentLayoutType;
      }
    }
  }

  Widget dialogContent(
    BuildContext context,{
    required String timeStamp,
    required String acVoltage,
    required String twentyFourV,
    required String eightV,
    required String fiveV,
    required String threeV,
    required String alarm,
    required String configBitmask,
    required String pilotPrimary,
    required String pilotBackup,
    required String pilotMarker,
    required String temperature,
    required String dsAttn,
    required String dsEQ,
    required String usAttn,
    required String usEQ, required bool isShowTelemetryALSCPilots, required String selectTemUnit,
  }) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(15.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            AppText(
              timeStamp,
              style: TextStyle(
                fontWeight: FontWeight.w800,
                fontFamily: AppAssetsConstants.openSans,
                color: AppColorConstants.colorBlack,
                fontSize: getSize(14),
              ),
            ),
            SizedBox(height: getSize(10)),
            AppText(AppStringConstants.healthVoltagesTemps,style: TextStyle( fontWeight: FontWeight.w600,
              color: AppColorConstants.colorLightBlue),),
            Divider(color: AppColorConstants.colorBlack,),
            dialogTitleAndValueView(
                context,
                selectTemUnit == AppStringConstants.fahrenheit
                    ? S.of(context).tempF
                    : S.of(context).temp,
                temperature),
            SizedBox(height: getSize(7)),
            // if (isShowTelemetryALSCPilots) ...[
            //   dialogTitleAndValueView(context, S.of(context).alarmsBitmask, alarm),
            //   SizedBox(height: getSize(7)),
            //   dialogTitleAndValueView(context, S.of(context).configBitmask, configBitmask),
            //   SizedBox(height: getSize(7)),
            // ],
            dialogTitleAndValueView(context, S.of(context).acVoltage+" (${S.of(context).vac})", acVoltage),
            SizedBox(height: getSize(7)),
            dialogTitleAndValueView(context, S.of(context).twentyFourV+" (${S.of(context).vDC})", twentyFourV),
            SizedBox(height: getSize(7)),
            dialogTitleAndValueView(context, S.of(context).dc8v+" (${S.of(context).vDC})", eightV),
            SizedBox(height: getSize(7)),
            dialogTitleAndValueView(context, S.of(context).fiveV+" (${S.of(context).vDC})", fiveV),
            SizedBox(height: getSize(7)),
            dialogTitleAndValueView(context, S.of(context).threePointThreeV+" (${S.of(context).vDC})", threeV),
            if (isShowTelemetryALSCPilots) ...[
              SizedBox(height: getSize(10)),
              AppText(AppStringConstants.alscPilots,
                  style: TextStyle(
                      fontWeight: FontWeight.w600, color: AppColorConstants.colorLightBlue)),
              Divider(
                color: AppColorConstants.colorBlack,
              ),
              dialogTitleAndValueView(
                  context,
                  S.of(context).pilotPrimary + " (${S.of(context).mhZ} / ${S.of(context).dBmV})",
                  pilotPrimary),
              SizedBox(height: getSize(7)),
              dialogTitleAndValueView(
                  context,
                  S.of(context).pilotBackup + " (${S.of(context).mhZ} / ${S.of(context).dBmV})",
                  pilotBackup),
              SizedBox(height: getSize(7)),
              dialogTitleAndValueView(
                  context,
                  S.of(context).pilotMarker + " (${S.of(context).mhZ} / ${S.of(context).dBmV})",
                  pilotMarker),
            ],
            SizedBox(height: getSize(10)),
            AppText(AppStringConstants.attnEqSettings,style: TextStyle( fontWeight: FontWeight.w600,
                color: AppColorConstants.colorLightBlue)),
            Divider(color: AppColorConstants.colorBlack,),
            dialogTitleAndValueView(context, S.of(context).dSAttn+" (${S.of(context).dB})", dsAttn),
            SizedBox(height: getSize(7)),
            dialogTitleAndValueView(context, S.of(context).dSEQ+" (${S.of(context).dB})", dsEQ),
            SizedBox(height: getSize(7)),
            dialogTitleAndValueView(context, S.of(context).uSAttn+" (${S.of(context).dB})", usAttn),
            SizedBox(height: getSize(7)),
            dialogTitleAndValueView(context, S.of(context).uSEQ+" (${S.of(context).dB})", usEQ),
          ],
        ),
      ),
    );
  }

  Widget dialogTitleAndValueView(BuildContext context, String title, String value) {
    return Row(mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        AppText(
          title,
          style: TextStyle(
            fontWeight: FontWeight.w800,
            fontFamily: AppAssetsConstants.openSans,
            color: AppColorConstants.colorBlack,
            fontSize: getSize(14),
          ),
        ),
        Expanded(child: AppText(value, style: dataTableHelper.dataRowTextStyle,textAlign: TextAlign.right,))
      ],
    );
  }

  void _showDialog(BuildContext context, String deviceEui, Widget contentView) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          surfaceTintColor: AppColorConstants.colorWhite,
          backgroundColor: AppColorConstants.colorWhite,
          insetPadding: const EdgeInsets.symmetric(horizontal: 8.0),
          contentPadding: EdgeInsets.zero,
          titlePadding: EdgeInsets.zero,
          actionsPadding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
          title: getCustomAppBarWithClose(deviceEui),
          content: contentView,
        );
      },
    );
  }
}
