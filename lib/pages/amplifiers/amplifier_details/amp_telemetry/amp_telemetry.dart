import 'dart:developer';

import 'package:intl/intl.dart';
import 'package:quantumlink_node/app/cache/app_shared_preference.dart';
import 'package:quantumlink_node/app_import.dart';
import 'package:http/http.dart' as http;
import 'package:quantumlink_node/pages/amplifiers/amplifier_details/amp_telemetry/mobile_telemetry.dart';
import 'package:quantumlink_node/utils/dialog_utils.dart';
import "package:universal_html/html.dart" as html;
import 'telemetry_datasource.dart';
import 'package:flutter/material.dart';

class AmpTelemetry extends StatefulWidget {
  final AmplifierDeviceItem amplifierItem;
  final AmplifierPageHelper ampPageHelper;

  const AmpTelemetry({super.key, required this.amplifierItem, required this.ampPageHelper});

  @override
  State<AmpTelemetry> createState() => _AmpTelemetryState();
}

class _AmpTelemetryState extends State<AmpTelemetry> with SingleTickerProviderStateMixin {
  AmplifierController amplifierController =Get.put<AmplifierController>(AmplifierController());
  late AmplifierDeviceItem ampItem;
  late AmplifierPageHelper ampPageHelper;
  final double heightOfDataTableCell = 48;
  int recordsInPage = 0; // For Set Fixed and Static Height Of Table
  int currentPageIndex = 0;
  TextEditingController searchController = TextEditingController();
  late ScreenLayoutType screenLayoutType;
  List dateRangeList =[];
  String selectedOption = "";
  DateRangeFilterHelper dateHelper = DateRangeFilterHelper();
  ApiStatus apiStatus = ApiStatus.initial;
  Timer? _timer;
  PaginatorController telemetryPageController = PaginatorController();
  List tempList = [AppStringConstants.celsius, AppStringConstants.fahrenheit];
  int _selectedTabIndex = 0;

  final List<String> _tabTitles = [
    AppStringConstants.healthVoltagesTemps,
    AppStringConstants.alscPilots,
    AppStringConstants.attnEqSettings,
  ];

  initializeListValue() {
   dateRangeList = [
      S.of(context).today,
      S.of(context).thisWeek,
      S.of(context).lastWeek,
      S.of(context).custom
    ];
  selectedOption =dateRangeList[0];
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    ampItem = widget.amplifierItem;
    ampPageHelper =widget.ampPageHelper;
    String ampVersion= ampItem.ampDeviceSummary.result.versionInfo.fwVersion ?? "0.0.0";
    ampItem.isShowTelemetryALSCPilots = isBetterAmpsFWVersion(ampVersion);
    initializeTimer();
    dateInitialize();
    _startTimer();
    initCall();
    // Initialize TabController
    widget.ampPageHelper.selectedTelemetryTabIndex = _selectedTabIndex;
  }
  dateInitialize(){
    ampPageHelper.startDate = DateTime.parse(DateFormat("yyyy-MM-dd").format(DateTime.now()));
    ampPageHelper.endDate = DateTime.now();
  }
  initCall() async {
    int refreshIndex =
    widget.ampPageHelper.listTabs.indexWhere((tab) => tab.title == ampItem.deviceEui);
    widget.ampPageHelper.telemetryDataList = [];
    widget.ampPageHelper.telemetryPaginationHelper.initializePagination();
    DateTime now = DateTime.now();
    int startDate =
        DateTime(now.year, now.month, now.day).millisecondsSinceEpoch ~/
            1000;
    int endDate = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    await widget.ampPageHelper.getTelemetryThresholdData(
        context, widget.ampPageHelper.checkDeviceType(ampItem.type), refreshIndex);
    await widget.ampPageHelper.getTelemetryData(context, ampItem.deviceEui, refreshIndex, startDate, endDate,AppStringConstants.telemetryPrePageLimit);
    ampItem.telemetryUpdateTime = DateTime.now();
    widget.ampPageHelper.getTelemetryDifferenceTime();
  }


  dataSourceUpdate({required List<TelemetryItem> dataList, required int tabIndex,required String selectedUnit } ){
    ampPageHelper.telemetryDataSource = TelemetryDataSource(
      context,
      dataList,
      widget.ampPageHelper,
          (value) {},
      selectedTabIndex: tabIndex,
      isShowTelemetryALSCPilots:ampItem.isShowTelemetryALSCPilots,
      selectedUnit: selectedUnit
    );
  }

  @override
  Widget build(BuildContext context) {
    if(dateRangeList.isEmpty) {
      initializeListValue();
    }
    return GetBuilder<AmplifierController>(
      init: AmplifierController(),
      builder: (AmplifierController controller) {
        return ScreenLayoutTypeBuilder(
          builder: (context, screenType, constraints) {
            screenLayoutType = screenType;
            return Padding(
              padding: EdgeInsets.symmetric(vertical: getSize(20)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  //telemetryRefreshButtonView(),
                  getPageAppBar(),
                  telemetryBoardView(),
                  Row(children: [
                    Expanded(child: buildLastSeenView()),
                    telemetryRefreshButtonView(),
                  ],)
                ],
              ),
            );
          },
        );
      },
    );
  }
  //INITIALIZE TIMER FOR REFRESH
  void initializeTimer() {
    ampItem.telemetryStatus = ApiStatus.loading;
    ampPageHelper.differenceTimeOfTelemetry = null;
    ampPageHelper.onTapTimeOfTelemetry = DateTime.now();
    ampPageHelper.isShowTextOfTelemetry = true;
    ampPageHelper.refreshTimerOfTelemetry?.cancel();
  }



  Widget buildLastSeenView() {
    if ( ampPageHelper.isShowTextOfTelemetry) {
      return getTimeDurationView(
        refreshStatus:  apiStatus == ApiStatus.loading ? ApiStatus.loading :  ampItem.telemetryStatus,
        updateTime: ampItem.telemetryUpdateTime,
        onTapTime:  ampPageHelper.onTapTimeOfTelemetry,
        difference:  ampPageHelper.differenceTimeOfTelemetry,textColor: apiStatus == ApiStatus.loading ? AppColorConstants.colorAppbar : AppColorConstants.colorGreen
      );
    } else {
      if (ampItem.telemetryUpdateTime != null) {
        return getLastSeenView(ampItem.telemetryUpdateTime,textColor: apiStatus == ApiStatus.loading ? AppColorConstants.colorAppbar : AppColorConstants.colorGreen);
      } else {
        return Container();
      }
    }
  }


  String dropDownValueString(String originalString) {
    int startIndex = originalString.indexOf('(');
    if(startIndex == -1) {
      return originalString;
    }
    return originalString.substring(0, startIndex).trim();
  }
  Widget getPageAppBar() {
    return Container(
      alignment: Alignment.centerLeft,
      decoration: BoxDecoration(
          border: Border.all(color: AppColorConstants.colorH2),
          color: AppColorConstants.colorWhite,
          borderRadius: BorderRadius.only(
              topRight: Radius.circular(getSize(7)), topLeft: Radius.circular(getSize(7)))),
      padding: EdgeInsets.only(left: getSize(15),top: getSize(12),bottom: getSize(12),right: getSize(15)),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              dateRangeDropDownView(),
              MobileAmpTelemetry().selectTableTypeButtonView(ampPageHelper)
            ],
          ),
        ],
      ),
    );
  }

  Widget dateRangeDropDownView() {
    return Container(
      decoration: BoxDecoration(
          border: Border.all(color: AppColorConstants.colorH2.withOpacity(0.5)),
          borderRadius: const BorderRadius.all(Radius.circular(5))),
      height: (screenLayoutType == ScreenLayoutType.mobile) ? 70 : 42,
      width: screenLayoutType == ScreenLayoutType.mobile ? 200 : 330,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (screenLayoutType == ScreenLayoutType.mobile)
            Container(
              width: 100,
              alignment: Alignment.center,
              padding: EdgeInsets.only(right: getSize(5), bottom: getSize(4),left: getSize(5)),
              child: AppText("${dropDownValueString(selectedOption)} :",
                  style: TextStyle(
                      fontFamily: AppAssetsConstants.openSans,
                      fontWeight: FontWeight.w600,
                      fontSize: getSize(15),
                      color: AppColorConstants.colorBlack)),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
              if (screenLayoutType != ScreenLayoutType.mobile)
                Container(
                  width: 100,
                  alignment: Alignment.center,
                  padding: EdgeInsets.only(right: getSize(5), bottom: getSize(4)),
                  child: AppText("${dropDownValueString(selectedOption)} :",
                      style: TextStyle(
                          fontFamily: AppAssetsConstants.openSans,
                          fontWeight: FontWeight.w600,
                          fontSize: getSize(15),
                          color: AppColorConstants.colorBlack)),
                ),
              Flexible(
                  child: dateHelper.commonDateRangeDropDown(
                      onChanged: (value) => dateHelper.onDropdownChanged(value, context,
                              (startDateValue, endDateValue, selectedOptionValue) async {
                            initializeTimer();
                            _timer?.cancel();
                            ampPageHelper.telemetryPaginationHelper.setPage(0);
                            apiStatus = ApiStatus.loading;
                            amplifierController.update();
                            ampPageHelper.startDate = startDateValue;
                            ampPageHelper.endDate = endDateValue;
                            selectedOption = selectedOptionValue;
                            int fromDate = ampPageHelper.startDate.millisecondsSinceEpoch ~/ 1000;
                            int toDate = ampPageHelper.endDate.millisecondsSinceEpoch ~/ 1000;
                            int refreshIndex = ampPageHelper.listTabs
                                .indexWhere((tab) => tab.title == ampItem.deviceEui);
                            await ampPageHelper.getTelemetryData(
                                context,
                                ampItem.deviceEui,
                                refreshIndex,
                                fromDate,
                                toDate,
                                AppStringConstants.telemetryPrePageLimit);
                            ampPageHelper.getTelemetryDifferenceTime();
                            apiStatus = ApiStatus.success;
                            _startTimer();
                            amplifierController.update();
                          },customDialogShownOnce: true),
                      selectedValue: selectedOption,
                      hintText: S.of(context).selectDateRange,
                      items: dateRangeList,
                      startDate: ampPageHelper.startDate,
                      endDate: ampPageHelper.endDate),
                )
              ],
            ),
        ],
      ),
    );
  }

  Widget buildLastSeenWithSettingsView() {
    return Row(
      children: [
        /*IconButton(
          icon: Icon(
            Icons.settings,
            color: ampPageHelper.isShowTelemetryThreshold
                ? AppColorConstants.colorPrimary
                : AppColorConstants.colorH1Grey,
          ),
          onPressed:ampPageHelper.isShowTelemetryThreshold
              ? () {
                  initializeTimer();
                  TelemetryThreshold telemetryThreshold = ampPageHelper.currentTelemetryThreshold;
                  TelemetryThreshold defaultTelemetryThreshold = ampPageHelper.defaultTelemetryThreshold;
                  updateThresholdSettings(context, telemetryThreshold,defaultTelemetryThreshold,
                      (TelemetryThreshold telemetryThreshold) async {

                    await ampPageHelper.updateTelemetryThresholdData(
                        context, ampPageHelper.checkDeviceType(ampItem.type), telemetryThreshold);
                    ampPageHelper.getTelemetryDifferenceTime();
                    refreshTelemetry(ampPageHelper.startDate, ampPageHelper.endDate);
                    debugLogs(" amplifierController.update(); : $value");
                  });
                }
              : null,
        ),*/
      ],
    );
  }

  Widget telemetryBoardView() {
    if (ampPageHelper.telemetryDataList.isEmpty && ampItem.telemetryStatus == ApiStatus.loading || apiStatus == ApiStatus.loading ) {
      return Center(
        child: Container(
            decoration: ampPageHelper.dataTableHelper.tableBorderDeco(),
            height: 350,
            child: const Align(alignment: Alignment.center, child: AppLoader())),
      );
    }
    int itemsPerPage =
   ampPageHelper.dataTableHelper.getCurrentPageDataLength(widget.ampPageHelper.telemetryDataList, currentPageIndex ,perPageLimit: AppStringConstants.telemetryPrePageLimit);
    recordsInPage = (widget.ampPageHelper.telemetryDataList.length > AppStringConstants.telemetryPrePageLimit)
        ? itemsPerPage
        :ampPageHelper.telemetryDataList.length;
    return Column(
      children: [
        Container(
            width: double.infinity,
            decoration:ampPageHelper.dataTableHelper.tableBorderDeco(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Table
                if (ampPageHelper.isTableView && screenLayoutType == ScreenLayoutType.mobile)
                  selectTelemetryButtonView(),
                Row(
                  mainAxisAlignment:
                      (ampPageHelper.isTableView && screenLayoutType != ScreenLayoutType.mobile)
                          ? MainAxisAlignment.spaceBetween
                          : MainAxisAlignment.end,
                  children: [
                    if (ampPageHelper.isTableView && screenLayoutType != ScreenLayoutType.mobile)
                      selectTelemetryButtonView(),
                    tempToggleButtonView()
                  ],
                ),
                SizedBox(
                  height:(!ampPageHelper.isTableView)
                      ? null
                      : (widget.ampPageHelper.telemetryDataList.isNotEmpty)
                      ? (recordsInPage * heightOfDataTableCell) + (recordsInPage * 0.1) + 110
                      : (recordsInPage * heightOfDataTableCell) + (recordsInPage * 0.1) + 250,
                  child: getTelemetryDataTableView(),
                ),
                AppPaginationWidget(
                  apiStatus: apiStatus,
                  paginationHelper: ampPageHelper.telemetryPaginationHelper,
                  onLoadNext: () async {
                    await loadNextLogs(context);
                    amplifierController.update();
                  },

                  onLoadPrevious: () async {
                    await loadPreviousLogs(context);
                    amplifierController.update();
                  },
                  onGoToFirstPage: () {
                    _timer?.cancel();
                    _startTimer();
                    ampPageHelper.telemetryPaginationHelper.setPage(0);
                    if (ampPageHelper.isTableView) {
                      telemetryPageController.goToFirstPage();
                    }
                    amplifierController.update();
                  },
                  onGoToLastPage: () {
                    if (ampPageHelper.isTableView) {
                      telemetryPageController.goToLastPage();
                    }
                    amplifierController.update();
                  },
                  itemsPerPage: AppStringConstants.telemetryPrePageLimit,
                  onChanged: (value) {
                    AppStringConstants.telemetryPrePageLimit = int.parse(value);
                    if (ampItem.telemetryStatus != ApiStatus.loading) {
                      dateHelper.onDropdownChanged(selectedOption, context,
                              (startDateValue, endDateValue, selectedOptionValue) async {
                                apiStatus = ApiStatus.loading;
                            amplifierController.update();
                            ampPageHelper.telemetryPaginationHelper.setPage(0);
                            if(ampPageHelper.isTableView) {
                              telemetryPageController.goToFirstPage();
                            }
                            initializeTimer();
                            _timer?.cancel();
                            ampPageHelper.startDate = startDateValue;
                            ampPageHelper.endDate = endDateValue;
                            selectedOption = selectedOptionValue;
                            int fromDate = ampPageHelper.startDate.millisecondsSinceEpoch ~/ 1000;
                            int toDate = ampPageHelper.endDate.millisecondsSinceEpoch ~/ 1000;
                            int refreshIndex =
                            ampPageHelper.listTabs.indexWhere((tab) => tab.title == ampItem.deviceEui);
                                ampItem.telemetryPageOffset = 0;
                            await ampPageHelper.getTelemetryData(
                                context, ampItem.deviceEui, refreshIndex, fromDate, toDate,AppStringConstants.telemetryPrePageLimit);
                            ampItem.telemetryUpdateTime = DateTime.now();
                            ampPageHelper.getTelemetryDifferenceTime();
                            _startTimer();
                        apiStatus = ApiStatus.success;
                        amplifierController.update();
                      });
                    }
                    amplifierController.update();
                    ampPageHelper.telemetryDataSource!.notifyListeners();
                  },
                ),
                SizedBox(height: getSize(20)),
                // Divider
                Container(
                  height: 1,
                  width: double.infinity,
                  color: AppColorConstants.colorBlack12,
                ),
                Container(
                  padding: const EdgeInsets.only(left: 16, top: 15, bottom: 15,right: 16),
                  width: double.infinity,
                  decoration: BoxDecoration(
                      color: AppColorConstants.colorBackgroundDark,
                      borderRadius: const BorderRadius.only(
                          bottomRight: Radius.circular(5), bottomLeft: Radius.circular(5))),
                  child: Row(
                    children: [
                      getExportButtonView(),
                    ],
                  ),
                ),
              ],
            ))
      ],
    );
  }


  Widget selectTelemetryButtonView(){
    return Wrap(
      children: List.generate(_tabTitles.length, (index) {
        final isSelected = _selectedTabIndex == index;
        const int aLSCIndex=1;
        if(!ampItem.isShowTelemetryALSCPilots && index == aLSCIndex){
          return const SizedBox();
        }
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 5,vertical: 8),
          child: ElevatedButton(
            onPressed: () {
              _selectedTabIndex = index;
              widget.ampPageHelper.selectedTelemetryTabIndex = _selectedTabIndex;
              dataSourceUpdate(
                dataList: ampPageHelper.telemetryDataList,
                tabIndex: _selectedTabIndex,
                selectedUnit:ampPageHelper.selectedUnit
              );
              amplifierController.update();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: isSelected
                  ? AppColorConstants.colorChartLine1
                  : Colors.grey.shade200,
              foregroundColor: isSelected
                  ? Colors.white
                  : AppColorConstants.colorH1Grey,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              elevation: isSelected ? 2 : 0,
            ),
            child: AppText(isSelectableText: false,
              _tabTitles[index],
              style: TextStyle(
                fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                color: isSelected ? AppColorConstants.colorWhite:AppColorConstants.colorH3
              ),
            ),
          ),
        );
      }),
    );
  }

  updateThresholdSettings(
    mainContext,
    TelemetryThreshold telemetryThreshold,
    TelemetryThreshold defaultTelemetryThreshold,
    Function function,
  ) {
    final GlobalKey<FormState> formKey = GlobalKey<FormState>();
    TextEditingController txtTemperatureHigh = TextEditingController();
    TextEditingController txtTemperatureLow = TextEditingController();
    TextEditingController txtDc5vHigh = TextEditingController();
    TextEditingController txtDc5vLow = TextEditingController();
    TextEditingController txtDc8vHigh = TextEditingController();
    TextEditingController txtDc8vLow = TextEditingController();
    TextEditingController txtDc24vHigh = TextEditingController();
    TextEditingController txtDc24vLow = TextEditingController();
    TextEditingController txtAcVoltageHigh = TextEditingController();
    TextEditingController txtAcVoltageLow = TextEditingController();
    TextEditingController txtVdd33vHigh = TextEditingController();
    TextEditingController txtVdd33vLow = TextEditingController();
    TextEditingController txtDsGainAdjustHigh = TextEditingController();
    TextEditingController txtDsGainAdjustLow = TextEditingController();
    TextEditingController txtDsSlopeAdjustHigh = TextEditingController();
    TextEditingController txtDsSlopeAdjustLow = TextEditingController();
    TextEditingController txtUsGainAdjustHigh = TextEditingController();
    TextEditingController txtUsGainAdjustLow = TextEditingController();
    TextEditingController txtUsSlopeAdjustHigh = TextEditingController();
    TextEditingController txtUsSlopeAdjustLow = TextEditingController();

    print(
        "--- Value of AC Voltage 0 - Low = ${ampPageHelper.currentTelemetryThreshold.acVoltage.low} -- High = ${ampPageHelper.currentTelemetryThreshold.acVoltage.high}");

    updateTextController(
        txtTemperatureHigh, telemetryThreshold.temperature.high);
    updateTextController(txtTemperatureLow, telemetryThreshold.temperature.low);
    updateTextController(txtDc5vHigh, telemetryThreshold.dc5v.high);
    updateTextController(txtDc5vLow, telemetryThreshold.dc5v.low);
    updateTextController(txtDc8vHigh, telemetryThreshold.dc8v.high);
    updateTextController(txtDc8vLow, telemetryThreshold.dc8v.low);
    updateTextController(txtDc24vHigh, telemetryThreshold.dc24v.high);
    updateTextController(txtDc24vLow, telemetryThreshold.dc24v.low);
    updateTextController(txtAcVoltageHigh, telemetryThreshold.acVoltage.high);
    updateTextController(txtAcVoltageLow, telemetryThreshold.acVoltage.low);
    updateTextController(txtVdd33vHigh, telemetryThreshold.vdd33v.high);
    updateTextController(txtVdd33vLow, telemetryThreshold.vdd33v.low);
    updateTextController(
        txtDsGainAdjustHigh, telemetryThreshold.dsGainAdjust.high);
    updateTextController(
        txtDsGainAdjustLow, telemetryThreshold.dsGainAdjust.low);
    updateTextController(
        txtDsSlopeAdjustHigh, telemetryThreshold.dsSlopeAdjust.high);
    updateTextController(
        txtDsSlopeAdjustLow, telemetryThreshold.dsSlopeAdjust.low);
    updateTextController(
        txtUsGainAdjustHigh, telemetryThreshold.usGainAdjust.high);
    updateTextController(
        txtUsGainAdjustLow, telemetryThreshold.usGainAdjust.low);
    updateTextController(
        txtUsSlopeAdjustHigh, telemetryThreshold.usSlopeAdjust.high);
    updateTextController(
        txtUsSlopeAdjustLow, telemetryThreshold.usSlopeAdjust.low);

    print(
        "--- Value of AC Voltage 1 - Low = ${ampPageHelper.currentTelemetryThreshold.acVoltage.low} -- High = ${ampPageHelper.currentTelemetryThreshold.acVoltage.high}");
    DialogUtils().telemetryThresholdDialog(
        context,
        "${S.of(context).telemetryThreshold}",
        " (${ampPageHelper.checkDeviceType(ampItem.type)})",
        StatefulBuilder(builder: (context, snapshot) {
      return SingleChildScrollView(
          child: Padding(
        padding: EdgeInsets.only(
            left: 24,
            right: 24,
            bottom: MediaQuery.of(context).viewInsets.bottom),
        //padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 500,
              padding: const EdgeInsets.only(left: 16, right: 16, top: 16),
              child: Form(
                key: formKey,
                child: Column(
                  children: [
                    dialogBoxThresholdHighLowTitle(
                        title: S.of(context).low, title2: S.of(context).high),
                    const SizedBox(height: 25),
                    dialogBoxThresholdEdit(
                      title: S.of(context).acVoltage,
                      hintText: S.of(context).acVoltage,
                      isReadOnly: false,
                      controller1: txtAcVoltageLow,
                      controller2: txtAcVoltageHigh,
                      defaultLow: defaultTelemetryThreshold.acVoltage.low,
                      defaultHigh: defaultTelemetryThreshold.acVoltage.high,
                    ),
                    const SizedBox(height: 25),
                    dialogBoxThresholdEdit(
                        title: S.of(context).twentyFourV,
                        hintText: S.of(context).twentyFourV,
                        isReadOnly: false,
                        controller1: txtDc24vLow,
                        controller2: txtDc24vHigh,
                      defaultLow: defaultTelemetryThreshold.dc24v.low,
                      defaultHigh: defaultTelemetryThreshold.dc24v.high,),
                    const SizedBox(height: 25),
                    dialogBoxThresholdEdit(
                        title: S.of(context).dc8v,
                        hintText: S.of(context).dc8v,
                        isReadOnly: false,
                        controller1: txtDc8vLow,
                        controller2: txtDc8vHigh,
                      defaultLow: defaultTelemetryThreshold.dc8v.low,
                      defaultHigh: defaultTelemetryThreshold.dc8v.high,),
                    const SizedBox(height: 25),
                    dialogBoxThresholdEdit(
                        title: S.of(context).fiveV,
                        hintText: S.of(context).fiveV,
                        isReadOnly: false,
                        controller1: txtDc5vLow,
                        controller2: txtDc5vHigh,
                      defaultLow: defaultTelemetryThreshold.dc5v.low,
                      defaultHigh: defaultTelemetryThreshold.dc5v.high,),
                    const SizedBox(height: 25),
                    dialogBoxThresholdEdit(
                        title: S.of(context).vdd33v,
                        hintText: S.of(context).vdd33v,
                        isReadOnly: false,
                        controller1: txtVdd33vLow,
                        controller2: txtVdd33vHigh,
                      defaultLow: defaultTelemetryThreshold.vdd33v.low,
                      defaultHigh: defaultTelemetryThreshold.vdd33v.high,),
                    const SizedBox(height: 25),
                    dialogBoxThresholdEdit(
                        title: S.of(context).temp,
                        hintText: S.of(context).temperatureTitle,
                        isReadOnly: false,
                        controller1: txtTemperatureLow,
                        controller2: txtTemperatureHigh,
                      defaultLow: defaultTelemetryThreshold.temperature.low,
                      defaultHigh: defaultTelemetryThreshold.temperature.high,),
                    const SizedBox(height: 25),
                    dialogBoxThresholdEdit(
                        title: S.of(context).dsGainAdjust,
                        hintText: S.of(context).dsGainAdjust,
                        isReadOnly: false,
                        controller1: txtDsGainAdjustLow,
                        controller2: txtDsGainAdjustHigh,
                      defaultLow: defaultTelemetryThreshold.dsGainAdjust.low,
                      defaultHigh: defaultTelemetryThreshold.dsGainAdjust.high,),
                    const SizedBox(height: 25),
                    dialogBoxThresholdEdit(
                        title: S.of(context).dsSlopeAdjust,
                        hintText: S.of(context).dsSlopeAdjust,
                        isReadOnly: false,
                        controller1: txtDsSlopeAdjustLow,
                        controller2: txtDsSlopeAdjustHigh,
                      defaultLow: defaultTelemetryThreshold.dsSlopeAdjust.low,
                      defaultHigh: defaultTelemetryThreshold.dsSlopeAdjust.high,),
                    const SizedBox(height: 25),
                    dialogBoxThresholdEdit(
                        title: S.of(context).usGainAdjust,
                        hintText: S.of(context).usGainAdjust,
                        isReadOnly: false,
                        controller1: txtUsGainAdjustLow,
                        controller2: txtUsGainAdjustHigh,
                      defaultLow: defaultTelemetryThreshold.usGainAdjust.low,
                      defaultHigh: defaultTelemetryThreshold.usGainAdjust.high,),
                    const SizedBox(height: 25),
                    dialogBoxThresholdEdit(
                        title: S.of(context).usSlopeAdjust,
                        hintText: S.of(context).usSlopeAdjust,
                        isReadOnly: false,
                        controller1: txtUsSlopeAdjustLow,
                        controller2: txtUsSlopeAdjustHigh,
                      defaultLow: defaultTelemetryThreshold.usSlopeAdjust.low,
                      defaultHigh: defaultTelemetryThreshold.usSlopeAdjust.high,),
                    const SizedBox(height: 25),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
          ],
        ),
      ));
    }), onSubmitPressed: () {
      print("CALL");

      if (!formKey.currentState!.validate()) {
        return;
      }

      telemetryThreshold.acVoltage.high = multiply1000(txtAcVoltageHigh.text);
      telemetryThreshold.acVoltage.low = multiply1000(txtAcVoltageLow.text);
      telemetryThreshold.temperature.high =
          multiply1000(txtTemperatureHigh.text);
      telemetryThreshold.temperature.low = multiply1000(txtTemperatureLow.text);
      telemetryThreshold.dc5v.high = multiply1000(txtDc5vHigh.text);
      telemetryThreshold.dc5v.low = multiply1000(txtDc5vLow.text);
      telemetryThreshold.dc8v.high = multiply1000(txtDc8vHigh.text);
      telemetryThreshold.dc8v.low = multiply1000(txtDc8vLow.text);
      telemetryThreshold.dc24v.high = multiply1000(txtDc24vHigh.text);
      telemetryThreshold.dc24v.low = multiply1000(txtDc24vLow.text);
      telemetryThreshold.vdd33v.high = multiply1000(txtVdd33vHigh.text);
      telemetryThreshold.vdd33v.low = multiply1000(txtVdd33vLow.text);
      telemetryThreshold.dsGainAdjust.high =
          multiply1000(txtDsGainAdjustHigh.text);
      telemetryThreshold.dsGainAdjust.low =
          multiply1000(txtDsGainAdjustLow.text);
      telemetryThreshold.dsSlopeAdjust.high =
          multiply1000(txtDsSlopeAdjustHigh.text);
      telemetryThreshold.dsSlopeAdjust.low =
          multiply1000(txtDsSlopeAdjustLow.text);
      telemetryThreshold.usGainAdjust.high =
          multiply1000(txtUsGainAdjustHigh.text);
      telemetryThreshold.usGainAdjust.low =
          multiply1000(txtUsGainAdjustLow.text);
      telemetryThreshold.usSlopeAdjust.high =
          multiply1000(txtUsSlopeAdjustHigh.text);
      telemetryThreshold.usSlopeAdjust.low =
          multiply1000(txtUsSlopeAdjustLow.text);
      function.call(telemetryThreshold);
      goBack();
    }, onResetDefaultPressed: () {

      updateTextController(
          txtTemperatureHigh, defaultTelemetryThreshold.temperature.high);
      updateTextController(
          txtTemperatureLow, defaultTelemetryThreshold.temperature.low);
      updateTextController(txtDc5vHigh, defaultTelemetryThreshold.dc5v.high);
      updateTextController(txtDc5vLow, defaultTelemetryThreshold.dc5v.low);
      updateTextController(txtDc8vHigh, defaultTelemetryThreshold.dc8v.high);
      updateTextController(txtDc8vLow, defaultTelemetryThreshold.dc8v.low);
      updateTextController(txtDc24vHigh, defaultTelemetryThreshold.dc24v.high);
      updateTextController(txtDc24vLow, defaultTelemetryThreshold.dc24v.low);
      updateTextController(txtAcVoltageHigh, defaultTelemetryThreshold.acVoltage.high);
      updateTextController(txtAcVoltageLow, defaultTelemetryThreshold.acVoltage.low);
      updateTextController(txtVdd33vHigh, defaultTelemetryThreshold.vdd33v.high);
      updateTextController(txtVdd33vLow, defaultTelemetryThreshold.vdd33v.low);
      updateTextController(
          txtDsGainAdjustHigh, defaultTelemetryThreshold.dsGainAdjust.high);
      updateTextController(
          txtDsGainAdjustLow, defaultTelemetryThreshold.dsGainAdjust.low);
      updateTextController(
          txtDsSlopeAdjustHigh, defaultTelemetryThreshold.dsSlopeAdjust.high);
      updateTextController(
          txtDsSlopeAdjustLow, defaultTelemetryThreshold.dsSlopeAdjust.low);
      updateTextController(
          txtUsGainAdjustHigh, defaultTelemetryThreshold.usGainAdjust.high);
      updateTextController(
          txtUsGainAdjustLow, defaultTelemetryThreshold.usGainAdjust.low);
      updateTextController(
          txtUsSlopeAdjustHigh, defaultTelemetryThreshold.usSlopeAdjust.high);
      updateTextController(
          txtUsSlopeAdjustLow, defaultTelemetryThreshold.usSlopeAdjust.low);
    });
  }

  void updateTextController(TextEditingController controller, double value) {
    controller.text = divide1000(value);
  }
  onExportClick(context, String gwStartDateTime, String gwEndDateTime,
      Function function) {
    var startDate = DateFormat(AppStringConstants.FORMATDATETIME3)
        .format(DateTime.parse(gwStartDateTime));
    var endDate = DateFormat(AppStringConstants.FORMATDATETIME3)
        .format(DateTime.parse(gwEndDateTime));

    TextEditingController txtStartDateTime = TextEditingController();
    txtStartDateTime.text = startDate;

    TextEditingController txtEndDateTime = TextEditingController();
    txtEndDateTime.text = endDate;

    DialogUtils().regularDialog(context, S.of(context).exportTelemetry, null,
        StatefulBuilder(builder: (context, snapshot) {
      return Padding(
        padding: EdgeInsets.only(
            left: 24,
            right: 24,
            bottom: MediaQuery.of(context).viewInsets.bottom),
        //padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 500,
              padding: const EdgeInsets.only(left: 16, right: 16, top: 16),
              child: Column(
                children: [
                  const SizedBox(height: 25),
                  dialogBoxTelemetryExport(
                      title: S.of(context).fromDateTime,
                      hintText: S.of(context).fromDateTime,
                      isReadOnly: true,
                      dateHelper: dateHelper,
                      controller: txtStartDateTime),
                  const SizedBox(height: 25),
                  dialogBoxTelemetryExport(
                      title: S.of(context).toDateTime,
                      hintText: S.of(context).toDateTime,
                      isReadOnly: true,
                      dateHelper: dateHelper,
                      controller: txtEndDateTime),
                  const SizedBox(height: 25),
                ],
              ),
            ),
            const SizedBox(height: 24),
          ],
        ),
      );
    }), () {
      DateTime startDate = DateFormat(AppStringConstants.FORMATDATETIME3)
          .parse(txtStartDateTime.text);
      DateTime endDate = DateFormat(AppStringConstants.FORMATDATETIME3)
          .parse(txtEndDateTime.text);
      if (startDate.isBefore(endDate)) {
        function.call(startDate,endDate);
        goBack();
      } else {
        S.of(context).startEndDateValidation.toString().showError(context);
      }
      /* goBack();*/
    });
  }

  Widget getExportButtonView() {
    return AppButton(
      buttonHeight: 35,
      fontSize: 14.5,
      buttonRadius: 9,
      buttonName: S.of(context).export,
      onPressed: () async {

        onExportClick(context,ampPageHelper.startDate.toString(),ampPageHelper.endDate.toString(), (
            DateTime startDate, DateTime endDate)async {
          debugPrint("----StartDate=$startDate----EndDate=$endDate");
          int formDate = startDate.millisecondsSinceEpoch ~/ 1000;
          int toDate = endDate.millisecondsSinceEpoch ~/ 1000;
          await AppConfig.shared.loadJsonAsset();
          if (AppConfig.shared.baseUrl.isNotEmpty) {
            downloadCsvFile(
                context,
                "${AppConfig.shared.baseUrl}/${RestConstants.instance.amps}${ampItem.deviceEui}/${RestConstants.instance.telemetry}/${RestConstants.instance.dataKey}?${RestConstants.instance.fromDateKey}=$formDate&${RestConstants.instance.toDateKey}=$toDate&${RestConstants.instance.exportAsKey}=csv",
                ampItem.deviceEui);
          }

          amplifierController.update();
        });
      },
      fontFamily: AppAssetsConstants.openSans,
    );
  }

  Widget telemetryRefreshButtonView() {
    return AppRefresh(enabled: (getDetectedStatusType(ampItem.status) == DetectedStatusType.online),
      loadingStatus: ampItem.telemetryStatus,
      buttonColor: (getDetectedStatusType(ampItem.status) == DetectedStatusType.online
          ? AppColorConstants.colorPrimary
          : AppColorConstants.colorH1Grey),
      onPressed: () {
        if (getDetectedStatusType(ampItem.status) != DetectedStatusType.online) {
          return;
        }

        if (ampItem.telemetryStatus != ApiStatus.loading) {
          dateHelper.onDropdownChanged(selectedOption, context,
              (startDateValue, endDateValue, selectedOptionValue) async {
            ampItem.telemetryStatus = ApiStatus.loading;
            amplifierController.update();
            ampPageHelper.telemetryPaginationHelper.setPage(0);
            if(ampPageHelper.isTableView) {
              telemetryPageController.goToFirstPage();
            }
            initializeTimer();
            _timer?.cancel();
            ampPageHelper.startDate = startDateValue;
            ampPageHelper.endDate = endDateValue;
            selectedOption = selectedOptionValue;
            int fromDate = ampPageHelper.startDate.millisecondsSinceEpoch ~/ 1000;
            int toDate = ampPageHelper.endDate.millisecondsSinceEpoch ~/ 1000;
            int refreshIndex =
                ampPageHelper.listTabs.indexWhere((tab) => tab.title == ampItem.deviceEui);
            ampItem.telemetryPageOffset = 0;
            await ampPageHelper.getTelemetryData(
                context, ampItem.deviceEui, refreshIndex, fromDate, toDate,AppStringConstants.telemetryPrePageLimit);
            ampItem.telemetryUpdateTime = DateTime.now();
            ampPageHelper.getTelemetryDifferenceTime();
            _startTimer();
            amplifierController.update();
          });
        }
      },
    );
  }

  refreshTelemetry(startDateValue, endDateValue){
    ampPageHelper.startDate = startDateValue;
    ampPageHelper.endDate = endDateValue;
    int fromDate =
        ampPageHelper.startDate.millisecondsSinceEpoch ~/ 1000;
    int toDate =
        ampPageHelper.endDate.millisecondsSinceEpoch ~/ 1000;
    int refreshIndex = ampPageHelper.listTabs
        .indexWhere((tab) => tab.title == ampItem.deviceEui);
    ampPageHelper.getTelemetryThresholdData(
        context, ampPageHelper.checkDeviceType(ampItem.type) ,refreshIndex);
    ampPageHelper.getTelemetryData(context, ampItem.deviceEui,
        refreshIndex, fromDate, toDate,AppStringConstants.telemetryPrePageLimit);
    dataSourceUpdate(
        dataList: ampPageHelper.telemetryDataList, tabIndex: _selectedTabIndex,selectedUnit: ampPageHelper.selectedUnit);
    amplifierController.update();
  }

  Future<void> downloadCsvFile(
      BuildContext context, String url, String deviceEui) async {
    try {
      String? accessToken = await getPrefStringValue(AppSharedPreference.accessToken) ?? '';
      final response = await http.get(
        Uri.parse(url),
        headers: (accessToken.isNotEmpty && AppConfig.shared.aadAuth) ? {
          'Authorization': 'Bearer $accessToken',
        } : null,
      );

      if (response.statusCode == 200) {
        final bytes = response.bodyBytes;
        final blob = html.Blob([bytes]);

        final url = html.Url.createObjectUrlFromBlob(blob);
        final anchor = html.AnchorElement(href: url)
          ..setAttribute('download', '${deviceEui}_telemetry.csv')
          ..click();

        html.Url.revokeObjectUrl(url);
        debugLogs(
            'Request Export csv --> ${response.statusCode} : ${response.request!.url}');
        S.of(context).exportCSVSuccess.toString().showSuccess(context);
      } else {
        Map<String, dynamic> responseData = jsonDecode(response.body);
        final errorPart = responseData["error"] != null ? "${responseData["error"]}: " : S.of(context).exportCSVError;
        final messagePart = responseData["message"] ?? "";
        String exportCSVError = "$errorPart$messagePart".trim();
        exportCSVError.showError(context);
      }
    } catch (e) {
      e.toString().showError(context);
    }
  }


  Widget getTelemetryDataTableView() {
    if(ampPageHelper.telemetryDataSource == null) return Container();
    if(!ampPageHelper.isTableView)return MobileAmpTelemetry().buildTelemetryList(context, ampPageHelper,ampItem.isShowTelemetryALSCPilots);
    return PaginatedDataTable2(
      columnSpacing: 8,
      initialFirstRowIndex: ampPageHelper.telemetryPaginationHelper.currentPage *
          AppStringConstants.telemetryPrePageLimit,
      rowsPerPage: AppStringConstants.telemetryPrePageLimit,
      showCheckboxColumn: false,
      headingTextStyle:TextStyle(
          color: AppColorConstants.colorWhite,
          fontWeight: FontWeight.w600,
          fontFamily: AppAssetsConstants.openSans,
          fontSize: 15),
      wrapInCard: false,
      border:ampPageHelper.dataTableHelper.tableBorder(),
      renderEmptyRowsInTheEnd: false,
      // ignore: deprecated_member_use
      headingRowColor:ampPageHelper.dataTableHelper.headingRowColor(),
      columns: _getDataColumns(_selectedTabIndex),
      controller:telemetryPageController,
      source:ampPageHelper.telemetryDataSource!,
      minWidth: 1400,
      dataRowHeight: 51,
      hidePaginator: true,
      empty:ampPageHelper.dataTableHelper.getEmptyTableContent(context),
    );
  }

  List<DataColumn> _getDataColumns(int tabIndex) {
    switch (tabIndex) {
      case 0: // Basic Info
        return [
          DataColumn2(
            fixedWidth: 200,
            label: SelectableText(S.of(context).timestamp),
          ),
          DataColumn2(
            fixedWidth: 150,
            label: Center(
                child: ampPageHelper.selectedUnit == AppStringConstants.celsius
                    ? AppText(S.of(context).temp)
                    : AppText(S.of(context).tempF)),
          ),
          // if (ampItem.isShowTelemetryALSCPilots) ...[
          //   DataColumn2(
          //     fixedWidth: 150,
          //     label:
          //         Center(child: AppText(S.of(context).alarmsBitmask, textAlign: TextAlign.center)),
          //   ),
          //   DataColumn2(
          //     fixedWidth: 150,
          //     label:
          //         Center(child: AppText(S.of(context).configBitmask, textAlign: TextAlign.center)),
          //   ),
          // ] ,
          DataColumn2(
            fixedWidth: 120,
            label: Center(child: AppText(S.of(context).acVoltage+" (${S.of(context).vac})",textAlign: TextAlign.center,)),
          ),
          DataColumn2(
            fixedWidth: 120,
            label: Center(child: AppText(S.of(context).twentyFourV+" (${S.of(context).vDC})")),
          ),
          DataColumn2(
            fixedWidth: 120,
            label: Center(child: AppText(S.of(context).dc8v+" (${S.of(context).vDC})")),
          ),
          DataColumn2(
            fixedWidth: 120,
            label: Center(child: AppText(S.of(context).fiveV+" (${S.of(context).vDC})")),
          ),
          DataColumn2(
            fixedWidth: 120,
            label: Center(child: AppText(S.of(context).threePointThreeV+" (${S.of(context).vDC})")),
          ),
          DataColumn2(
            label: SizedBox(),
          ),

        ];
      case 1: // Pilot Signals
        return [
           DataColumn2(
            fixedWidth: 200,
            label: SelectableText(S.of(context).timestamp),
          ), // Include Timestamp for context
          DataColumn2(fixedWidth: 250,
            label: Center(child: AppText(S.of(context).pilotPrimary+" (${S.of(context).mhZ} / ${S.of(context).dBmV})",textAlign: TextAlign.center)),
          ),
          DataColumn2(fixedWidth: 250,
            label: Center(child: AppText(S.of(context).pilotBackup+" (${S.of(context).mhZ} / ${S.of(context).dBmV})",textAlign: TextAlign.center)),
          ),
          DataColumn2(fixedWidth: 350,
            label: Center(child: AppText(S.of(context).pilotMarker+" (${S.of(context).mhZ} / ${S.of(context).dBmV})",textAlign: TextAlign.center,)),
          ),
          DataColumn2(
            label: SizedBox(),
          ),
        ];
      case 2: // Signal Quality
        return [
          DataColumn2(
            fixedWidth: 200,
            label: SelectableText(S.of(context).timestamp),
          ), // Include Timestamp for context
          DataColumn2(
            fixedWidth: 150,
            label: Center(child: AppText(S.of(context).dSAttn+" (${S.of(context).dB})")),
          ),
          DataColumn2(
            fixedWidth: 150,
            label: Center(child: AppText(S.of(context).dSEQ+" (${S.of(context).dB})")),
          ),
          DataColumn2(
            fixedWidth: 150,
            label: Center(child: AppText(S.of(context).uSAttn+" (${S.of(context).dB})")),
          ),
          DataColumn2(
            fixedWidth: 150,
            label: Center(child: AppText(S.of(context).uSEQ+" (${S.of(context).dB})")),
          ),
          DataColumn2(
            label: SizedBox(),
          ),
        ];
      default:
        return []; // Return empty list or a default set of columns
    }
  }
  Future<void> loadPreviousLogs(BuildContext context) async {
    if (ampPageHelper.telemetryPaginationHelper.canGoToPreviousPage) {
      ampPageHelper.telemetryPaginationHelper.setPage(ampPageHelper.telemetryPaginationHelper.currentPage - 1);
      if(ampPageHelper.isTableView) {
        telemetryPageController.goToPreviousPage();
      }
      amplifierController.update();
    }else{
      _timer?.cancel();
      _startTimer();
      amplifierController.update();
    }
  }

  Future<void> loadNextLogs(BuildContext context) async {
    if (ampPageHelper.telemetryPaginationHelper.canGoToNextPage) {
      ampPageHelper.telemetryPaginationHelper.setPage(ampPageHelper.telemetryPaginationHelper.currentPage + 1);
      if (ampPageHelper.telemetryPaginationHelper.currentPage >= ampPageHelper.telemetryPaginationHelper.totalPages) {
        ampItem.telemetryPageOffset = ampPageHelper.telemetryDataList.length ;
        await updateTelemetryData();
      } else {
        if(ampPageHelper.isTableView) {
          telemetryPageController.goToNextPage();
        }
        amplifierController.update();

      }
    }
  }

  updateTelemetryData() async {
    apiStatus = ApiStatus.loading;
    initializeTimer();
    amplifierController.update();
    int formDate = ampPageHelper.startDate.millisecondsSinceEpoch ~/ 1000;
    int toDate = ampPageHelper.endDate.millisecondsSinceEpoch ~/ 1000;
    TelemetryData telemetryData = await amplifierController.getTelemetry(
        deviceEui: ampItem.deviceEui,
        context: context,
        formDate: "$formDate",
        toDate: "$toDate",
        pageOffset: ampItem.telemetryPageOffset,
    perPageLimit: AppStringConstants.telemetryPrePageLimit);
    if (telemetryData.result != null && telemetryData.result!.isNotEmpty) {
      ampPageHelper.telemetryDataList.addAll(telemetryData.result!);
      dataSourceUpdate(
          dataList: ampPageHelper.telemetryDataList, tabIndex: _selectedTabIndex,selectedUnit: ampPageHelper.selectedUnit);
      bool hasMoreData = telemetryData.result!.length == AppStringConstants.telemetryPrePageLimit;
      ampPageHelper.telemetryPaginationHelper.updatePagination(ampPageHelper.telemetryDataList.length,
          hasMore: hasMoreData,pageLimit: AppStringConstants.telemetryPrePageLimit);
      ampPageHelper.telemetryDataSource!.notifyListeners();
      ampPageHelper.state.amplifierController.update();
    } else {
      ampPageHelper.telemetryPaginationHelper.setPage(ampPageHelper.telemetryPaginationHelper.currentPage - 1);
      ampPageHelper.telemetryPaginationHelper
          .updatePagination(ampPageHelper.telemetryDataList.length, hasMore: false,pageLimit: AppStringConstants.telemetryPrePageLimit);
      Future.delayed(const Duration(milliseconds: 100)).then((value) {
        if (ampPageHelper.isTableView) {
          if (telemetryData.result!.isNotEmpty) telemetryPageController.goToLastPage();
        }
        ampPageHelper.telemetryDataSource!.notifyListeners();
        ampPageHelper.state.amplifierController.update();
      });
    }
    ampPageHelper.getTelemetryDifferenceTime();
    ampItem.telemetryUpdateTime = DateTime.now();
    apiStatus = ApiStatus.success;
    ampItem.telemetryStatus = ApiStatus.success;
    amplifierController.update();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: AppStringConstants.refreshTimeIntervalSeconds), (timer) {
      if (ampPageHelper.telemetryPaginationHelper.currentPage == 0) {
        dateHelper.onDropdownChanged(selectedOption, context,
                (startDateValue, endDateValue, selectedOptionValue) async {
                initializeTimer();
              ampPageHelper.startDate = startDateValue;
              ampPageHelper.endDate = endDateValue;
              selectedOption = selectedOptionValue;
              int fromDate =
                  ampPageHelper.startDate.millisecondsSinceEpoch ~/ 1000;
              int toDate =
                  ampPageHelper.endDate.millisecondsSinceEpoch ~/ 1000;
              int refreshIndex = ampPageHelper.listTabs
                  .indexWhere((tab) => tab.title == ampItem.deviceEui);
                ampItem.telemetryPageOffset = 0;
              await ampPageHelper.getTelemetryData(context, ampItem.deviceEui,
                  refreshIndex, fromDate, toDate,AppStringConstants.telemetryPrePageLimit);
              ampItem.telemetryUpdateTime = DateTime.now();
              ampPageHelper.getTelemetryDifferenceTime();
            });
      }
    });
  }
  Widget tempToggleButtonView() {
    final units = [AppStringConstants.celsius, AppStringConstants.fahrenheit];
    final selectedIndex = units.indexOf(ampPageHelper.selectedUnit);
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          AppText(
            S.of(context).temperatureTitle,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          ToggleButtons(
            constraints: const BoxConstraints(maxHeight: 30, minWidth: 36),
            borderRadius: BorderRadius.circular(12),
            selectedColor: AppColorConstants.colorWhite, // applies only to icon/text color if no custom Text widget
            borderWidth: 1.5,
            fillColor: AppColorConstants.colorPrimary,
            borderColor: AppColorConstants.colorPrimary,
            selectedBorderColor: AppColorConstants.colorPrimary,
            isSelected: List.generate(units.length, (index) => index == selectedIndex),
            onPressed: (index) {
              ampPageHelper.selectedUnit = units[index];
              amplifierController.update();
              dataSourceUpdate(
                dataList: ampPageHelper.telemetryDataList,
                tabIndex: _selectedTabIndex,
                selectedUnit: ampPageHelper.selectedUnit,
              );
            },
            children: List.generate(units.length, (index) {
              final unit = units[index];
              final isSelected = index == selectedIndex;
              String label;
              switch (unit) {
                case AppStringConstants.celsius:
                  label = "°C";
                  break;
                case AppStringConstants.fahrenheit:
                  label = "°F";
                  break;
                default:
                  label = unit;
              }
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Text(
                  label,
                  style: TextStyle(
                    color: isSelected
                        ? AppColorConstants.colorWhite
                        : AppColorConstants.colorPrimary,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              );
            }),
          ),
        ],
      ),
    );
  }


  void _stopTimer() {
    _timer?.cancel();
    _timer = null;
  }

  @override
  void dispose() {
    _stopTimer();
    telemetryPageController.dispose();
    super.dispose();
  }

}
