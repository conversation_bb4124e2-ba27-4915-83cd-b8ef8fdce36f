// ignore_for_file: deprecated_member_use

import 'dart:developer';

import 'package:quantumlink_node/app_import.dart';

class TelemetryDataSource extends DataTableSource {
  AmplifierPageHelper amplifierPageHelper;
  final int selectedTabIndex;
  final String? selectedUnit;
  bool isShowTelemetryALSCPilots =  false;

  TelemetryDataSource.empty(
      this.selectedUnit, this.context, this.telemetryList, this.amplifierPageHelper, this.onTap,
      {required this.selectedTabIndex, required this.isShowTelemetryALSCPilots});

  TelemetryDataSource(
    this.context,
    this.telemetryList,
    this.amplifierPageHelper,
    this.onTap, {
    this.selectedTabIndex = 0,
    this.selectedUnit = AppStringConstants.celsius,
    this.isShowTelemetryALSCPilots = false,
    bool sortedByEUI = false, // fix here
  }) {
    if (sortedByEUI) {
      sort((d) => d.deviceEui, true);
    }
  }

  final BuildContext context;
  final List<TelemetryItem> telemetryList;
  final Function(TelemetryItem) onTap;
  DataTableHelper dataTableHelper = DataTableHelper();

  void sort<T>(
      Comparable<T> Function(TelemetryItem d) getField, bool ascending) {
    telemetryList.sort((a, b) {
      final aValue = getField(a);
      final bValue = getField(b);
      return ascending
          ? Comparable.compare(aValue, bValue)
          : Comparable.compare(bValue, aValue);
    });
    notifyListeners();
  }

  @override
  DataRow2 getRow(int index, [Color? color]) {
    assert(index >= 0);
    if (index >= telemetryList.length) throw 'index > _desserts.length';
    final dessert = telemetryList[index];
    checkPilots(dynamic pilotsValue ,{bool isDivide100=false}){
      if(isDivide100){
        return dessert.divideMeasuredBy100(pilotsValue);
      }
      return dessert.checkFrequency(pilotsValue);
    }

        final allCells = [
          DataCell(AppText(
              dessert.timestamp != null ? getUtcTimeZone(dessert.timestamp) : '',
              style: dataTableHelper.dataRowTextStyle)),
          DataCell(Center(
            child: AppText(
                "${dessert.changeTemp(checkValue(dessert.temperature), isCelsius: selectedUnit==AppStringConstants.celsius) ?? ""}",
                style: getTextStyle(checkValue(dessert.temperature),
                    amplifierPageHelper.currentTelemetryThreshold.temperature)),
          )),
          // DataCell(Center(
          //   child: AppText("${dessert.alarmFlags ?? ""}",
          //       style: dataTableHelper.dataRowTextStyle),
          // )),
          // DataCell(Center(
          //   child: AppText("${dessert.configBitmask ?? ""}",
          //       style: dataTableHelper.dataRowTextStyle),
          // )),
          DataCell(Center(
            child: AppText("${changeDouble(checkValue(dessert.acVoltage)) ?? ""}",
                style: getTextStyle(checkValue(dessert.acVoltage),
                    amplifierPageHelper.currentTelemetryThreshold.acVoltage)),
          )),
          DataCell(Center(
            child: AppText("${changeDouble(checkValue(dessert.dc24v)) ?? ""}",
                style: getTextStyle(checkValue(dessert.dc24v),
                    amplifierPageHelper.currentTelemetryThreshold.dc24v)),
          )),
          DataCell(Center(
            child: AppText("${changeDouble(checkValue(dessert.dc8v)) ?? ""}",
                style: getTextStyle(checkValue(dessert.dc8v),
                    amplifierPageHelper.currentTelemetryThreshold.dc8v)),
          )),
          DataCell(Center(
            child: AppText("${changeDouble(checkValue(dessert.dc5v)) ?? ""}",
                style: getTextStyle(checkValue(dessert.dc5v),
                    amplifierPageHelper.currentTelemetryThreshold.dc5v)),
          )),
          DataCell(Center(
            child: AppText("${changeDouble(checkValue(dessert.vdd33v)) ?? ""}",
                style: getTextStyle(checkValue(dessert.vdd33v),
                    amplifierPageHelper.currentTelemetryThreshold.vdd33v)),
          )),
          DataCell(Center(
        child: AppText(
            "${checkPilots(dessert.pilotPrimary0)} / ${checkPilots(dessert.pilotPrimaryLevel0,isDivide100: true)}\n${checkPilots(dessert.pilotPrimary1)} / ${checkPilots(dessert.pilotPrimaryLevel1,isDivide100: true)}",
            style: dataTableHelper.dataRowTextStyle,textAlign: TextAlign.center),
      )),
      DataCell(Center(
        child: AppText(
            "${checkPilots(dessert.pilotBackup0)} / ${checkPilots(dessert.pilotBackupLevel0,isDivide100: true)}\n${checkPilots(dessert.pilotBackup1)} / ${checkPilots(dessert.pilotBackupLevel1,isDivide100: true)}",
            style: dataTableHelper.dataRowTextStyle,textAlign: TextAlign.center),
      )),
      DataCell(Center(
        child: AppText(
          "${checkPilots(dessert.pilotMarker0)} / ${checkPilots(dessert.pilotMarkerLevel0, isDivide100: true)} , ${checkPilots(dessert.pilotMarker1)} / ${checkPilots(dessert.pilotMarkerLevel1, isDivide100: true)}\n${checkPilots(dessert.pilotMarker2)} / ${checkPilots(dessert.pilotMarkerLevel2, isDivide100: true)} , ${checkPilots(dessert.pilotMarker3)} / ${checkPilots(dessert.pilotMarkerLevel3, isDivide100: true)}",
          style: dataTableHelper.dataRowTextStyle,textAlign: TextAlign.center,),
      )),
      DataCell(Center(
            child: AppText("${checkValue(dessert.divideBy10(dessert.dsGainAdjust)) ?? ""}",
                style: getTextStyle(checkValue(dessert.dsGainAdjust),
                    amplifierPageHelper.currentTelemetryThreshold.dsGainAdjust)),
          )),
          DataCell(Center(
            child: AppText("${checkValue(dessert.divideBy10(dessert.dsSlopAdjust)) ?? ""}",
                style: getTextStyle(checkValue(dessert.dsSlopAdjust),
                    amplifierPageHelper.currentTelemetryThreshold.dsSlopeAdjust)),
          )),
          DataCell(Center(
            child: AppText("${checkValue(dessert.divideBy10(dessert.usGainAdjust)) ?? ""}",
                style: getTextStyle(checkValue(dessert.usGainAdjust),
                    amplifierPageHelper.currentTelemetryThreshold.usGainAdjust)),
          )),
          DataCell(Center(
            child: AppText("${checkValue(dessert.divideBy10(dessert.usSlopAdjust)) ?? ""}",
                style: getTextStyle(checkValue(dessert.usSlopAdjust),
                    amplifierPageHelper.currentTelemetryThreshold.usSlopeAdjust)),
          )),
    ];

    switch (selectedTabIndex) {
      case 0:
        return DataRow2.byIndex(
          index: index,
          color: MaterialStateProperty.all(AppColorConstants.colorWhite),
          cells: [
            allCells[0],
            allCells[1],
            allCells[2],
            allCells[3],
            allCells[4],
            allCells[5],
            allCells[6],
            const DataCell(SizedBox())
          ],
        );
      case 1:
        return DataRow2.byIndex(
          index: index,
          color: MaterialStateProperty.all(AppColorConstants.colorWhite),
          cells: [
            allCells[0],
            allCells[7],
            allCells[8],
            allCells[9],
            const DataCell(SizedBox())
          ],
        );
      case 2:
        return DataRow2.byIndex(
          index: index,
          color: MaterialStateProperty.all(AppColorConstants.colorWhite),
          cells: [
            allCells[0],
            allCells[10],
            allCells[11],
            allCells[12],
            allCells[13],
            const DataCell(SizedBox())
          ],
        );
      default:
        return DataRow2.byIndex(index: index, cells: []);
    }
  }

  getTextStyle(int value, TelemetryThresholdItem thresholdItem) {
    // print("--- Value=$value,high=${thresholdItem.high}---low=${thresholdItem.low}");
    return TextStyle(
      fontWeight: getMediumBoldFontWeight(),
      fontFamily: AppAssetsConstants.openSans,
      color: getTelemetryColor(value, thresholdItem),
      fontSize: getSize(14),
    );
  }

  String changeDouble(int value) {
    return (value / 1000.0).toStringAsFixed(2);
  }



  Color getTelemetryColor(int intValue, TelemetryThresholdItem thresholdItem) {
     return AppColorConstants.colorBlack;

    /*double value = intValue.toDouble() / 1000.0;
    // convert the thresholdItem low and high to double

    if (thresholdItem.low == null || thresholdItem.high == null) {
      // print("--- Value -- Is Null =$intValue,high=${thresholdItem.high}---low=${thresholdItem.low}");
      return Colors.black;
    }

    double low = thresholdItem.low!.toDouble()/1000;
    double high = thresholdItem.high!.toDouble()/1000;

    // Ensure proper ordering and handle boundaries carefully
    if (value >= low && value <= high) {
      return Colors.green;
    } else if (value < low - 3 ||
        value > high + 3) {
      return Colors.red;
    } else if (value < low - 2 ||
        value > high + 2) {
      return Colors.orange;
    } else if (value < low  ||
        value > high) {
      return Colors.yellow;
    } else {
      return Colors.black; // Fallback color
    }*/
  }

  checkValue(dynamic value) {
    return (value != null) ? value : 0;
  }

  @override
  int get rowCount => telemetryList.length;

  @override
  bool get isRowCountApproximate => false;

  @override
  int get selectedRowCount => 0;
}
