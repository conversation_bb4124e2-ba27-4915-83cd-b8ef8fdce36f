import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/pages/amplifiers/amplifier_details/alarms_history/alarms_history.dart';
import 'package:quantumlink_node/pages/amplifiers/amplifier_details/audit_log_history/audit_log_history.dart';

class MobileAlarmsHistoryPage {
  int itemPerPage = 10;

  Widget buildAuditLogHistoryList(BuildContext context, AuditLogHistoryPageState pageState) {
    if (pageState.auditLogsDataSource.rowCount == 0) {
      return SizedBox(
        height: 350,
        child: pageState.tableHelper.getEmptyTableContent(context),
      );
    }
    List<AuditLogData> fullList = pageState.auditLogsDataSource.list;
    List<AuditLogData> paginatedList = fullList
        .skip(
            pageState.paginationHelper.currentPage * AppStringConstants.auditLogsDevicePerPageLimit)
        .take(AppStringConstants.auditLogsDevicePerPageLimit)
        .toList();
    TextStyle  titleTextStyle = TextStyle(
      fontWeight: getMediumBoldFontWeight(),
      fontFamily: AppAssetsConstants.openSans,
      color: AppColorConstants.colorBlack,
      fontSize: getSize(14),
    );
    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: paginatedList.length,
      itemBuilder: (context, index) {
        AuditLogData auditLogData = paginatedList[index];
        String operation =auditLogData.operation ?? "";
        String userEmail =auditLogData.userEmail ?? "";
        String application =auditLogData.application ?? "";
        String message =auditLogData.message ?? "";
        String timestamp =(auditLogData.timestamp != null)? formatIsoDate(auditLogData.timestamp) : "" ;
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 4,horizontal: 5),
          child: CustomListTile(
            titleWidget: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    AppText("${S.of(context).timestamp}: ",style: titleTextStyle,),
                    AppText(timestamp,style: pageState.tableHelper.dataRowTextStyle,),
                  ],
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AppText("${S.of(context).email}: ",style: titleTextStyle),
                    Expanded(child: AppText(userEmail,style: pageState.tableHelper.dataRowTextStyle,)),
                  ],
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AppText("${S.of(context).application}: ",style: titleTextStyle),
                    Expanded(child: AppText(application,style: pageState.tableHelper.dataRowTextStyle,)),
                  ],
                ), Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AppText("${S.of(context).operation}: ",style: titleTextStyle),
                    Expanded(child: AppText(operation,style: pageState.tableHelper.dataRowTextStyle,)),
                  ],
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AppText("${S.of(context).message}: ",style: titleTextStyle),
                    Expanded(child: AppText(message,style: pageState.tableHelper.dataRowTextStyle,)),
                  ],
                )
              ],
            ), index: index,
          ),
        );
      },
    );
  }

  Widget selectTableTypeButtonView(AuditLogHistoryPageState pageState) {
    return pageState.tableHelper.selectTableTypeButtonView(
      isTableType: pageState.isTableView,
      onPressed: () {
        pageState.isTableView = !pageState.isTableView;
        pageState.ampController.update();
      },
    );
  }

  void autoSelectTableType(AuditLogHistoryPageState pageState) {
    ScreenLayoutType currentLayoutType = pageState.screenLayoutType;
    bool isMobile = currentLayoutType != ScreenLayoutType.desktop;
    if (pageState.previousLayoutType != currentLayoutType) {
      pageState.isTableView = !isMobile;
      pageState.previousLayoutType = currentLayoutType;
    }
  }
}
