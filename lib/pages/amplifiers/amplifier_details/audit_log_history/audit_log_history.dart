import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/pages/amplifiers/amplifier_details/audit_log_history/audit_log_history_datasource.dart';

import 'mobile_audit_log_history_page.dart';

class AuditLogHistoryPage extends StatefulWidget {
  final AmplifierDeviceItem amplifierItem;
  const AuditLogHistoryPage({super.key, required this.amplifierItem});

  @override
  State<AuditLogHistoryPage> createState() => AuditLogHistoryPageState();
}

class AuditLogHistoryPageState extends State<AuditLogHistoryPage> {
  late AmplifierController ampController;
  late ScreenLayoutType screenLayoutType;
  ScreenLayoutType previousLayoutType = ScreenLayoutType.desktop;
  ApiStatus apiStatus = ApiStatus.initial;
  final key = GlobalKey<PaginatedDataTableState>();
  final double heightOfDataTableCell = 48;
  int recordsInPage = 0; // For Set Fixed and Static Height Of Table
  int currentPageIndex = 0;
  bool isShowDiscovered = false;
  PaginatorController auditPageController = PaginatorController();
  TextEditingController searchController = TextEditingController();
  PaginationHelper paginationHelper = PaginationHelper();
  List<AuditLogData> searchAuditLogsDataList = [];
  List<AuditLogData> listAuditLogsData = [];
  late AuditLogHistoryDataSource auditLogsDataSource;
  DataTableHelper tableHelper = DataTableHelper();
  DateTime? onTapTime;
  Duration ? differenceTime;
  bool isShowText = true;
  bool isTableView = true;
  Timer? refreshTimer;
  DateRangeFilterHelper dateHelper = DateRangeFilterHelper();
  DateTime ?auditLogUpdateTime ;
  int auditLogsPageOffset = 0;
  @override
  void initState() {
    // TODO: implement initState
    apiStatus = ApiStatus.loading;
    super.initState();
    ampController = Get.put<AmplifierController>(AmplifierController());
    WidgetsBinding.instance.addPostFrameCallback((_) {
      getAuditLogsData();
    });
    getCurrantPageIndex();
  }
  getAuditLogsData() async {
    initializeTimer();
    apiStatus = ApiStatus.loading;
   ampController.update();
    await ampController
        .getAuditLogHistory(
        context: context,
        pageOffset: auditLogsPageOffset,
        perPageLimit: AppStringConstants.auditLogsDevicePerPageLimit,deviceEui: widget.amplifierItem.deviceEui)
        .then(
          (value) {
        if (value != null) {
          AuditLogResponse auditLogResponse = value;
          listAuditLogsData = auditLogResponse.data ?? [];
          if (mounted) {
            auditLogsDataSource = AuditLogHistoryDataSource(
              context,
              listAuditLogsData,
                  (value) {},
            );
            getDifferenceTime();
            auditLogUpdateTime = DateTime.now();
            apiStatus = ApiStatus.success;
            ampController.update();
          }
          bool hasMoreData = listAuditLogsData.length == AppStringConstants.auditLogsDevicePerPageLimit;
          paginationHelper.updatePagination(listAuditLogsData.length,
              hasMore: hasMoreData, pageLimit: AppStringConstants.auditLogsDevicePerPageLimit);
        } else {
          auditLogsDataSource = AuditLogHistoryDataSource(
            context,
            listAuditLogsData,
                (value) {},
          );
          auditLogUpdateTime = DateTime.now();
          apiStatus = ApiStatus.failed;
          ampController.update();
        }
      },
    );
  }

  void initializeTimer() {
    differenceTime = null;
    onTapTime = DateTime.now();
    isShowText = true;
    refreshTimer?.cancel();
  }
  getDifferenceTime() {
    differenceTime = DateTime.now().difference(onTapTime!);
    refreshTimer= Timer(const Duration(seconds: 2), () {
      isShowText = false;
      ampController.update();
    });
  }
  getCurrantPageIndex() {
    paginationHelper.initializePagination();
    auditPageController.addListener(() {
      currentPageIndex =
          (auditPageController.currentRowIndex / AppStringConstants.auditLogsDevicePerPageLimit).ceil();
      ampController.update();
    });
  }

  @override
  Widget build(BuildContext context) {
   
    return GetBuilder<AmplifierController>(
      init: AmplifierController(),
      builder: (AmplifierController controller) {
        ampController = controller;
        return Stack(
          children: [
            getBodyView(),
          ],
        );
      },
    );
  }

  Widget getBodyView() {
    return ScreenLayoutTypeBuilder(
      builder: (context, screenType, constraints) {
        screenLayoutType = screenType;
        MobileAlarmsHistoryPage().autoSelectTableType(this);
        return getAuditBoardView();
      },
    );
  }

  Widget getAuditBoardView() {
    return Container(
      padding: EdgeInsets.only(
          top:getSize(20) ,
          bottom: getSize(10)),
      child: ClipRRect(
        borderRadius: const BorderRadius.all(Radius.circular(5)),
        child: Container(
          width: double.infinity,
          child: ListView(
            physics: NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            children: [
              getPageAppBar(),
              auditLogsBoardView(),
              buildLastSeenView(),
            ],
          ),
        ),
      ),
    );
  }
  Widget buildLastSeenView() {
    if (isShowText) {
      return getTimeDurationView(
        refreshStatus: apiStatus,
        updateTime: auditLogUpdateTime,
        onTapTime: onTapTime,
        difference: differenceTime,
      );
    } else {
      if (auditLogUpdateTime != null) {
        return getLastSeenView(auditLogUpdateTime);
      } else {
        return Container();
      }
    }
  }

  Widget getPageAppBar() {
    return Container(
      alignment: Alignment.centerLeft,
      decoration: BoxDecoration(
          border: Border.all(color: AppColorConstants.colorH2),
          color:  buildTableAppbarColor(),
          borderRadius: BorderRadius.only(
              topRight: Radius.circular(getSize(7)),
              topLeft: Radius.circular(getSize(7)))),
      padding: EdgeInsets.only(left: getSize(18)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: getSize(10)),
          Row(
            children: [
              if (screenLayoutType == ScreenLayoutType.desktop)  const Spacer(flex: 2),
              Flexible(child:  _searchTextFieldView()),
              MobileAlarmsHistoryPage().selectTableTypeButtonView(this),
              SizedBox(width: getSize(10)),
            ],
          ),
          SizedBox(height: getSize(10)),
        ],
      ),
    );
  }

  String dropDownValueString(String originalString) {
    int startIndex = originalString.indexOf('(');
    if (startIndex == -1) {
      return originalString;
    }
    return originalString.substring(0, startIndex).trim();
  }

  Widget _searchTextFieldView() {
    return Padding(
      padding: EdgeInsets.only(
          right: screenLayoutType == ScreenLayoutType.desktop ? 0 : 20),
      child: Container(
        decoration: BoxDecoration(
            color:  buildTextFiledColor(),
            borderRadius: BorderRadius.circular(getSize(8))),
        child: AppTextFormField(
          onChanged: (value) {
            paginationHelper.currentPage = 0;
            if(isTableView) auditPageController.goToFirstPage();
            searchAuditLogsDataList = listAuditLogsData
                .where((element) =>
                    element.devEui.toLowerCase().contains(value.toLowerCase()) ||
                    element.operation.toLowerCase().contains(value.toLowerCase()) ||
                    element.userEmail.toLowerCase().contains(value.toLowerCase()) ||
                    element.sourceService.toLowerCase().contains(value.toLowerCase()) ||
                    element.application.toLowerCase().contains(value.toLowerCase()) ||
                    element.requestPath.toLowerCase().contains(value.toLowerCase()) ||
                    element.requestMethod.toLowerCase().contains(value.toLowerCase()) ||
                    element.message.toLowerCase().contains(value.toLowerCase()) ||
                    element.deviceType.toLowerCase().contains(value.toLowerCase()))
                .toList();
            auditLogsDataSource = AuditLogHistoryDataSource(
              context,
              searchAuditLogsDataList,
              (value) {},
            );
            ampController.update();
          },
          focusedBorderColor: AppColorConstants.colorPrimary,
          controller: searchController,
          enabledBorderColor: AppColorConstants.colorWhite1,
          hintText: S.of(context).quickSearch,
          maxLines: 1,
          textInputType: TextInputType.text,
          borderRadius: getSize(8),
          hintTextColor: AppColorConstants.colorDarkBlue,
          suffixIcon: const Padding(
            padding: EdgeInsets.all(12),
            child: AppImageAsset(image: AppAssetsConstants.searchIcon),
          ),
          hintFontSize: 17,
          fontFamily: AppAssetsConstants.openSans,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }

  Widget auditLogsBoardView() {
    if (apiStatus == ApiStatus.loading) {
      return SizedBox(
          height: 400,
          width: 300,
          child: Align(
              alignment: Alignment.center,
              child: Container(
                  width: double.infinity,
                  decoration:
                      tableHelper.tableBorderDeco(),
                  child: const AppLoader())));
    }
    int itemsPerPage = tableHelper
        .getCurrentPageDataLength(listAuditLogsData,
        currentPageIndex,perPageLimit: AppStringConstants.auditLogsDevicePerPageLimit);
    recordsInPage =
    (listAuditLogsData.length > AppStringConstants.auditLogsDevicePerPageLimit)
        ? itemsPerPage
        : listAuditLogsData.length;

    return Column(
      children: [
        Container(
            width: double.infinity,
            decoration: tableHelper.tableBorderDeco(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Table
                SizedBox(
                  height: !isTableView ? null :(listAuditLogsData.isNotEmpty)
                      ? (recordsInPage *
                              heightOfDataTableCell) +
                          (recordsInPage * 0.1) +
                          100
                      : (recordsInPage *
                              heightOfDataTableCell) +
                          (recordsInPage * 0.1) +
                          300,
                  child: !isTableView ? MobileAlarmsHistoryPage().buildAuditLogHistoryList(context,this) :getAuditLogsDataTableView(),
                ),
                AppPaginationWidget(
                  apiStatus: apiStatus,
                  paginationHelper: paginationHelper,
                  onLoadNext: () async {
                    await loadNextLogs(context);
                  },
                  onLoadPrevious: () async {
                    await loadPreviousLogs(context);
                    ampController.update();
                  },
                  onGoToFirstPage: () {
                    paginationHelper.setPage(0);
                    if(isTableView) auditPageController.goToFirstPage();
                    ampController.update();
                  },
                    onGoToLastPage: () {
                      if(isTableView) auditPageController.goToLastPage();
                      ampController.update();
                    },
                    itemsPerPage: AppStringConstants.auditLogsDevicePerPageLimit,
                    onChanged: (value) {
                      AppStringConstants.auditLogsDevicePerPageLimit = int.parse(value);
                      if (apiStatus != ApiStatus.loading) {
                        paginationHelper.setPage(0);
                        if(isTableView) auditPageController.goToFirstPage();
                        auditLogsPageOffset = 0;
                        getAuditLogsData();
                        ();
                        ampController.update();
                      }
                    }),
                const SizedBox(height: 10,),
              ],
            ))
      ],
    );
  }

  Widget getAuditLogsDataTableView() {
    return PaginatedDataTable2(
      columnSpacing: 2,
      rowsPerPage: AppStringConstants.auditLogsDevicePerPageLimit,
      showCheckboxColumn: false,
      initialFirstRowIndex: paginationHelper.currentPage *
          AppStringConstants.auditLogsDevicePerPageLimit,
      headingTextStyle: tableHelper.headingTextStyle(),
      wrapInCard: false,
      border: tableHelper.tableBorder(),
      renderEmptyRowsInTheEnd: false,
      // ignore: deprecated_member_use
      headingRowColor: tableHelper.headingRowColor(),
      columns: [
        DataColumn2(
          fixedWidth: 180,
          label: AppText(S.of(context).timestamp),
        ),
        DataColumn2(
          fixedWidth: 210,
          label: Center(child: AppText(S.of(context).email)),
        ),
        DataColumn2(
          fixedWidth: 180,
          label: Center(child: AppText(S.of(context).application)),
        ),
        DataColumn2(
          fixedWidth: 300,
          label: Center(child: AppText(S.of(context).operation)),
        ),
        const DataColumn(label: AppText(""))
      ],
      controller: auditPageController,
      source: auditLogsDataSource,
      minWidth: 1000,
      dataRowHeight: 51,
      hidePaginator: true,
      empty: tableHelper.getEmptyTableContent(context),
    );
  }


  Future<void> loadPreviousLogs(BuildContext context) async {
    if (paginationHelper.canGoToPreviousPage) {
      paginationHelper.setPage(paginationHelper.currentPage - 1);
      if(isTableView) auditPageController.goToPreviousPage();
    }
  }

  Future<void> loadNextLogs(BuildContext context) async {
    if (paginationHelper.canGoToNextPage) {
      paginationHelper.setPage(paginationHelper.currentPage + 1);
      if (paginationHelper.currentPage >= paginationHelper.totalPages) {
        auditLogsPageOffset = listAuditLogsData.length;
        await updateAuditLogsData();
      } else {
        if(isTableView)  auditPageController.goToNextPage();
       ampController.update();
      }
    }
  }

  updateAuditLogsData() async {
    initializeTimer();
    apiStatus = ApiStatus.loading;
   ampController.update();
    AuditLogResponse auditLogResponse = await ampController.getAuditLogHistory(
        context: context,
        pageOffset: auditLogsPageOffset,perPageLimit: AppStringConstants.auditLogsDevicePerPageLimit,deviceEui: widget.amplifierItem.deviceEui);
    List<AuditLogData> listAuditLogs = auditLogResponse.data ?? [];
    if (listAuditLogs.isNotEmpty) {
      listAuditLogsData.addAll(listAuditLogs);
      auditLogsDataSource = AuditLogHistoryDataSource(
      context,
        listAuditLogsData,
            (value) {},
      );
      bool hasMoreData = listAuditLogs.length == AppStringConstants.auditLogsDevicePerPageLimit;
      paginationHelper.updatePagination(listAuditLogsData.length,
          hasMore: hasMoreData, pageLimit: AppStringConstants.auditLogsDevicePerPageLimit);
    } else {
      paginationHelper.setPage(paginationHelper.currentPage - 1);
      paginationHelper.updatePagination(listAuditLogsData.length,
          hasMore: false, pageLimit: AppStringConstants.auditLogsDevicePerPageLimit);
      Future.delayed(const Duration(milliseconds: 100)).then((value) {
        if(isTableView)  auditPageController.goToLastPage();
        auditLogsDataSource.notifyListeners();
      });
    }
    getDifferenceTime();
    auditLogUpdateTime = DateTime.now();
    apiStatus = ApiStatus.success;
    ampController.update();
  }
}
