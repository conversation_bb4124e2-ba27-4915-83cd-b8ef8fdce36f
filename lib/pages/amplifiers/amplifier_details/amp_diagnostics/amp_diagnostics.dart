import 'package:quantumlink_node/app_import.dart';
import 'package:pub_semver/pub_semver.dart' as semver;
class AmpDiagnostics extends StatefulWidget {
  final AmplifierDeviceItem amplifierItem;
  final AmplifierPageHelper ampPageHelper;

  const AmpDiagnostics({super.key, required this.amplifierItem, required this.ampPageHelper});

  @override
  State<AmpDiagnostics> createState() => _AmpDiagnosticsState();
}

class _AmpDiagnosticsState extends State<AmpDiagnostics> {
  AmplifierController amplifierController =Get.put<AmplifierController>(AmplifierController());
  late AmplifierDeviceItem ampItem;
  List diagnosticsList = [];
  String selectedOption = "";


  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    ampItem = widget.amplifierItem;
  }

  initializeListValue() {
    String ampVersion= ampItem.ampDeviceSummary.result.versionInfo.fwVersion ?? "0.0.0";
    ampItem.isMultiIngressSwitch =  checkMultiIngressVersion(ampVersion);

    diagnosticsList = [
  //    S.of(context).amplifierDownstream,
      S.of(context).testPointConfig,
      if (ampItem.isMultiIngressSwitch || ((ampItem.type ?? "") == AppStringConstants.selectTypeLE) )
        S.of(context).amplifierIngress
    ];
    selectedOption = diagnosticsList[0];
  }

  checkMultiIngressVersion(versionString){
    // deprecated due to upper checks on tab
    return true;
    /*debugLogs("checkVersion for multi ingress -- >");
    versionString = versionString.replaceAll(RegExp(r'[^0-9.]'), '');
    final semver.Version version = semver.Version.parse(versionString);
    debugLogs("version ->");
    debugLogs(version);
    final semver.Version unstableVersion = semver.Version.parse(AppStringConstants.ampIngressSwitchVersion);
    if (version >= unstableVersion) { // if grater Or Equal 2.7.0 then show multi ingress
      return true;
    }
    return false;*/
  }



  @override
  Widget build(BuildContext context) {
    if(diagnosticsList.isEmpty){
      initializeListValue();
    }
    return GetBuilder<AmplifierController>(
      init: AmplifierController(),
      builder: (AmplifierController controller) {
        return ScreenLayoutTypeBuilder(
          builder: (context, screenType, constraints) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SizedBox(height: getSize(10)),
                Row(
                  children: [
                    SizedBox(width: screenType != ScreenLayoutType.mobile ? 300 : 200,
                      child: CommonDropdownButton(iconColor:  AppColorConstants.colorH3,
                        selectedValue: selectedOption,
                        buttonHeight: getSize(35),
                        hintText: '',
                        items: diagnosticsList,
                        onChanged: (value) {
                          selectedOption = value;
                          amplifierController.update();
                        },
                      ),
                    ),
                  ],
                ),
                if (selectedOption == S.of(context).testPointConfig)
                  AmpTestPointConfig(ampPageHelper: widget.ampPageHelper, amplifierItem: ampItem),
                if (selectedOption == S.of(context).amplifierIngress)
                  AmpIngressSwitch(ampPageHelper: widget.ampPageHelper, amplifierItem: ampItem),
                // if (selectedOption == S.of(context).amplifierDownstream)
                //   AmpDownStream(ampPageHelper: widget.ampPageHelper, amplifierItem: ampItem),
              ],
            );
          },
        );
      },
    );
  }

}