import 'package:quantumlink_node/app_import.dart';

class AmpTestPointConfig extends StatefulWidget {
  final AmplifierDeviceItem amplifierItem;
  final AmplifierPageHelper ampPageHelper;

  const AmpTestPointConfig({super.key, required this.amplifierItem, required this.ampPageHelper});

  @override
  State<AmpTestPointConfig> createState() => _AmpTestPointConfigState();
}

class _AmpTestPointConfigState extends State<AmpTestPointConfig> {
  AmplifierController amplifierController =Get.put<AmplifierController>(AmplifierController());
  late AmplifierDeviceItem ampItem;
  bool isListValueUpdate = false;
  int? refreshIndex;
  double indicatorValue = 0;
late ScreenLayoutType screenLayoutType;
  TextEditingController searchController = TextEditingController();
  DateTime? onTapTime;
  Duration ? differenceTime;
  bool isShowText = true;
  Timer? refreshTimer;
  @override
  void initState() {
    super.initState();
    ampItem = widget.amplifierItem;
    WidgetsBinding.instance.addPostFrameCallback((_) {
    getTestPointData();
    });
  }
  getTestPointData() async {
    initializeTimer();

    refreshIndex = widget.ampPageHelper.listTabs
        .indexWhere((tab) => tab.title == ampItem.deviceEui);
    
    await widget.ampPageHelper
        .getTestPointConfiguration(context, ampItem.deviceEui,refreshIndex!, true);
    
    getDifferenceTime();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AmplifierController>(
      init: AmplifierController(),
      builder: (AmplifierController controller) {
        amplifierController = controller;
        return ScreenLayoutTypeBuilder(
          builder: (context, screenType, constraints) {
            screenLayoutType = screenType;
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                diagnosticsRefreshButtonView(),
                Row(
                  children: [
                    Expanded(
                      child: testPointConfigView(
                          controller: controller, title: S.of(context).testPointConfig),
                    ),
                    if(screenType != ScreenLayoutType.mobile)const Spacer()
                  ],
                ),

              ],
            );
          },
        );
      },
    );
  }

  Widget diagnosticsRefreshButtonView() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5.0),
      child: Obx(() {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Row(
              mainAxisAlignment: (screenLayoutType != ScreenLayoutType.mobile)
                  ? MainAxisAlignment.spaceBetween
                  : MainAxisAlignment.end,
              children: [
                if (screenLayoutType != ScreenLayoutType.mobile)
                  Wrap(
                    direction: screenLayoutType == ScreenLayoutType.desktop
                        ? Axis.horizontal
                        : Axis.vertical,
                    children: [
                      if (ampItem.testPointConfigError != null)
                        CommonAPIErrorView(errorMessage: ampItem.testPointConfigError ?? "")
                      else
                        const SizedBox(height: 39),
                    ],
                  ),
                if (!ampItem.isSetDataInProgressing.value)
                  Row(
                    children: [
                      if (isShowText)
                        getTimeDurationView(
                            differenceMessage: ampItem.testPointConfigError != null
                                ? S.of(context).refreshFailedMessage
                                : null,
                            refreshStatus: ampItem.testPointConfigRefreshStatus,
                            updateTime: ampItem.ampTestPointUpdateTime,
                            onTapTime: onTapTime,
                            difference: differenceTime,
                            textColor: ampItem.testPointConfigError == null
                                ? AppColorConstants.colorGrn
                                : AppColorConstants.colorAppbar)
                      else ...[
                        if (ampItem.ampTestPointUpdateTime != null)
                          getLastSeenView(ampItem.ampTestPointUpdateTime,
                              textColor: ampItem.testPointConfigError == null
                                  ? AppColorConstants.colorGrn
                                  : AppColorConstants.colorAppbar),
                      ],
                      AppRefresh(
                        enabled:
                            (getDetectedStatusType(ampItem.status) == DetectedStatusType.online),
                        loadingStatus: ampItem.testPointConfigRefreshStatus,
                        buttonColor:
                            (getDetectedStatusType(ampItem.status) == DetectedStatusType.online
                                ? AppColorConstants.colorPrimary
                                : AppColorConstants.colorH1Grey),
                        onPressed:
                            getDetectedStatusType(ampItem.status) == DetectedStatusType.online
                                ? () async {
                                    initializeTimer();
                                    await widget.ampPageHelper.getTestPointConfiguration(
                                        context, ampItem.deviceEui, refreshIndex!, true);
                                    getDifferenceTime();
                                    amplifierController.update();
                                  }
                                : null,
                      ),
                    ],
                  )
              ],
            ),
            if (screenLayoutType == ScreenLayoutType.mobile)...[
              if (ampItem.testPointConfigError != null)
                CommonAPIErrorView(errorMessage: ampItem.testPointConfigError ?? "",rightPadding: 5)
            ]
          ],
        );
      }),
    );
  }

  void initializeTimer() {
    differenceTime = null;
    onTapTime = DateTime.now();
    isShowText = true;
    refreshTimer?.cancel();
  }
  getDifferenceTime() {
    differenceTime = DateTime.now().difference(onTapTime!);
    refreshTimer= Timer(const Duration(seconds: 3), () {
      if (mounted) {
        isShowText = false;
        amplifierController.update();
      }
    });
  }

  Widget testPointConfigView({
    required String title,
    required AmplifierController controller,
  }) {
    String dropDownSelectedValue = S.of(context).forward;
      return Container(
              decoration: borderViewDecoration,
            padding: EdgeInsets.symmetric(vertical: getSize(10), horizontal: getSize(16)),
        child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Flexible(
                          child: AppText(
                        title,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          fontFamily: AppAssetsConstants.openSans,
                          color: AppColorConstants.colorLightBlue,
                        ),
                      )),
                      (ampItem.testPointConfigRefreshStatus == ApiStatus.loading)
                          ? const SizedBox(height: 40, width: 50, child: AppLoader())
                          : const SizedBox(height: 40, width: 50,)
                    ],
                  ),
                  (ampItem.testPointConfigList.isNotEmpty)
                      ? SizedBox(height: getSize(14))
                      : Container(),
                  ListView.builder(
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    itemCount: ampItem.testPointConfigList.length,
                    itemBuilder: (context, index) {
                      TestPointItem testPointItem =
                      ampItem.testPointConfigList[index];
                      dropDownSelectedValue = getValueName(testPointItem.value, testPointItem.id);
                      return StatefulBuilder(builder: (context, snapshot) {
                        return commonStreamingDropDown(
                            testPointItem: testPointItem,
                            defaultValue:null,
                            items: getItemList(testPointItem.id),
                            onChanged: (value) {
                              if (ampItem.testPointConfigRefreshStatus ==
                                  ApiStatus.loading) {
                                return;
                              }
                              if (value.contains(S.of(context).forward)) {
                                testPointItem.value = 1;
                              } else {
                                testPointItem.value = 2;
                              }
                              setState(() {});
                            },
                            title: testPointItem.getName(testPointItem.id),
                            selectedValue: dropDownSelectedValue,
                            hintText: S.of(context).select,
                            onPressed: () async {
                              if(widget.ampPageHelper.isTestPointConfigUpdating.value){
                                S.of(context).pleaseWait.showMessage();
                                return;
                              }
                              initializeTimer();

                             await widget.ampPageHelper.setTestPointConfiguration(
                                  context, refreshIndex!,ampItem.deviceEui, testPointItem);
                              getDifferenceTime();
                            });
                      });
                    },
                  ),
                  const SizedBox(height: 10,)
                ],
              ),
            );
  }

  String getValueName(int value, int id) {
    return (id == 1)
        ? (value == 1 ? "${S.of(context).forward}In" : "${S.of(context).reverse}Out")
        : (id == 2)
            ? (value == 1 ? "${S.of(context).forward}Out" : "${S.of(context).reverse}In")
            : (value == 1 ? S.of(context).forward : S.of(context).reverse);
  }
  MapEntry<int, int>? getIdValueByIndex(int index, Map<int, int> testPointMap) {
    if (index < 0 || index >= testPointMap.length) return null;
    return testPointMap.entries.elementAt(index);
  }
  List<dynamic> getItemList(dynamic id) {
    if (id == 1) {
      return ["${S.of(context).forward}In" , "${S.of(context).reverse}Out"];
    }else if( id == 2){
      return ["${S.of(context).forward}Out" , "${S.of(context).reverse}In"];
    }
    return [S.of(context).forward, S.of(context).reverse];
  }

  Widget commonTitleView({required String title}) {
    return Container(alignment: Alignment.center,
      height: getSize(50),
      padding: EdgeInsets.only(top: getSize(13), left: getSize(20)),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(getSize(7)),
          topLeft: Radius.circular(getSize(7)),
        ),
      ),
      child: AppText(
        title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            fontFamily: AppAssetsConstants.openSans,
            color: AppColorConstants.colorLightBlue,
          ),
      ),
    );
  }

  Widget commonStreamingDropDown(
      {required String title,
      required TestPointItem testPointItem,
      required String hintText,
      required  List<dynamic>? items,
      String? selectedValue,
        String? defaultValue,
      required VoidCallback onPressed,
      required ValueChanged onChanged}) {
    print("selectedValue--$selectedValue --------- defaultValue--$defaultValue");
    return Padding(
        padding: EdgeInsets.only(
            left: getSize(20), right: getSize(20), bottom: getSize(10)),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: AppText(
                    title,
                    style: const TextStyle(
                        color: AppColorConstants.colorDarkBlue,
                        fontSize: 14,
                        fontFamily: AppAssetsConstants.openSans,
                        fontWeight: FontWeight.w600),
                  ),
                ),
                if (screenLayoutType == ScreenLayoutType.desktop )
                  Expanded(
                    child: commonDropDownView(
                        hintText: hintText,
                        testPointItem: testPointItem,
                        selectedValue: selectedValue,
                        onChanged: onChanged,
                        onPressed: onPressed,items: items),
                  ),
              ],
            ),
            if (screenLayoutType != ScreenLayoutType.desktop)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: commonDropDownView(
                    hintText: hintText,
                    testPointItem: testPointItem,
                    selectedValue: selectedValue,
                    onChanged: onChanged,
                    onPressed: onPressed,items: items),
              )
          ],
        ));
  }

  commonDropDownView(
      {required String hintText,
      String? selectedValue,
      String? defaultValue,
      required TestPointItem testPointItem,
      required  List<dynamic>? items,
      required VoidCallback onPressed,
      required ValueChanged onChanged}) {
    return Obx(() {
      return Row(
        children: [
          Flexible(
            child: CommonDropdownButton(
              iconColor: AppColorConstants.colorH3,
              selectedValue: selectedValue,
              buttonHeight: getSize(36),
              hintText: hintText,
              items: items,
              onChanged: onChanged,
            ),
          ),
          SizedBox(
            width: 10,
          ),
          (testPointItem.isProgressing.value)
              ? const SizedBox(height: 36, width: 55, child: AppLoader())
              : AppButton(
                  buttonWidth: 40,
                  buttonRadius: 8,
                  buttonHeight: 18,
                  padding: MaterialStateProperty.all(const EdgeInsets.all(4)),
                  buttonName: S.of(context).apply,
                  fontSize: 12,
                  onPressed: onPressed,
                  fontFamily: AppAssetsConstants.openSans,
                )
        ],
      );
    });
  }


  BoxDecoration borderViewDecoration = BoxDecoration(
    color: AppColorConstants.colorWhite,
    border: Border.all(color: AppColorConstants.colorChart, width: 1.8),
    borderRadius: const BorderRadius.all(Radius.circular(8)),
  );
}

