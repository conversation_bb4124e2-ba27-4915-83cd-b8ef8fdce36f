// ignore_for_file: deprecated_member_use

import 'package:quantumlink_node/app_import.dart';

class DownStreamDataSource extends DataTableSource {

  DownStreamDataSource.empty(this.context, this.downStreamList, this.onTap) {
    //list = [];
  }

  DownStreamDataSource(this.context, this.downStreamList, this.onTap,
      [sortedByEUI = false]) {
    if (sortedByEUI) {
      sort((d) => d.deviceEui, true);
    }
  }

  final BuildContext context;
  final List<DownStreamAmpsModel> downStreamList;
  final Function(DownStreamAmpsModel) onTap;
  DataTableHelper dataTableHelper = DataTableHelper();


  void sort<T>(Comparable<T> Function(DownStreamAmpsModel d) getField, bool ascending) {
    downStreamList.sort((a, b) {
      final aValue = getField(a);
      final bValue = getField(b);
      return ascending ? Comparable.compare(aValue, bValue) : Comparable.compare(bValue, aValue);
    });
    notifyListeners();
  }

  @override
  DataRow2 getRow(int index, [Color? color]) {
    assert(index >= 0);
    if (index >= downStreamList.length) throw 'index > _desserts.length';
    final dessert = downStreamList[index];
    DetectedStatusType? detectedStatusType =
    getDetectedStatusType(dessert.status);
    return DataRow2.byIndex(
      index: index,
      color:  (index.isEven
          ? MaterialStateProperty.all(AppColorConstants.colorWhite)
          : MaterialStateProperty.all(AppColorConstants.colorBackgroundDark)),
      onTap: () => onTap(dessert),
      cells: [
        DataCell(AppText("${dessert.deviceEui ?? ""}",
            style: dataTableHelper.dataRowTextStyle)),
        DataCell(AppText("${dessert.type ?? ""}",
            style: dataTableHelper.dataRowTextStyle)),
        DataCell(AppText(
            dessert.lastSeen != null ? getUtcTimeZone(dessert.lastSeen) : '',
          style: dataTableHelper.dataRowTextStyle,
        )),
        DataCell(Container(
          alignment: Alignment.center,
          height: getSize(30),
          width: getSize(70),
          decoration: BoxDecoration(
              color: detectedStatusType == DetectedStatusType.online
                  ? AppColorConstants.colorGreen2
                  : detectedStatusType == DetectedStatusType.offline
                  ? AppColorConstants.colorH2.withOpacity(0.4)
                  : (detectedStatusType == DetectedStatusType.pending)
                  ? AppColorConstants.colorOrange
                  : AppColorConstants.colorH2.withOpacity(0.4),
              borderRadius: BorderRadius.circular(getSize(18))),
          child: AppText(
            detectedStatusType == DetectedStatusType.online
                ? S.of(context).online
                : detectedStatusType == DetectedStatusType.offline
                ? S.of(context).offline
                : detectedStatusType == DetectedStatusType.pending
                ? S.of(context).pending
                : S.of(context).offline,
            style: TextStyle(
                color: (detectedStatusType == DetectedStatusType.offline ||
                    dessert.status == null)
                    ? AppColorConstants.colorH3
                    : AppColorConstants.colorWhite,
                fontFamily: AppAssetsConstants.sourceSans,
                fontSize: 14,
                fontWeight: FontWeight.w500),
          ),
        )),
      ],
    );

  }

  @override
  int get rowCount => downStreamList.length;

  @override
  bool get isRowCountApproximate => false;

  @override
  int get selectedRowCount => 0;
}