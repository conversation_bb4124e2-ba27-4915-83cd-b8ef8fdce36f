import 'package:quantumlink_node/app_import.dart';

class AmpDownStream extends StatefulWidget {
  final AmplifierDeviceItem amplifierItem;
  final AmplifierPageHelper ampPageHelper;

  const AmpDownStream({super.key, required this.amplifierItem, required this.ampPageHelper});

  @override
  State<AmpDownStream> createState() => _AmpDownStreamState();
}

class _AmpDownStreamState extends State<AmpDownStream> {
  AmplifierController amplifierController =Get.put<AmplifierController>(AmplifierController());
  late AmplifierDeviceItem ampItem;
  final double heightOfDataTableCell = 48;
  int recordsInPage = 0; // For Set Fixed and Static Height Of Table
  int currentPageIndex = 0;
  PaginatorController pageController = PaginatorController();
  List<DownStreamAmpsModel> listDownStreamData = [];
  late DownStreamDataSource downStreamDataSource;
  ApiStatus apiStatus = ApiStatus.initial;
  DateTime? onTapTime;
  Duration ? differenceTime;
  bool isShowText = true;
  Timer? refreshTimer;
  late ScreenLayoutType screenLayoutType;
  @override
  void initState() {
    super.initState();
    ampItem = widget.amplifierItem;
    if(listDownStreamData.isEmpty) {
      getDownStreamData();
    }
  }

  getDownStreamData() async {
    listDownStreamData.clear();
    ampItem.downStreamAmpsUpdateTime=null;
    apiStatus = ApiStatus.loading;
    initializeTimer();
    DownstreamAmplifiers response = await amplifierController.getDownstreamAmplifiers(
        deviceEui: ampItem.deviceEui, context: context);

    for (var devicesElement in response.devices) {
      AmplifierDeviceItem ampDeviceItem = widget.ampPageHelper.amplifierDataSource.list
          .firstWhere((element) => element.deviceEui == devicesElement);
      dynamic lastSeen = ampDeviceItem.lastSeen;
      dynamic status = ampDeviceItem.status;
      dynamic type = ampDeviceItem.type;
      listDownStreamData.add(DownStreamAmpsModel(
          deviceEui: devicesElement, lastSeen: lastSeen, status: status, type: type));
    }
    downStreamDataSource = DownStreamDataSource(
      context,
      listDownStreamData,
          (value) {},
    );
    ampItem.downStreamAmpsUpdateTime = DateTime.now();
    getDifferenceTime();
    apiStatus = ApiStatus.success;
    amplifierController.update();
  }

  //INITIALIZE TIMER FOR REFRESH
  void initializeTimer() {
    differenceTime = null;
    onTapTime = DateTime.now();
    isShowText = true;
    refreshTimer?.cancel();
  }
  getDifferenceTime() {
    differenceTime = DateTime.now().difference(onTapTime!);
    refreshTimer= Timer(const Duration(seconds: 3), () {
      if (mounted) {
        isShowText = false;
        amplifierController.update();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AmplifierController>(
      init: AmplifierController(),
      builder: (AmplifierController controller) {
        return ScreenLayoutTypeBuilder(
          builder: (context, screenType, constraints) {
            screenLayoutType = screenType;
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                buildTitleWithRefreshButtonView(),
                buildDownstreamAmpsStreaming(),
               // getLastSeenView(ampItem.downStreamAmpsUpdateTime)

              ],
            );
          },
        );
      },
    );
  }

  Widget buildTitleView() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10),
      child: Row(
        children: [
          AppText(
            S.of(context).downstreamAmps,
            style: TextStyle(
                fontSize: getSize(24),
                fontFamily: AppAssetsConstants.openSans,
                color: AppColorConstants.colorPrimary,
                fontWeight: FontWeight.w600),
          ),
          SizedBox(width: getSize(10)),
          Flexible(
            child: Container(
                height: getSize(20),
                decoration: BoxDecoration(
                    border: Border(
                        bottom: BorderSide(color: AppColorConstants.colorBlack, width: 0.4)))),
          )
        ],
      ),
    );
  }

  buildDownstreamAmpsStreaming(){
    return StreamBuilder<String>(
        stream: widget.ampPageHelper.updateStreamView,
        builder: (context, snapshot) {
          if (listDownStreamData.isNotEmpty) {
            for (var findElement in listDownStreamData) {
              for (var ampItem in widget.ampPageHelper.amplifierDataSource.list) {
                if (ampItem.deviceEui == findElement.deviceEui) {
                  downStreamDataSource.downStreamList
                      .firstWhere((updateElement) =>
                  updateElement.deviceEui == ampItem.deviceEui)
                      .lastSeen = ampItem.lastSeen;
                  downStreamDataSource.downStreamList
                      .firstWhere((updateElement) =>
                  updateElement.deviceEui == ampItem.deviceEui)
                      .status = ampItem.status;
                  downStreamDataSource.notifyListeners();
                }
              }
            }
          }
          return downstreamAmpsBoardView();
        });
  }

  Widget downstreamAmpsBoardView() {
    if (apiStatus == ApiStatus.loading) {
      return Container(
          decoration: BoxDecoration(
            color: AppColorConstants.colorWhite,
            border: Border.all(color: AppColorConstants.colorH2, width: 0.8),
          ),
          height: 400,
          child: const Align(alignment: Alignment.center, child: AppLoader()));
    }
    int itemsPerPage =
    widget.ampPageHelper.dataTableHelper.getCurrentPageDataLength(listDownStreamData, currentPageIndex);
    recordsInPage = (listDownStreamData.length > 10)
        ? itemsPerPage
        :listDownStreamData.length;
    return Column(
      children: [
        Container(
            width: double.infinity,
            decoration:widget.ampPageHelper.dataTableHelper.tableBorderDeco(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Table
                SizedBox(
                  height: (listDownStreamData.isNotEmpty)
                      ? (recordsInPage *
                      heightOfDataTableCell) +
                      (recordsInPage * 0.1) +
                      150
                      : (recordsInPage *
                      heightOfDataTableCell) +
                      (recordsInPage * 0.1) +
                      300,
                  child: getDownStreamDataTableView(),
                ),
                SizedBox(height: getSize(20)),
                // Divider
                Container(
                  height: 1,
                  width: double.infinity,
                  color: AppColorConstants.colorBlack12,
                )
              ],
            ))
      ],
    );
  }
  Widget buildTitleWithRefreshButtonView() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10),
      child: Column(
        children: [
          Row(mainAxisAlignment: MainAxisAlignment.end,
            children: [
              AppText(
                S.of(context).downstreamAmps,
                style: TextStyle(
                    fontSize: getSize(24),
                    fontFamily: AppAssetsConstants.openSans,
                    color: AppColorConstants.colorPrimary,
                    fontWeight: FontWeight.w600),
              ),
              SizedBox(width: getSize(10)),
              Flexible(
                child: Container(
                    height: getSize(20),
                    decoration: BoxDecoration(
                        border: Border(
                            bottom: BorderSide(color: AppColorConstants.colorBlack, width: 0.4)))),
              ),
              SizedBox(width: getSize(10)),
              if(screenLayoutType == ScreenLayoutType.desktop)lastSeenViewWithRefreshButton()

            ],
          ),
          if (screenLayoutType != ScreenLayoutType.desktop)
            lastSeenViewWithRefreshButton()
        ],
      ),
    );
  }

  Widget lastSeenViewWithRefreshButton() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        if (isShowText)
          getTimeDurationView(
              refreshStatus: apiStatus,
              updateTime: ampItem.downStreamAmpsUpdateTime,
              onTapTime: onTapTime,
              difference: differenceTime,
              textColor: apiStatus == ApiStatus.loading
                  ? AppColorConstants.colorAppbar
                  : AppColorConstants.colorGreen)
        else ...[
          if (ampItem.downStreamAmpsUpdateTime != null)
            getLastSeenView(ampItem.downStreamAmpsUpdateTime,
                textColor: apiStatus == ApiStatus.loading
                    ? AppColorConstants.colorAppbar
                    : AppColorConstants.colorGreen),
        ],
        AppRefresh(
          enabled: (getDetectedStatusType(ampItem.status) == DetectedStatusType.online),
          loadingStatus: apiStatus,
          buttonColor: getDetectedStatusType(ampItem.status) == DetectedStatusType.online
              ? AppColorConstants.colorPrimary
              : AppColorConstants.colorH1Grey,
          onPressed: getDetectedStatusType(ampItem.status) == DetectedStatusType.online
              ? () {
                  initializeTimer();
                  getDownStreamData();
                  amplifierController.update();
                }
              : null,
        ),
      ],
    );
  }

  Widget getDownStreamDataTableView() {
    return PaginatedDataTable2(
      showCheckboxColumn: false,
      headingTextStyle: widget.ampPageHelper.dataTableHelper.headingTextStyle(),
      wrapInCard: false,
      border: widget.ampPageHelper.dataTableHelper.tableBorder(),
      renderEmptyRowsInTheEnd: false,
      // ignore: deprecated_member_use
      headingRowColor: widget.ampPageHelper.dataTableHelper.headingRowColor(),
      columns: _getDataColumns(),
      controller: pageController,
      source: downStreamDataSource,
      minWidth: 700,
      dataRowHeight: 51,
      // For progress indicator
      hidePaginator: false,
      empty: widget.ampPageHelper.dataTableHelper.getEmptyTableContent(context),
    );
  }
  List<DataColumn> _getDataColumns() {
    return[
      DataColumn2(
        size: ColumnSize.S,
        label: SelectableText(S.of(context).devEUI),
      ),
      DataColumn2(
        size: ColumnSize.S,
        label: AppText(S.of(context).type),
      ),
      DataColumn(
        label: AppText(S.of(context).lastSeen),
      ),
      DataColumn(
        label: AppText(S.of(context).status),
      ),
    ];
  }
}