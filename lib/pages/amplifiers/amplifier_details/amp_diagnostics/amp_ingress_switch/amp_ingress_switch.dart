import 'package:quantumlink_node/app/helper/click_prevention_helper.dart';
import 'package:quantumlink_node/app_import.dart';

import '../../../../../utils/dialog_utils.dart';

class AmpIngressSwitch extends StatefulWidget {
  final AmplifierDeviceItem amplifierItem;
  final AmplifierPageHelper ampPageHelper;

  const AmpIngressSwitch({super.key, required this.amplifierItem, required this.ampPageHelper});

  @override
  State<AmpIngressSwitch> createState() => _AmpIngressSwitchState();
}

class _AmpIngressSwitchState extends State<AmpIngressSwitch> {

  AmplifierController amplifierController =Get.put<AmplifierController>(AmplifierController());
  late AmplifierDeviceItem ampItem;
  double indicatorValue = 0;
late ScreenLayoutType screenLayoutType;
  DateTime? onTapTime;
  Duration ? differenceTime;
  bool isShowText = true;
  Timer? refreshTimer;
  int? loadingIndex;
  @override
  void initState() {
    super.initState();
    ampItem = widget.amplifierItem;
    getIngressAmpData();
  }

  getIngressAmpData() async {
    initializeTimer();
    int refreshIndex = widget.ampPageHelper.listTabs
        .indexWhere((tab) => tab.title == ampItem.deviceEui);
    widget.ampPageHelper.listTabs[refreshIndex].ampDeviceItem.setIngressStatus = ApiStatus.initial;
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      IngressSwitchItem ingressSwitchItem = IngressSwitchItem(ingressSwitch: indicatorValue);
      await widget.ampPageHelper
          .getIngressSwitchData(context, ampItem.deviceEui, ingressSwitchItem, refreshIndex,ampItem.isMultiIngressSwitch);
      getDifferenceTime();
      amplifierController.update();
    });
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AmplifierController>(
      init: AmplifierController(),
      builder: (AmplifierController controller) {
        amplifierController = controller;
        return ScreenLayoutTypeBuilder(
          builder: (context, screenType, constraints) {
            screenLayoutType = screenType;
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                refreshButtonView(),
                Wrap(
                  children: (ampItem.ingressSwitchItemList.isEmpty
                      ? [
                          Container(
                            constraints: const BoxConstraints(maxWidth: 500),
                            decoration: borderViewDecoration,
                            padding: EdgeInsets.symmetric(
                                vertical: getSize(10), horizontal: getSize(16)),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Flexible(
                                    child: AppText(
                                  S.of(context).amplifierIngress,
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    fontFamily: AppAssetsConstants.openSans,
                                    color: AppColorConstants.colorLightBlue,
                                  ),
                                )),
                                (ampItem.configRefreshStatus == ApiStatus.loading)
                                    ? const SizedBox(height: 40, width: 50, child: AppLoader())
                                    : const SizedBox(
                                        height: 40,
                                        width: 50,
                                      )
                              ],
                            ),
                          ),
                        ] // Show dummy view
                      : ampItem.ingressSwitchItemList.asMap().entries.map((entry) {
                          final int index = entry.key;
                          final IngressSwitchItem item = entry.value;
                          return ingressSwitchView(item, index);
                        }).toList()),
                )
                // Container(
                //   alignment: Alignment.bottomRight,width: getSize(500),child: getLastSeenView(ampItem.ampIngressUpdateTime),),
              ],
            );
          },
        );
      },
    );
  }

  Widget refreshButtonView() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5),
      child: screenLayoutType == ScreenLayoutType.mobile ? Column(
        children: [
          buildActionRow(),
          if (ampItem.ingressSwitchError != null)
            CommonAPIErrorView(errorMessage: ampItem.ingressSwitchError ?? "",rightPadding: 5),
        ],
      ) : Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          if (ampItem.ingressSwitchError != null)
            CommonAPIErrorView(errorMessage: ampItem.ingressSwitchError ?? "")
          else
            const SizedBox(height: 39),
          buildActionRow(),
        ],
      ),
    );
  }

  Widget buildActionRow() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        if (isShowText)
          getTimeDurationView(
            refreshStatus: ampItem.configRefreshStatus,
            updateTime: ampItem.ampIngressUpdateTime,
            onTapTime: onTapTime,
            differenceMessage: ampItem.ingressSwitchError != null
                ? S.of(context).refreshFailedMessage
                : null,
            difference: differenceTime,
            textColor: ampItem.ingressSwitchError == null
                ? AppColorConstants.colorGrn
                : AppColorConstants.colorAppbar,
          )
        else if (ampItem.ampIngressUpdateTime != null)
          getLastSeenView(
            ampItem.ampIngressUpdateTime,
            textColor: ampItem.ingressSwitchError == null
                ? AppColorConstants.colorGrn
                : AppColorConstants.colorAppbar,
          ),
        const SizedBox(width: 10), // Spacing for better alignment
        AppRefresh(
          enabled: (getDetectedStatusType(ampItem.status) == DetectedStatusType.online),
          loadingStatus: ampItem.configRefreshStatus,
          buttonColor: (getDetectedStatusType(ampItem.status) == DetectedStatusType.online
              ? AppColorConstants.colorPrimary
              : AppColorConstants.colorH1Grey),
          onPressed: getDetectedStatusType(ampItem.status) == DetectedStatusType.online
              ? () async {
            initializeTimer();
            int refreshIndex = widget.ampPageHelper.listTabs
                .indexWhere((tab) => tab.title == ampItem.deviceEui);
            if (ampItem.configRefreshStatus != ApiStatus.loading) {
              IngressSwitchItem ingressSwitchItem = IngressSwitchItem(
                  ingressSwitch:  ampItem.ingressSwitchItemList.isNotEmpty ? ampItem.ingressSwitchItemList.first.ingressSwitch : 0);
              await widget.ampPageHelper.getIngressSwitchData(
                  context, ampItem.deviceEui, ingressSwitchItem, refreshIndex,ampItem.isMultiIngressSwitch);
              getDifferenceTime();
              amplifierController.update();
            }
          }
              : null,
        ),
      ],
    );
  }

  void initializeTimer() {
    ampItem.ampIngressUpdateTime = null;
    differenceTime = null;
    onTapTime = DateTime.now();
    isShowText = true;
    refreshTimer?.cancel();
  }

  getDifferenceTime() {
    differenceTime = DateTime.now().difference(onTapTime!);
    refreshTimer = Timer(const Duration(seconds: 3), () {
      if (mounted) {
        isShowText = false;
        amplifierController.update();
      }
    });
  }

  Widget ingressSwitchView(IngressSwitchItem ingressSwitchItem, int switchIndex) {
    return Container(
      margin: const EdgeInsets.all(8),
      constraints: const BoxConstraints(maxWidth: 500),
      decoration: borderViewDecoration,
      child: Column(
        children: [
          commonIngressSwitchView(
            onChanged: (value) {
              if (ampItem.configRefreshStatus == ApiStatus.loading) {
                return;
              }
              indicatorValue = _findClosestDivision(value);
              amplifierController.update();
            },
            isLoading: (ampItem.setIngressStatus == ApiStatus.loading) && loadingIndex == switchIndex,
            isActive: (ingressSwitchItem.ingressSwitch != null),
            onChangeEnd: (value) async {
              if (!shouldProceed(ampItem, indicatorValue,switchIndex)) return;

              if (value.isGreaterThan(6)) return;

              int lastIngressValue =
                  ingressSwitchItem.ingressSwitch ?? 0;

              String msg = (indicatorValue == 0)
                  ? S.of(context).messageIngressDisable
                  : S.of(context).messageIngressEnable;
              DialogUtils().confirmationDialog(
                  context,
                  S.of(context).ingressSwitch,
                  msg,
                  S.of(context).yes,
                  S.of(context).no, () {
                 loadingIndex = switchIndex;
                _handleIngressSwitchUpdate(lastIngressValue,ingressSwitchItem);
              },
                    () => goBack(),
              );
            },
            title:switchIndex == 0 ? S.of(context).main :"${S.of(context).aux} : $switchIndex",
            ingressValue: ingressSwitchItem.ingressSwitch == 0
                ? 0
                : ingressSwitchItem.ingressSwitch == 1
                    ? 6
                    : ingressSwitchItem.ingressSwitch == 2
                        ? 30
                        : indicatorValue,
          ),
          SizedBox(height: getSize(20)),
        ],
      ),
    );
  }

  void _handleIngressSwitchUpdate(int lastIngressValue, IngressSwitchItem ingressSwitchItem) {
    final updatedItem = IngressSwitchItem(
      ingressSwitch: indicatorValue, // New value
      result: ingressSwitchItem.result,
      portSelect: ingressSwitchItem.portSelect,
    );
    setIngressSwitch(updatedItem, indicatorValue);


    int refreshIndex = widget.ampPageHelper.listTabs
        .indexWhere((tab) => tab.title == ampItem.deviceEui);

    widget.ampPageHelper.listTabs[refreshIndex].ampDeviceItem
        .setIngressStatus = ApiStatus.loading;
    initializeTimer();
    amplifierController.update();

    widget.ampPageHelper.setIngressSwitchAPICall(context, refreshIndex, lastIngressValue,
        ampItem.deviceEui, updatedItem, true, ampItem.isMultiIngressSwitch);

    goBack();
  }

  bool shouldProceed(AmplifierDeviceItem ampItem, double indicatorValue , int switchIndex) {
    if (ampItem.configRefreshStatus == ApiStatus.loading) {
      return false;
    }

    if (ClickPreventionHelper.lastOnClick()) {
      return false;
    }

    if (ampItem.ingressSwitchItemList[switchIndex].ingressSwitch == null) {
      return false;
    }

    if (getIndicatorValue(ampItem.ingressSwitchItemList[switchIndex].ingressSwitch) == indicatorValue) {
      return false;
    }

    return true;
  }

  void setIngressSwitch(IngressSwitchItem item, double indicatorValue) {
    switch (indicatorValue) {
      case 0:
        item.ingressSwitch = 0;
        break;
      case 6:
        item.ingressSwitch = 1;
        break;
      default:
        item.ingressSwitch = 2;
        break;
    }
  }

  int getIndicatorValue(int ingressSwitch) {
    switch (ingressSwitch) {
      case 0:
        return 0;
      case 1:
        return 6;
      case 2:
        return 30;
      default:
        return 0;
    }
  }

  String onChangeDropDownValue(String value, AmplifierController controller) {
    String selectedValue = value;
    controller.update();
    return selectedValue;
  }

  List<double> divisionPoints = [0, 6, 30];

  Widget commonIngressSwitchView(
      {required double ingressValue,
      required String title,
      required ValueChanged<double>? onChanged,
      required bool isActive,
      required bool isLoading,
      required ValueChanged<double>? onChangeEnd}) {
    print("---isActive= $isActive");

    return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 30).copyWith(top: 5),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(left: 10, bottom: getSize(20)),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  AppText(title,style:  const TextStyle(fontWeight: FontWeight.w600,fontSize: 16),),
                  (isLoading)
                      ? const SizedBox(height: 40, width: 50, child: AppLoader())
                      : const SizedBox(height: 40, width: 50, )
                ],
              ),
            ),
            Column(
              children: [
                Stack(
                  children: [
                    SliderTheme(
                      data: SliderTheme.of(context).copyWith(
                          trackHeight: 8.3,
                          thumbColor: AppColorConstants.colorPrimary,
                          overlayColor: AppColorConstants.colorChartLine.withOpacity(0.4),
                          thumbShape: CustomThumbShape(),
                          overlayShape:
                              const RoundSliderOverlayShape(overlayRadius: 12),
                          activeTrackColor: AppColorConstants.colorChartLine,
                          valueIndicatorColor: AppColorConstants.colorChartLine.withOpacity(0.8),
                          showValueIndicator:
                              ShowValueIndicator.onlyForContinuous),
                      child: Slider(
                        secondaryTrackValue: 6,
                        secondaryActiveColor: AppColorConstants.colorChartLine.withOpacity(0.3),
                        inactiveColor: Colors.grey.shade100,
                        value: ingressValue.toDouble(),
                        max: getSize(30),
                        label: ingressValue > 0 ? '-$ingressValue' : '$ingressValue',
                        onChangeEnd: onChangeEnd,
                        onChanged: (double newValue) {
                          if (isActive) {
                            onChanged!(newValue);
                          }
                        },
                      ),
                    ),
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    Padding(
                      padding: const EdgeInsets.only(left: 8.0),
                      child: commonGainTypeString('0 ${S.of(context).dB}'),
                    ),
                    Flexible(child: commonGainTypeString('-6 ${S.of(context).dB}').paddingOnly(right: 30)),
                    const Spacer(),
                    Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: commonGainTypeString('-30 ${S.of(context).dB}',fontColor: AppColorConstants.colorDotLine,)
                    ),
                  ],
                )
              ],
            ),
          ],
        ));
  }
  Widget commonGainTypeString(String text, {Color? fontColor}){
    return   Column(
      children: [
        Container(
          height: getSize(5),
          width: getSize(1.5),
          color:fontColor?? AppColorConstants.colorH1,
        ),
        AppText(
          text,
          style:  TextStyle(fontFamily: AppAssetsConstants.openSans,fontWeight: FontWeight.w600,fontSize: 13,color: fontColor),
        ),
      ],
    );
  }
  double _findClosestDivision(double value) {
    double minDistance = double.infinity;
    double closestDivision = divisionPoints.first;

    for (double division in divisionPoints) {
      double distance = (value - division).abs();
      if (distance < minDistance) {
        minDistance = distance;
        closestDivision = division;
      }
    }

    return closestDivision;
  }

  BoxDecoration borderViewDecoration = BoxDecoration(
    color: AppColorConstants.colorWhite,
    border: Border.all(color: AppColorConstants.colorChart, width: 1.8),
    borderRadius: const BorderRadius.all(Radius.circular(8)),
  );
}
