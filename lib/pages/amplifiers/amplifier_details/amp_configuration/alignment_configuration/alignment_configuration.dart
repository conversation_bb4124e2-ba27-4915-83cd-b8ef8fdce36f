// ignore_for_file: deprecated_member_use

import 'dart:io';
import "package:universal_html/html.dart" as html;
import 'package:quantumlink_node/app_import.dart';


class AlignmentConfiguration extends StatefulWidget {
  final AmplifierDeviceItem amplifierItem;
  final AmplifierPageHelper ampPageHelper;

  const AlignmentConfiguration({super.key, required this.amplifierItem, required this.ampPageHelper});

  @override
  State<AlignmentConfiguration> createState() => _AlignmentConfigurationState();
}

class _AlignmentConfigurationState extends State<AlignmentConfiguration> {
  AmplifierController amplifierController =Get.put<AmplifierController>(AmplifierController());
  bool isStartDownStream = false;
  double constraintsWidth = 0.0;
  late AmplifierDeviceItem ampItem;
  AlignmentConfig alignmentConfig= AlignmentConfig();
  AlignmentConfig exportAlignmentConfig= AlignmentConfig();
  String fileName="";
  String diplexLabel="";
  String openMode="";
  String virtualTilt="";
  List<PointData> configurationPoints = [];
  double baselineX = 0;
  double endPoint = 0;
  bool isLoading=false;
  FilePickerResult ?pickerResult;
  String getErrorMessage="";
 String commentDiplexfilter ="1 for 85/108, 2 for 204/258 and 3 for 396/492 and 4 for 492/606, 5 for 684/834 ";
 String commentOperationalMOde ="1 for 1.2MHz and 2 for 1.8 MHz";
  DateTime ?lastUpdateTime;
  // Add controllers for all editable fields
   TextEditingController startFreqController = TextEditingController();
   TextEditingController startPowerController= TextEditingController();
   TextEditingController endFreqController= TextEditingController();
   TextEditingController endPowerController= TextEditingController();
   TextEditingController step1FreqController= TextEditingController();
   TextEditingController step1PowerController= TextEditingController();

  // Controllers for marker pilots
  late List<TextEditingController> markerPilotControllers;

  // Controllers for ALSC
  late List<TextEditingController> mainPilotControllers;
  late List<TextEditingController> backupPilotControllers;
  Future<void> pickJsonFile() async {
    getErrorMessage= "";
    pickerResult= await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['json'],
      withData: true
    );
    fileName=pickerResult!.files.first.name;
    applyPickFileData();
    amplifierController.update();
  }

  applyPickFileData(){
    if (pickerResult != null && pickerResult!.files.single.bytes != null) {
      Uint8List fileBytes = pickerResult!.files.single.bytes!;
      String content = utf8.decode(fileBytes);
      setState(() async {
        Map<String, dynamic>  jsonData = json.decode(content);
        alignmentConfig = AlignmentConfig.fromJson(jsonData);
        updateConfigValue();
      });
    }
  }
  updateConfigValue() async {
    exportAlignmentConfig= alignmentConfig;
    AutoConfig auto = alignmentConfig.autoConfig ?? AutoConfig() ;
    String _formatValue(dynamic value) {
      return value != null ? value.toString() : '-';
    }
    startFreqController.text = _formatValue(auto.start?.freq);
    startPowerController.text = _formatValue(auto.start?.power);

    endFreqController.text = _formatValue(auto.end?.freq);
    endPowerController.text = _formatValue(auto.end?.power);

    step1FreqController.text = _formatValue(auto.step1?.freq);
    step1PowerController.text = _formatValue(auto.step1?.power);

    diplexLabel= extractLabelFromComment(
      alignmentConfig.diplexfilter,
      commentDiplexfilter,
    ) ;
    openMode = extractLabelFromComment(
      alignmentConfig.operationalMode,
      commentOperationalMOde,
    ) ;
    await buildGraph();
  }

  Future<void> buildGraph() async {
    isLoading=true;

    configurationPoints.clear();
    int startx = double.parse(alignmentConfig.autoConfig!.start!.freq.toString()).toInt();
    int stopx = double.parse(alignmentConfig.autoConfig!.end!.freq.toString()).toInt();
    int stepfreq = double.parse(alignmentConfig.autoConfig!.step1!.freq.toString()).toInt();
    int startpower = double.parse(alignmentConfig.autoConfig!.start!.power.toString()).toInt() * 10;
    int stoppower = double.parse(alignmentConfig.autoConfig!.end!.power.toString()).toInt() * 10;
    int stepvalue = double.parse(alignmentConfig.autoConfig!.step1!.power.toString()).toInt() * 10;
    endPoint= alignmentConfig.autoConfig!.end!.freq;
    double tilt = (stoppower + stepvalue - startpower).toDouble();
    virtualTilt = (tilt / 10.0).toString();
    int numofpoints;
    numofpoints = ((stopx - startx) ~/ 6) + 1;
    int freq = startx;
    double slope, c = 0;

    if (tilt > 0) {
      stoppower = (startpower + tilt).toInt();
    }
    slope = (stoppower - startpower) / (stopx - startx);
    c = startpower - slope * startx;

   await Future.delayed(const Duration(milliseconds: 200), () {
      setState(() {
        if (stepfreq > 0) numofpoints++;
        for (int i = 0; i < numofpoints; i++) {
          double x;
          double y;
          x = freq.toDouble();
          if (stepfreq > 0 && x == stepfreq) {
            x = stepfreq.toDouble();
            y = slope * x + c;
            y /= 10;
            configurationPoints.add(PointData(freq:x, value:y));
            x = stepfreq.toDouble();
            y = slope * x + c - stepvalue;
            y /= 10;
            configurationPoints.add(PointData(freq:x, value:y));
          } else if (stepfreq > 0 && x > stepfreq) {
            y = slope * x + c - stepvalue;
            y /= 10;
            configurationPoints.add(PointData(freq:x, value:y));
          } else {
            y = slope * x + c;
            y /= 10;
            configurationPoints.add(PointData(freq:x, value:y));
          }
          freq += 6;
        }
      });
    });
    isLoading=false;
  }

  String extractLabelFromComment(dynamic key, String comment) {
    // Normalize the comment string
    final normalized = comment.replaceAll('and', ',');
    final regex = RegExp(r'(\d+)\s+for\s+([^,]+)');
    final matches = regex.allMatches(normalized);
    final Map<dynamic, dynamic> map = {
      for (final match in matches)
        int.parse(match.group(1)!): match.group(2)!.trim()
    };
    final intKey = key is int ? key : int.tryParse(key.toString());
    return map[intKey] ?? "Unknown" ;
  }

  //Update Value
  void updateConfigFromControllers() {
    List<dynamic> markerPilots = markerPilotControllers.map((controller) {
      final value = int.tryParse(controller.text);
      return value ?? 0;
    }).toList();
    List<dynamic> mainPilots = exportAlignmentConfig.alscConfig?.mainPilots ?? [] ;
    List<dynamic> backupPilotS = exportAlignmentConfig.alscConfig?.backupPilots ?? [] ;
    exportAlignmentConfig.autoConfig = AutoConfig(end: FreqPower(freq: int.tryParse(endFreqController.text), power: int.tryParse(endPowerController.text)),
        start: FreqPower(freq: int.tryParse(startFreqController.text), power: int.tryParse(startPowerController.text)),
        markerPilots: markerPilots,
        step1: FreqPower(freq:  int.tryParse(step1FreqController.text), power: int.tryParse(step1PowerController.text)));
    exportAlignmentConfig.alscConfig = AlscConfig(backupPilots: backupPilotS,mainPilots: mainPilots);

  }

  //Export JsonFile
  void exportToJsonFile() {
    updateConfigFromControllers();
    final String jsonString =
    const JsonEncoder.withIndent('  ').convert(exportAlignmentConfig.toJson());

    final bytes = utf8.encode(jsonString);
    final blob = html.Blob([Uint8List.fromList(bytes)], 'application/json');
    final url = html.Url.createObjectUrlFromBlob(blob);
    final anchor = html.AnchorElement(href: url)
      ..setAttribute("download", "alignment_config.json")
      ..click();
    html.Url.revokeObjectUrl(url); // clean up
    lastUpdateTime=DateTime.now();
  }

  cancelChangesValue(){
    AutoConfig auto = alignmentConfig.autoConfig ?? AutoConfig() ;
    String _formatValue(dynamic value) {
      return value != null ? value.toString() : '-';
    }
    AlscConfig alscConfig = alignmentConfig.alscConfig ?? AlscConfig() ;
    startFreqController.text = _formatValue(auto.start?.freq);
    startPowerController.text = _formatValue(auto.start?.power);
    endFreqController.text = _formatValue(auto.end?.freq);
    endPowerController.text = _formatValue(auto.end?.power);
    step1FreqController.text = _formatValue(auto.step1?.freq);
    step1PowerController.text = _formatValue(auto.step1?.power);
    mainPilotControllers = alscConfig.mainPilots
        !.map((e) => TextEditingController(text: e.toString()))
        .toList();
    backupPilotControllers = alscConfig.backupPilots
    !.map((e) => TextEditingController(text: e.toString()))
        .toList();
    markerPilotControllers= auto.markerPilots
    !.map((e) => TextEditingController(text: e.toString()))
        .toList();

    amplifierController.update();
  }
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    ampItem = widget.amplifierItem;
    getConfigFileDataUsingAPI(isRefresh:false);
  }

  getConfigFileDataUsingAPI({required bool isRefresh}) async {
    pickerResult= null;
    fileName="";
    getErrorMessage="";
    isLoading = true;
    final data = await amplifierController.getAlignmentConfigFileInfo(
      deviceEui: ampItem.deviceEui,
      context: context,
      isRefresh:isRefresh
    );
    if (data is ConfigFileResponse) {
      if(data.result != null) alignmentConfig = AlignmentConfig.fromConfigFile(data.result!.configFile);
      updateConfigValue();
    } else {
      getErrorMessage= data['detail'].toString();
    }

    isLoading = false;
    lastUpdateTime=DateTime.now();
    amplifierController.update();
  }

  setConfigFileDataUsingAPI() async {
    getErrorMessage= "";
    isLoading = true;
    amplifierController.update();
    updateConfigFromControllers();
    final data = await amplifierController.setAlignmentConfigInfo(
      deviceEui: ampItem.deviceEui,
      context: context,
      alignmentConfig: exportAlignmentConfig
    );
    if (data is ConfigFileResponse) {
      if(data.result != null) alignmentConfig = AlignmentConfig.fromConfigFile(data.result!.configFile);
      await Future.delayed(const Duration(seconds: 5));
      await getConfigFileDataUsingAPI(isRefresh: true);
    } else {
      getErrorMessage= data['detail']?.toString() ?? S.of(context).somethingWentWrong;
    }
    isLoading = false;
    lastUpdateTime=DateTime.now();
    amplifierController.update();
  }

  late ScreenLayoutType screenLayoutType;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AmplifierController>(
      init: AmplifierController(),
      builder: (AmplifierController controller) {
        return ScreenLayoutTypeBuilder(
          builder: (context, screenType, constraints) {
            screenLayoutType = screenType;
            constraintsWidth = constraints.maxWidth;
            return Padding(
              padding: EdgeInsets.symmetric(horizontal: getSize(8.0), vertical: getSize(15)),
              child: MergeSemantics(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    buildAlignmentConfigurationHeader(),
                    buildConfigurationFileName(),
                    configurationChartView(configurationPoints),
                    buildConfigPanels(),
                    if(constraintsWidth < 1550) Padding(padding: EdgeInsets.only(top: 10),child: buildAutoAlignMarkerPilotsView(),),
                    buildConfigPanelsForNonDesktop(),
                    buildFrequencyConfigView(),
                    buildActionButtons(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        buildLastSeenView(updateTime: lastUpdateTime),
                        AppRefresh(
                          buttonColor: AppColorConstants.colorPrimary,
                          loadingStatus: isLoading ? ApiStatus.loading : ApiStatus.success,
                          onPressed: () {
                            isLoading = true;
                            amplifierController.update();
                            getConfigFileDataUsingAPI(isRefresh:true);
                          },
                        )
                      ],
                    )
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }


  Widget buildAlignmentConfigurationHeader() {
    return Column(
      children: [
        Row(
          children: [
            AppText(
              S.of(context).alignmentConfiguration,
              style: TextStyle(
                  fontSize: getSize(24),
                  fontFamily: AppAssetsConstants.openSans,
                  color: AppColorConstants.colorPrimary,
                  fontWeight: FontWeight.w600),
            ),
            SizedBox(width: getSize(10)),
            Expanded(
              child: Container(
                  height: getSize(20),
                  decoration: BoxDecoration(
                      border: Border(
                          bottom: BorderSide(color: AppColorConstants.colorBlack, width: 0.4)))),
            )
          ],
        ),
        if (getErrorMessage.isNotEmpty)
          Align(alignment: Alignment.centerRight,child: errorMessageView(
            errorMessage: getErrorMessage,
          ),)
      ],
    );
  }

  Widget buildConfigurationFileName() {
    return Align(
      alignment: Alignment.centerLeft,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: AppText(
          fileName,
          style: TextStyle(
              fontFamily: AppAssetsConstants.openSans,
              fontWeight: getMediumBoldFontWeight(),
              letterSpacing: 0.32,
              color: AppColorConstants.colorLightBlue,
              fontSize: getSize(16)),
        ),
      ),
    );
  }

  Widget configurationChartView(
    List<PointData> configurationPoints,
      ) {
    return SingleChildScrollView(
      child: Container(
        constraints: BoxConstraints(maxWidth: screenLayoutType == ScreenLayoutType.desktop ? 900 :  MediaQuery.of(context).size.width),
        padding: const EdgeInsets.only(left: 8, right: 8, top: 20),
        decoration: BoxDecoration(
            color: AppColorConstants.colorWhite,
            borderRadius: BorderRadius.circular(9),
            border: Border.all(color: AppColorConstants.colorChart, width: 1.8)),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
          child: Column(crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Wrap(alignment: WrapAlignment.end,
                spacing: 5,runSpacing: 8,
                children: [
                  exportFileButtonView(),
                  AppButton(
                    buttonWidth: 180,
                    buttonRadius: 8,
                    buttonHeight: 25,
                    padding: MaterialStateProperty.all(const EdgeInsets.all(15)),
                    buttonName: S.of(context).selectFile,
                    fontSize: 16,
                    onPressed: () {
                      pickJsonFile();
                    },
                    borderColor:AppColorConstants.colorLightBlue,
                    fontColor:AppColorConstants.colorWhite,
                    buttonColor:AppColorConstants.colorLightBlue,
                    borderWidth: 1.2,
                    fontFamily: AppAssetsConstants.openSans,
                  ),
                ],
              ),
              SizedBox(
                height: getSize(400),
                child: isLoading ? const AppLoader() :LineChart(LineChartData(
                  borderData: FlBorderData(
                      show: true,
                      border: Border(
                          left: BorderSide(color: AppColorConstants.colorChart, width: 2),
                          bottom: BorderSide(color: AppColorConstants.colorChart, width: 2))),
                  lineTouchData: LineTouchData(
                    getTouchedSpotIndicator: (LineChartBarData barData, List<int> spotIndexes) {
                      return spotIndexes.map((spotIndex) {
                        return TouchedSpotIndicatorData(
                          FlLine(color: AppColorConstants.colorH1.withOpacity(0.5), strokeWidth: 1),
                          const FlDotData(),
                        );
                      }).toList();
                    },
                    touchTooltipData: LineTouchTooltipData(
                      maxContentWidth: getSize(200),
                      getTooltipColor: (touchedSpot) => Colors.white,
                      getTooltipItems: (touchedSpots) {
                        return touchedSpots.map((LineBarSpot touchedSpot) {
                          final titleTextStyle = TextStyle(
                            fontWeight: FontWeight.w500,
                            fontFamily: AppAssetsConstants.openSans,
                            color: touchedSpot.bar.gradient?.colors[0] ?? touchedSpot.bar.color,
                            fontSize: getSize(14),
                          );
                          final textStyle = TextStyle(
                            fontWeight: FontWeight.w500,
                            color: AppColorConstants.colorH1,
                            fontSize: getSize(14),
                          );
                     
                            return LineTooltipItem(
                                '${touchedSpot.x}\n',
                                TextStyle(
                                  fontFamily: AppAssetsConstants.openSans,
                                  color: AppColorConstants.colorH1,
                                  fontSize: getSize(14),
                                ),
                                children: [

                                  TextSpan(
                                    text: "${touchedSpot.y.toStringAsFixed(2)}",
                                    style: textStyle,
                                  ),
                                ],
                                textAlign: TextAlign.start);

                        }).toList();
                      },
                    ),
                    handleBuiltInTouches: true,
                  ),
                  lineBarsData: [
                    LineChartBarData(
                      color: AppColorConstants.colorLevelChartBorder,
                      spots: configurationPoints
                          .where((point) {
                        return point.freq >= baselineX && point.freq <= endPoint;
                      })
                          .toList()
                          .map((point) => FlSpot(point.freq, point.value))
                          .toList(),
                      isCurved: true,
                      isStrokeCapRound: true,
                      barWidth: 2,
                      belowBarData:
                      BarAreaData(show: true, color: AppColorConstants.colorLevelChartBackGround),
                      dotData: const FlDotData(
                        show: false,
                      ),
                    ),
                  ],
                  minY: 0,
                  maxY: 60,
                  baselineX: baselineX,
                  maxX: endPoint,
                  gridData: FlGridData(
                    verticalInterval: 10,
                    getDrawingVerticalLine: (value) {
                      return const FlLine(
                        color: Colors.grey, // Color of horizontal grid lines
                        strokeWidth: 2, // Width of horizontal grid lines
                      );
                    },
                    show: false,
                    checkToShowHorizontalLine: (value) => false,
                    checkToShowVerticalLine: (value) => true,
                    horizontalInterval: 30,
                  ),
                  titlesData: FlTitlesData(
                    bottomTitles: AxisTitles(
                        axisNameWidget: AppText(S.of(context).mhZ,
                            style: TextStyle(fontSize: 13,
                              color: AppColorConstants.colorH1,
                            )),
                        drawBelowEverything: true,
                        sideTitles: SideTitles(interval: 200,
                          showTitles: true,
                          getTitlesWidget: (value, meta) => Column(
                            children: [
                              Container(
                                height: getSize(5), // Adjust height as needed
                                width: getSize(1), // Adjust width as needed
                                color: AppColorConstants.colorH1,
                              ),
                              Text(
                                value.toStringAsFixed(0),
                                style: TextStyle(fontSize: 12, color: AppColorConstants.colorH1),
                              ),
                            ],
                          ),
                          reservedSize: getSize(40),
                        )),
                    rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                    topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                    leftTitles: AxisTitles(
                      axisNameWidget: AppText(S.of(context).dBmV,
                          style: TextStyle(fontSize: 13,
                            color: AppColorConstants.colorH1,
                          )),
                      sideTitles: SideTitles(
                        interval: 10,
                        showTitles: true,
                        getTitlesWidget: (value, meta) => AppText(
                          "$value",
                          style: TextStyle(fontSize: 12, color: AppColorConstants.colorH1),
                        ),
                        reservedSize: getSize(30),
                      ),
                    ),
                  ),
                )),
              ),
            ],
          ),
        ),
      ),
    );
  }
  Widget exportFileButtonView(){
    return  ElevatedButton(
      onPressed: (){
        exportToJsonFile();
      },
      style:  ElevatedButton.styleFrom(padding: const EdgeInsets.symmetric(vertical: 15,horizontal: 30),
        backgroundColor: AppColorConstants.colorLightBlue,
        foregroundColor: AppColorConstants.colorLightBlue,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: BorderSide.none
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.download,color: AppColorConstants.colorWhite,),
          const SizedBox(width: 8),
          AppText(isSelectableText: false,
            S.of(context).export,
            style : TextStyle(color: AppColorConstants.colorWhite),

            textAlign:TextAlign.center,
          ),
        ],
      ),
    );
  }


  Widget buildActionButtons() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 30),
      child: Wrap(
        // mainAxisAlignment: MainAxisAlignment.start,
        children: [
          AppButton(
            buttonWidth: 70,
            buttonRadius: 8,
            buttonHeight: 35,
            buttonName: S.of(context).apply,
            fontSize: 14,
            loadingStatus: isLoading ? ApiStatus.loading:ApiStatus.success,
            onPressed: isLoading ? null :() {
              setConfigFileDataUsingAPI();
            },
            fontFamily: AppAssetsConstants.openSans,
          ),
          const SizedBox(
            width: 24,
          ),
          AppButton(
            buttonWidth: 70,
            buttonRadius: 8,
            buttonHeight: 35,
            fontColor: AppColorConstants.colorBlack,
            buttonName: S.of(context).cancel,
            borderColor: Colors.grey.withOpacity(0.9),
            buttonColor: AppColorConstants.colorWhite1,
            fontSize: 14,
            onPressed: () {
              cancelChangesValue();
            },
            fontFamily: AppAssetsConstants.openSans,
          ),

        ],
      ),
    );
  }

  //-------------------------- ConfigPanel View-------------------------------------------
  Widget buildConfigPanels() {

    return Padding(
      padding: const EdgeInsets.only(top: 50),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            buildConfigPanel(S.of(context).autoConfig, [
              buildAutoConfigRow(
                S.of(context).start,
                  startFreqController,
                S.of(context).power,
                startPowerController,
                isLabel: true
              ),
              buildAutoConfigRow(
                S.of(context).end,
               endFreqController,
                S.of(context).power,
                endPowerController,
              ),
              buildAutoConfigRow(
                S.of(context).step,
                step1FreqController,
                S.of(context).power,
                step1PowerController,
              ),
              buildSingleConfigRow(S.of(context).virtual_tilt, TextEditingController(),text: virtualTilt, minWidth: 210,titleWidth: 120),
            ]),
            if (screenLayoutType == ScreenLayoutType.desktop) ...[
              const SizedBox(
                width: 20,
              ),
              buildConfigPanel(S.of(context).aLSCConfig, [
               if(buildPilotRows().isEmpty) buildEmptyView() else  Row(
                 crossAxisAlignment: CrossAxisAlignment.center,
                 mainAxisAlignment: MainAxisAlignment.center,
                 children: [
                   Column(
                     children: [
                       AppText(
                         "${S.of(context).main}\n(${S.of(context).mhZ})",
                         style: const TextStyle(fontWeight: FontWeight.w600),
                         textAlign: TextAlign.center,
                       ).paddingAll(10),
                       AppText("${S.of(context).backup}\n(${S.of(context).mhZ})",
                           style: const TextStyle(fontWeight: FontWeight.w600),
                           textAlign: TextAlign.center)
                           .paddingAll(12),
                     ],
                   ),
                   Row(
                     children: buildPilotRows(),
                   ),
                 ],
               ),
                const SizedBox(
                  height: 50,
                )
              ]),
              const SizedBox(
                width: 20,
              ),
              if(constraintsWidth > 1550)buildAutoAlignMarkerPilotsView(),
            ]
          ],
        ),
      ),
    );
  }
  List<Widget> buildPilotRows() {
    List<Widget> rows = [];
    List<dynamic> mainPilots = alignmentConfig.alscConfig?.mainPilots ?? [];
    List<dynamic> backupPilots = alignmentConfig.alscConfig?.backupPilots ?? [];
    int maxLength = mainPilots.length > backupPilots.length
        ? mainPilots.length
        : backupPilots.length;
    mainPilotControllers = List.generate(mainPilots.length, (index) {
      return TextEditingController(
          text: index < mainPilots.length ? mainPilots[index].toString() : '-'
      );
    });
    backupPilotControllers = List.generate(backupPilots.length, (index) {
      return TextEditingController(
          text: index < backupPilots.length ? backupPilots[index].toString() : '-'
      );
    });
    for (int i = 0; i < maxLength; i++) {
      rows.add(buildALSCConfigRow(
        i,
        mainPilotControllers[i],
        backupPilotControllers[i],
      ));
    }

    return rows;
  }

  Widget buildAutoAlignMarkerPilotsView(){
    return SingleChildScrollView(scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          buildConfigPanel(S.of(context).autoAlignMarkerPilots, [
            if(buildMakerPilotRows().isEmpty) buildEmptyView() else  Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                AppText(
                  "${S.of(context).main}\n(${S.of(context).mhZ})",
                  style: const TextStyle(fontWeight: FontWeight.w600),
                  textAlign: TextAlign.center,
                ).paddingAll(10),
                Row(mainAxisSize: MainAxisSize.min,
                  children: buildMakerPilotRows(),
                ),
              ],
            ),
          ]),
        ],
      ),
    );
  }

  List<Widget> buildMakerPilotRows() {
    List<Widget> rows = [];
    List<dynamic> markerPilots = alignmentConfig.autoConfig?.markerPilots ?? [];
    markerPilotControllers =  List.generate(markerPilots.length, (index) {
      return TextEditingController(
          text: index < markerPilots.length ? markerPilots[index].toString() : '-'
      );
    });
    for (int i = 0; i < markerPilots.length; i++) {

      rows.add(buildAutoAlignMakerPilotColumn(
        i + 1,
        markerPilotControllers[i],
      ));
    }

    return rows;
  }

  Widget buildEmptyView(){
    return Container(
      alignment: Alignment.center,
      height: 150,
      width: 300,
      child:  Column(mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AppImageAsset(color: AppColorConstants.colorChartLine,
            image: AppAssetsConstants.emptyLogo,
            height: 50,
          ),
          const SizedBox(height: 10,),
          AppText(S.of(context).noData,style:TextStyle(fontWeight:  FontWeight.w600),)
        ],
      ),
    );
}
  Widget buildConfigPanelsForNonDesktop() {
    if (screenLayoutType != ScreenLayoutType.desktop) {
      return Column(crossAxisAlignment: CrossAxisAlignment.start,children: [
        SizedBox(height: getSize(20)),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: buildConfigPanel(S.of(context).aLSCConfig, [
            if (buildPilotRows().isEmpty)
              buildEmptyView()
            else
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Column(
                    children: [
                      AppText(
                        "${S.of(context).main}\n(${S.of(context).mhZ})",
                        style: const TextStyle(fontWeight: FontWeight.w600),
                        textAlign: TextAlign.center,
                      ).paddingAll(10),
                      AppText("${S.of(context).backup}\n(${S.of(context).mhZ})",
                              style: const TextStyle(fontWeight: FontWeight.w600),
                              textAlign: TextAlign.center)
                          .paddingAll(12),
                    ],
                  ),
                  Row(
                    children: buildPilotRows(),
                  ),
                ],
              ),
            const SizedBox(
              height: 50,
            )
          ]),
        ),
      ]);
    }
    return Container();
  }

  Widget buildConfigPanel(String title, List<Widget> column) {
    return Container(padding:  EdgeInsets.symmetric(horizontal: screenLayoutType==ScreenLayoutType.mobile?getSize(10):getSize(20), vertical: getSize(10)),
      decoration:borderViewDecoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          AppText(
            title,
            style:  TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w700,
              color: AppColorConstants.colorLightBlue,
              fontFamily: AppAssetsConstants.openSans,
            ),
          ),
          const SizedBox(height: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: column,
          ),
        ],
      ),
    );
  }

  Widget buildAutoConfigRow(String label1, TextEditingController controller1, String label2, TextEditingController controller2,{bool isLabel=false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            if(isLabel) AppText("(${S.of(context).mhZ})",style: const TextStyle(
              color: AppColorConstants.colorDarkBlue,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),),
            buildSingleConfigRow(label1, controller1,titleWidth: 90,minWidth: 110,isEditable: false),
          ],
        ),
        const SizedBox(width: 60,),
        Column(crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            if(isLabel) AppText("(${S.of(context).dbMV})",style: const TextStyle(
              color: AppColorConstants.colorDarkBlue,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),),
            buildSingleConfigRowForBackup(label2, controller2,minWidth: 80,titleWidth: 60,isEditable: false),
          ],
        ),
      ],
    );
  }
  Widget buildALSCConfigRow( int  index , TextEditingController controller1,  TextEditingController controller2) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
         AppText("${S.of(context).freq} ${index+1}",style: const TextStyle(
          color: AppColorConstants.colorDarkBlue,
          fontSize: 14,
          fontWeight: FontWeight.w600,
        ),).paddingAll(8),
        Container(
          width: 80,
          height: 30,
          alignment: Alignment.center,
          padding: const EdgeInsets.symmetric(horizontal: 8), // only horizontal
          decoration: BoxDecoration(
            color: controller1.text == "0" ? AppColorConstants.colorH2 : AppColorConstants.colorWhite,
            border: Border.all(color: AppColorConstants.colorBlack),
            borderRadius: BorderRadius.circular(8),
          ),
          child: buildTextField(controller1,onChanged: (value) {
            final mainPilot = int.tryParse(value) ?? 0;
            exportAlignmentConfig.alscConfig?.mainPilots?[index]=mainPilot;
          },),
        ).paddingAll(8),
        Container(
          width: 80,
          height: 30,
          padding: const EdgeInsets.all(5),
          decoration: BoxDecoration(
            color: controller2.text == "0" ? AppColorConstants.colorH2 : AppColorConstants.colorWhite,
            border: Border.all(color:AppColorConstants.colorBlack),
            borderRadius: BorderRadius.circular(8),
          ),
          child: buildTextField(controller2,onChanged: (value) {
            final backupPilot = int.tryParse(value) ?? 0;
            exportAlignmentConfig.alscConfig?.backupPilots?[index]=backupPilot;
          },),
        ).paddingAll(8),
      ],
    );
  }

  Widget buildAutoAlignMakerPilotColumn(
    int index,
    TextEditingController controller,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        AppText(
          "${S.of(context).freq} $index",
          style: const TextStyle(
            color: AppColorConstants.colorDarkBlue,
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ).paddingAll(8),
        Container(
          width: 80,
          height: 30,
          padding: const EdgeInsets.all(5),
          decoration: BoxDecoration(
            color: controller.text == "0" ? AppColorConstants.colorH2 : AppColorConstants.colorWhite,
            border: Border.all(color:AppColorConstants.colorBlack),
            borderRadius: BorderRadius.circular(8),
          ),
          child: buildTextField(controller,isEditable: false),
        ).paddingAll(8),
      ],
    );
  }

  Widget buildFrequencyConfigView(){
    return Column(children: [
      SizedBox(height: 10,),
    if(screenLayoutType != ScreenLayoutType.mobile)  Row(
        children: [
          buildConfigPanel(S.of(context).frequencyConfig, [
            Wrap(children: [
              buildSingleConfigRow(S.of(context).diplexFilter, TextEditingController(),text: diplexLabel,titleWidth: 90,minWidth: 110),
              SizedBox(width: 50,),
              buildSingleConfigRow(S.of(context).openMode, TextEditingController(), text: openMode,titleWidth: 90,minWidth: 110)
            ],)
          ]),
        ],
      )else buildConfigPanel(S.of(context).frequencyConfig, [
      Wrap(children: [
        buildSingleConfigRow(S.of(context).diplexFilter, TextEditingController(),text: diplexLabel,titleWidth: 90,minWidth: 110),
        SizedBox(width: 50,),
        buildSingleConfigRow(S.of(context).openMode, TextEditingController(), text: openMode,titleWidth: 90,minWidth: 110)
      ],)
    ]),
    ],);
  }
  Widget buildSingleConfigRow(String label, TextEditingController controller , {double? minWidth,double? titleWidth , String ?text,  bool ?isEditable}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10).copyWith(right: 10), // Adjust vertical spacing if needed
      child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SizedBox(width: titleWidth ?? 60,
            child: AppText(
              label,
              style: const TextStyle(
                color: AppColorConstants.colorDarkBlue,
                fontSize: 14,
                fontFamily: AppAssetsConstants.openSans,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          SizedBox(width: (minWidth != null) ? minWidth / 4 : 0),
          Container(
            width: minWidth ?? 80,
            height: 30,
            alignment: Alignment.center,
            padding: const EdgeInsets.symmetric(horizontal: 8), // only horizontal
            decoration: BoxDecoration(
              color: text != null ? AppColorConstants.colorChart: controller.text == "0" ? AppColorConstants.colorH2 : AppColorConstants.colorWhite,
              border: Border.all(color: AppColorConstants.colorBlack),
              borderRadius: BorderRadius.circular(8),
            ),
            child: text != null
                ? AppText(
                    text,
                    style: const TextStyle(
                      color: AppColorConstants.colorDarkBlue,
                      fontSize: 14,
                      fontFamily: AppAssetsConstants.openSans,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.right,
                  )
                : buildTextField(controller,isEditable: isEditable),
          ),
        ],
      ),
    );
  }

  Widget buildSingleConfigRowForBackup(String label, TextEditingController controller , {double? minWidth,double? titleWidth, required bool isEditable}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10), // Adjust vertical spacing if needed
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SizedBox(width: titleWidth ??80,
            child: AppText(
              label,
              style: const TextStyle(
                color: AppColorConstants.colorDarkBlue,
                fontSize: 14,
                fontFamily: AppAssetsConstants.openSans,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          const SizedBox(width: 5),
          Container(
            width: minWidth ?? 80,
            height: 30,
            alignment: Alignment.center,
            padding: const EdgeInsets.symmetric(horizontal: 8), // only horizontal
            decoration: BoxDecoration(
              color: controller.text == "0" ? AppColorConstants.colorH2 : AppColorConstants.colorWhite,
              border: Border.all(color: AppColorConstants.colorBlack),
              borderRadius: BorderRadius.circular(8),
            ),
            child: buildTextField(controller,isEditable: isEditable),
          ),
        ],
      ),
    );
  }
  Widget buildTextField(TextEditingController controller, {bool ? isEditable,ValueChanged<String>? onChanged}) {
    return TextField(enabled: isEditable,
      controller: controller,
      textAlign: TextAlign.right,
      keyboardType: TextInputType.number,
      cursorHeight: 16,
      onChanged: onChanged,// Adjust to match font size visually
      style: const TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: AppColorConstants.colorDarkBlue,
        height: 1.0, // tightly controls line height
      ),
      decoration: const InputDecoration(
        isDense: true,
        border: InputBorder.none,
        contentPadding: EdgeInsets.symmetric(vertical: 6), // balances vertical space
      ),
    );
  }
  BoxDecoration borderViewDecoration = BoxDecoration(
    color:  AppColorConstants.colorWhite,
    border: Border.all(color: AppColorConstants.colorChart, width: 1.8),
    borderRadius: const BorderRadius.all(Radius.circular(8)),
  );
}