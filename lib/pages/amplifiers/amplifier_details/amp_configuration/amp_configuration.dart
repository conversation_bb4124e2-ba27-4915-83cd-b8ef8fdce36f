import 'package:quantumlink_node/app_import.dart';
import 'amp_ds_alignment/amp_ds_alignment.dart';
import 'amp_us_alignment/amp_us_alignment.dart';
import 'package:pub_semver/pub_semver.dart' as semver;

class AmpConfiguration extends StatefulWidget {
  final AmplifierDeviceItem amplifierItem;
  final AmplifierPageHelper ampPageHelper;

  const AmpConfiguration({super.key, required this.amplifierItem, required this.ampPageHelper});

  @override
  State<AmpConfiguration> createState() => _AmpConfigurationState();
}

class _AmpConfigurationState extends State<AmpConfiguration> {
  AmplifierController amplifierController =Get.put<AmplifierController>(AmplifierController());
  late AmplifierDeviceItem ampItem;
  List configurationList = [];
  String selectedOption = "";


  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    ampItem = widget.amplifierItem;
    String ampVersion= ampItem.ampDeviceSummary.result.versionInfo.fwVersion ?? "0.0.0";
    ampItem.isShowManualConfiguration = isBetterAmpsFWVersion(ampVersion);
    ampItem.isShowAlignmentConfiguration = isBetterAmpsFWVersion(ampVersion,verifyStableVersion: AppStringConstants.betterAlignmentConfigVersion);
  }

  initializeListValue() {
    configurationList = [
      S.of(context).dsAlignCfg,
      S.of(context).usAlignCfg,
      if (ampItem.isShowAlignmentConfiguration) S.of(context).alignmentConfiguration
    ];
    selectedOption = configurationList[0];
    if (selectedOption == S.of(context).dsAlignCfg) {
      widget.ampPageHelper.isRevertDSAutoAlignmentConfiguration = true;
    }
  }




  @override
  Widget build(BuildContext context) {
    if(configurationList.isEmpty){
      initializeListValue();
    }
    return GetBuilder<AmplifierController>(
      init: AmplifierController(),
      builder: (AmplifierController controller) {
        return ScreenLayoutTypeBuilder(
          builder: (context, screenType, constraints) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SizedBox(height: getSize(10)),
                 SizedBox(width: screenType != ScreenLayoutType.mobile ? 300 :250,
                  child: CommonDropdownButton(iconColor:  AppColorConstants.colorH3,
                    selectedValue: selectedOption,fontSize:getSize(14) ,
                    buttonHeight: getSize(35),
                    hintText: '',
                    items: configurationList,
                    onChanged: (value) async{
                      int deviceListIndex = widget.ampPageHelper.amplifierDeviceList.result
                          .indexWhere((tab) => tab.deviceEui == widget.amplifierItem.deviceEui);

                      if(deviceListIndex==-1){
                        return;
                      }
                      if (widget.ampPageHelper.getConfigurationMap(deviceListIndex) != null) {
                        bool isSuccess = await widget.ampPageHelper.checkConfigurationMap(
                            context, deviceListIndex);
                        if (isSuccess) {
                          selectedOption = value;
                          if(selectedOption==S.of(context).dsAlignCfg){
                            widget.ampPageHelper.isRevertDSAutoAlignmentConfiguration=true;
                          }else{
                            widget.ampPageHelper.isRevertDSAutoAlignmentConfiguration=false;
                          }
                          amplifierController.update();
                        }else{
                          return;
                        }
                      } else {
                        selectedOption = value;
                        if(selectedOption==S.of(context).dsAlignCfg){
                          widget.ampPageHelper.isRevertDSAutoAlignmentConfiguration=true;
                        }else{
                          widget.ampPageHelper.isRevertDSAutoAlignmentConfiguration=false;
                        }
                        amplifierController.update();
                        return;
                      }
                    },
                  ),
                ).paddingOnly(left: 10),
                if (selectedOption == S.of(context).dsAlignCfg)
                  AmpDsAlignment(amplifierItem: ampItem, ampPageHelper: widget.ampPageHelper,selectedOption: selectedOption, ),
                if (selectedOption == S.of(context).usAlignCfg)
                  AmpUsAlignment(amplifierItem: ampItem, ampPageHelper: widget.ampPageHelper),
                if (selectedOption == S.of(context).alignmentConfiguration)
                AlignmentConfiguration(amplifierItem: ampItem, ampPageHelper: widget.ampPageHelper),
              ],
            );
          },
        );
      },
    );
  }

}