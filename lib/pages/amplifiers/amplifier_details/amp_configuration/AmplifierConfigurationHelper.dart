import '../../../../app_import.dart';

class AmplifierConfigurationHelper {
  final AmplifierPageHelper amplifierPageHelper;
  AmplifierController amplifierController =Get.put<AmplifierController>(AmplifierController());

  DsManualAlignmentItem dsManualAlignmentItem = DsManualAlignmentItem.empty();
  DsAutoAlignmentModel dsAutoAlignmentModel = DsAutoAlignmentModel.empty();
  AlignmentSettingModel dsAlignmentSettingModel = AlignmentSettingModel.empty();

  DsManualAlignmentItem usManualAlignmentItem = DsManualAlignmentItem.empty();
  DsAutoAlignmentModel usAutoAlignmentModel = DsAutoAlignmentModel.empty();
  AlignmentSettingModel usAlignmentSettingModel = AlignmentSettingModel.empty();

  String? dsSpectrumDataError;
  List<DSManualValues> dsManualValuesList = [];
  List<DSPointData> dsSpectrumDataPoints = [];
  ApiStatus spectrumApiStatus = ApiStatus.initial;
  RxBool isSelected = false.obs;
  DateTime? dsSpectrumOnTapTime;
  Duration ? dsSpectrumDifferenceTime;
  bool dsSpectrumIsShowText = true;
  Timer? dsSpectrumRefreshTimer;
  //Manual Alignment
  String? manualAlignmentError;
  DateTime? manualAlignmentUpdateTime;
  DateTime? onTapTimeOfManualAlignment;
  Duration ? differenceTimeOfManualAlignment;
  bool isShowTextOfManualAlignment = true;
  Timer? refreshTimerOfManualAlignment;
  ApiStatus manualAlignmentApiStatus = ApiStatus.initial;
  ApiStatus autoAlignmentApiStatus = ApiStatus.initial;
  bool isDSSwitchOfAuto = true;
  bool isUsSwitchOfAuto = true;


  //Save/Revert API Call
  Rx<ApiStatus> saveRevertApiStatus = ApiStatus.initial.obs;
  Rx<ApiStatus> saveRevertApiStatusOfAutoAlign = ApiStatus.initial.obs;

  AmplifierConfigurationHelper({required this.amplifierPageHelper});

  void initializeManualConfiguration() {
    amplifierPageHelper.dsConfigurationDraftMessage="";
    amplifierPageHelper.usConfigurationDraftMessage="";
    manualAlignmentError = null;
    manualAlignmentUpdateTime=null;
    onTapTimeOfManualAlignment = DateTime.now();
    differenceTimeOfManualAlignment = null;
    isShowTextOfManualAlignment = true;
    refreshTimerOfManualAlignment?.cancel();
  }

  void dsSpectrumInitializeTimer(AmplifierDeviceItem amplifierItem) {
    amplifierItem.dsSpectrumUpdateTime == null;
    dsSpectrumDifferenceTime = null;
    dsSpectrumOnTapTime = DateTime.now();
    dsSpectrumIsShowText = true;
    dsSpectrumRefreshTimer?.cancel();
  }
  dsSpectrumGetDifferenceTime() {
    dsSpectrumDifferenceTime = DateTime.now().difference(dsSpectrumOnTapTime!);
    dsSpectrumRefreshTimer=Timer(const Duration(seconds: 3), () {
      dsSpectrumIsShowText = false;
      amplifierController.update();
    });
  }


  Future<dynamic> getDsAutoAlignmentSpectrumData(
      BuildContext context, String deviceEui, AmplifierDeviceItem amplifierItem,
      {bool isRefresh = false}) async {
    dsSpectrumInitializeTimer(amplifierItem);
    dsSpectrumDataError = null;
    amplifierItem.downStreamAutoAlignmentError= null;
    spectrumApiStatus = ApiStatus.loading;
    amplifierController.update();
    try {
      await amplifierController
          .dsAutoAlignmentSpectrumData(deviceEui: deviceEui, context: context,isRefresh: isRefresh)
          .then((value) {
        if (value['body'] is DSAutoAlignSpectrum) {
          if (value['body'].result != null) {
            spectrumApiStatus = ApiStatus.success;
            DSAutoAlignSpectrumItem dsAutoAlignSpectrumItem = value['body'].result;
            dsSpectrumDataPoints = parseSpectrumData(dsAutoAlignSpectrumItem);
          } else {
            spectrumApiStatus = ApiStatus.failed;
            dsSpectrumDataError=S.of(context).socketExceptionMessage;
            return;
          }
        } else {
          spectrumApiStatus = ApiStatus.failed;
          dsSpectrumDataError = value['body']['detail'];
        }
        amplifierItem.dsSpectrumUpdateTime = getLastUpdateTime(value['headers']['updated_at']) ?? DateTime.now();
      });
    } catch (e) {
      debugLogs("getDsAutoAlignmentSpectrumData error--> $e");
      spectrumApiStatus = ApiStatus.failed;
      dsSpectrumDataError=S.of(context).socketExceptionMessage;
    } finally {
      dsSpectrumGetDifferenceTime();
      amplifierController.update();
    }
  }

  void addAutoAlignWritten(int index) {
    final listTabs = amplifierPageHelper.listTabs;
    var key = {
      listTabs[index].ampDeviceItem.mapCtrlStage.keys.first:
          listTabs[index].ampDeviceItem.mapCtrlStage.keys.first
    };
    var map = listTabs[index].ampDeviceItem.mapWrittenCtrlDSSpectrum;
    if (!map.containsKey(key)) {
      map[key] = 1;
    }
  }

  void checkAndWriteDirtyFlag({
    required List<DSManualValues> values,
    required int index,
    required bool isDS,
    required bool isFormAPI,
  }) {
    if (isDS && isDSSwitchOfAuto) return;
    if (!isDS && isUsSwitchOfAuto) return;
    final anyDirty = values.where((item) => item.stage != 9).any((item) => item.isDirty);
    if (anyDirty) {
      if (isDS) {
        addMapWritten(true, index);
        amplifierPageHelper.dsConfigurationDraftMessage= S.of(amplifierPageHelper.state.context).draftMessageForConfiguration;
      } else {
        addMapWritten(false, index);
        amplifierPageHelper.usConfigurationDraftMessage= S.of(amplifierPageHelper.state.context).draftMessageForConfiguration;
      }
    } else {
      if(isDS && isFormAPI ) isDSSwitchOfAuto = true;
      if(!isDS && isFormAPI) isUsSwitchOfAuto = true;
      clearMapWritten(isDS ? true : false, index);
      amplifierPageHelper.dsConfigurationDraftMessage="";
      amplifierPageHelper.usConfigurationDraftMessage="";
    }
    amplifierController.update();
  }

  void addMapWritten(bool isDS,int index) {
    final listTabs = amplifierPageHelper.listTabs;
    var key = {
      listTabs[index].ampDeviceItem.mapCtrlStage.keys.first : listTabs[index].ampDeviceItem.mapCtrlStage.keys.first
    };
    var map = (isDS) ?   listTabs[index].ampDeviceItem.mapWrittenCtrlDS :   listTabs[index].ampDeviceItem.mapWrittenCtrlUS;
    if (!map.containsKey(key)) {
      map[key] = 1;
    }
  }

  void clearMapWritten(bool isDS, int index) {
    final listTabs = amplifierPageHelper.listTabs;
    isDS? listTabs[index].ampDeviceItem.mapWrittenCtrlDS.clear():listTabs[index].ampDeviceItem.mapWrittenCtrlUS.clear();
  }

  updateSetAPIValue(DsManualAlignmentItem dsManualAlignmentItem, int refreshIndex){
    final ampItem = amplifierPageHelper.listTabs[refreshIndex].ampDeviceItem;
    int index = dsManualAlignmentItem.dsValues.indexWhere((element) => (element.ctrlType == ampItem.mapCtrlStage.keys.first && element.stage == ampItem.mapCtrlStage.values.first));
    if (index != -1) {
      //dsManualAlignmentItem.dsValues[index].isProgressing.value = false;
      dsManualAlignmentItem.dsValues[index].isSelected = false;
    }
  }
  List<DSPointData> parseSpectrumData(DSAutoAlignSpectrumItem? data) {
    if (data == null) return [];
    return (data.dsSpectrumData)
        .map((item) => DSPointData(
      freq: item.frequency.toDouble(),
      level: item.level.toDouble() / 100,
      reference: item.reference.toDouble() / 100,
    ))
        .toList();
  }


  //GET DS Manual ALIGNMENT
  Future<dynamic> getDsManualAlignment(
      BuildContext context, String deviceEui,int index,{bool isFromSetAPI = false, bool isRefreshDSSpectrum = false}) async {
    try {
      if(manualAlignmentApiStatus == ApiStatus.loading) return;
      manualAlignmentApiStatus = ApiStatus.loading;
      initializeManualConfiguration();
      amplifierController.update();
      await amplifierController
          .dsManualAlignment(deviceEui: deviceEui, context: context)
          .then((value) async {
        print("---Value===${value.toString()}");
        if (value['body'] is DsManualAlignmentModel) {
          if (value['body'].result != null) {
            dsManualAlignmentItem = value['body'].result;
            if (dsManualAlignmentItem.dsValues.isNotEmpty) {
              checkAndWriteDirtyFlag(values: dsManualAlignmentItem.dsValues ,index: index,isDS: true,isFormAPI: true);
              if (isFromSetAPI) updateSetAPIValue(dsManualAlignmentItem, index);
              if (isRefreshDSSpectrum) {
                 getDsAutoAlignmentSpectrumData(
                    context, deviceEui, amplifierPageHelper.listTabs[index].ampDeviceItem,
                    isRefresh: true);
              }
            }
            dsAlignmentSettingModel = getInitializeDBValue(
                alignmentSettingModel: dsAlignmentSettingModel,
                manualAlignmentItem: dsManualAlignmentItem);
            manualAlignmentApiStatus = ApiStatus.success;
          } else {
            manualAlignmentApiStatus = ApiStatus.failed;
            manualAlignmentError = S.of(context).socketExceptionMessage;
          }
        } else {
          manualAlignmentApiStatus = ApiStatus.failed;
          manualAlignmentError = value['body']['detail'];
        }
        manualAlignmentUpdateTime = getLastUpdateTime(value['headers']['updated_at']) ?? DateTime.now();
      });
    } catch (e) {
      debugLogs("getDsManualAlignment error--> $e");
      manualAlignmentApiStatus = ApiStatus.failed;
      manualAlignmentError = S.of(context).socketExceptionMessage;
    } finally {
      dsManualAlignmentItem.isProgressing.value = false;
      differenceTimeOfManualAlignment = DateTime.now().difference( onTapTimeOfManualAlignment!);
      refreshTimerOfManualAlignment = Timer(const Duration(seconds: 3), () {
        isShowTextOfManualAlignment = false;
        amplifierController.update();
      });
      amplifierController.update();
    }
  }

  //GET US Manual ALIGNMENT
  Future<dynamic> getUsManualAlignment(
      BuildContext context, String deviceEui,int index,{bool isFromSetAPI = false}) async {
    try {
      if(manualAlignmentApiStatus == ApiStatus.loading) return;
      manualAlignmentApiStatus = ApiStatus.loading;
      initializeManualConfiguration();
      amplifierController.update();
      await amplifierController
          .usManualAlignment(deviceEui: deviceEui, context: context)
          .then((value) {
        if (value['body'] is DsManualAlignmentModel) {
          if (value['body'].result != null) {
            usManualAlignmentItem = value['body'].result;
            usAlignmentSettingModel = getInitializeDBValue(
                alignmentSettingModel: usAlignmentSettingModel,
                manualAlignmentItem: usManualAlignmentItem);
            if (usManualAlignmentItem.dsValues.isNotEmpty) {
              checkAndWriteDirtyFlag(values: usManualAlignmentItem.dsValues, index: index,isDS: false,isFormAPI: true);
              if(isFromSetAPI) updateSetAPIValue(usManualAlignmentItem,index);
            }
            manualAlignmentApiStatus = ApiStatus.success;
          } else {
            manualAlignmentApiStatus = ApiStatus.failed;
            manualAlignmentError = S.of(context).socketExceptionMessage;
          }
        } else {
          manualAlignmentApiStatus = ApiStatus.failed;
          manualAlignmentError = value['body']['detail'];
        }
        manualAlignmentUpdateTime =  getLastUpdateTime(value['headers']['updated_at']) ?? DateTime.now();
      });
    } catch (ex) {
      manualAlignmentError = S.of(context).socketExceptionMessage;
      manualAlignmentApiStatus = ApiStatus.failed;
    } finally {
      usManualAlignmentItem.isProgressing.value = false;
      differenceTimeOfManualAlignment = DateTime.now().difference( onTapTimeOfManualAlignment!);
      refreshTimerOfManualAlignment = Timer(const Duration(seconds: 3), () {
        isShowTextOfManualAlignment = false;
        amplifierController.update();
      });
      amplifierController.update();
    }
  }

  AlignmentSettingModel getInitializeDBValue({
    required AlignmentSettingModel alignmentSettingModel,
    required DsManualAlignmentItem manualAlignmentItem,
  }) {
    // Get gain and tilt values from the manual alignment item
    alignmentSettingModel
      ..gainDbValues = _getInitialManualValue(isGainDB: true, manualAlignmentItem: manualAlignmentItem)
      ..tiltDbValues = _getInitialManualValue(isGainDB: false, manualAlignmentItem: manualAlignmentItem)
      ..gainValue.value = alignmentSettingModel.gainDbValues.value / 10
      ..tiltValue.value = alignmentSettingModel.tiltDbValues.value / 10
      ..gainTextController.text = alignmentSettingModel.gainValue.value.toStringAsFixed(1)
      ..tiltTextController.text = alignmentSettingModel.tiltValue.value.toStringAsFixed(1);

    return alignmentSettingModel;
  }

  DSManualValues _getInitialManualValue({
    required bool isGainDB,
    required DsManualAlignmentItem manualAlignmentItem,
  }) {
    return manualAlignmentItem.dsValues.firstWhere(
          (element) => element.stage == 9 && element.ctrlType == (isGainDB ? 1 : 2),
      orElse: DSManualValues.empty,
    );
  }

  //SET DS Manual ALIGNMENT
  Future<dynamic> setDsManualAlignment(BuildContext context, String deviceEui,
      DsManualAlignmentItem dsManualItem,int index) async {
    try {
      await amplifierController
          .setDsManualAlignment(
              dsManualAlignmentItem: dsManualItem,
              deviceEui: deviceEui,
              context: context)
          .then((value)  {
        if (value['body'] is DsManualAlignmentModel) {
          if (value['body'].result != null) {
            dsManualAlignmentItem.dsValues = value['body'].result.dsValues;
            manualAlignmentApiStatus = ApiStatus.success;
          } else {
            displayToastNotificationSetManualAlignment(context,true,dsManualAlignmentItem,false,index);
          }
        }else{
          displayToastNotificationSetManualAlignment(context,true,dsManualAlignmentItem,false,index);
        }
      });
    } catch (ex) {
      displayToastNotificationSetManualAlignment(context,true,dsManualAlignmentItem,false,index);
    } finally {
      amplifierController.update();
    }
  }

  //SET US Manual ALIGNMENT
  Future<dynamic> setUsManualAlignment(BuildContext context, String deviceEui,
      DsManualAlignmentItem usManualItem,int index) async {
    try {
      await amplifierController
          .setUsManualAlignment(
              dsManualAlignmentItem: usManualItem,
              deviceEui: deviceEui,
              context: context)
          .then((value) async {
        if (value['body'] is DsManualAlignmentModel) {
          if (value['body'].result != null) {
            usManualAlignmentItem.dsValues = value['body'].result.dsValues;
            manualAlignmentApiStatus = ApiStatus.success;
          } else {
            displayToastNotificationSetManualAlignment(context,false,usManualAlignmentItem,false,index);
          }
        }else{
          displayToastNotificationSetManualAlignment(context,false,usManualAlignmentItem,false,index);
        }
      });
    } catch (ex) {
      displayToastNotificationSetManualAlignment(context,false,usManualAlignmentItem,false,index);
    } finally {
      amplifierController.update();
    }
  }

  displayToastNotificationSetManualAlignment(
      context, bool isDsAlignment,DsManualAlignmentItem dsManualAlignmentItem, bool isSuccess,int index) {
    String msg = "";
    debugLogs("isSuccess ===>> $isSuccess");
    if (isDsAlignment)
      msg = isSuccess
          ? S.of(context).setDsAlignmentCompleted
          : S.of(context).setDsAlignmentFailed;
    else
      msg = isSuccess
          ? S.of(context).setUsAlignmentCompleted
          : S.of(context).setUsAlignmentFailed;

    if (isSuccess)
      msg.showSuccess(context);
    else
      msg.showError(context);

    updateSetAPIValue(dsManualAlignmentItem,index);
  }

  //Save/Revert DS Manual ALIGNMENT
  Future<dynamic> saveRevertDsManualAlignment(BuildContext context, String deviceEui,
      DsManualAlignmentItem dsManualItem, bool isSave,int index) async {
    try {
      saveRevertApiStatus.value = ApiStatus.loading;
      await amplifierController
          .saveRevertDsManualAlignment(
          dsManualAlignmentItem: dsManualItem,
          deviceEui: deviceEui,
          context: context)
          .then((value) async {
        if (value['body'].result != null) {
          manualAlignmentApiStatus = ApiStatus.success;
           clearMapWritten(true,index);
           await Future.delayed(Duration(seconds: 3));
           getDsManualAlignment(context, deviceEui,index,isFromSetAPI: true, isRefreshDSSpectrum: true);
        } else {
          displayToastNotification(context,true,isSave,false);
          return;
        }
      });
    } catch (ex) {
      debugLogs("Save Revert Error : ${ex.toString()}");
      displayToastNotification(context,true,isSave,false);
    } finally {
      saveRevertApiStatus.value = ApiStatus.success;
      amplifierController.update();
    }
  }

  //Save Revert US Manual ALIGNMENT
  Future<dynamic> saveRevertUsManualAlignment(BuildContext context, String deviceEui,
      DsManualAlignmentItem usManualItem, bool isSave,int index) async {
    try {
      saveRevertApiStatus.value = ApiStatus.loading;
      await amplifierController
          .saveRevertUsManualAlignment(
              dsManualAlignmentItem: usManualItem,
              deviceEui: deviceEui,
              context: context)
          .then((value) async {
        if (value['body'].result != null) {
          manualAlignmentApiStatus = ApiStatus.success;
          clearMapWritten(false,index);
          await Future.delayed(Duration(seconds: 3));
          getUsManualAlignment(context, deviceEui,index,isFromSetAPI: true);
        } else {
            displayToastNotification(context,false,isSave,false);
            return;
        }
      });
    } catch (ex) {
      displayToastNotification(context,false,isSave,false);
    } finally {
      saveRevertApiStatus.value = ApiStatus.success;
      amplifierController.update();
    }
  }
  //Save/Revert DS AUTO ALIGNMENT
  Future<dynamic> saveRevertDsAutoAlignment(BuildContext context, String deviceEui, bool isSave) async {
    try {
      saveRevertApiStatusOfAutoAlign.value = ApiStatus.loading;
      final ampPageHelper = amplifierPageHelper;
      final index = ampPageHelper.listTabs
          .indexWhere((tab) => tab.title == ampPageHelper.amplifierItem.deviceEui);
      await amplifierController
          .saveRevertDsAutoAlignment(
          isSave:isSave,
          deviceEui: deviceEui,
          context: context)
          .then((value) async {
        if (value['body'].result != null) {
          debugLogs("Save Revert Result : ${value['body'].result}");
          amplifierPageHelper.listTabs[index].ampDeviceItem.mapWrittenCtrlDSSpectrum.clear();
          await getDsAutoAlignmentSpectrumData(context, deviceEui, ampPageHelper.amplifierItem,
              isRefresh: true);
          autoAlignmentApiStatus = ApiStatus.success;
          dsAutoAlignmentModel.result?.dsValues = value['body'].result.dsValues;
        } else {
          displayToastNotification(context,true,isSave,false);
          return;
        }
      });
    } catch (ex) {
      displayToastNotification(context,true,isSave,false);
    } finally {
      saveRevertApiStatusOfAutoAlign.value = ApiStatus.success;
      amplifierController.update();
    }
  }

  //Save/Revert US AUTO ALIGNMENT
  Future<dynamic> saveRevertUsAutoAlignment(BuildContext context, String deviceEui,bool isSave) async {
    try {
      saveRevertApiStatus.value = ApiStatus.loading;
      final ampPageHelper = amplifierPageHelper;
      final index = ampPageHelper.listTabs
          .indexWhere((tab) => tab.title == ampPageHelper.amplifierItem.deviceEui);
      await amplifierController
          .saveRevertUsAutoAlignment(
          isSave: isSave,
          deviceEui: deviceEui,
          context: context)
          .then((value) async {
        if (value['body'].result != null) {
          manualAlignmentApiStatus = ApiStatus.success;
          usAutoAlignmentModel.result?.dsValues = value['body'].result.dsValues;
          await checkDSAndUsAlignment(context, deviceEui: deviceEui, tabIndex: index);
          await getUsManualAlignment(context, ampPageHelper.amplifierItem.deviceEui, index);
        } else {
          displayToastNotification(context,false,isSave,false);
          return;
        }
      });
    } catch (ex) {
      displayToastNotification(context,false,isSave,false);
    } finally {
      saveRevertApiStatus.value = ApiStatus.success;
      amplifierController.update();
    }
  }

  Future<void> checkDSAndUsAlignment(context, {required String deviceEui, required int tabIndex}) async {
    manualAlignmentApiStatus = ApiStatus.loading;
    isShowTextOfManualAlignment =true;
    onTapTimeOfManualAlignment = DateTime.now();
    amplifierController.update();
    try {
      final value = await amplifierController.getDeviceSummary(
        deviceEui: deviceEui,
        context: context,
        isRefresh: true,
        bitMask: amplifierPageHelper.staticBitmaskList[4],
      );
      if (value['body'] != null && value['body'] is AmpDeviceSummary) {
        AmpDeviceSummary ampDeviceSummary = value['body'];
        ConfigStatus configStatus = ampDeviceSummary.result.configStatus;
        List<String> configList = configStatus.getConfigStatusList;
        bool isDoneUSAndDsAligment =
            !(configList.contains("DS ALIGNMENT") && configList.contains("US ALIGNMENT"));
        if (isDoneUSAndDsAligment) {
         await setAlscConfig(context,deviceEui: deviceEui);
        }
      } else {
        String errorMessage = value['body'];
        errorMessage.showError(context);
      }
    } catch (e) {
      debugLogs("checkDSAndUsAlignment-->$e");
      S.of(context).somethingWentWrong.showError(context);
    }finally{
      manualAlignmentApiStatus = ApiStatus.success;
      amplifierController.update();
    }
  }

  Future<void> setAlscConfig(context, {required String deviceEui}) async {
    try {
      final Map<String, dynamic>? responseData =
          await amplifierController.setAlscConfigData(deviceEui: deviceEui, context: context);
      if (responseData != null) {
        if (responseData['message'] != null) {
          String successMessage =responseData['message'];
            successMessage.showSuccess(context);
        } else {
          String errorMessage = responseData['detail'];
          errorMessage.showError(context);
        }
      } else {
        S.of(context).somethingWentWrong.showError(context);
      }
    } catch (e) {
      debugLogs("setAlscConfig-->$e");
      S.of(context).somethingWentWrong.showError(context);
    }
  }

  displayToastNotification(
      context, bool isDsAlignment, bool isSave, bool isSuccess) {
    String msg = "";
    if (isDsAlignment)
      if(isSuccess)
        msg = isSave
            ? S.of(context).saveDsAlignmentCompleted
            : S.of(context).revertDsAlignmentCompleted;
      else
        msg = isSave
            ? S.of(context).saveDsAlignmentFailed
            : S.of(context).revertDsAlignmentFailed;
    else
      if(isSuccess)
        msg = isSave
            ? S.of(context).saveUsAlignmentCompleted
            : S.of(context).revertUsAlignmentCompleted;
      else
        msg = isSave
            ? S.of(context).saveUsAlignmentFailed
            : S.of(context).revertUsAlignmentFailed;

    if (isSuccess)
      msg.showSuccess(context);
    else
      msg.showError(context);
  }

}
