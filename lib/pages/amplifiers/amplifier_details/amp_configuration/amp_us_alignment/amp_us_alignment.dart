// ignore_for_file: deprecated_member_use

import 'package:flutter/cupertino.dart';
import 'package:quantumlink_node/app/ui/manual_alignment_page.dart';
import 'package:quantumlink_node/app_import.dart';

import '../../../../../utils/dialog_utils.dart';
import '../AmplifierConfigurationHelper.dart';


class AmpUsAlignment extends StatefulWidget {
  final AmplifierDeviceItem amplifierItem;
  final AmplifierPageHelper ampPageHelper;

  const AmpUsAlignment({super.key, required this.amplifierItem, required this.ampPageHelper});

  @override
  State<AmpUsAlignment> createState() => _AmpUsAlignmentState();
}

class _AmpUsAlignmentState extends State<AmpUsAlignment> {
 late AmplifierConfigurationHelper amplifierConfigurationHelper;
  AmplifierController amplifierController =Get.put<AmplifierController>(AmplifierController());
  String? selectedInputTestValue;
  String? selectedOutputTestValue;
  int upGain = 0;
  int upSlope = 0;
  bool isStartUpStream = false;
  double constraintsWidth = 0.0;
  late AmplifierDeviceItem ampItem;
  DateTime? onTapTime;
  Duration ? differenceTime;
  bool isShowText = true;
  Timer? refreshTimer;

  double aTTN1Value = 0;
  double eQ1Value = 0;
  double aTTN2Value = 0;
  late ScreenLayoutType screenLayoutType;
  @override
  void initState() {
    super.initState();
    amplifierConfigurationHelper = AmplifierConfigurationHelper(amplifierPageHelper : widget.ampPageHelper);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      int refreshIndex = widget.ampPageHelper.listTabs.indexWhere((tab) => tab.title == widget.amplifierItem.deviceEui);
      amplifierConfigurationHelper.getUsManualAlignment(context,widget.amplifierItem.deviceEui,refreshIndex);
    });
    ampItem = widget.amplifierItem;
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AmplifierController>(
      init: AmplifierController(),
      builder: (AmplifierController controller) {
        return ScreenLayoutTypeBuilder(
          builder: (context, screenType, constraints) {
            constraintsWidth = constraints.maxWidth;
            screenLayoutType = screenType;
            return Padding(
              padding: EdgeInsets.symmetric(horizontal: getSize(8.0), vertical: getSize(15)),
              child: MergeSemantics(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    //upStreamView(ampItem.ampUpStreamItem, title: S.of(context).upstream, controller: controller),
                    //SizedBox(height: getSize(20)),
                    buildTitleView(),
                    // buildButtonsView(ampItem.ampUpStreamItem,  controller),
                    // if (screenLayoutType == ScreenLayoutType.mobile) buildSwitchButtonView(),
                    usManualAlignmentDraftMessageView(),
                    ampInterstageValuesView(),
                    SizedBox(height: getSize(25)),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget buildTitleView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Row(
          children: [
            AppText(
              S.of(context).usAlignCfg,
              style: TextStyle(
                  fontSize: getSize(24),
                  fontFamily: AppAssetsConstants.openSans,
                  color: AppColorConstants.colorPrimary,
                  fontWeight: FontWeight.w600),
            ),
            SizedBox(width: getSize(10)),
            Flexible(
              child: Container(
                  height: getSize(20),
                  decoration: BoxDecoration(
                      border: Border(
                          bottom: BorderSide(color: AppColorConstants.colorBlack, width: 0.4)))),
            ),
           // if (screenLayoutType == ScreenLayoutType.desktop) uSRefreshButtonView()
          ],
        ),
        SizedBox(height: getSize(10)),
        startAutoAlignmentWidget()
        // if (screenLayoutType != ScreenLayoutType.desktop) uSRefreshButtonView(),
      ],
    );
  }
  Widget startAutoAlignmentWidget(){
    return Column(
      crossAxisAlignment:  CrossAxisAlignment.end,
      children: [
        AppButton(
          buttonRadius: 9,
          loadingStatus: isStartUpStream ? ApiStatus.loading : ApiStatus.success,
          buttonHeight: getSize(35),
          buttonWidth: getSize(220),
          fontColor: amplifierConfigurationHelper.isUsSwitchOfAuto &&
              getDetectedStatusType(ampItem.status) == DetectedStatusType.online
              ? AppColorConstants.colorWhite
              : AppColorConstants.colorH1Grey,
          borderColor: amplifierConfigurationHelper.isUsSwitchOfAuto &&
              getDetectedStatusType(ampItem.status) == DetectedStatusType.online
              ? AppColorConstants.colorLightBlue.withOpacity(0.5)
              : AppColorConstants.colorH1.withOpacity(0.5),
          buttonName: S.of(context).startAutoAlignment,
          onPressed: getDetectedStatusType(ampItem.status) == DetectedStatusType.online ?() async {
            if (amplifierConfigurationHelper.isUsSwitchOfAuto&&!isStartUpStream) {
              DialogUtils().confirmationDialog(
                  context,
                  S.of(context).msgAskConfirmationTitle,
                  S.of(context).msgAskConfirmationAutoAlign,
                  S.of(context).yes,
                  S.of(context).no, () async {
                goBack();
                await getUsAlignment(context, ampItem.deviceEui).then((value) {},);
              },
                  () => goBack(),
              );
            }
          } : null,
          buttonColor: isStartUpStream
              ? AppColorConstants.colorLightBlue.withOpacity(0.6)
              : amplifierConfigurationHelper.isUsSwitchOfAuto &&
              getDetectedStatusType(ampItem.status) == DetectedStatusType.online
              ? AppColorConstants.colorLightBlue
              : AppColorConstants.colorBackgroundDark,
          fontSize: getSize(16),
        ),
        if (ampItem.upStreamAutoAlignmentError != null)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5),
            child: CustomPaint(
              painter: DottedBorderPainter(
                borderColor: AppColorConstants.colorRedLight.withOpacity(0.8),
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 8.0),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.error_outline, color: AppColorConstants.colorRedLight, size: 15),
                    const SizedBox(width: 5),
                    Flexible(
                      child: AppText(
                        "${ampItem.upStreamAutoAlignmentError}",
                        style: TextStyle(
                          color: AppColorConstants.colorDarkBlue,
                          fontSize: 12,
                          fontFamily: AppAssetsConstants.openSans,
                          fontWeight: getMediumFontWeight(),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          )
        else
          Container(height: widget.ampPageHelper.usConfigurationDraftMessage.isNotEmpty ? 10 : 35),
      ],
    );
  }
  Widget uSRefreshButtonView() {
    return Row(
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 25.0,bottom: 10),
          child: AppButton(
            buttonRadius: 9,
            loadingStatus: ampItem.configRefreshStatus,
            buttonHeight: getSize(35),
            buttonWidth: getSize(80),
            fontColor: AppColorConstants.colorWhite,
            borderColor: AppColorConstants.colorLightBlue.withOpacity(0.5),
            buttonName: S.of(context).refresh,
            onPressed: () async {
              int refreshIndex =
              widget.ampPageHelper.listTabs.indexWhere((tab) => tab.title == ampItem.deviceEui);
              if (ampItem.configRefreshStatus == ApiStatus.success) {
               // widget.ampPageHelper.getIngressSwitchData(context, ampItem.deviceEui, refreshIndex);
                amplifierController.update();
              }
            },
            buttonColor: AppColorConstants.colorLightBlue,
            fontSize: getSize(15),
          ),
        ),
      ],
    );
  }

  //GET US AUTO ALIGNMENT
  Future<dynamic> getUsAlignment(BuildContext context, String deviceEui) async {
    isStartUpStream = true;
    ampItem.upStreamAutoAlignmentError = null;
    initializeTimer();
    amplifierController.update();
    try {
      int ?status ;
      await amplifierController
          .usAutoAlignment(deviceEui: deviceEui, context: context,isStatusCheck: false)
          .then((value) {
        if (value['body']["result"] != null) {
          status = value['body']["result"]['auto_alignment_status']['auto_align_status'];
        } else {
          ampItem.upStreamAutoAlignmentError = value['body']["detail"];
          S.of(context).usAlignmentFailed.showError(context);
          isStartUpStream = false;
          amplifierController.update();
          return;
        }

      });
      if (status != null) {
        await Future.delayed(const Duration(seconds: 10));
        await checkUsAutoAlignmentStatus(deviceEui);
      } else if (ampItem.upStreamAutoAlignmentError == null && status == null) {
        S.of(context).usAlignmentFailed.showError(context);
      }
    } catch (e) {
      print(" getUsAlignment-->${e.toString()}");
      S.of(context).dsAlignmentFailed.showError(context);
      ampItem.upStreamAutoAlignmentError = S.of(context).socketExceptionMessage;
    } finally {
      getDifferenceTime();
      isStartUpStream = false;
      amplifierController.update();
    }
  }
  Future<void> checkUsAutoAlignmentStatus(String deviceEui) async {
    const int maxAttempts = 3;
    const Duration delayBetweenAttempts = Duration(seconds: 10);
    int refreshIndex =
    widget.ampPageHelper.listTabs.indexWhere((tab) => tab.title == widget.amplifierItem.deviceEui);
    for (int attempt = 1; attempt <= maxAttempts; attempt++) {
        final response = await amplifierController.usAutoAlignment(
          deviceEui: deviceEui,
          context: context,
          isStatusCheck: true,
        );
        if (response['body']?["result"] != null) {
          int status = response['body']["result"]['auto_alignment_status']['auto_align_status'];
          if (status == 3) {
            ampItem.upStreamUpdateTime = getLastUpdateTime(response['headers']['updated_at']);
            await amplifierConfigurationHelper.getUsManualAlignment(
                context, widget.amplifierItem.deviceEui, refreshIndex);
            await saveOrRevertDsAutoAlignment();
            return;
          } else if (attempt == maxAttempts) {
            S.of(context).dsAlignmentFailed.showError(context);
          }
        } else {
          debugLogs("Unexpected response format in attempt $attempt");
          ampItem.upStreamAutoAlignmentError = S.of(context).socketExceptionMessage;
          break;
        }
        if (attempt < maxAttempts) {
          await Future.delayed(delayBetweenAttempts);
        }else{
          ampItem.upStreamAutoAlignmentError = "Auto alignment failed.";
        }

    }
  }
  Future<void> saveOrRevertDsAutoAlignment() async{
    await DialogUtils().confirmationDialog(
        context,
        S.of(context).autoAlignment,
        S.of(context).autoAlignSaveRevertMessage,
        S.of(context).save,
        S.of(context).revert,
            () async {
          goBack();
          await amplifierConfigurationHelper.saveRevertUsAutoAlignment(context, ampItem.deviceEui,true);
        },
            ()async {
          goBack();
          await amplifierConfigurationHelper.saveRevertUsAutoAlignment(context, ampItem.deviceEui,false);
        }
    );
  }

  void initializeTimer() {
    ampItem.upStreamUpdateTime=null;
    differenceTime = null;
    onTapTime = DateTime.now();
    isShowText = true;
    refreshTimer?.cancel();
  }
  getDifferenceTime() {
    differenceTime = DateTime.now().difference(onTapTime!);
    Timer(const Duration(seconds: 3), () {
      if (mounted) {
        isShowText = false;
        amplifierController.update();
      }
    });
  }
  Widget buildLastSeenView() {
    if (isShowText) {
      return getTimeDurationView(differenceMessage:
      ampItem.upStreamAutoAlignmentError != null ? S.of(context).refreshFailedMessage : null,
        refreshStatus: isStartUpStream ? ApiStatus.loading : ApiStatus.success,
        updateTime: ampItem.upStreamUpdateTime,
        onTapTime: onTapTime,
        difference: differenceTime,textColor: ampItem.upStreamUpdateTime != null ? AppColorConstants.colorGreen : AppColorConstants.colorAppbar
      ,alignment: Alignment.centerLeft);
    } else {
      if (ampItem.upStreamUpdateTime != null) {
        return getLastSeenView(alignment: Alignment.centerLeft,ampItem.upStreamUpdateTime,textColor: ampItem.upStreamUpdateTime != null ? AppColorConstants.colorGreen : AppColorConstants.colorAppbar);
      } else {
        return Container();
      }
    }
  }
  Widget buildSwitchButtonView(){
    if(ampItem.isShowManualConfiguration==false){
      return Container();
    }
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        AppText(
          S.of(context).enableManualMode,
          style: TextStyle(fontStyle:   FontStyle.italic,
              fontFamily: AppAssetsConstants.openSans,
              fontWeight: FontWeight.w700,
              letterSpacing: 0.32,
              color: AppColorConstants.colorPrimary,
              fontSize: getSize(15)),
        ),
        SizedBox(width: getSize(10)),
        CupertinoSwitch(
          activeColor: AppColorConstants.colorPrimary,
          value: !amplifierConfigurationHelper.isUsSwitchOfAuto,
          onChanged: (onChangeValue) async {
            if (!onChangeValue) {
              int deviceListIndex = widget.ampPageHelper.amplifierDeviceList.result
                  .indexWhere((tab) => tab.deviceEui == widget.amplifierItem.deviceEui);

              if(deviceListIndex==-1){
                return;
              }
              if (widget.ampPageHelper.getConfigurationMap(deviceListIndex) != null) {
                bool isSuccess = await widget.ampPageHelper.checkConfigurationMap(
                    context, deviceListIndex);
                if (isSuccess) {
                  amplifierConfigurationHelper.getUsManualAlignment(context, ampItem.deviceEui, deviceListIndex);
                  _updateSelections();
                  amplifierConfigurationHelper.isUsSwitchOfAuto = !onChangeValue;
                }else{
                  return;
                }
              } else {
                _updateSelections();
                amplifierConfigurationHelper.isUsSwitchOfAuto = !onChangeValue;
              }
            }else{
              int index =
              widget.ampPageHelper.listTabs.indexWhere((tab) => tab.title == ampItem.deviceEui);
              amplifierConfigurationHelper.amplifierPageHelper.listTabs[index].ampDeviceItem.mapCtrlStage = {1:1};
              await _updateSelectedValue();
              amplifierConfigurationHelper.isUsSwitchOfAuto = !onChangeValue;
              amplifierConfigurationHelper.checkAndWriteDirtyFlag(
                  values: amplifierConfigurationHelper.usManualAlignmentItem.dsValues,
                  index: index,
                  isDS: false,isFormAPI: false);
            }
            amplifierController.update();
          },
        ),
      ],
    );
  }

  /*Future<bool?> confirmationView({
    required AmplifierController controller,
  }) async {
    return await showManualAlignmentConfirmationDialog(
      context,
      "Changes are not Saved!!",
      "Are you sure you want to discard the changes?",
    );
  }*/

  Future<void> _updateSelections() async {
    await Future.forEach(amplifierConfigurationHelper.usManualAlignmentItem.dsValues, (element) async {
      element.isSelected = false;
    });
  }
  /*Future<bool> compareWithOldValue() async {
    bool isUpdated = true;
    for (var element in amplifierConfigurationHelper.dsManualAlignmentItem.dsValues) {
      String key = "${element.ctrlType}_${element.stage}";
      if (amplifierConfigurationHelper.dsManualAlignmentListMap.containsKey(key)) {
        double oldValue;
        try {
          oldValue = double.parse(amplifierConfigurationHelper.dsManualAlignmentListMap[key].toString());
        } catch (e) {
          continue;
        }
        if (element.value != oldValue) {
          isUpdated = false;
          break;
        }
      }
    }
    return isUpdated;
  }
  updateOriginalValue() async{
    amplifierConfigurationHelper.usManualAlignmentItem.dsValues.forEach((element) {
      String key = "${element.ctrlType}_${element.stage}";
      if (amplifierConfigurationHelper.usManualAlignmentListMap.containsKey(key)) {
        element.value = double.parse(amplifierConfigurationHelper.usManualAlignmentListMap[key].toString());
      }
    });
  }*/

  Future<void> _updateSelectedValue() async {
    int index = amplifierConfigurationHelper.usManualAlignmentItem.dsValues
        .indexWhere((element) => (element.ctrlType == ampItem.mapCtrlStage.keys.first && element.stage == ampItem.mapCtrlStage.values.first));
    if (index != -1) {
      amplifierConfigurationHelper.usManualAlignmentItem.dsValues[index].isSelected = false;
    }
  }
  Widget commonSliderView(
      {required String title,
        required double min,
        required double max,
        required double indicatorValue,
        required ValueChanged<double> onChangedSlider,
        required VoidCallback onPressed}) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0).copyWith(bottom: 20),
      child: Column(
        children: [
          AppText(
            title,
            style: const TextStyle(
                fontFamily: AppAssetsConstants.openSans, fontSize: 15, fontWeight: FontWeight.w500),
          ),
          AppText('[$max]',
              style: const TextStyle(
                  fontFamily: AppAssetsConstants.openSans,
                  fontSize: 14,
                  fontWeight: FontWeight.w400)),
          SizedBox(
            height: 250,
            child: RotatedBox(
              quarterTurns: 3,
              child: SliderTheme(
                data: SliderTheme.of(context).copyWith(
                    trackHeight: 7,
                    disabledThumbColor: AppColorConstants.colorLimeGray,
                    thumbColor: AppColorConstants.colorWhite,
                    overlayColor: AppColorConstants.colorLightBlue,
                    thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6.5),
                    overlayShape: const RoundSliderOverlayShape(overlayRadius: 13),
                    showValueIndicator: ShowValueIndicator.onlyForContinuous),
                child: Slider(
                  inactiveColor: AppColorConstants.colorLimeGray,
                  value: indicatorValue,
                  max: max,
                  min: min,
                  onChanged: onChangedSlider,
                ),
              ),
            ),
          ),
          AppText("[$min]",
              style: TextStyle(
                  fontFamily: AppAssetsConstants.openSans,
                  fontSize: 14,
                  color: AppColorConstants.colorBlackBlue,
                  fontWeight: FontWeight.w400)),
          const SizedBox(height: 10),
          Container(
            constraints: const BoxConstraints(minWidth: 60),
            alignment: Alignment.center,
            padding: const EdgeInsets.symmetric(vertical: 1, horizontal: 8),
            decoration: BoxDecoration(border: Border.all(color: AppColorConstants.colorH2)),
            child: AppText(indicatorValue.toStringAsFixed(0),
                style: TextStyle(
                    color: AppColorConstants.colorBlackBlue,
                    fontFamily: AppAssetsConstants.openSans,
                    fontSize: 15,
                    fontWeight: FontWeight.w700)),
          ),
          const SizedBox(height: 30),
          AppButton(
            buttonWidth: 60,
            buttonRadius: 8,
            buttonHeight: 32,
            padding: MaterialStateProperty.all(const EdgeInsets.all(12)),
            buttonName: S.of(context).write,
            fontSize: 16,
            onPressed: onPressed,
            fontFamily: AppAssetsConstants.openSans,
          ),
        ],
      ),
    );
  }

//-------------------------- Interstage Values View (Buttons)-------------------------------------------

  Widget ampInterstageValuesView() {
    int refreshIndex = widget.ampPageHelper.listTabs.indexWhere((tab) => tab.title == widget.amplifierItem.deviceEui);
    return ManualAlignmentPage(
      isSwitchOfAuto: amplifierConfigurationHelper.isUsSwitchOfAuto,
      isDSAlignment: false,
      screenLayoutType: screenLayoutType,
      amplifierController: amplifierController,
      dsManualAlignmentItem: amplifierConfigurationHelper.usManualAlignmentItem,
      onTapWrite: (DsManualAlignmentItem dsManualAlignmentItem) async {
        await amplifierConfigurationHelper.setUsManualAlignment(
            context, widget.amplifierItem.deviceEui, dsManualAlignmentItem, refreshIndex);
        await amplifierConfigurationHelper.getUsManualAlignment(
            context, widget.amplifierItem.deviceEui, refreshIndex,
            isFromSetAPI: (amplifierConfigurationHelper.isUsSwitchOfAuto == false));
      },
      onTapSave: (DsManualAlignmentItem dsManualAlignmentItem) {
        amplifierConfigurationHelper.saveRevertUsManualAlignment(
            context, widget.amplifierItem.deviceEui, dsManualAlignmentItem,true,refreshIndex);
      },
      onTapRevert: (DsManualAlignmentItem dsManualAlignmentItem) {
        amplifierConfigurationHelper.saveRevertUsManualAlignment(
            context, widget.amplifierItem.deviceEui, dsManualAlignmentItem,false,refreshIndex);
      },
      onRefreshClicked: (){
      amplifierConfigurationHelper.getUsManualAlignment(context, widget.amplifierItem.deviceEui,refreshIndex, isFromSetAPI: (amplifierConfigurationHelper.isUsSwitchOfAuto == false));
      },
      amplifierConfigurationHelper: amplifierConfigurationHelper, isOffline: (getDetectedStatusType(ampItem.status) == DetectedStatusType.online),
    buttonView: buildSwitchButtonView(),
    settingModel:amplifierConfigurationHelper.usAlignmentSettingModel,
    );
  }

  usManualAlignmentDraftMessageView() {
    if (widget.ampPageHelper.usConfigurationDraftMessage.isEmpty) {
      return const SizedBox(height: 15);
    }
    return Align(
      alignment: Alignment.center,
      child: Padding(
        padding: const EdgeInsets.only(left: 8.0, bottom: 8.0),
        child: CustomPaint(
          painter: DottedBorderPainter(
            borderColor: AppColorConstants.colorLow,
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 8.0),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.error_outline, color: AppColorConstants.colorLightBlue, size: 15),
                const SizedBox(width: 5),
                Flexible(
                  child: AppText(
                    widget.ampPageHelper.usConfigurationDraftMessage,
                    style: TextStyle(
                      color: AppColorConstants.colorDarkBlue,
                      fontSize: 12,
                      fontFamily: AppAssetsConstants.openSans,
                      fontWeight: getMediumBoldFontWeight(),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

/*
  Widget ampInterstageValuesView() {
    print("---amplifierConfigurationHelper.isSelected.value=${amplifierConfigurationHelper.isSelected.value}");
    return Container(
        padding: EdgeInsets.symmetric( vertical: getSize(10),horizontal: getSize(20)),
        decoration:borderViewDecoration,
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 15, bottom: 18),
              child: AppText(
                amplifierConfigurationHelper.isUsSwitchOfAuto ? "" : S.of(context).ampsFineTuning,
                style: TextStyle(
                    fontFamily: AppAssetsConstants.openSans,
                    fontWeight: getMediumBoldFontWeight(),
                    letterSpacing: 0.32,
                    color: AppColorConstants.colorLightBlue,
                    fontSize: getSize(18)),
              ),
            ),
            SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: getSize(50)),
              scrollDirection: screenLayoutType == ScreenLayoutType.mobile
                  ? Axis.horizontal
                  : Axis.vertical,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(top: 40),
                        child: AppText(
                          S.of(context).amplifierInterstageValues,
                          style: TextStyle(
                              fontFamily: AppAssetsConstants.openSans,
                              fontWeight: getMediumBoldFontWeight(),
                              letterSpacing: 0.32,
                              color: AppColorConstants.colorLightBlue,
                              fontSize: getSize(18)),
                        ),
                      ),
                      const SizedBox(height: 80),
                      Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                                if(amplifierConfigurationHelper.isVisibleManualBox(1, 1, 2, 1))
                                displayStageTitle(S.of(context).inputStage),
                                if(amplifierConfigurationHelper.isVisibleManualBox(1, 2, 2, 2))...[
                                  const SizedBox(width: 20),
                                  displayStageTitle(S.of(context).intermediateStage)],
                                if(amplifierConfigurationHelper.isVisibleManualBox(1, 3, 2, 3))...[
                                  const SizedBox(width: 20),
                                  displayStageTitle(S.of(context).outputStage)],
                            ],
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              if (amplifierConfigurationHelper.findDsManualItem(1,1) !=null || amplifierConfigurationHelper.findDsManualItem(2,1) !=null) ...[
                                buildAmplifierInterstageValuesView(S.of(context).inputStage, [
                                  if(amplifierConfigurationHelper.findDsManualItem(1,1) !=null)
                                    amplifierInterstageColumnView(
                                        getCtrlType(1, 1),
                                        amplifierConfigurationHelper.findDsManualItem(1,1)!.value.toString(),
                                        isEQUView: true,
                                        AppAssetsConstants.equIcon, onTap: () {
                                      amplifierConfigurationHelper.onSelectStage(1, 1);
                                      amplifierController.update();
                                    }, isSelected: amplifierConfigurationHelper.checkIsSelected(1, 1)),
                                  if(amplifierConfigurationHelper.findDsManualItem(2,1) != null)...[
                                    SizedBox(height: getSize(10)),
                                    amplifierInterstageColumnView(
                                        getCtrlType(2, 1),
                                        amplifierConfigurationHelper.findDsManualItem(2,1)!.value.toString(),
                                        isEQUView: false,
                                        AppAssetsConstants.attnIcon, onTap: () {
                                      amplifierConfigurationHelper.onSelectStage(2, 1);
                                      amplifierController.update();
                                    }, isSelected: amplifierConfigurationHelper.checkIsSelected(2, 1)),]
                                ]),
                              ],
                              if (amplifierConfigurationHelper.findDsManualItem(1,2) != null || amplifierConfigurationHelper.findDsManualItem(2,2) != null)...[
                                Padding(
                                  padding: const EdgeInsets.only(top: 15),
                                  child: AppImageAsset(
                                    fit: BoxFit.fitWidth,
                                    width: 50,
                                    image: AppAssetsConstants.rightArrowIcon,
                                    color: AppColorConstants.colorH2,
                                  ),
                                ),
                                buildAmplifierInterstageValuesView(S.of(context).intermediateStage, [
                                  if(amplifierConfigurationHelper.findDsManualItem(1,2) != null)
                                    amplifierInterstageColumnView(
                                        getCtrlType(1, 2),
                                        amplifierConfigurationHelper.findDsManualItem(1,2)!.value.toString(),
                                        isEQUView: true,
                                        AppAssetsConstants.equ2Icon, onTap: () {
                                      amplifierConfigurationHelper.onSelectStage(1, 2);
                                      amplifierController.update();
                                    }, isSelected: amplifierConfigurationHelper.checkIsSelected(1, 2)),
                                  if(amplifierConfigurationHelper.findDsManualItem(2,2) != null)...[
                                    SizedBox(height: getSize(10)),
                                    amplifierInterstageColumnView(
                                        getCtrlType(2, 2),
                                        amplifierConfigurationHelper.findDsManualItem(2,2)!.value.toString(),
                                        isEQUView: false,
                                        AppAssetsConstants.attnIcon, onTap: () {
                                      amplifierConfigurationHelper.onSelectStage(2, 2);
                                      amplifierController.update();
                                    }, isSelected: amplifierConfigurationHelper.checkIsSelected(2, 2)),]
                                ]),
                              ],
                              if(amplifierConfigurationHelper.findDsManualItem(1,3) != null)...[
                                Padding(
                                  padding: const EdgeInsets.only(top: 15),
                                  child: AppImageAsset(
                                    fit: BoxFit.fitWidth,
                                    width: 50,
                                    image: AppAssetsConstants.rightArrowIcon,
                                    color: AppColorConstants.colorH2,
                                  ),
                                ),
                                buildAmplifierInterstageValuesView(S.of(context).outputStage, [
                                  amplifierInterstageColumnView(
                                      getCtrlType(1, 3),
                                      amplifierConfigurationHelper.findDsManualItem(1,3)!.value.toString(),
                                      isEQUView: true,
                                      AppAssetsConstants.equ3Icon, onTap: () {
                                    amplifierConfigurationHelper.onSelectStage(1, 3);
                                    amplifierController.update();
                                  }, isSelected: amplifierConfigurationHelper.checkIsSelected(1, 3)),
                                ])]
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 60),
                      if (!amplifierConfigurationHelper.isUsSwitchOfAuto) Padding(
                        padding: const EdgeInsets.symmetric(vertical: 35),
                        child: Wrap(
                          // mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            AppButton(
                              buttonWidth: 80,
                              buttonRadius: 8,
                              buttonHeight: 32,
                              padding: MaterialStateProperty.all(const EdgeInsets.all(12)),
                              buttonName: S.of(context).save,
                              fontSize: 16,
                              onPressed: () {},
                              fontFamily: AppAssetsConstants.openSans,
                            ),
                            const SizedBox(
                              width: 50,
                            ),
                            AppButton(
                              buttonWidth: 80,
                              buttonRadius: 8,
                              buttonHeight: 32,
                              padding: MaterialStateProperty.all(const EdgeInsets.all(12)),
                              buttonName: S.of(context).revert,
                              fontSize: 16,
                              onPressed: () {
                                amplifierController.update();
                              },
                              fontFamily: AppAssetsConstants.openSans,
                            ),
                          ],
                        ),
                      ) else
                        const SizedBox(
                            height: 80
                        ),
                    ],
                  ),
                  if(!amplifierConfigurationHelper.isUsSwitchOfAuto && amplifierConfigurationHelper.isSelected.value != null)
                    Obx(() {
                      return (amplifierConfigurationHelper.isSelected.value)?
                      Padding(
                        padding:  const EdgeInsets.symmetric(horizontal: 30),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(top: 25),
                              child: Container(width: 200 ,alignment: Alignment.center,
                                child: AppText(
                                  getSelectedStageName(amplifierConfigurationHelper.dsManualValues!.stage),
                                  style: TextStyle(
                                      fontFamily: AppAssetsConstants.openSans,
                                      fontWeight: getMediumBoldFontWeight(),
                                      letterSpacing: 0.32,
                                      color: AppColorConstants.colorLightBlue,
                                      fontSize: getSize(18)),
                                ),
                              ),
                            ),
                            const SizedBox(height: 15),
                            commonSliderView(
                                onChangedSlider: (value) {
                                  var newValue = value.toInt() * amplifierConfigurationHelper.usManualAlignmentItem!.factor!;
                                  amplifierConfigurationHelper.dsManualValues!.value = newValue.toDouble();
                                  amplifierController.update();
                                },
                                title: getSelectedCtrlType(amplifierConfigurationHelper.dsManualValues!.ctrlType, amplifierConfigurationHelper.dsManualValues!.stage),
                                min: amplifierConfigurationHelper.dsManualValues!.minVal/amplifierConfigurationHelper.usManualAlignmentItem!.factor!,
                                max: amplifierConfigurationHelper.dsManualValues!.maxVal/amplifierConfigurationHelper.usManualAlignmentItem!.factor!,
                                indicatorValue: amplifierConfigurationHelper.dsManualValues!.value,
                                onPressed: () {
                                  usManualAlignmentItem usManualAlignmentItem = usManualAlignmentItem(dsValues: [amplifierConfigurationHelper.dsManualValues!]);
                                  amplifierConfigurationHelper.setDsManualAlignment(context,widget.amplifierItem.deviceEui,usManualAlignmentItem);
                                }),
                          ],
                        ),
                      ):Container();
                    }
                    ),
                ],
              ),
            ),
          ],
        ));
  }
*/

  Widget displayStageTitle(String title){
   return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4.0),
      child: Center(
        child: AppText(
          title,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 13,
            fontWeight: FontWeight.w400,
            fontFamily: AppAssetsConstants.openSans,
            color: AppColorConstants.colorH1,
          ),
        ),
      ),
    );
  }

  String getCtrlType(int type, int stage) {
    return "${(type == 1) ? S.of(context).equ : (type == 2) ? S.of(context).attn : "-"}$stage";
  }

  String getSelectedCtrlType(int type, int stage) {
    return "${(type == 1) ? S.of(context).EQ : (type == 2) ? S.of(context).ATTN : "-"}$stage";
  }

  String getSelectedStageName(int stage) {
    print("--Stage == ${stage}");
    return (stage == 1)
        ? S.of(context).inputStage
        : (stage == 2)
        ? S.of(context).intermediateStage
        : (stage == 3)
        ? S.of(context).outputStage
        : "";
  }
//-------------------------- Interstage Values View (Button)-------------------------------------------
  /*Widget ampInterstageValuesView(){
    return Container(
        padding: EdgeInsets.symmetric(vertical: getSize(10),horizontal: getSize(20)),
        decoration: borderViewDecoration,
        child: SingleChildScrollView(padding:   EdgeInsets.symmetric(horizontal: getSize(50)),
          scrollDirection: screenLayoutType == ScreenLayoutType.mobile ? Axis.horizontal : Axis.vertical,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Column(mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 40),
                    child: AppText(
                      S.of(context).amplifierInterstageValues,
                      style: TextStyle(
                          fontFamily: AppAssetsConstants.openSans,
                          fontWeight: getMediumBoldFontWeight(),
                          letterSpacing: 0.32,
                          color: AppColorConstants.colorLightBlue,
                          fontSize: getSize(18)),
                    ),
                  ),
                  SizedBox(height: getSize(60)),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const SizedBox(width: 8),
                      AppText(
                        S.of(context).inputStage,
                        style: TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.w400,
                          fontFamily: AppAssetsConstants.openSans,
                          color: AppColorConstants.colorH1,
                        ),
                      ),
                      const SizedBox(width: 20),
                      AppText(
                        S.of(context).intermediateStage,
                        style: TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.w400,
                          fontFamily: AppAssetsConstants.openSans,
                          color: AppColorConstants.colorH1,
                        ),
                      ),
                      const SizedBox(width: 20),
                      AppText(
                        S.of(context).outputStage,
                        style: TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.w400,
                          fontFamily: AppAssetsConstants.openSans,
                          color: AppColorConstants.colorH1,
                        ),
                      ),
                    ],
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      buildAmplifierInterstageValuesView(S.of(context).inputStage, [
                        amplifierInterstageColumnView(onTap:  () {
                          resetAll();
                          isInputStage =true;
                          amplifierController.update();
                        },
                            S.of(context).attn1,
                            '0.00',
                            isEQUView: false,
                            AppAssetsConstants.attnIcon,isStageView: isInputStage),

                      ]),
                      Padding(
                        padding: const EdgeInsets.only(top:15),
                        child: AppImageAsset(fit:  BoxFit.fitWidth,width: 50,
                          image: AppAssetsConstants.rightArrowIcon,
                          color: AppColorConstants.colorH2,
                        ),
                      ),
                      buildAmplifierInterstageValuesView(S.of(context).intermediateStage, [
                        amplifierInterstageColumnView(onTap:  () {
                          resetAll();
                          isIntermediateStage = true;
                          amplifierController.update();
                        },
                            S.of(context).equ2,
                            '0.00',
                            isEQUView: true,
                            AppAssetsConstants.equ2Icon,isStageView: isIntermediateStage),

                      ]),
                      Padding(
                        padding: const EdgeInsets.only(top:15),
                        child: AppImageAsset(fit:  BoxFit.fitWidth,width: 50,
                          image: AppAssetsConstants.rightArrowIcon,
                          color: AppColorConstants.colorH2,
                        ),
                      ),
                      buildAmplifierInterstageValuesView(S.of(context).outputStage, [
                        amplifierInterstageColumnView(onTap:  () {
                          resetAll();
                          isOutputStage = true;
                          amplifierController.update();
                        },
                            S.of(context).attn2,
                            '0.00',
                            isEQUView: false,
                            AppAssetsConstants.attnIcon,isStageView: isOutputStage),
                      ])
                    ],
                  ),
                  const SizedBox(height: 40),
                 if(!amplifierConfigurationHelper.isUsSwitchOfAuto) Padding(
                    padding: const EdgeInsets.only(top: 80,bottom: 40,left: 35,right: 35),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        AppButton(
                          buttonWidth: 80,
                          buttonRadius: 8,
                          buttonHeight: 32,
                          padding: MaterialStateProperty.all(const EdgeInsets.all(12)),
                          buttonName: S.of(context).save,
                          fontSize: 16,
                          onPressed: () {},
                          fontFamily: AppAssetsConstants.openSans,
                        ),
                        const SizedBox(
                          width: 50,
                        ),
                        AppButton(
                          buttonWidth: 80,
                          buttonRadius: 8,
                          buttonHeight: 32,
                          padding: MaterialStateProperty.all(const EdgeInsets.all(12)),
                          buttonName: S.of(context).revert,
                          fontSize: 16,
                          onPressed: () {
                            resetAll();
                            amplifierController.update();
                          },
                          fontFamily: AppAssetsConstants.openSans,
                        ),
                      ],
                    ),
                  )else const SizedBox(height: 80),
                ],
              ),
           if(!amplifierConfigurationHelper.isUsSwitchOfAuto) Column(mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(height: getSize(30)),
                  Padding(
                    padding: const EdgeInsets.only(bottom: 15),
                    child: Container(width:200 ,alignment: Alignment.center,
                      child: AppText(
                        (isInputStage)
                            ? S.of(context).inputStage
                            : (isIntermediateStage)
                            ? S.of(context).intermediateStage
                            : (isOutputStage)
                            ? S.of(context).outputStage
                            : "",
                        style: TextStyle(
                            fontFamily: AppAssetsConstants.openSans,
                            fontWeight: getMediumBoldFontWeight(),
                            letterSpacing: 0.32,
                            color: AppColorConstants.colorLightBlue,
                            fontSize: getSize(18)),
                      ),
                    ),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (isInputStage)
                          commonSliderView(
                              onChangedSlider: (value) {
                                aTTN1Value = value;
                                amplifierController.update();
                              },
                              title: S.of(context).ATTN1,
                              subTitle: "3000",
                              indicatorValue: aTTN1Value,
                              onPressed: () {}),
                        if (isIntermediateStage)
                          commonSliderView(
                              onChangedSlider: (value) {
                                eQ1Value = value;
                                amplifierController.update();
                              },
                              title: S.of(context).EQ1,
                              subTitle: "3000",
                              indicatorValue: eQ1Value,
                              onPressed: () {}),
                        if (isOutputStage)
                          commonSliderView(
                              onChangedSlider: (value) {
                                aTTN2Value = value;
                                amplifierController.update();
                              },
                              title: S.of(context).ATTN2,
                              subTitle: "3000",
                              indicatorValue: aTTN2Value,
                              onPressed: () {}),
                      ],
                  ),
                  SizedBox(height: getSize(10)),

                ],
              )
            ],
          ),
        ));
  }*/

  Widget buildAmplifierInterstageValuesView( String title ,List<Widget> column ) {
    return Padding(
      padding: const EdgeInsets.only(top: 15),
      child: CustomPaint(
        painter: DottedBorderPainter(strokeWidth: 2.7,
          cornerRadiusValue: 20,
          borderColor: AppColorConstants.colorChart,
        ),
        child: Container(
          padding: const EdgeInsets.only(
            left: 12,
            right: 12,
            top: 8,
            bottom: 9
          ),
          decoration: BoxDecoration(
            color: AppColorConstants.colorBackgroundDark,
            borderRadius: const BorderRadius.all(Radius.circular(20)),
          ),
          child: Column(
            children: column,
          ),
        ),
      ),
    );
  }

  Widget amplifierInterstageColumnView(String title, String value, String iconImage,
      {bool? isEQUView, required GestureTapCallback onTap, required bool isSelected}) {
    print("title=$title---Value--$value---isEQUView=$isEQUView---isSelected=$isSelected");
    return Column(
      children: [
        AppText(
          title,
          style: TextStyle(
            fontSize: 11,
            fontWeight: FontWeight.w400,
            fontFamily: AppAssetsConstants.openSans,
            color: isEQUView! ? AppColorConstants.colorLightBlue : AppColorConstants.colorPrimary,
          ),
        ),
        const SizedBox(height: 3),
        InkWell(
          onTap: amplifierConfigurationHelper.isUsSwitchOfAuto ? null : onTap,
          child: Container(
            decoration: BoxDecoration(
                color: isSelected
                    ? AppColorConstants.colorGreen1
                    : isEQUView
                    ? AppColorConstants.colorChartLine1
                    : AppColorConstants.colorLightPurple,
                borderRadius: const BorderRadius.all(Radius.circular(8))),
            height: 50,
            width: 50,
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: AppImageAsset(image: iconImage),
            ),
          ),
        ),
        const SizedBox(height: 3),
        AppText(
          value,
          style: TextStyle(
            fontSize: 11,
            fontWeight: FontWeight.w400,
            fontFamily: AppAssetsConstants.openSans,
            color: isEQUView ? AppColorConstants.colorLightBlue : AppColorConstants.colorPrimary,
          ),
        ),

      ],
    );
  }

//-------------------------- Up Stream View -------------------------------------------

  Widget upStreamView(
    AmpUpStreamItem ampUpStreamItem, {
    required String title,
    required AmplifierController controller,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: getSize(20)),
        Container(
          margin: EdgeInsets.only(
              right: screenLayoutType == ScreenLayoutType.desktop
                  ? MediaQuery.of(context).size.width * 0.4
                  : screenLayoutType == ScreenLayoutType.tablet
                      ? MediaQuery.of(context).size.width * 0.25
                      : 0),
          decoration: borderViewDecoration,
          child: Column(
            children: [
              commonTitleView(title: title),
              SizedBox(
                height: getSize(10),
              ),
              commonStreamingButtonView(
                title: S.of(context).gain,
                value: ampUpStreamItem.upGain ?? upGain,
                onDecrement: () {
                  if (ampUpStreamItem.upGain > 0) {
                    ampUpStreamItem.upGain--;
                    controller.updateUsConfig(context, ampItem.deviceEui, ampUpStreamItem);
                    controller.update();
                  }
                },
                onIncrement: () {
                  ampUpStreamItem.upGain++;
                  controller.updateUsConfig(context, ampItem.deviceEui, ampUpStreamItem);
                  controller.update();
                },
              ),
              commonStreamingButtonView(
                title: S.of(context).slop,
                value: ampUpStreamItem.upSlope ?? upSlope,
                onDecrement: () {
                  if (ampUpStreamItem.upSlope > 0) {
                    ampUpStreamItem.upSlope--;
                    controller.updateUsConfig(context, ampItem.deviceEui, ampUpStreamItem);
                    controller.update();
                  }
                },
                onIncrement: () {
                  ampUpStreamItem.upSlope++;
                  controller.updateUsConfig(context, ampItem.deviceEui, ampUpStreamItem);
                  controller.update();
                },
              ),
              (ampItem.ingressSwitchError != null)
                  ? CommonAPIErrorView(errorMessage: ampItem.ingressSwitchError ?? "")
                  : SizedBox(height: getSize(40)),
            ],
          ),
        ),
      ],
    );
  }

  Widget commonTitleView({required String title}) {
    return Container(alignment: Alignment.center,
      height: getSize(50),
      width: double.infinity,
      padding: EdgeInsets.only(top: getSize(13), left: getSize(20)),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(getSize(7)),
          topLeft: Radius.circular(getSize(7)),
        ),
      ),
      child:  AppText(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          fontFamily: AppAssetsConstants.openSans,
          color: AppColorConstants.colorLightBlue,
        ),
      ),
    );
  }

  Widget commonStreamingButtonView(
      {required VoidCallback onIncrement,
      required VoidCallback onDecrement,
      required int value,
      required String title}) {
    return Padding(
        padding: EdgeInsets.only(left: getSize(20), right: getSize(20), bottom: getSize(10)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            AppText(
              title,
              style: const TextStyle(
                  color: AppColorConstants.colorDarkBlue,
                  fontSize: 14,
                  fontFamily: AppAssetsConstants.openSans,
                  fontWeight: FontWeight.w600),
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                AppButton(
                  padding: MaterialStateProperty.all<EdgeInsets>(EdgeInsets.zero),
                  minimumSize: MaterialStateProperty.all<Size>(const Size(15, 40)),
                  buttonName: '-',
                  buttonWidth: getSize(30),
                  buttonHeight: getSize(30),
                  fontSize: getSize(20),
                  onPressed: onDecrement,
                ),
                SizedBox(
                  width: getSize(10),
                ),
                Container(
                  alignment: Alignment.center,
                  width: getSize(60),
                  height: getSize(30),
                  decoration: BoxDecoration(
                      color: AppColorConstants.colorWhite,
                      borderRadius: BorderRadius.circular(5),
                      border: Border.all(color: AppColorConstants.colorH2.withOpacity(0.5))),
                  child: AppText(
                    "$value",
                    style: TextStyle(
                        fontWeight: getMediumFontWeight(), fontFamily: AppAssetsConstants.openSans),
                  ),
                ),
                SizedBox(
                  width: getSize(10),
                ),
                AppButton(
                  onPressed: onIncrement,
                  padding: MaterialStateProperty.all<EdgeInsets>(EdgeInsets.zero),
                  minimumSize: MaterialStateProperty.all<Size>(const Size(15, 40)),
                  buttonName: '+',
                  buttonWidth: getSize(30),
                  buttonHeight: getSize(30),
                  fontSize: getSize(20),
                ),
              ],
            ),
          ],
        ));
  }
}

