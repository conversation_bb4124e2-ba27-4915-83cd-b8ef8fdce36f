// ignore_for_file: deprecated_member_use

import 'package:flutter/cupertino.dart';
import 'package:quantumlink_node/app/ui/manual_alignment_page.dart';
import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/pages/amplifiers/amplifier_details/amp_configuration/AmplifierConfigurationHelper.dart';
import 'package:quantumlink_node/utils/dialog_utils.dart';

class AmpDsAlignment extends StatefulWidget {
  final AmplifierDeviceItem amplifierItem;
  final AmplifierPageHelper ampPageHelper;
  final String selectedOption;

  const AmpDsAlignment({super.key, required this.amplifierItem, required this.ampPageHelper, required this.selectedOption});

  @override
  State<AmpDsAlignment> createState() => _AmpDsAlignmentState();
}

class _AmpDsAlignmentState extends State<AmpDsAlignment> {
  late AmplifierConfigurationHelper amplifierConfigurationHelper;
  List<String> agcConfigList = [];
  List<String> universalPluginList = [];
  AmplifierController amplifierController =Get.put<AmplifierController>(AmplifierController());
  String? selectedAGCValue;
  String? selectedUniversalValue;
  int downGain = 0;
  int downSlope = 0;
  bool isStartDownStream = false;
  double constraintsWidth = 0.0;
  late AmplifierDeviceItem ampItem;
  bool isListValueUpdate = false;
  double baselineX = 0;
  double endPoint = 0;
  DateTime? autoAlignmentOnTapTime;
  Duration ? autoAlignmentDifferenceTime;
  bool autoAlignmentIsShowText = true;
  Timer? autoAlignmentRefreshTimer;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    amplifierConfigurationHelper = AmplifierConfigurationHelper(amplifierPageHelper : widget.ampPageHelper);
    amplifierConfigurationHelper.spectrumApiStatus = ApiStatus.loading;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      int refreshIndex = widget.ampPageHelper.listTabs.indexWhere((tab) => tab.title == widget.amplifierItem.deviceEui);
      amplifierConfigurationHelper.getDsAutoAlignmentSpectrumData(context, widget.amplifierItem.deviceEui,widget.amplifierItem);
      amplifierConfigurationHelper.getDsManualAlignment(context,widget.amplifierItem.deviceEui,refreshIndex);
    });
    ampItem = widget.amplifierItem;
    if (ampItem.dsLevelPoints.isNotEmpty) {
      endPoint = ampItem.dsLevelPoints.last.freq;
    }
  }

  initializeListValue() {
    agcConfigList = [S.of(context).thermal, S.of(context).manual, S.of(context).on];
    universalPluginList = [S.of(context).present, S.of(context).absent];
    isListValueUpdate = true;
  }
  late ScreenLayoutType screenLayoutType;

  @override
  Widget build(BuildContext context) {
    if (!isListValueUpdate) {
      initializeListValue();
    }

    return GetBuilder<AmplifierController>(
      init: AmplifierController(),
      builder: (AmplifierController controller) {
        return ScreenLayoutTypeBuilder(
          builder: (context, screenType, constraints) {
            screenLayoutType = screenType;
            constraintsWidth = constraints.maxWidth;
            return Padding(
              padding: EdgeInsets.symmetric(
                  horizontal: getSize(8.0), vertical: getSize(15)),
              child: MergeSemantics(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // downStreamView(ampItem.ampDownStreamItem, title: S.of(context).downstream, controller: controller),
                    //SizedBox(height: getSize(20)),
                    buildTitleView(),
                    // buildStartButton(ampItem.ampDownStreamItem,controller),
                    // if (amplifierConfigurationHelper.isDSSwitchOfAuto) ...[
                    /*buildSpectrumBarChart(
                          amplifierConfigurationHelper.dsSpectrumDataPoints),
                      SizedBox(height: getSize(25)),*/
                    // ],
                    dsManualAlignmentDraftMessageView(),
                   // SizedBox(height: getSize(15)),
                    if(screenType == ScreenLayoutType.desktop)...[
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: buildSpectrumBarChart(
                                amplifierConfigurationHelper
                                    .dsSpectrumDataPoints),
                          ),
                          SizedBox(width: getSize(10)),
                          Expanded(child: ampInterstageValuesView()),
                        ],
                      ),
                    ] else
                      ...[
                        buildSpectrumBarChart(
                            amplifierConfigurationHelper
                                .dsSpectrumDataPoints),
                        SizedBox(height: getSize(25)),
                        ampInterstageValuesView(),
                      ]
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  void autoAlignmentInitializeTimer() {
    ampItem.dsAutoAlignUpdateTime == null;
    autoAlignmentDifferenceTime = null;
    autoAlignmentOnTapTime = DateTime.now();
    autoAlignmentIsShowText = true;
    autoAlignmentRefreshTimer?.cancel();
  }
  autoAlignmentGetDifferenceTime() {
    autoAlignmentDifferenceTime = DateTime.now().difference(autoAlignmentOnTapTime!);
    autoAlignmentRefreshTimer=Timer(const Duration(seconds: 3), () {
      if (mounted) {
        autoAlignmentIsShowText = false;
        amplifierController.update();
      }
    });
  }

  //---------------------------New Widget---------------------------------------------
  Widget buildTitleView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Row(
          children: [
            AppText(
             S.of(context).dsAlignCfg,
              style: TextStyle(
                  fontSize: getSize(24),
                  fontFamily: AppAssetsConstants.openSans,
                  color: AppColorConstants.colorPrimary,
                  fontWeight: FontWeight.w600),
            ),
            SizedBox(width: getSize(10)),
            Flexible(
              child: Container(
                  height: getSize(20),
                  decoration: BoxDecoration(
                      border: Border(
                          bottom: BorderSide(color: AppColorConstants.colorBlack, width: 0.4)))),
            ),
            //if (screenLayoutType == ScreenLayoutType.desktop) dSRefreshButtonView()
          ],
        ),
        SizedBox(height: getSize(10)),
        startAutoAlignmentWidget()
      ],
    );
  }

  Widget  startAutoAlignmentWidget(){
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        AppButton(
          buttonRadius: 9,
          loadingStatus: isStartDownStream ? ApiStatus.loading : ApiStatus.success,
          buttonHeight: getSize(35),
          buttonWidth: getSize(220),
          fontColor: amplifierConfigurationHelper.isDSSwitchOfAuto &&
              getDetectedStatusType(ampItem.status) == DetectedStatusType.online
              ? AppColorConstants.colorWhite
              : AppColorConstants.colorH1Grey,
          borderColor: amplifierConfigurationHelper.isDSSwitchOfAuto &&
              getDetectedStatusType(ampItem.status) == DetectedStatusType.online
              ? AppColorConstants.colorLightBlue.withOpacity(0.5)
              : AppColorConstants.colorH1.withOpacity(0.5),
          buttonName: S.of(context).startAutoAlignment,
          onPressed: getDetectedStatusType(ampItem.status) == DetectedStatusType.online
              ? () async {
            if (amplifierConfigurationHelper.isDSSwitchOfAuto && !isStartDownStream) {
              DialogUtils().confirmationDialog(
                  context,
                  S.of(context).msgAskConfirmationTitle,
                  S.of(context).msgAskConfirmationAutoAlign,
                  S.of(context).yes,
                  S.of(context).no, () async {
                goBack();
               await getDsAlignment(context, ampItem.deviceEui).then((value) {});
              },
                    () => goBack(),
              );
            }
          }
              : null,
          buttonColor: isStartDownStream
              ? AppColorConstants.colorLightBlue.withOpacity(0.6)
              : amplifierConfigurationHelper.isDSSwitchOfAuto &&
              getDetectedStatusType(ampItem.status) == DetectedStatusType.online
              ? AppColorConstants.colorLightBlue
              : AppColorConstants.colorBackgroundDark,
          fontSize: getSize(16),
        ),
        if (ampItem.downStreamAutoAlignmentError != null)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5),
            child: CustomPaint(
              painter: DottedBorderPainter(
                borderColor: AppColorConstants.colorRedLight.withOpacity(0.8),
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 8.0),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.error_outline, color: AppColorConstants.colorRedLight, size: 15),
                    const SizedBox(width: 5),
                    Flexible(
                      child: AppText(
                        "${ampItem.downStreamAutoAlignmentError}",
                        style: TextStyle(
                          color: AppColorConstants.colorDarkBlue,
                          fontSize: 12,
                          fontFamily: AppAssetsConstants.openSans,
                          fontWeight: getMediumFontWeight(),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          )
        else
          Container(height: widget.ampPageHelper.dsConfigurationDraftMessage.isNotEmpty ? 10 : 35),
      ],
    );
  }
  Widget dSRefreshButtonView() {
    return Row(
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 25.0,bottom: 10),
          child: AppButton(
            buttonRadius: 9,
            loadingStatus: ampItem.configRefreshStatus,
            buttonHeight: getSize(35),
            buttonWidth: getSize(80),
            fontColor: AppColorConstants.colorWhite,
            borderColor: AppColorConstants.colorLightBlue.withOpacity(0.5),
            buttonName: S.of(context).refresh,
            onPressed: () async {
              int refreshIndex =
              widget.ampPageHelper.listTabs.indexWhere((tab) => tab.title == ampItem.deviceEui);
              if (ampItem.configRefreshStatus == ApiStatus.success) {
                widget.ampPageHelper.getDsConfigData(context, ampItem.deviceEui, refreshIndex);
                amplifierController.update();
              }
            },
            buttonColor: AppColorConstants.colorLightBlue,
            fontSize: getSize(15),
          ),
        ),
      ],
    );
  }

  List<PointData> mainDsSpectrumPointsData = [];
  //GET DS AUTO ALIGNMENT
  Future<dynamic> getDsAlignment(BuildContext context, String deviceEui) async {
    autoAlignmentInitializeTimer();
    ampItem.downStreamAutoAlignmentError= null;
    mainDsSpectrumPointsData.clear();
    ampItem.dsLevelPoints.clear();
    isStartDownStream = true;
    amplifierController.update();
    try {
      int? status;
      await amplifierController
          .dsAutoAlignment(deviceEui: deviceEui, context: context, isStatusCheck: false)
          .then((value) async {
        if (value['body'] is DsAutoAlignmentModel) {
          DsAutoAlignmentModel model = value['body'];
          if (model.result != null) {
             status = model.result!.sampDownstreamAutoAlignStatus!.autoAlignStatus;
          }
        }else{
          debugLogs("dsAlignmentFailed--> ");
          ampItem.downStreamAutoAlignmentError = value['body']['detail'];
          S.of(context).dsAlignmentFailed.showError(context);
          return;
        }
      });
      //Step 3: Check status up to 3 times
      if (status != null) {
        await Future.delayed(const Duration(seconds: 10));
        await checkDsAutoAlignmentStatus(deviceEui);
      } else if (ampItem.downStreamAutoAlignmentError == null && status == null) {
        S.of(context).dsAlignmentFailed.showError(context);
      }
    } catch (e) {
      debugLogs("getDsAutoAlignment--> ${e.toString()}");
      S.of(context).dsAlignmentFailed.showError(context);
      ampItem.downStreamAutoAlignmentError = S.of(context).socketExceptionMessage;
    } finally {
      autoAlignmentGetDifferenceTime();
      isStartDownStream = false;
      amplifierController.update();
    }
  }

  Future<void> checkDsAutoAlignmentStatus(String deviceEui) async {
    const int maxAttempts = 3;
    const Duration delayBetweenAttempts = Duration(seconds: 10);
    int refreshIndex =
    widget.ampPageHelper.listTabs.indexWhere((tab) => tab.title == widget.amplifierItem.deviceEui);
    for (int attempt = 1; attempt <= maxAttempts; attempt++) {
        final response = await amplifierController.dsAutoAlignment(
          deviceEui: deviceEui,
          context: context,
          isStatusCheck: true,
        );
        if (response['body'] is DsAutoAlignmentModel) {
          DsAutoAlignmentModel model = response['body'];
          if (model.result != null) {
            int status = model.result!.sampDownstreamAutoAlignStatus!.autoAlignStatus;
            if (status == 3) {
              ampItem.dsAutoAlignmentModel = model;
              await saveOrRevertDsAutoAlignment();
            await amplifierConfigurationHelper.getDsAutoAlignmentSpectrumData(
                context, widget.amplifierItem.deviceEui, widget.amplifierItem,
                isRefresh: true);
            await amplifierConfigurationHelper.getDsManualAlignment(
                context, widget.amplifierItem.deviceEui, refreshIndex);
            ampItem.dsAutoAlignUpdateTime= getLastUpdateTime(response['headers']['updated_at']);
              return; // Exit on success
            } else if (attempt == maxAttempts) {
              S.of(context).dsAlignmentFailed.showError(context);
            }
          }
        } else {
          ampItem.downStreamAutoAlignmentError = S.of(context).socketExceptionMessage;
          break;
        }
        if (attempt < maxAttempts) {
          await Future.delayed(delayBetweenAttempts);
        }else{
          ampItem.downStreamAutoAlignmentError = "Auto alignment failed.";
        }
    }
  }

  Future<void>  saveOrRevertDsAutoAlignment() async {
    await DialogUtils().confirmationDialog(
        context,
        S.of(context).autoAlignment,
        S.of(context).autoAlignSaveRevertMessage,
        S.of(context).save,
        S.of(context).revert, () async {
      goBack();
      await amplifierConfigurationHelper.saveRevertDsAutoAlignment(
          context, ampItem.deviceEui, true);
    }, () async {
      goBack();
      await amplifierConfigurationHelper.saveRevertDsAutoAlignment(
          context, ampItem.deviceEui, false);
    });
  }

  buildDSSpectrumDataFunction(DsAutoAlignmentModel value){
    endPoint = ampItem.dsAutoAlignmentModel.result!.dsValues!.first.dsSpectrumValues!.last;
    num startLevelFreq = value.result!.dsValues!.first.startFreq;
    var values =value.result!.dsValues!.first.dsSpectrumValues;
    for (int i = 0; i < values!.length; i++) {
      double value = values[i] / 10;
      if(i>0){
        startLevelFreq = startLevelFreq += 24;
      }
      mainDsSpectrumPointsData.add(PointData(freq: startLevelFreq, value: value));
      ampItem.dsLevelPoints = List<PointData>.from(mainDsSpectrumPointsData);
    }
    endPoint = ampItem.dsLevelPoints.last.freq;
    amplifierController.update();
  }

  Widget buildSwitchButtonView(){
    if(ampItem.isShowManualConfiguration==false){
      return Container();
    }
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        AppText(
          S.of(context).enableManualMode,
          style: TextStyle(fontStyle:   FontStyle.italic,
              fontFamily: AppAssetsConstants.openSans,
              fontWeight: FontWeight.w700,
              letterSpacing: 0.32,
              color: AppColorConstants.colorPrimary,
              fontSize: getSize(15)),
        ),
        SizedBox(width: getSize(10)),
        CupertinoSwitch(
          activeColor: AppColorConstants.colorPrimary,
          value: !amplifierConfigurationHelper.isDSSwitchOfAuto,
          onChanged: (onChangeValue) async {
            debugLogs("On Change Value : $onChangeValue");
            debugLogs("is Switch Auto : $amplifierConfigurationHelper.isDSSwitchOfAuto");
            if (!onChangeValue) {
              //Auto Alignment ON
              int deviceListIndex = widget.ampPageHelper.amplifierDeviceList.result
                  .indexWhere((tab) => tab.deviceEui == widget.amplifierItem.deviceEui);

              if(deviceListIndex==-1){
                return;
              }

              if (widget.ampPageHelper.getConfigurationMap(deviceListIndex) != null) {
                bool isSuccess = await widget.ampPageHelper.checkConfigurationMap(
                    context, deviceListIndex);
                if (isSuccess) {
                   amplifierConfigurationHelper.getDsManualAlignment(context, ampItem.deviceEui, deviceListIndex);
                  _updateSelections();
                  amplifierConfigurationHelper.isDSSwitchOfAuto = !onChangeValue;
                }else{
                  return;
                }
              } else {
                _updateSelections();
                amplifierConfigurationHelper.isDSSwitchOfAuto = !onChangeValue;
              }
            }else{
              //Manual Alignment ON
              int index = widget.ampPageHelper.listTabs.indexWhere((tab) => tab.title == ampItem.deviceEui);
              // ampItem.mapCtrlStage = {1:1};
              amplifierConfigurationHelper.amplifierPageHelper.listTabs[index].ampDeviceItem.mapCtrlStage = {1:1};
              await _updateSelectedValue();
              amplifierConfigurationHelper.isDSSwitchOfAuto = !onChangeValue;
              amplifierConfigurationHelper.checkAndWriteDirtyFlag(
                  values: amplifierConfigurationHelper.dsManualAlignmentItem.dsValues,
                  index: index,
                  isDS: true,isFormAPI: false);
            }
            print("-------VALUE 1-------");
            amplifierController.update();
          },
        ),
      ],
    );
  }

  Future<void> _updateSelections() async {
    await Future.forEach(amplifierConfigurationHelper.dsManualAlignmentItem.dsValues, (element) async {
      element.isSelected = false;
    });
  }

  Future<void> _updateSelectedValue() async {
    int refreshIndex = widget.ampPageHelper.listTabs.indexWhere((tab) => tab.title == ampItem.deviceEui);
    final deviceItem = widget.ampPageHelper.listTabs[refreshIndex].ampDeviceItem;
    int index = amplifierConfigurationHelper.dsManualAlignmentItem.dsValues
        .indexWhere((element) => ((element.ctrlType == deviceItem.mapCtrlStage.keys.first) && (element.stage == deviceItem.mapCtrlStage.values.first)));
    // int index = amplifierConfigurationHelper.dsManualAlignmentItem.dsValues
    //     .indexWhere((element) => (element.ctrlType ==  ampItem.mapCtrlStage.keys.first && element.stage ==  ampItem.mapCtrlStage.values.first));
    if (index != -1) {
      amplifierConfigurationHelper.dsManualAlignmentItem.dsValues[index].isSelected = false;
    }
  }
  /*Future<bool> compareWithOldValue() async {
    bool isUpdated = true;
    for (var element in amplifierConfigurationHelper.dsManualAlignmentItem.dsValues) {
      String key = "${element.ctrlType}_${element.stage}";
      if (amplifierConfigurationHelper.dsManualAlignmentListMap.containsKey(key)) {
        double oldValue;
        try {
          oldValue = double.parse(amplifierConfigurationHelper.dsManualAlignmentListMap[key].toString());
        } catch (e) {
          continue;
        }
        if (element.value != oldValue) {
          isUpdated = false;
          break;
        }
      }
    }
    return isUpdated;
  }

  updateOriginalValue() async {
    amplifierConfigurationHelper.dsManualAlignmentItem.dsValues
        .forEach((element) {
      String key = "${element.ctrlType}_${element.stage}";
      if (amplifierConfigurationHelper.dsManualAlignmentListMap
          .containsKey(key)) {
        element.value = double.parse(amplifierConfigurationHelper
            .dsManualAlignmentListMap[key]
            .toString());
      }
    });
  }*/

  //---------------------------Capture Spectrum Chart Widget---------------------------
  Widget buildSpectrumBarChart(List<DSPointData> dataPoints) {
    double height = (screenLayoutType == ScreenLayoutType.mobile) ? 630 : (!amplifierConfigurationHelper.isDSSwitchOfAuto) ? 600 : 615;
    return Container(
      height:
          amplifierConfigurationHelper.spectrumApiStatus == ApiStatus.success
              ? height
              : null,
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
      decoration: BoxDecoration(
        color: AppColorConstants.colorWhite,
        borderRadius: BorderRadius.circular(9),
        border: Border.all(color: AppColorConstants.colorChart, width: 1.8),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        children: [
          if (amplifierConfigurationHelper.spectrumApiStatus ==
              ApiStatus.success) ...[
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const SizedBox(height: 20),
                  SizedBox(
                    height: 400,
                    child: BarChart(
                      BarChartData(
                        alignment: BarChartAlignment.spaceAround,
                        maxY: 50,
                        minY: 0,
                        barGroups: dataPoints.map((point) {
                          return BarChartGroupData(
                             showingTooltipIndicators: [0,1],
                            x: point.freq.toInt(),
                            barRods: [
                              BarChartRodData(
                                toY: point.reference,
                                color: AppColorConstants.colorRefChartBorder,
                                width: 8,
                                borderRadius: const BorderRadius.only(
                                    topLeft: Radius.circular(3),
                                    topRight: Radius.circular(3)),
                              ),
                              BarChartRodData(
                                toY: point.level,
                                color: AppColorConstants.colorLevelChartBorder,
                                width: 8,
                                borderRadius: const BorderRadius.only(
                                    topLeft: Radius.circular(3),
                                    topRight: Radius.circular(3)),
                              ),
                            ],
                          );
                        }).toList(),
                        titlesData: FlTitlesData(
                            bottomTitles: AxisTitles(
                              axisNameSize: 100,
                              axisNameWidget: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  AppText(S.of(context).mhZ,
                                      style: TextStyle(
                                        color: AppColorConstants.colorH1,
                                      )),
                                  SizedBox(
                                    height: getSize(60),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        Padding(
                                          padding: const EdgeInsets.all(8.0),
                                          child: Container(
                                            height: getSize(11),
                                            width: getSize(12),
                                            decoration: BoxDecoration(
                                              color: AppColorConstants
                                                  .colorRefChartBackGround,
                                              border: Border.all(
                                                  color: AppColorConstants
                                                      .colorRefChartBorder),
                                            ),
                                          ),
                                        ),
                                        AppText(S.of(context).ref,
                                            style: TextStyle(
                                                color: AppColorConstants
                                                    .colorBlack,
                                                fontSize: getSize(13),
                                                fontFamily:
                                                    AppAssetsConstants.notoSans,
                                                fontWeight:
                                                    getMediumBoldFontWeight())),
                                        Padding(
                                          padding: const EdgeInsets.all(8.0),
                                          child: Container(
                                            height: getSize(11),
                                            width: getSize(12),
                                            decoration: BoxDecoration(
                                                color: AppColorConstants
                                                    .colorLevelChartBackGround,
                                                border: Border.all(
                                                    color: AppColorConstants
                                                        .colorLevelChartBorder)),
                                          ),
                                        ),
                                        AppText(
                                          S.of(context).level,
                                          style: TextStyle(
                                              color:
                                                  AppColorConstants.colorBlack,
                                              fontSize: getSize(13),
                                              fontFamily:
                                                  AppAssetsConstants.notoSans,
                                              fontWeight:
                                                  getMediumBoldFontWeight()),
                                        ),
                                      ],
                                    ),
                                  )
                                ],
                              ),
                              sideTitles: SideTitles(
                                getTitlesWidget: (value, meta) => Padding(
                                  padding: const EdgeInsets.only(top: 5),
                                  child: AppText("$value",
                                      style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.grey[700])),
                                ),
                                showTitles: true,
                              ),
                            ),
                            leftTitles: AxisTitles(
                              axisNameWidget: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceEvenly,
                                children: [
                                  SizedBox(width: getSize(10)),
                                  AppText(S.of(context).dBmV,
                                      style: TextStyle(
                                        fontSize: 13,
                                        color: AppColorConstants.colorH1,
                                      )),
                                ],
                              ),
                              sideTitles: SideTitles(
                                interval: 10,
                                showTitles: true,
                                getTitlesWidget: (value, meta) => AppText(
                                  "$value",
                                  style: TextStyle(
                                      fontSize: 12, color: Colors.grey[700]),
                                ),
                                reservedSize: 40,
                              ),
                            ),
                            rightTitles:
                                const AxisTitles(drawBelowEverything: false),
                            topTitles:
                                const AxisTitles(drawBelowEverything: false)),
                        barTouchData: barTouchData,
                        gridData: FlGridData(
                          show: true,
                          horizontalInterval: 10,
                          getDrawingHorizontalLine: (value) => FlLine(
                              color: AppColorConstants.colorDivider,
                              strokeWidth: 0.5),
                        ),
                        borderData: FlBorderData(
                          border: Border(
                            bottom: BorderSide(
                                color: AppColorConstants.colorDivider,
                                width: 1),
                            left: BorderSide(
                                color: AppColorConstants.colorDivider,
                                width: 1),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
          refreshingBar(),
        ],
      ),
    );
  }

  saveRevertButtonOfAutoAlignWidget() {
    bool isSaveRevertDisplay = isSaveRevertEnable();
    Color? color = !isSaveRevertDisplay ? Colors.grey : null;
    if(amplifierConfigurationHelper
        .saveRevertApiStatusOfAutoAlign.value ==
        ApiStatus.loading) {
      return const SizedBox(
          height: 85, width: 50, child: AppLoader());
    }
    return Padding(
      padding: const EdgeInsets.only(top: 5),
      child: Wrap(
        children: [
          AppButton(
            buttonWidth: 80,
            buttonRadius: 8,
            buttonHeight: 32,
            buttonColor: color,
            borderColor: color,
            padding: MaterialStateProperty.all(const EdgeInsets.all(12)),
            buttonName: S
                .of(context)
                .save,
            fontSize: 16,
            onPressed: !isSaveRevertDisplay
                ? null
                : () {
              amplifierConfigurationHelper.saveRevertDsAutoAlignment(
                  context, ampItem.deviceEui, true);
            },
            fontFamily: AppAssetsConstants.openSans,
          ),
          const SizedBox(
            width: 50,
          ),
          AppButton(
            buttonWidth: 80,
            buttonRadius: 8,
            buttonHeight: 32,
            buttonColor: color,
            borderColor: color,
            padding: MaterialStateProperty.all(const EdgeInsets.all(12)),
            buttonName: S
                .of(context)
                .revert,
            fontSize: 16,
            onPressed: !isSaveRevertDisplay
                ? null
                : () async {

               amplifierConfigurationHelper.saveRevertDsAutoAlignment(
                  context, ampItem.deviceEui, false);
            },
            fontFamily: AppAssetsConstants.openSans,
          ),
        ],
      ),
    );
  }
  saveRevertInfo() {
    return Column(
      children: [
      SizedBox(height: 5,),
      Row(mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.info, color: Colors.grey,),
          SizedBox(width: 5,),
          Flexible(
            child: AppText(
              S
                  .of(context)
                  .saveRevertInfoText,
              style: TextStyle(
                  fontFamily: AppAssetsConstants.openSans,
                  fontWeight: getMediumBoldFontWeight(),
                  letterSpacing: 0.32,
                  color: Colors.grey,
                  fontSize: getSize(12)),
            ),
          ),
        ],
      )
    ],);
  }

  bool isSaveRevertEnable() {
    final ampPageHelper = widget.ampPageHelper;
    final index = ampPageHelper.listTabs
        .indexWhere((tab) => tab.title == ampPageHelper.amplifierItem.deviceEui);
    if (index == -1) return false;
    return ampPageHelper.listTabs[index].ampDeviceItem.mapWrittenCtrlDSSpectrum.isNotEmpty;
  }

  //Refreshing Top bar with Error Message and Refresh Button.
  Widget refreshingBar() {
    final String? errorMessage =
        amplifierConfigurationHelper
            .dsSpectrumDataError;
    return Column(
      children: [
          if (errorMessage != null)
            Align(alignment: AlignmentDirectional.centerStart ,child: errorMessageView(errorMessage: errorMessage),),
          buildDsSpectrumLastSeenViewWithRefreshButton(),
      ],
    );
  }

  BarTouchData get barTouchData => BarTouchData(
    enabled: false,
    touchTooltipData: BarTouchTooltipData(maxContentWidth:  70,
      getTooltipColor: (group) => Colors.transparent,
      tooltipPadding:  EdgeInsets.zero,
      tooltipMargin:0,
      getTooltipItem: (
          BarChartGroupData group,
          int groupIndex,
          BarChartRodData rod,
          int rodIndex,
          ) {
        return BarTooltipItem(
        textAlign: rodIndex == 0 ? TextAlign.left : TextAlign.right,
              rod.toY == 0
                  ? " "
                  : (rodIndex == 0
                  ? rod.toY.toStringAsFixed(2).padRight(18)
                  : rod.toY.toStringAsFixed(2).padLeft(18)),
              TextStyle(
                fontSize: 11,
                color: rodIndex == 0
                    ? AppColorConstants.colorRefChartBorder
                    : AppColorConstants.colorLevelChartBorder,
                fontWeight: FontWeight.bold,
              ),
            );
          },
    ),
  );

  Widget buildDsSpectrumLastSeenViewWithRefreshButton() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Flexible(
          child: buildLastSeenView(
              onTapTime: amplifierConfigurationHelper.dsSpectrumOnTapTime,
              apiStatus: amplifierConfigurationHelper.spectrumApiStatus,
              difference: amplifierConfigurationHelper.dsSpectrumDifferenceTime,
              isShow: amplifierConfigurationHelper.dsSpectrumIsShowText,
              updateTime: widget.amplifierItem.dsSpectrumUpdateTime,
              differenceMessage:
                  amplifierConfigurationHelper.dsSpectrumDataError != null
                      ? S.of(context).refreshFailedMessage
                      : null),
        ),
        AppRefresh(
          buttonColor: AppColorConstants.colorPrimary,
          loadingStatus: amplifierConfigurationHelper.spectrumApiStatus,
          onPressed: () {
            if(amplifierConfigurationHelper.spectrumApiStatus != ApiStatus.loading) {
              amplifierConfigurationHelper.getDsAutoAlignmentSpectrumData(
                  context, widget.amplifierItem.deviceEui, widget.amplifierItem,
                  isRefresh: true);
            }
          },
          enabled: (getDetectedStatusType(ampItem.status) ==
              DetectedStatusType.online),
        )
      ],
    );
  }

//-------------------------- Interstage Values View (Buttons)-------------------------------------------

  Widget ampInterstageValuesView() {
    int refreshIndex =
    widget.ampPageHelper.listTabs.indexWhere((tab) => tab.title == widget.amplifierItem.deviceEui);
    return ManualAlignmentPage(isSwitchOfAuto: amplifierConfigurationHelper.isDSSwitchOfAuto,
      isDSAlignment: true,
      screenLayoutType: screenLayoutType,
      amplifierController: amplifierController,
      isOffline : (getDetectedStatusType(ampItem.status) == DetectedStatusType.online),
      amplifierConfigurationHelper: amplifierConfigurationHelper,
      dsManualAlignmentItem:
          amplifierConfigurationHelper.dsManualAlignmentItem,
      onTapWrite: (DsManualAlignmentItem dsManualAlignmentItem) async {
        await amplifierConfigurationHelper.setDsManualAlignment(context, widget.amplifierItem.deviceEui, dsManualAlignmentItem,refreshIndex);
        await amplifierConfigurationHelper.getDsManualAlignment(
            context, widget.amplifierItem.deviceEui, refreshIndex,
            isFromSetAPI: (amplifierConfigurationHelper.isDSSwitchOfAuto == false),isRefreshDSSpectrum: true);
      },
      onTapSave: (DsManualAlignmentItem dsManualAlignmentItem) {
        amplifierConfigurationHelper.saveRevertDsManualAlignment(
            context, widget.amplifierItem.deviceEui, dsManualAlignmentItem,true,refreshIndex);
      },
      onTapRevert: (DsManualAlignmentItem dsManualAlignmentItem) {
        amplifierConfigurationHelper.saveRevertDsManualAlignment(
            context, widget.amplifierItem.deviceEui, dsManualAlignmentItem,false,refreshIndex);
      },
      onRefreshClicked: () {
        amplifierConfigurationHelper.getDsManualAlignment(context, widget.amplifierItem.deviceEui,refreshIndex,isFromSetAPI: (amplifierConfigurationHelper.isDSSwitchOfAuto == false));
      },
      buttonView: buildSwitchButtonView(),
      settingModel:amplifierConfigurationHelper.dsAlignmentSettingModel,
    );
  }

  dsManualAlignmentDraftMessageView() {
    if (widget.ampPageHelper.dsConfigurationDraftMessage.isEmpty) {
      return const SizedBox(height: 15);
    }
    return Align(
      alignment: Alignment.center,
      child: Padding(
        padding: const EdgeInsets.only(left: 8.0, bottom: 8.0),
        child: CustomPaint(
          painter: DottedBorderPainter(
            borderColor: AppColorConstants.colorLow,
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 8.0),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.error_outline, color: AppColorConstants.colorLightBlue, size: 15),
                const SizedBox(width: 5),
                Flexible(
                  child: AppText(
                    widget.ampPageHelper.dsConfigurationDraftMessage,
                    style: TextStyle(
                      color: AppColorConstants.colorDarkBlue,
                      fontSize: 12,
                      fontFamily: AppAssetsConstants.openSans,
                      fontWeight: getMediumBoldFontWeight(),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

//-------------------------- Down Stream View-------------------------------------------
  Widget downStreamView(
    AmpDownStreamItem ampDownStreamItem, {
    required String title,
    required AmplifierController controller,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: getSize(20)),
        Container(
          margin: EdgeInsets.only(
              right : screenLayoutType == ScreenLayoutType.desktop
                  ? MediaQuery.of(context).size.width * 0.4
                  : screenLayoutType == ScreenLayoutType.tablet
                      ? MediaQuery.of(context).size.width * 0.25
                      : 0),
          decoration: borderViewDecoration,
          child: Column(
            children: [
              commonTitleView(title: title),
              SizedBox(
                height: getSize(10),
              ),
              commonStreamingButtonView(
                title: S.of(context).gain,
                value: ampDownStreamItem.downGain ?? downGain,
                onDecrement: () {
                  if (ampDownStreamItem.downGain > 0) {
                    ampDownStreamItem.downGain--;
                    controller.updateDownStream(context, ampItem.deviceEui, ampDownStreamItem);
                  }
                  controller.update();
                },
                onIncrement: () {
                  ampDownStreamItem.downGain++;
                  controller.updateDownStream(context, ampItem.deviceEui, ampDownStreamItem);
                  controller.update();
                },
              ),
              commonStreamingButtonView(
                title: S.of(context).slop,
                value: ampDownStreamItem.downSlope ?? downSlope,
                onDecrement: () {
                  if (ampDownStreamItem.downSlope > 0) {
                    ampDownStreamItem.downSlope--;
                    controller.updateDownStream(context, ampItem.deviceEui, ampDownStreamItem);
                    controller.update();
                  }
                },
                onIncrement: () {
                  ampDownStreamItem.downSlope++;
                  controller.updateDownStream(context, ampItem.deviceEui, ampDownStreamItem);
                  controller.update();
                },
              ),
              commonStreamingDropDown(
                  onChanged: (value) {
                    ampDownStreamItem.agcConfig = null;
                    selectedAGCValue = onChangeDropDownValue(value, controller);
                    ampDownStreamItem.agcConfig = _agcValueUpdate(selectedAGCValue);
                    controller.updateDownStream(context, ampItem.deviceEui, ampDownStreamItem);
                    controller.update();
                  },
                  title: S.of(context).agcConfig,
                  selectedValue: ampDownStreamItem.agcConfig == null
                      ? selectedAGCValue
                      : agcConfigList[ampDownStreamItem.agcConfig - 1],
                  hintText: S.of(context).selectAgc,
                  items: agcConfigList),
              commonStreamingDropDown(
                  onChanged: (value) {
                    ampDownStreamItem.universalPlugin = null;
                    selectedUniversalValue = onChangeDropDownValue(value, controller);
                    ampDownStreamItem.universalPlugin =
                        _universalPluginUpdate(selectedUniversalValue);
                    controller.updateDownStream(context, ampItem.deviceEui, ampDownStreamItem);
                    controller.update();
                  },
                  title: S.of(context).universalPlugin,
                  selectedValue: ampDownStreamItem.universalPlugin == true
                      ? universalPluginList[0]
                      : universalPluginList[1],
                  hintText: S.of(context).selectUniversal,
                  items: universalPluginList),
            ],
          ),
        ),
      ],
    );
  }

  String onChangeDropDownValue(String value, AmplifierController controller) {
    String selectedValue = value;
    controller.update();
    return selectedValue;
  }

  int _agcValueUpdate(String? aGCValue) {
    if (aGCValue == AppStringConstants.thermal) {
      return 1;
    } else if (aGCValue == AppStringConstants.manual) {
      return 2;
    } else if (aGCValue == AppStringConstants.on) {
      return 3;
    }
    return 1;
  }

  bool _universalPluginUpdate(String? universalPluginValue) {
    if (universalPluginValue == AppStringConstants.present) {
      return true;
    } else if (universalPluginValue == AppStringConstants.absent) {
      return false;
    }
    return false;
  }


//--------------------------------------------OLD Widgets Code
  commonTitleView({required String title}) {
    return Container(alignment: Alignment.center,
      height: getSize(50),
      width: double.infinity,
      padding: EdgeInsets.only(top: getSize(13), left: getSize(20)),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(getSize(7)),
          topLeft: Radius.circular(getSize(7)),
        ),
      ),
      child: AppText(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          fontFamily: AppAssetsConstants.openSans,
          color: AppColorConstants.colorLightBlue,
        ),
      ),
    );
  }

  Widget commonStreamingButtonView(
      {required VoidCallback onIncrement,
      required VoidCallback onDecrement,
      required int value,
      required String title}) {
    return Padding(
        padding: EdgeInsets.only(left: getSize(20), right: getSize(20), bottom: getSize(10)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            AppText(
              title,
              style: const TextStyle(
                  color: AppColorConstants.colorDarkBlue,
                  fontSize: 14,
                  fontFamily: AppAssetsConstants.openSans,
                  fontWeight: FontWeight.w600),
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                AppButton(
                  padding: MaterialStateProperty.all<EdgeInsets>(EdgeInsets.zero),
                  minimumSize: MaterialStateProperty.all<Size>(const Size(15, 40)),
                  buttonName: '-',
                  buttonWidth: getSize(30),
                  buttonHeight: getSize(30),
                  fontSize: getSize(20),
                  onPressed: onDecrement,
                ),
                SizedBox(
                  width: getSize(10),
                ),
                Container(
                  alignment: Alignment.center,
                  width: getSize(60),
                  height: getSize(30),
                  decoration: BoxDecoration(
                      color: AppColorConstants.colorWhite,
                      borderRadius: BorderRadius.circular(5),
                      border: Border.all(color: AppColorConstants.colorH2.withOpacity(0.5))),
                  child: AppText(
                    "$value",
                    style: TextStyle(
                        fontWeight: getMediumFontWeight(), fontFamily: AppAssetsConstants.openSans),
                  ),
                ),
                SizedBox(
                  width: getSize(10),
                ),
                AppButton(
                  onPressed: onIncrement,
                  padding: MaterialStateProperty.all<EdgeInsets>(EdgeInsets.zero),
                  minimumSize: MaterialStateProperty.all<Size>(const Size(15, 40)),
                  buttonName: '+',
                  buttonWidth: getSize(30),
                  buttonHeight: getSize(30),
                  fontSize: getSize(20),
                ),
              ],
            ),
          ],
        ));
  }

  Widget commonStreamingDropDown(
      {required String title,
      required String hintText,
      String? selectedValue,
      required List<dynamic> items,
      required ValueChanged onChanged}) {
    return Padding(
        padding: EdgeInsets.only(
            left: getSize(20), right: getSize(20), bottom: getSize(10)),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: AppText(
                    title,
                    style: const TextStyle(
                        color: AppColorConstants.colorDarkBlue,
                        fontSize: 14,
                        fontFamily: AppAssetsConstants.openSans,
                        fontWeight: FontWeight.w600),
                  ),
                ),
                if (screenLayoutType != ScreenLayoutType.mobile)
                  Flexible(
                    child: CommonDropdownButton(iconColor:  AppColorConstants.colorH3,
                      selectedValue: selectedValue,
                      buttonHeight: getSize(32),
                      hintText: hintText,
                      items: items,
                      onChanged: onChanged,
                    ),
                  ),
              ],
            ),
            if (screenLayoutType == ScreenLayoutType.mobile)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: CommonDropdownButton(
                  selectedValue: selectedValue,
                  buttonHeight: getSize(32),
                  hintText: hintText,
                  items: items,
                  onChanged: onChanged,
                ),
              )
          ],
        ));
  }



  BoxDecoration borderViewDecoration = BoxDecoration(
    border: Border.all(color: AppColorConstants.colorChart, width: 1.8),
    borderRadius: const BorderRadius.all(Radius.circular(8)),
  );
}