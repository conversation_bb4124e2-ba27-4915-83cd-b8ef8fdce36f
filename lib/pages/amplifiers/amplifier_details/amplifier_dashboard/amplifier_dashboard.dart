import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:geolocator/geolocator.dart';
import 'package:intl/intl.dart';
import 'package:latlong2/latlong.dart';
import 'package:quantumlink_node/app_import.dart';
import '../../../../app/helper/location_helper.dart';
import '../../../../utils/dialog_utils.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart' as latlong;
import "package:universal_html/html.dart" as html;


class AmplifierDashboard extends StatefulWidget {
  final AmplifierDeviceItem amplifierItem;
  final AmplifierPageHelper ampPageHelper;

  const AmplifierDashboard(
      {super.key, required this.amplifierItem, required this.ampPageHelper});

  @override
  State<AmplifierDashboard> createState() => _AmplifierDashboard();
}

class _AmplifierDashboard extends State<AmplifierDashboard> {
  final MapController mapController = MapController();
  late ScreenLayoutType screenLayoutType;
  late AmplifierDeviceItem ampItem;
  AmplifierController amplifierController = Get.put<AmplifierController>(AmplifierController());
  List<String> amplifierInitializationState = [];
  double constraintsMaxWidth = 0;
  String image = "";

  bool isOffline() {
    return getDetectedStatusType(ampItem.status) == DetectedStatusType.offline;
  }

  Color getHeaderColor() {
    return isOffline()
        ? AppColorConstants.colorGray
        : AppColorConstants.colorLightBlue;
  }

  initializationStateList() {
    if (amplifierInitializationState.isEmpty) {
      amplifierInitializationState = [
        "DS ALIGNMENT UNKNOWN",
        "US ALIGNMENT UNKNOWN",
        "ALSC UNKNOWN",
        "LOCATION UNKNOWN",
      ];
    }
  }

  @override
  void initState() {
    super.initState();
    ampItem = widget.amplifierItem;
  }

  @override
  void dispose() {
    mapController.dispose();
    super.dispose();
  }

  RegExp locationRegExp = RegExp(
      ValidationUtils.locationRegExp);

  Widget deviceOfflineWidget() {
    return Padding(
      padding: const EdgeInsets.only(top:10.0),
      child: Center(
          child: errorMessageView(errorMessage: "Device Offline")),
    );
  }

  @override
  Widget build(BuildContext context) {
    initializationStateList();
    return GetBuilder<AmplifierController>(
      init: AmplifierController(),
      builder: (AmplifierController controller) {
        return ScreenLayoutTypeBuilder(
          builder: (context, screenType, constraints) {
            screenLayoutType = screenType;
            constraintsMaxWidth = constraints.maxWidth;

            return MergeSemantics(
              child: Stack(
                alignment: Alignment.topRight,
                children: [
                  ListView(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    children: [
                      if (isOffline()) deviceOfflineWidget(),
                      buildPlacementIdentityAndConfigView(),
                      buildAmplifierInfoView(),
                      buildTransponder(),
                      buildPowerSupply(),
                      if (screenLayoutType != ScreenLayoutType.desktop) ...[
                        if (ampItem
                                .ampDeviceSummary.result.deviceIdentity.model !=
                            null)
                          SizedBox(
                            height: 500,
                            child: Column(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(top: 12),
                                  child: AppText(
                                      "${S.of(context).deviceType} : ${ampItem.ampDeviceSummary.result.deviceIdentity.deviceType ?? ""}",
                                      style: TextStyle(
                                        color: AppColorConstants.colorDarkBlue,
                                        fontSize: 15,
                                        fontFamily: AppAssetsConstants.openSans,
                                        fontWeight: getMediumBoldFontWeight(),
                                      )),
                                ),
                                Flexible(
                                  child: AppImageAsset(
                                    image: buildAmplifierImage(ampItem
                                        .ampDeviceSummary.result.deviceIdentity),
                                    fit: BoxFit.fitHeight,
                                  ),
                                ),
                              ],
                            ),
                          ),
                      ],
                      buildBottomBorder(),
                    ],
                  ),
                  if (screenLayoutType == ScreenLayoutType.desktop) ...[
                    Row(
                      children: [
                        const Spacer(),
                        Expanded(
                            child: Padding(
                          padding:  EdgeInsets.only(top:  isOffline() ? 290:250 ),
                          child: Column(
                            children: [
                              if (ampItem.ampDeviceSummary.result.deviceIdentity.model != null)...[
                                Builder(
                                  builder: (context) {
                                    image = buildAmplifierImage(ampItem.ampDeviceSummary.result.deviceIdentity);
                                    return Column(
                                      children: [
                                        AppText(
                                            "${S.of(context).deviceType} : ${ampItem.ampDeviceSummary.result.deviceIdentity.deviceType ?? ""}",
                                            style: TextStyle(
                                              color: AppColorConstants.colorDarkBlue,
                                              fontSize: 15,
                                              fontFamily: AppAssetsConstants.openSans,
                                              fontWeight: getMediumBoldFontWeight(),
                                            ),
                                        ),
                                        AppImageAsset(
                                          image: image,
                                          fit: BoxFit.fitHeight,
                                        ),
                                      ],
                                    );
                                  }
                                ),
                              ],
                            ],
                          ),
                        )),
                      ],
                    ),
                  ],
                ],
              ),
            );
          },
        );
      },
    );
  }

  String buildAmplifierImage(DeviceIdentity deviceIdentity) {

    if (widget.ampPageHelper.amplifierDataSource.list.isEmpty) {
      return '';
    }
    var productId = widget.ampPageHelper.amplifierDataSource.list
        .firstWhere((element) =>
    element.deviceEui == widget.amplifierItem.deviceEui)
        .productId ??
        "";

    return getImageFromHex(productId);

    String modalName = deviceIdentity.model;
    if (modalName.contains('LE') || modalName.contains('Line Extender')) {
      return AppAssetsConstants.q18LineExtender;
    } else if (modalName.contains('HGD') || modalName.contains('Dual')) {
      return AppAssetsConstants.q18HGDExtender;
    } else if (modalName.contains('HGBT') || modalName.contains('Triple')) {
      return AppAssetsConstants.q18HGBT;
    } else { 
      return AppAssetsConstants.q18LineExtender;
    }
  }



  Widget buildPlacementIdentityAndConfigView() {
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            getPlacementIdentityView(
                title: S.of(context).ampDetails,
                deviceSummaryDetail: ampItem.ampDeviceSummary.result),
            if (screenLayoutType == ScreenLayoutType.desktop) ...[
              SizedBox(width: getSize(20)),
              getConfigView(
                  title: S.of(context).amplifierInitializationState,
                  deviceSummaryDetail: ampItem.ampDeviceSummary.result)
            ]
          ],
        ),
        if (screenLayoutType != ScreenLayoutType.desktop)
          Row(
            children: [
              getConfigView(
                  title: S.of(context).amplifierInitializationState,
                  deviceSummaryDetail: ampItem.ampDeviceSummary.result)
            ],
          )
      ],
    );
  }

  Widget buildAmplifierInfoView() {
    return Row(
      children: [
        getAmplifierInfoView(
            title: S.of(context).amplifierInfo,
            ampDeviceSummary: ampItem.ampDeviceSummary.result,firmwareImageInfo :ampItem.ampFirmwareImageInfo.result ),
        if (screenLayoutType == ScreenLayoutType.desktop) ...[
          SizedBox(width: getSize(20)),
          const Spacer()
        ],
      ],
    );
  }

  Widget buildTransponder() {
    return Row(
      children: [
        getTransponderView(
            title: S.of(context).transponder,
            transponderInfo: ampItem.transponderInfo.result, fwImageInfo: ampItem.transponderFWImageInfo.result),
        if (screenLayoutType == ScreenLayoutType.desktop) ...[
          SizedBox(width: getSize(20)),
          const Spacer()
        ],
      ],
    );
  }

  Widget buildPowerSupply() {
    return Row(
      children: [
        getPowerSupplyView(
            title: S.of(context).powerSupply,
            powerSupplyInfo: ampItem.ampDeviceSummary.result.powerSupplyInfo),
        if (screenLayoutType == ScreenLayoutType.desktop) ...[
          SizedBox(width: getSize(20)),
          const Spacer()
        ],
      ],
    );
  }

  Widget buildBottomBorder() {
    return Container(
      height: getSize(20),
      decoration: BoxDecoration(
        border: Border(
          bottom:
              BorderSide(color: AppColorConstants.colorLightBlue, width: 0.6),
        ),
      ),
    );
  }

  Widget buildAmplifierStatusAndAlarms() {
    return Row(
      children: [
        getAmplifierStatusView(
            title: S.of(context).ampStatus,
            sensorDataItem: ampItem.sensorDataItem),
        if (screenLayoutType == ScreenLayoutType.desktop) ...[
          SizedBox(width: getSize(20)),
        ],
      ],
    );
  }

  // GET LAST SEEN TIME FORMAT
  String getLastSeenTime() {
    if (widget.ampPageHelper.amplifierDataSource.list.isEmpty) {
      return '';
    }
    double timestamp = widget.ampPageHelper.amplifierDataSource.list
            .firstWhere((element) =>
                element.deviceEui == widget.amplifierItem.deviceEui)
            .lastSeen ??
        0;
    int microseconds = (timestamp * 1000000).toInt();
    DateTime dateTime = DateTime.fromMicrosecondsSinceEpoch(microseconds);
    DateFormat formatter = DateFormat(formatDateTimeWithMilliseconds);
    return formatter.format(dateTime);
  }

  BoxDecoration infoViewDecoration = BoxDecoration(
    color: AppColorConstants.colorWhite,
    border: Border.all(color: AppColorConstants.colorDotLine, width: 1.5),
    borderRadius: BorderRadius.circular(
      getSize(10),
    ),
  );

  String getDiplexInfoFromId(int id) {
    switch (id) {
      case 0:
        return '42/54 MHz'; //SAMP_SPLIT_E__SPLIT_42_54
      case 1:
        return '65/85 MHz'; //SAMP_SPLIT_E__SPLIT_65_85
      case 2:
        return "85/108 MHz"; //SAMP_SPLIT_E__SPLIT_85_108
      case 3:
        return "204/258 MHz"; //SAMP_SPLIT_E__SPLIT_204_258
      case 4:
        return "300/372 MHz"; //SAMP_SPLIT_E__SPLIT_300_372
      case 5:
        return "396/492 MHz"; //SAMP_SPLIT_E__SPLIT_396_492
      case 6:
        return "492/606 MHz"; //SAMP_SPLIT_E__SPLIT_492_606
      case 7:
        return "684/834 MHz"; //SAMP_SPLIT_E__SPLIT_684_834
      default:
        return "";
    }
  }

  getText(String? val){
    if(val == null || val == ""){
      return "Not set";
    }
    return val;
  }

  Widget getPlacementIdentityView(
      {required String title,
      required DeviceSummaryDetail deviceSummaryDetail}) {
    UserDeviceInfo userDeviceInfo = deviceSummaryDetail.userDeviceInfo;
    String diplexInfo = getDiplexInfoFromId(deviceSummaryDetail.diplexInfo);
    deviceSummaryDetail.diplexInfo;
    String ampDeviceMode = deviceSummaryDetail.bwMode == null
        ? ""
        : (deviceSummaryDetail.bwMode == 0
            ? "1.2"
            : (deviceSummaryDetail.bwMode == 1 ? "1.8" : ""));
    return Expanded(
      child: Padding(
        padding: EdgeInsets.only(top: getSize(15), bottom: getSize(5)),
        child: Container(
          decoration: infoViewDecoration,
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  commonTitleView(title: title, width: 250),
                  IconButton(
                    color: getHeaderColor(),
                    icon: const Icon(Icons.edit),
                    onPressed: isOffline()? null : () {
                      widget.ampPageHelper.isMapPickerDisplay.value = false;
                      var placement = TypeDDItem.fromName(
                          getPlacementsDropdownValue(userDeviceInfo.placement));
                      showPlacementIdentityDlg(context, placement,
                          (String deviceAlias,
                              String description,
                              String location,
                              TypeDDItem selectType) async {
                        print("---SElect location ---- ${location}");
                            userDeviceInfo.location = location;
                            userDeviceInfo.deviceAlias = deviceAlias;
                            userDeviceInfo.description = description;
                            userDeviceInfo.placement = getPlacementsValue(selectType.name.toString());
                            ampItem.placementAndIdentityError = null;
                            ampItem.dashboardRefreshStatus = ApiStatus.loading;
                            amplifierController.update();
                        int placements =
                            getPlacementsValue(selectType.name.toString());

                        AmplifierDeviceItem2 amplifierItem =
                            AmplifierDeviceItem2(
                                deviceAlias: deviceAlias,
                                description: description,
                                location: location,
                                placement: placements);
                        try {
                          widget.ampPageHelper.initializePAI(widget.amplifierItem);
                          dynamic value = await amplifierController.updateAmpPlacements(
                              context, widget.amplifierItem.deviceEui, amplifierItem);
                          ampItem.differenceTimeOfPAI =
                              DateTime.now().difference(widget.amplifierItem.onTapTimeOfPAI!);
                          ampItem.refreshTimerOfPAI =
                              Timer(const Duration(seconds: 3), () {
                                ampItem.isShowTextOfPAI = false;
                                amplifierController.update();
                              });
                          if (value is AmplifierDeviceItem2) {
                            userDeviceInfo.location = location;
                            userDeviceInfo.placement = placements;
                            amplifierController.update();
                            int refreshIndex = widget.ampPageHelper.listTabs
                                .indexWhere((tab) => tab.title == ampItem.deviceEui);
                            await widget.ampPageHelper.getPlacementAndIdentity(
                                context, widget.amplifierItem.deviceEui, refreshIndex,
                                isRefresh: true);
                          } else {
                            ampItem.placementAndIdentityError = value;
                          }
                        } catch (e) {
                          ampItem.placementAndIdentityError =
                              S.of(context).errorDeviceInfoMessage;
                          ampItem.differenceTimeOfPAI =
                              DateTime.now().difference(widget.amplifierItem.onTapTimeOfPAI!);
                          ampItem.refreshTimerOfPAI =
                              Timer(const Duration(seconds: 3), () {
                                ampItem.isShowTextOfPAI = false;
                                amplifierController.update();
                              });
                        } finally {
                          ampItem.dashboardRefreshStatus = ApiStatus.success;
                          amplifierController.update();
                        }
                      }, userDeviceInfo);
                    },
                  ),
                ],
              ),
              commonSubTitleView(
                  subTitle: S.of(context).assetID,
                  value: getText(userDeviceInfo.assetId)),
              if (AppConfig.shared.isQLCentral)
                commonSubTitleView(subTitle: S.of(context).site, value: "default"),
              commonSubTitleView(
                subTitle: S.of(context).diplexFilter,
                value: getText(diplexInfo),
              ),
              commonSubTitleView(
                subTitle: S.of(context).amplifierMode,
                value: deviceSummaryDetail.bwMode != null
                    ? "$ampDeviceMode ${S.of(context).ghZ}"
                    : "Not set",
              ),
              commonSubTitleView(
                  subTitle: S.of(context).location,
                  value: getText(userDeviceInfo.location?.trim() != "," ? userDeviceInfo.location : "Not set")),
              commonSubTitleView(
                  subTitle: S.of(context).placement,
                  value: getPlacementsDropdownValue(userDeviceInfo.placement)),
              commonSubTitleView(
                  subTitle: S.of(context).deviceAlias,
                  value: getText(userDeviceInfo.deviceAlias)),
              commonSubTitleView(
                  subTitle: S.of(context).description,
                  value: getText(userDeviceInfo.description)),
              errorRefreshView(
                ifCondition: ampItem.placementAndIdentityError != null,
                elseCondition: screenLayoutType == ScreenLayoutType.desktop,
                errorMessage: ampItem.placementAndIdentityError ?? "",
                refreshWidget: buildPAILastSeenWithRefreshButton(),
                screenLayout: screenLayoutType,
              ),
            ],
          ),
        ),
      ),
    );
  }

  buildPAILastSeenWithRefreshButton() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Flexible(
          child: buildLastSeenView(
              isShow: widget.amplifierItem.isShowTextOfPAI,
              difference: widget.amplifierItem.differenceTimeOfPAI,
              onTapTime: widget.amplifierItem.onTapTimeOfPAI,
              apiStatus: ampItem.dashboardRefreshStatus == ApiStatus.loading
                  ? ampItem.dashboardRefreshStatus
                  : ampItem.pAInfoRefreshStatus,
              updateTime: ampItem.placementAndIdentityUpdateTime,
              textColor: ampItem.placementAndIdentityError == null
                  ? AppColorConstants.colorGrn
                  : AppColorConstants.colorAppbar,
               differenceMessage: ampItem.placementAndIdentityError != null ? S.of(context).refreshFailedMessage: null),
        ),
        Padding(
          padding: const EdgeInsets.only(right: 8.0),
          child: AppRefresh(
            buttonColor: AppColorConstants.colorPrimary,
            loadingStatus: ampItem.dashboardRefreshStatus == ApiStatus.loading
                ? ampItem.dashboardRefreshStatus
                : ampItem.pAInfoRefreshStatus,
            onPressed: () {
              int refreshIndex = widget.ampPageHelper.listTabs
                  .indexWhere((tab) => tab.title == ampItem.deviceEui);
              widget.ampPageHelper.getPlacementAndIdentity(
                  context, ampItem.deviceEui, refreshIndex,
                  isRefresh: true);
            },
            enabled: !isOffline(),
          ),
        )
      ],
    );
  }

  int getPlacementsValue(String value) {
    switch (value) {
      case AppStringConstants.selectTypeUnknown:
        return 0;
      case AppStringConstants.selectTypeAerial:
        return 1;
      case AppStringConstants.selectTypeUnderground:
        return 2;
      case AppStringConstants.selectTypeIndoorCommercial:
        return 3;
      case AppStringConstants.selectTypeResidential:
        return 4;
      case AppStringConstants.selectTypeOther:
        return 10;
      default:
        return 0;
    }
  }

  String getPlacementsDropdownValue(int? value) {
    if (value == null) {
      return AppStringConstants.selectTypeUnknown;
    }
    switch (value) {
      case 0:
        return AppStringConstants.selectTypeUnknown;
      case 1:
        return AppStringConstants.selectTypeAerial;
      case 2:
        return AppStringConstants.selectTypeUnderground;
      case 3:
        return AppStringConstants.selectTypeIndoorCommercial;
      case 4:
        return AppStringConstants.selectTypeResidential;
      case 10:
        return AppStringConstants.selectTypeOther;
      default:
        return AppStringConstants.selectTypeUnknown;
    }
  }

  showPlacementIdentityDlg(
    context,
    TypeDDItem? typeSelectedDDItem,
    Function fun,
    UserDeviceInfo? userDeviceInfo,
  ) {
    {
      TypeDDItem typeDDItem = typeSelectedDDItem ??
          TypeDDItem.fromName(AppStringConstants.selectTypeUnknown);
      List<dynamic> listTypeDDItem = [
        TypeDDItem.fromName(AppStringConstants.selectTypeUnknown),
        TypeDDItem.fromName(AppStringConstants.selectTypeAerial),
        TypeDDItem.fromName(AppStringConstants.selectTypeUnderground),
        TypeDDItem.fromName(AppStringConstants.selectTypeIndoorCommercial),
        TypeDDItem.fromName(AppStringConstants.selectTypeResidential),
        TypeDDItem.fromName(AppStringConstants.selectTypeOther),
      ];

      TextEditingController txtDeviceAlias = TextEditingController();
      TextEditingController txtDescription = TextEditingController();
      TextEditingController txtLocation = TextEditingController();
      bool isLocationNotEmpty =false;

      LatLng _initialPosition = defaultLatLng;
      LatLng? _currantPosition ;
      LatLng? _setPosition ;
      if (userDeviceInfo != null) {
        print("ISO 6709 format: ----------- ${userDeviceInfo.location}");

        txtDeviceAlias.text = userDeviceInfo.deviceAlias ?? "";
        txtDescription.text = userDeviceInfo.description ?? "";
          isLocationNotEmpty = (userDeviceInfo.location.toString().isNotEmpty &&
            userDeviceInfo.location != null &&
            userDeviceInfo.location?.trim() != ",");
        txtLocation.text = isLocationNotEmpty
            ? userDeviceInfo.location.toString()
            : "";
        try {
          _initialPosition = parseLatLng(userDeviceInfo.location??"");
        }catch(e){
          _initialPosition = defaultLatLng;
        }
      }
      Future<void> getCurrentLocation(StateSetter snapshot) async {
        try {
          _setPosition = null;
          Position position = await Geolocator.getCurrentPosition(
            locationSettings: const LocationSettings(accuracy: LocationAccuracy.high),
          );
          LatLng latLng = LatLng(position.latitude, position.longitude);
          _currantPosition = latLng;
          mapController.move(latLng, 16);
          String iso6709Value = latLngToIso6709(position.latitude, position.longitude);
          txtLocation.text = iso6709Value;
          snapshot(() {});
        } catch (e) {
          debugLogs('Error getting location: $e');
        }
      }

      final formKey = GlobalKey<FormState>();

      return showDialog(
        context: context,
        builder: (contextDialog) {
          return GetBuilder<ProvisionController>(
            init: ProvisionController(),
            builder: (ProvisionController controller) {
              return AlertDialog(
                surfaceTintColor: AppColorConstants.colorWhite,
                backgroundColor: AppColorConstants.colorWhite,
                insetPadding: const EdgeInsets.symmetric(horizontal: 8.0),
                contentPadding: EdgeInsets.zero,
                titlePadding: EdgeInsets.zero,
                actionsPadding: EdgeInsets.zero,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(5)),
                title: Padding(
                  padding: const EdgeInsets.only(
                      left: 24, top: 24, right: 20, bottom: 5),
                  child: Row(
                    children: [
                      SizedBox(
                        width: getSize(2),
                        height: getSize(8),
                      ),
                      CircleAvatar(
                        maxRadius: 18,
                        backgroundColor: AppColorConstants.colorLightBlue,
                        child: Icon(
                          size: 18,
                          Icons.check,
                          color: AppColorConstants.colorWhite,
                        ),
                      ),
                      SizedBox(
                        width: getSize(18),
                      ),
                      Expanded(
                        child: AppText(
                          S.of(contextDialog).ampDetails,
                          maxLines: 1,
                          style: TextStyle(
                              color: AppColorConstants.colorAppbar,
                              fontWeight: FontWeight.w700,
                              fontSize: getSize(23),
                              fontFamily: AppAssetsConstants.openSans),
                        ),
                      ),
                      IconButton(
                          onPressed: () {
                            goBack();
                          },
                          icon: const Icon(Icons.close)),
                    ],
                  ),
                ),
                content: StatefulBuilder(builder: (context, snapshot) {
                  return SingleChildScrollView(
                    child: Padding(
                      padding: EdgeInsets.only(
                          left: 24,
                          right: 24,
                          bottom: MediaQuery.of(context).viewInsets.bottom),
                      //padding: const EdgeInsets.all(8.0),
                      child: Form(
                        key: formKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Container(
                              width: 500,
                              padding: const EdgeInsets.only(
                                  left: 16, right: 16, top: 16),
                              child: Column(
                                children: [
                                  const SizedBox(height: 8),
                                  const SizedBox(height: 25),
                                  dialogBoxEdit(
                                      title: S.of(context).deviceAlias,
                                      hintText: S.of(context).deviceAlias,
                                      isReadOnly: false,
                                      validator: (value) {
                                        if (value == null || value.isEmpty) {
                                          return S.of(context).thisFieldIsRequired;
                                        }else {
                                          if (value.length > 31) {
                                            return "The field should be less than 32 characters";
                                          }
                                        }
                                        return null;
                                      },
                                      controller: txtDeviceAlias),
                                  const SizedBox(height: 25),
                                  dialogBoxEdit(
                                      title: S.of(context).description,
                                      hintText: S.of(context).description,
                                      isReadOnly: false,
                                      validator: (value) {
                                        if (value == null || value.isEmpty) {
                                          return S.of(context).thisFieldIsRequired;
                                        } else {
                                          if (value.length > 128) {
                                            return "The field should be less than 129 characters";
                                          }
                                        }
                                        return null;
                                      },
                                      controller: txtDescription),
                                  const SizedBox(height: 25),
                                  Obx(() {
                                      return Column(
                                        children: [
                                          dialogBoxEdit(
                                            title: S.of(context).locationLatLong,
                                            hintText: "ISO 6709 Format: +/-DDMMSS.S,+/-DDDMMSS.S",
                                            controller: txtLocation,
                                            isReadOnly: widget.ampPageHelper.isMapPickerDisplay.value,
                                            validator: (value) {
                                              final regex = RegExp(r'^[+-]?\d+(\.\d+)?,\s*[+-]?\d+(\.\d+)?$');
                                              if (!regex.hasMatch(value)) {
                                                return S.of(context).locationValidateMessage;
                                              }
                                              if (value.length > 33) {
                                                return S.of(context).locationFieldLessThen33;
                                              }
                                              return null;
                                            },
                                            isMapIconDisplay: true,
                                            locationoOnPressed: () async {
                                            widget.ampPageHelper.isMapPickerDisplay.value =
                                                !widget.ampPageHelper.isMapPickerDisplay.value;
                                            if (widget.ampPageHelper.isMapPickerDisplay.value) {
                                              if (!await Geolocator.isLocationServiceEnabled()) {
                                                LocationPermission permission =
                                                    await Geolocator.requestPermission();
                                              }
                                              await getCurrentLocation(snapshot);
                                            }
                                          },
                                          amplifierPageHelper: widget.ampPageHelper,
                                          ),
                                          if(widget.ampPageHelper.isMapPickerDisplay.value)...[
                                            const SizedBox(height: 25),
                                            SizedBox(
                                              height: 400,
                                              child: PopScope(
                                                canPop: Navigator.of(context).userGestureInProgress,
                                                child: Stack(
                                                  children: [
                                                    FlutterMap(
                                                      mapController: mapController, // You need to define this in your state
                                                      options: MapOptions(
                                                        center: _initialPosition, // LatLng
                                                        zoom: !isLocationNotEmpty ? 10 : 18.0,
                                                        minZoom: 4,
                                                        maxZoom: 19,
                                                        onTap: (tapPosition, position) {
                                                          _setPosition = position;
                                                          mapController.move(position, mapController.zoom);
                                                          debugPrint("LatLng format: ----------- ${position.latitude},${position.longitude}");
                                                          String iso6709Value = latLngToIso6709(position.latitude, position.longitude);
                                                          txtLocation.text = iso6709Value;
                                                          snapshot(() {});
                                                        },
                                                      ),
                                                      children: [
                                                        TileLayer(
                                                          urlTemplate: AppAssetsConstants.openStreetMapUrl,
                                                        ),
                                                      if (isLocationNotEmpty)
                                                        MarkerLayer(
                                                          markers: [
                                                            Marker(
                                                              point: _initialPosition,
                                                              width: 40,
                                                              height: 40,
                                                              child:  Icon(Icons.location_on,
                                                                  color: AppColorConstants.colorRed, size: 40),
                                                            ),
                                                          ],
                                                        ),
                                                      if (_setPosition != null)
                                                        MarkerLayer(
                                                          markers: [
                                                            Marker(
                                                              point: _setPosition!,
                                                              width: 40,
                                                              height: 40,
                                                              child: Icon(Icons.location_on,
                                                                  color: AppColorConstants
                                                                      .colorPrimary,
                                                                  size: 40),
                                                            ),
                                                          ],
                                                        ),
                                                      if (_currantPosition != null)
                                                        MarkerLayer(
                                                          markers: [
                                                            Marker(
                                                              point: _currantPosition!,
                                                              width: 40,
                                                              height: 40,
                                                              child: Icon(Icons.my_location,
                                                                  color: AppColorConstants
                                                                      .colorLightBlue,
                                                                  size: 35),
                                                            ),
                                                          ],
                                                        )
                                                    ],
                                                    ),
                                                    Align(
                                                      alignment: Alignment.bottomLeft,
                                                      child: Padding(
                                                          padding:  EdgeInsets.all(8.0),
                                                          child: Column(
                                                            mainAxisAlignment: MainAxisAlignment.end,
                                                            children: [
                                                          InkWell(
                                                            onTap: () async {
                                                              await getCurrentLocation(snapshot);
                                                            },
                                                            child: Container(
                                                                  child: Icon(Icons.my_location),
                                                                  decoration: BoxDecoration(
                                                                      color: AppColorConstants.colorWhite,
                                                                      borderRadius: BorderRadius.all(Radius.circular(8)),
                                                                      border: Border.all(
                                                                          color: AppColorConstants
                                                                              .colorH2)),
                                                                  padding: EdgeInsets.all(5),
                                                                ),
                                                              ),
                                                              const SizedBox(height: 10,),
                                                              Opacity(
                                                                opacity: isLocationNotEmpty ? 1.0 : 0.6, // dim when disabled
                                                                child: FloatingActionButton(
                                                                  tooltip: AppStringConstants.myLocation,
                                                                  backgroundColor: Theme.of(context).primaryColor,
                                                                  foregroundColor: Theme.of(context).colorScheme.onPrimary,
                                                                  onPressed: isLocationNotEmpty
                                                                      ? () async {
                                                                    _setPosition = null;
                                                                    mapController.move(_initialPosition, 16);
                                                                    String iso6709Value = latLngToIso6709(
                                                                        _initialPosition.latitude, _initialPosition.longitude);
                                                                    txtLocation.text = iso6709Value;
                                                                    snapshot(() {});
                                                                  }
                                                                      : null, // disables the FAB
                                                                  child: const Icon(Icons.location_on),
                                                                ),
                                                              )
                                                            ],
                                                          ),
                                                        ),
                                                    ),
                                                    openStreetMapCopyRightLinkView()
                                                  ],
                                                ),
                                              ),
                                            )
                                        ]else...[Container()],
                                        ],
                                      );
                                    }
                                  ),
                                  const SizedBox(height: 25),
                                  Row(
                                    children: [
                                      Expanded(
                                        flex: 2,
                                        child: AppText(S.of(context).placement,
                                            style: TextStyle(
                                                fontFamily:
                                                    AppAssetsConstants.openSans,
                                                fontSize: 14,
                                                fontWeight: FontWeight.w500,
                                                color: AppColorConstants
                                                    .colorBlack)),
                                      ),
                                      const SizedBox(
                                        width: 20,
                                      ),
                                      Expanded(
                                        flex: 5,
                                        child: getAppDropDown(
                                            typeDDItem, listTypeDDItem, (n) {
                                          typeDDItem = TypeDDItem.fromName(n);
                                          snapshot(() {});
                                        },
                                            fontColor:
                                                AppColorConstants.colorBlack,
                                            dropdownButtonColor:
                                                AppColorConstants.colorWhite,
                                            dropdownColor:
                                                AppColorConstants.colorWhite,
                                            borderRadius: 4),
                                      )
                                    ],
                                  ),
                                  const SizedBox(height: 25),
                                ],
                              ),
                            ),
                            const SizedBox(height: 24),
                          ],
                        ),
                      ),
                    ),
                  );
                }),
                actions: [
                  Container(
                    color: AppColorConstants.colorWhiteShade,
                    child: Row(
                      children: [
                        const Spacer(),
                        Container(
                          padding: const EdgeInsets.only(
                              left: 16, bottom: 16, top: 10),
                          child: AppButton(
                            fontSize: 14,
                            buttonWidth: 50,
                            buttonHeight: 35,
                            fontFamily: AppAssetsConstants.openSans,
                            buttonName: S.of(contextDialog).cancel,
                            fontColor: AppColorConstants.colorBlackBlue,
                            onPressed: () {
                              goBack();
                            },
                            borderColor: AppColorConstants.colorBackgroundDark,
                            buttonColor: AppColorConstants.colorWhite1,
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.only(
                              left: 16, right: 16, bottom: 16, top: 8),
                          child: AppButton(
                            fontSize: 14,
                            buttonWidth: 50,
                            buttonHeight: 35,
                            fontFamily: AppAssetsConstants.openSans,
                            buttonName: S.of(contextDialog).update,
                            onPressed: () {
                              if (formKey.currentState!.mounted) {
                                if (formKey.currentState!.validate()) {
                                  fun.call(
                                      txtDeviceAlias.text.toString(),
                                      txtDescription.text.toString(),
                                      txtLocation.text.toString(),
                                      typeDDItem,);
                                  goBack();
                                }
                              }
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              );
            },
          );
        },
      );
    }
  }
  Widget openStreetMapCopyRightLinkView(){
    return Positioned(
      bottom: 5,
      right: 5,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.8),
          borderRadius: BorderRadius.circular(4),
        ),
        child: RichText(
          text: TextSpan(
            style:  TextStyle(fontSize: 12, color: AppColorConstants.colorBlackBlue,),
            children: [
              TextSpan(text: '${S.of(context).openStreetMap}',style: const TextStyle(
                color: Colors.blue,
                decoration: TextDecoration.underline,
              ),
                recognizer: TapGestureRecognizer()
                  ..onTap = () {
                    html.window.open(AppAssetsConstants.openStreetMapCopyRightURL, "");
                  },),
              TextSpan(text: ' ${S.of(context).contributors}',),
            ],
          ),
        ),
      ),
    );
  }
  LatLng getLatLong(String? value){
    if(value == null){
      return defaultLatLng;
    }
    if(value.split(",").length<=1){
      return defaultLatLng;
    }
    return LatLng(double.parse(value.split(",")[0]), double.parse(value.split(",")[1]));
  }
  String getFactoredValue(int value) {
    return (value / 1000).toStringAsFixed(2);
  }

  Widget getAmplifierStatusView(
      {required String title, required SensorDataItem sensorDataItem}) {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.only(top: getSize(25), bottom: getSize(5)),
        child: Container(
          decoration: infoViewDecoration,
          child: Column(
            children: [
              commonTitleView(title: title),
              commonSubTitleView(
                  subTitle: S.of(context).acVoltage,
                  value: sensorDataItem.acVoltage != null
                      ? "${getFactoredValue(sensorDataItem.acVoltage)} ${S.of(context).volt}"
                      : ""),
              commonSubTitleView(
                  subTitle: S.of(context).twentyFourDC,
                  value: sensorDataItem.twentyFourVDC != null
                      ? "${getFactoredValue(sensorDataItem.twentyFourVDC)} ${S.of(context).volt}"
                      : ""),
              commonSubTitleView(
                  subTitle: S.of(context).fiveVDC,
                  value: sensorDataItem.fiveVDC != null
                      ? "${getFactoredValue(sensorDataItem.fiveVDC)} ${S.of(context).volt}"
                      : ""),
              commonSubTitleView(
                  subTitle: S.of(context).threeVDc,
                  value: sensorDataItem.threeVDc != null
                      ? "${getFactoredValue(sensorDataItem.threeVDc)} ${S.of(context).volt}"
                      : ""),
              commonSubTitleView(
                  subTitle: S.of(context).temperature,
                  value: sensorDataItem.temperature != null
                      ? "${getFactoredValue(sensorDataItem.temperature)} \u2103"
                      : ""),
              (ampItem.ampSensorError != null)
                  ? CommonAPIErrorView(
                      errorMessage: ampItem.ampSensorError ?? "")
                  : SizedBox(height: getSize(40)),
            ],
          ),
        ),
      ),
    );
  }

  Widget getAmplifierInfoView(
      {required String title, required DeviceSummaryDetail ampDeviceSummary, required FirmwareImageResult firmwareImageInfo}) {
    DeviceIdentity deviceIdentity = ampDeviceSummary.deviceIdentity;
    UserDeviceInfo userDeviceInfo = ampDeviceSummary.userDeviceInfo;
    VersionInfo versionInfo = ampDeviceSummary.versionInfo;
    return Expanded(
      child: Padding(
        padding: EdgeInsets.only(top: getSize(20)),
        child: Container(
          decoration: infoViewDecoration,
          child: Column(
            children: [
              commonTitleView(title: title),
              commonSubTitleView(
                  subTitle: S.of(context).model,
                  value: getText(deviceIdentity.model),isFlex: screenLayoutType == ScreenLayoutType.mobile ),
              commonSubTitleView(
                  subTitle: S.of(context).serial,
                  value: getText(deviceIdentity.serialNum) ),
              commonSubTitleView(
                  subTitle: S.of(context).assetID,
                  value: getText(userDeviceInfo.assetId)),
              commonSubTitleView(
                  subTitle: S.of(context).mfgDate,
                  value: getText(deviceIdentity.mfgDateTime)),
              commonSubTitleView(
                  subTitle: S.of(context).hWVersion,
                  value: getText(versionInfo.hwVersion)),
              commonSubTitleView(
                  subTitle: S.of(context).fWVersion,
                  value: getText(versionInfo.fwVersion)),
              commonSubTitleView(
                  subTitle: S.of(context).apiVersion,
                  value: getText(versionInfo.apiVersion)),
              StreamBuilder<String>(
                  stream: widget.ampPageHelper.updateStreamView,
                  builder: (context, snapshot) {
                    String time = getLastSeenTime();
                    return commonSubTitleView(
                      subTitle: S.of(context).lastSeen,
                      value: time,
                    );
                  }),
              errorRefreshView(
                ifCondition: ampItem.ampDeviceSummaryError != null,
                elseCondition: screenLayoutType == ScreenLayoutType.desktop,
                errorMessage: ampItem.ampDeviceSummaryError ?? "",
                refreshWidget: buildAMPILastSeenWithRefreshButton(),
                screenLayout: screenLayoutType,
              ),
              if (ampItem.isShowSwitchBankAndReboot)
                Column(
                  children: [
                    const Padding(
                      padding: EdgeInsets.symmetric(horizontal:15.0),
                    child: Divider(thickness: 2,),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(left: 25,top: 10,bottom: 10),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if(screenLayoutType != ScreenLayoutType.desktop)
                          AppText(
                            S.of(context).ampVersion,
                            style: TextStyle(
                                fontFamily: AppAssetsConstants.openSans,
                                fontWeight: getMediumBoldFontWeight(),
                                letterSpacing: 0.32,
                                color: getHeaderColor(),
                                fontSize: getSize(18)),
                          ),
                          Row(
                            mainAxisAlignment: (screenLayoutType == ScreenLayoutType.desktop)
                                ? MainAxisAlignment.spaceBetween
                                : MainAxisAlignment.end,
                            children: [
                              if (screenLayoutType == ScreenLayoutType.desktop)
                                Expanded(
                                  child: AppText(
                                    S.of(context).ampVersion,
                                    style: TextStyle(
                                        fontFamily: AppAssetsConstants.openSans,
                                        fontWeight: getMediumBoldFontWeight(),
                                        letterSpacing: 0.32,
                                        color: getHeaderColor(),
                                        fontSize: getSize(18)),
                                  ),
                                ),
                              buildCommonSwitchButtonView(
                                  isValue: ampItem.isAMPBankReboot,
                                status: ampItem.ampBankRebootStatus,
                                f: () {
                                  widget.ampPageHelper.ampSetSwitchBankReboot(ampItem);
                                }),
                          ],
                        ),
                      ],
                    ),
                  ),
                  commonSubTitleView(
                      subTitle: S.of(context).activeVersion,
                      value: getText("${firmwareImageInfo.activeVersion??""}")),
                  commonSubTitleView(
                      subTitle: S.of(context).backUpVersion,
                      value: getText("${firmwareImageInfo.backupVersion??""}")),
                  errorRefreshView(
                    ifCondition: ampItem.ampImageInfoError != null,
                    elseCondition: screenLayoutType == ScreenLayoutType.desktop,
                    errorMessage: ampItem.ampImageInfoError ?? "",
                    refreshWidget: buildAmpFWImageINfoLastSeenWithRefreshButton(),
                    screenLayout: screenLayoutType,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  buildAMPILastSeenWithRefreshButton() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Flexible(
          child: buildLastSeenView(
              isShow: ampItem.isShowTextOfAI,
              difference:ampItem.differenceTimeOfAI,
              onTapTime: ampItem.onTapTimeOfAI,
              apiStatus: ampItem.aMPInfoRefreshStatus,
              updateTime: ampItem.aMPInfoUpdateTime,
              differenceMessage:
                  ampItem.ampDeviceSummaryError != null ? S.of(context).refreshFailedMessage : null,
              textColor: ampItem.ampDeviceSummaryError == null
                  ? AppColorConstants.colorGrn
                  : AppColorConstants.colorAppbar),
        ),
        Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: AppRefresh(
              buttonColor: AppColorConstants.colorPrimary,
              loadingStatus: ampItem.aMPInfoRefreshStatus,
              onPressed: () {
                int refreshIndex = widget.ampPageHelper.listTabs
                    .indexWhere((tab) => tab.title == ampItem.deviceEui);
                widget.ampPageHelper.getAmplifierInformation(
                    context, ampItem.deviceEui, refreshIndex,
                    isRefresh: true);
              },
              enabled: !isOffline(),
            )),
      ],
    );
  }
  buildAmpFWImageINfoLastSeenWithRefreshButton() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Flexible(
          child: buildLastSeenView(
              isShow: ampItem.isShowTextOfAmpImageInfo,
              difference:ampItem.differenceTimeOfAmpImageInfo,
              onTapTime: ampItem.onTapTimeOfAmpImageInfo,
              apiStatus: ampItem.ampFWImageInfoStatus,
              updateTime: ampItem.ampImageInfoUpdateTime,
              differenceMessage:
              ampItem.ampImageInfoError != null ? S.of(context).refreshFailedMessage : null,
              textColor: ampItem.ampImageInfoError == null
                  ? AppColorConstants.colorGrn
                  : AppColorConstants.colorAppbar),
        ),
        Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: AppRefresh(
              buttonColor: AppColorConstants.colorPrimary,
              loadingStatus: ampItem.ampFWImageInfoStatus,
              onPressed: () {
                int refreshIndex = widget.ampPageHelper.listTabs
                    .indexWhere((tab) => tab.title == ampItem.deviceEui);
                widget.ampPageHelper.getCheckAmplifierFWImage(
                     ampItem.deviceEui, refreshIndex,
                    isRefresh: true);
              },
              enabled: !isOffline(),
            )),
      ],
    );
  }
  buildTransponderFWImageINfoLastSeenWithRefreshButton() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Flexible(
          child: buildLastSeenView(
              isShow: ampItem.isShowTextOfTIImageInfo,
              difference:ampItem.differenceTimeOfTIImageInfo,
              onTapTime: ampItem.onTapTimeOfTIImageInfo,
              apiStatus: ampItem.transponderFWImageInfoStatus,
              updateTime: ampItem.tiImageInfoUpdateTime,
              differenceMessage:
              ampItem.transponderImageInfoError != null ? S.of(context).refreshFailedMessage : null,
              textColor: ampItem.transponderImageInfoError == null
                  ? AppColorConstants.colorGrn
                  : AppColorConstants.colorAppbar),
        ),
        Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: AppRefresh(
              buttonColor: AppColorConstants.colorPrimary,
              loadingStatus: ampItem.transponderFWImageInfoStatus,
              onPressed: () {
                int refreshIndex = widget.ampPageHelper.listTabs
                    .indexWhere((tab) => tab.title == ampItem.deviceEui);
                widget.ampPageHelper.getCheckTransponderFWImage(
                    ampItem.deviceEui, refreshIndex,
                    isRefresh: true);
              },
              enabled: !isOffline(),
            )),
      ],
    );
  }
  Widget getPowerSupplyView({
    required String title,
    required PowerSupplyInfo powerSupplyInfo,
  }) {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.only(top: getSize(25)),
        child: Container(
          decoration: infoViewDecoration,
          child: Column(
            children: [
              commonTitleView(title: title),
              commonSubTitleView(
                  subTitle: S.of(context).model,
                  value: getText(powerSupplyInfo.model)),
              commonSubTitleView(
                  subTitle: S.of(context).serial,
                  value: getText(powerSupplyInfo.serialNum)),
              commonSubTitleView(
                  subTitle: S.of(context).assetID,
                  value: getText(powerSupplyInfo.assetId)),
              commonSubTitleView(
                  subTitle: S.of(context).mfgDate,
                  value: getText(powerSupplyInfo.mfgDateTime)),
              commonSubTitleView(
                  subTitle: S.of(context).hWVersion,
                  value: getText(powerSupplyInfo.hwRev)),
              errorRefreshView(
                ifCondition: ampItem.powerSupplyError != null,
                elseCondition: screenLayoutType == ScreenLayoutType.desktop,
                errorMessage: ampItem.powerSupplyError ?? "",
                refreshWidget: buildPSILastSeenWithRefreshButton(),
                screenLayout: screenLayoutType,
              ),
            ],
          ),
        ),
      ),
    );
  }

  buildPSILastSeenWithRefreshButton() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Flexible(
          child: buildLastSeenView(
              isShow: ampItem.isShowTextOfPSI,
              difference: ampItem.differenceTimeOfPSI,
              onTapTime: ampItem.onTapTimeOfPSI,
              apiStatus: ampItem.pSInfoRefreshStatus,
              differenceMessage: ampItem.powerSupplyError != null? S.of(context).refreshFailedMessage: null,
              updateTime: ampItem.powerSupplyInfoUpdateTime,textColor: ampItem.powerSupplyError == null ?  AppColorConstants.colorGrn : AppColorConstants.colorAppbar),
        ),
        Padding(
          padding: const EdgeInsets.only(right: 8.0),
          child: AppRefresh(
            buttonColor: AppColorConstants.colorPrimary,
            loadingStatus: ampItem.pSInfoRefreshStatus,
            onPressed: () {
              int refreshIndex = widget.ampPageHelper.listTabs
                  .indexWhere((tab) => tab.title == ampItem.deviceEui);
              widget.ampPageHelper.getPowerSupplyInfo(
                  context, ampItem.deviceEui, refreshIndex,
                  isRefresh: true);
            },
            enabled: !isOffline(),
          ),
        ),
      ],
    );
  }

  Widget getTransponderView(
      {required String title, required TransponderItem transponderInfo, required FirmwareImageResult fwImageInfo}) {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.only(top: getSize(25)),
        child: Container(
          decoration: infoViewDecoration,
          child: Column(
            children: [
              commonTitleView(title: title),
              commonSubTitleView(
                  subTitle: S.of(context).deviceEUI,
                  value: getText(ampItem.deviceEui)),
              commonSubTitleView(
                  subTitle: S.of(context).model,
                  value: getText(transponderInfo.model)),
              commonSubTitleView(
                  subTitle: S.of(context).serialNumber,
                  value: getText(transponderInfo.serialNum)),
              commonSubTitleView(
                  subTitle: S.of(context).assetID,
                  value: getText(transponderInfo.assetId)),
              commonSubTitleView(
                  subTitle: S.of(context).mfgDate,
                  value: getText(transponderInfo.mfgDateTime)),
              commonSubTitleView(
                  subTitle: S.of(context).hWVersion,
                  value: getText(transponderInfo.hwVersion)),
              commonSubTitleView(
                  subTitle: S.of(context).fWVersion,
                  value: getText(transponderInfo.fWVersion)),
              commonSubTitleView(
                  subTitle: S.of(context).apiVersion,
                  value: getText(transponderInfo.apiVersion)),
              errorRefreshView(
                ifCondition: ampItem.ampDeviceTransponderError != null,
                elseCondition: screenLayoutType == ScreenLayoutType.desktop,
                errorMessage:  ampItem.ampDeviceTransponderError ?? "",
                refreshWidget: buildTILastSeenWithRefreshButton(),
                screenLayout: screenLayoutType,
              ),
              if (ampItem.isShowSwitchBankAndReboot)
                Column(
                  children: [
                    const Padding(
                      padding: EdgeInsets.symmetric(horizontal: 15),
                      child: Divider(
                        thickness: 2,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 25, top: 10, bottom: 10),
                      child: Column(crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if(screenLayoutType != ScreenLayoutType.desktop)
                            AppText(
                              S.of(context).transponderVersion,
                              style: TextStyle(
                                  fontFamily: AppAssetsConstants.openSans,
                                  fontWeight: getMediumBoldFontWeight(),
                                  letterSpacing: 0.32,
                                  color: getHeaderColor(),
                                  fontSize: getSize(18)),
                            ),
                          Row(
                            mainAxisAlignment: (screenLayoutType == ScreenLayoutType.desktop)
                                ? MainAxisAlignment.spaceBetween
                                : MainAxisAlignment.end,
                            children: [
                              if(screenLayoutType == ScreenLayoutType.desktop) Expanded(
                                child: AppText(
                                  S.of(context).transponderVersion,
                                  style: TextStyle(
                                      fontFamily: AppAssetsConstants.openSans,
                                      fontWeight: getMediumBoldFontWeight(),
                                      letterSpacing: 0.32,
                                      color: getHeaderColor(),
                                      fontSize: getSize(18)),
                                ),
                              ),
                              buildCommonSwitchButtonView(
                                  isValue: ampItem.isTransponderBankReboot,
                                  status: ampItem.transponderBankRebootStatus,
                                  f: () {
                                    widget.ampPageHelper.transponderSetSwitchBankReboot(ampItem);
                                  }),
                            ],
                          ),
                        ],
                      ),
                    ),
                    commonSubTitleView(
                        subTitle: S.of(context).activeVersion,
                        value: getText("${fwImageInfo.activeVersion ?? ""}")),
                    commonSubTitleView(
                        subTitle: S.of(context).backUpVersion,
                        value: getText("${fwImageInfo.backupVersion ?? ""}")),
                    errorRefreshView(
                      ifCondition: ampItem.transponderImageInfoError != null,
                      elseCondition: screenLayoutType == ScreenLayoutType.desktop,
                      errorMessage: ampItem.transponderImageInfoError ?? "",
                      refreshWidget: buildTransponderFWImageINfoLastSeenWithRefreshButton(),
                      screenLayout: screenLayoutType,
                    ),
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }

  buildTILastSeenWithRefreshButton() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Flexible(
          child: buildLastSeenView(
              isShow: ampItem.isShowTextOfTI,
              difference: ampItem.differenceTimeOfTI,
              onTapTime: ampItem.onTapTimeOfTI,
              apiStatus: ampItem.transponderRefreshStatus,
              updateTime: ampItem.transponderInfoUpdateTime,
              differenceMessage: ampItem.ampDeviceTransponderError != null? S.of(context).refreshFailedMessage: null,
              textColor: ampItem.ampDeviceTransponderError == null
                  ? AppColorConstants.colorGrn
                  : AppColorConstants.colorAppbar),
        ),
        Padding(
          padding: const EdgeInsets.only(right: 8.0),
          child: AppRefresh(
            buttonColor: AppColorConstants.colorPrimary,
            loadingStatus: ampItem.transponderRefreshStatus,
            onPressed: () {
              int refreshIndex = widget.ampPageHelper.listTabs
                  .indexWhere((tab) => tab.title == ampItem.deviceEui);
              widget.ampPageHelper.getTransponderInfo(
                  context, ampItem.deviceEui, refreshIndex,
                  isRefresh: true);
            },
            enabled: !isOffline(),
          ),
        ),
      ],
    );
  }

  Widget buildCommonSwitchButtonView({required bool isValue,required ApiStatus status , required Function f}){
    if(!ampItem.isShowSwitchBankAndReboot){
      return const SizedBox();
    }
    return Padding(
      padding: EdgeInsets.only(
          left: getSize(25),
          right: getSize(20)),
      child: Row(mainAxisAlignment: MainAxisAlignment.end,
        children: [
          AppText(
            S.of(context).switchBankAndReboot,
            style: TextStyle(
                color: AppColorConstants.colorAppbar,
                fontFamily: AppAssetsConstants.openSans,
                fontWeight: FontWeight.w600,
                fontStyle: FontStyle.italic,
                fontSize: getSize(15)),
          ),
          Row(
            children: [
              SizedBox(height: 35,width: 40,child: status == ApiStatus.loading? const AppLoader() : const SizedBox()),
              SizedBox(
                height: 20,
                width: 50,
                child: Transform.scale(
                  scale: 0.8,
                  child: CupertinoSwitch(
                    activeColor: AppColorConstants.colorPrimary,
                    value: isValue,
                    onChanged: isOffline() ? null :(onChangeValue) async {
                      if(status==ApiStatus.loading)return;
                      DialogUtils().confirmationDialog(
                        context,
                        S.of(context).msgAskConfirmationTitle,
                        S.of(context).msgSwitchBankReboot,
                        S.of(context).yes,
                        S.of(context).no,
                        () async {
                          f.call();
                        },
                        () async {
                          goBack();
                        },
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  Widget buildLastSeenView({ ApiStatus? apiStatus,DateTime? onTapTime, Duration? difference, bool isShow = true, DateTime? updateTime ,Color?textColor ,String? differenceMessage}) {
    if (isShow) {
      return Padding(
        padding: const EdgeInsets.only(bottom: 8.0),
        child: getTimeDurationView(
            differenceMessage: differenceMessage,
            refreshStatus: apiStatus,
            updateTime: updateTime,
            onTapTime: onTapTime,
            difference: difference,
            textColor: textColor),
      );
    } else {
      if (updateTime != null) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 8.0),
          child: getLastSeenView(updateTime, textColor: textColor, offline: isOffline()),
        );
      } else {
        return Container(height: 35);
      }
    }
  }

  Widget getConfigView(
      {required String title,
      required DeviceSummaryDetail deviceSummaryDetail}) {
    bool _isOffline = isOffline();
    return Expanded(
      child: Padding(
        padding: EdgeInsets.only(top: getSize(15), bottom: getSize(5)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Container(
              decoration: infoViewDecoration,
              child: Column(
                children: [
                  commonTitleView(title: title),
                  ListView(physics: NeverScrollableScrollPhysics(),
                      padding: EdgeInsets.only(
                          left: getSize(15), bottom: getSize(5)),
                      shrinkWrap: true,
                      children: amplifierInitializationState.map((configValue) {
                        bool isInConfigList = deviceSummaryDetail
                            .configStatus.getConfigStatusList
                            .contains(configValue);

                        var newValue = configValue;
                        configValue = (ampItem.configStatusRefreshStatus == ApiStatus.success)
                            ? (newValue.contains("ALIGNMENT"))
                              ? configValue.replaceAll("UNKNOWN", "NOT DONE")
                            : (newValue.contains("ALSC") || newValue.contains("LOCATION"))
                              ? configValue.replaceAll("UNKNOWN", "NOT SET")
                              : configValue
                            :configValue;

                        final textColor =
                              (_isOffline == false)
                                ? (ampItem.configStatusRefreshStatus == ApiStatus.failed)
                                   ? AppColorConstants.colorGray
                                   : (ampItem.configStatusRefreshStatus == ApiStatus.loading)
                                        ? AppColorConstants.colorBlackBlue
                                            :(isInConfigList && (ampItem.configStatusRefreshStatus == ApiStatus.success))
                                                ? AppColorConstants.colorRedLight
                                                : AppColorConstants.colorGreen2
                                : AppColorConstants.colorH2;

                        return Padding(
                          padding: const EdgeInsets.all(3),
                          child: Row(
                            children: [
                              Icon(
                                size: 20,
                                (ampItem.dashboardUpdateTime != null)
                                    ? (ampItem.configStatusRefreshStatus == ApiStatus.loading)
                                ? Icons.error_outline
                                :isInConfigList
                                        ? Icons.error_outline
                                        : Icons.check_circle_outline
                                    : Icons.error_outline,
                                color: textColor,
                              ),
                              const SizedBox(width: 10),
                              Flexible(
                                child: Row(
                                  children: [
                                    AppText(
                                      isInConfigList
                                          ? configValue
                                          : configValue.replaceAll('NOT ', ''),
                                      style: TextStyle(
                                          color: textColor,
                                          fontFamily:
                                              AppAssetsConstants.openSans,
                                          fontWeight: FontWeight.w500,
                                          fontSize: getSize(14)),
                                    ),
                                    Expanded(
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 12),
                                        child: DottedLine(
                                            color:
                                                AppColorConstants.colorDotLine),
                                      ),
                                    ),
                          /*          AppText(
                                   configValue,
                                      style: TextStyle(
                                          color: textColor,
                                          fontFamily:
                                          AppAssetsConstants.openSans,
                                          fontWeight: FontWeight.w500,
                                          fontSize: getSize(14)),
                                    ),  */
                                  ],
                                ),
                              )
                            ],
                          ),
                        );
                      }).toList()),
                  errorRefreshView(
                    ifCondition: ampItem.configStatusError != null,
                    elseCondition: screenLayoutType == ScreenLayoutType.desktop,
                    errorMessage: ampItem.configStatusError ?? "",
                    refreshWidget: buildConfigStatusLastSeenWithRefreshButton(),
                    screenLayout: screenLayoutType,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  buildConfigStatusLastSeenWithRefreshButton() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Flexible(
          child: buildLastSeenView(
              isShow: ampItem.isShowTextOfConfigStatus,
              difference: ampItem.differenceTimeOfConfigStatus,
              onTapTime: ampItem.onTapTimeOfConfigStatus,
              apiStatus: ampItem.configStatusRefreshStatus,
              differenceMessage: ampItem.configStatusError != null? S.of(context).refreshFailedMessage: null,
              updateTime: ampItem.configStatusUpdateTime,textColor: ampItem.configStatusError == null ?  AppColorConstants.colorGrn : AppColorConstants.colorAppbar),
        ),
        Padding(
          padding: const EdgeInsets.only(right: 8.0),
          child: AppRefresh(
            buttonColor: AppColorConstants.colorPrimary,
            loadingStatus: ampItem.configStatusRefreshStatus,
            onPressed: () {
              int refreshIndex = widget.ampPageHelper.listTabs
                  .indexWhere((tab) => tab.title == ampItem.deviceEui);
              widget.ampPageHelper.getConfigStatus(
                  context, ampItem.deviceEui, refreshIndex);
            },
            enabled: !isOffline(),
          ),
        ),
      ],
    );
  }

  Widget commonTitleView(
      {required String title, double? width = double.infinity}) {
    return Container(
      alignment: Alignment.center,
      height: getSize(50),
      width: width,
      padding: EdgeInsets.only(top: getSize(13), left: getSize(20)),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(getSize(7)),
          topLeft: Radius.circular(getSize(7)),
        ),
      ),
      child: AppText(
        title,
        style: TextStyle(
            fontFamily: AppAssetsConstants.openSans,
            fontWeight: getMediumBoldFontWeight(),
            letterSpacing: 0.32,
            color: getHeaderColor(),
            fontSize: getSize(18)),
      ),
    );
  }

  Widget commonSubTitleView(
      {required String subTitle, required dynamic value , bool isFlex = false}) {
    return Padding(
        padding: EdgeInsets.only(
            left: getSize(25),
            right: getSize(20),
            top: getSize(4),
            bottom: getSize(10)),
        child: Row(
          children: [
            AppText(
              subTitle,
              style: TextStyle(
                  color: AppColorConstants.colorBlackBlue,
                  fontFamily: AppAssetsConstants.openSans,
                  fontWeight: FontWeight.w500,
                  fontSize: getSize(14)),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: DottedLine(color: AppColorConstants.colorDotLine),
              ),
            ),
            if(isFlex || value.toString().length > 35)Expanded(child: AppText(
              "$value",
              textAlign: TextAlign.right,
              style: TextStyle(
                  fontFamily: AppAssetsConstants.openSans,
                  fontWeight: FontWeight.w500,
                  color: AppColorConstants.colorBlackBlue,
                  fontSize: getSize(14)),
            ))else
            AppText(
              "$value",
              style: TextStyle(
                  fontFamily: AppAssetsConstants.openSans,
                  fontWeight: FontWeight.w500,
                  color: AppColorConstants.colorBlackBlue,
                  fontSize: getSize(14)),
            ),
          ],
        ));
  }
}
