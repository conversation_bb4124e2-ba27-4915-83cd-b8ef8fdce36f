import 'package:flutter/scheduler.dart';
import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/pages/amplifiers/amplifier_details/amp_configuration/amp_configuration.dart';
import 'package:quantumlink_node/pages/amplifiers/amplifier_details/audit_log_history/audit_log_history.dart';
import 'package:quantumlink_node/pages/amplifiers/amplifier_details/mobile_amplifer_details/mobile_amplifier_halper.dart';
import '../alarms_history/alarms_history.dart';
import '../amp_diagnostics/amp_diagnostics.dart';
import '../amp_telemetry/amp_telemetry.dart';
import '../amplifier_dashboard/amplifier_dashboard.dart';
import '../amplifier_spectrum/amplifier_spectrum.dart';

class MobileAmplifierDetailsView extends StatefulWidget {
  final AmplifierDeviceItem amplifierItem;
  final AmplifierPageHelper ampPageHelper;
  final String initialDetail;

  const MobileAmplifierDetailsView({
    super.key,
    required this.amplifierItem,
    required this.ampPageHelper,
    this.initialDetail = RouteHelper.deviceInfo,
  });

  @override
  State<MobileAmplifierDetailsView> createState() => _MobileAmplifierDetailsViewState();
}

class _MobileAmplifierDetailsViewState extends State<MobileAmplifierDetailsView>
    with TickerProviderStateMixin {
  late MobileAmplifierDetailPageHelper _helper;
  String currentDetail = RouteHelper.deviceInfo;
  late AmplifierDeviceItem ampItem;
  late ScreenLayoutType screenLayoutType;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    ampItem = widget.amplifierItem;
    currentDetail = widget.initialDetail;
    _helper =
        MobileAmplifierDetailPageHelper(ampPageHelper: widget.ampPageHelper, ampItem: ampItem);
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AmplifierController>(
      init: AmplifierController(),
      initState: (state) async {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          onTapChangeDetail(currentDetail);
        });
      },
      builder: (AmplifierController controller) {
        return ScreenLayoutTypeBuilder(builder: (context, screenType, constraints) {
          screenLayoutType = screenType;
          return Column(
            children: [
              Container(color: AppColorConstants.colorPrimaryLime,
                child: Row(
                  children: [
                    const SizedBox(width: 48),
                    Expanded(
                      child: Align(
                        alignment: Alignment.center,
                        child: AppText(
                          _helper.gridList[_helper.getCurrentIndexFromRoute(currentDetail)],
                          style: TextStyle(
                            fontSize: 19,
                            fontWeight: FontWeight.bold,
                            color: AppColorConstants.colorPrimary,
                          ),
                        ),
                      ),
                    ),
                    IconButton(
                      icon:  Icon(Icons.cancel_outlined,size: 30,color: AppColorConstants.colorPrimary),
                      onPressed: () {
                        _helper.handleTabOnBackWithConfiguration(context);
                        controller.update();
                      },
                    ), // Optional: balance spacing with IconButton
                  ],
                ),
              ),
              Expanded(
                child: dashboardInfoViews(),
              ),
            ],
          );
        });
      },
    );
  }

  Widget dashboardInfoViews() {
    return ListView(
        physics: const ClampingScrollPhysics(),
        padding: EdgeInsets.symmetric(horizontal: getSize(15)),
        shrinkWrap: true,
        children: [
          if (currentDetail == RouteHelper.deviceInfo)
            AmplifierDashboard(
                amplifierItem: widget.amplifierItem, ampPageHelper: widget.ampPageHelper),
          if (currentDetail == RouteHelper.alarms)
            AlarmHistoryPage(amplifierItem: ampItem, ampPageHelper: widget.ampPageHelper),
          if (currentDetail == RouteHelper.spectrum)
            AmplifierSpectrum(amplifierItem: ampItem, ampPageHelper: widget.ampPageHelper),
          if (currentDetail == RouteHelper.auditHistory)
            AuditLogHistoryPage(amplifierItem: ampItem),
          if (currentDetail == RouteHelper.diagnostics)
            AmpDiagnostics(amplifierItem: ampItem, ampPageHelper: widget.ampPageHelper),
          if (currentDetail == RouteHelper.configuration)
            AmpConfiguration(amplifierItem: ampItem, ampPageHelper: widget.ampPageHelper),
        ]);
  }

  Future<void> onTapChangeDetail(String text) async {
    currentDetail = text;
    int refreshIndex =
        widget.ampPageHelper.listTabs.indexWhere((tab) => tab.title == ampItem.deviceEui);
    if (currentDetail == RouteHelper.deviceInfo) {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        if (ampItem.pAInfoRefreshStatus != ApiStatus.loading &&
            ampItem.configStatusRefreshStatus != ApiStatus.loading &&
            ampItem.aMPInfoRefreshStatus != ApiStatus.loading &&
            ampItem.transponderRefreshStatus != ApiStatus.loading &&
            ampItem.pSInfoRefreshStatus != ApiStatus.loading) {
          await widget.ampPageHelper.getDashboardData(context, ampItem.deviceEui, refreshIndex);
        }
      });
    }
  }
}
