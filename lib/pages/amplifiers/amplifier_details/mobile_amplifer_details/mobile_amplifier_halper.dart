import 'package:quantumlink_node/app_import.dart';

class MobileAmplifierDetailPageHelper {
  final AmplifierPageHelper ampPageHelper;
  final AmplifierDeviceItem ampItem;
  ApiStatus apiStatus= ApiStatus.initial;

  MobileAmplifierDetailPageHelper({
    required this.ampPageHelper,
    required this.ampItem,
  });

  //Gird Screen
  int currentSubTab = 0;
  int alarmsFlag = 0;
  String alarmsSeverity = "";

  List<String> gridList = [
    AppStringConstants.deviceInfo,
    AppStringConstants.alarms,
    AppStringConstants.spectrum,
    AppStringConstants.auditHistory,
    AppStringConstants.diagnostics,
    AppStringConstants.configuration,
  ];

  //View Screen


  manageSubTab() {
    gridList = [
      AppStringConstants.deviceInfo,
      AppStringConstants.alarms,
      AppStringConstants.spectrum,
      if (ampItem.isShowConfigurationAndDiagnostics) ...[
        AppStringConstants.auditHistory,
        AppStringConstants.diagnostics,
        AppStringConstants.configuration,
      ]
    ];
  }

  getAmplifierInformationForBetterAmpsFWVersion(BuildContext context, String deviceEui, int index,
      {bool isRefresh = false}) async {
    try {
      AmplifierDeviceItem amplifierDeviceItem = ampPageHelper.listTabs[index].ampDeviceItem;

      if (apiStatus != ApiStatus.loading) {
        apiStatus = ApiStatus.loading;
        ampPageHelper.state.amplifierController.update();

        final result = await Future.wait([
          ampPageHelper.state.amplifierController.getDeviceSummary(
            deviceEui: deviceEui,
            context: context,
            isRefresh: isRefresh,
            bitMask: ampPageHelper.staticBitmaskList[0] + 32,
          ),
        ]);
        final vIValue = result[0];
        ampPageHelper.state.amplifierController.update();

        if (vIValue['body'] != null && vIValue['body'] is AmpDeviceSummary) {
          AmpDeviceSummary ampDeviceSummary = vIValue['body'];
          amplifierDeviceItem.ampDeviceSummary.result.versionInfo =
              ampDeviceSummary.result.versionInfo;
          String ampVersion =
              amplifierDeviceItem.ampDeviceSummary.result.versionInfo.fwVersion ?? "";
          amplifierDeviceItem.isShowConfigurationAndDiagnostics = isBetterAmpsFWVersion(ampVersion);
          amplifierDeviceItem.isShowSwitchBankAndReboot = isBetterAmpsFWVersion(ampVersion);
          if (!amplifierDeviceItem.isShowConfigurationAndDiagnostics) {
            gridList = [
              AppStringConstants.deviceInfo,
              AppStringConstants.alarms,
              AppStringConstants.spectrum,
            ];
          } else {
            gridList = [
              AppStringConstants.deviceInfo,
              AppStringConstants.alarms,
              AppStringConstants.spectrum,
              AppStringConstants.auditHistory,
              AppStringConstants.diagnostics,
              AppStringConstants.configuration,
            ];
          }
          ampPageHelper.state.amplifierController.update();
        }
      }
    } catch (e) {

      debugLogs('Error Get getAmplifierInformationForValidateGrid: $e');
    } finally {
      apiStatus = ApiStatus.success;
      ampPageHelper.state.amplifierController.update();
    }
  }

  getAlarmsFlagCount() async {
    if (ampPageHelper.amplifierDataSource.list.isEmpty) {
      return;
    }
    ampItem.alarmFlagsSeverity = ampPageHelper.amplifierDataSource.list
        .firstWhere((element) => element.deviceEui == ampItem.deviceEui)
        .alarmFlagsSeverity;

    ampItem.alarm = ampPageHelper.amplifierDataSource.list
        .firstWhere((element) => element.deviceEui == ampItem.deviceEui)
        .alarm;

    alarmsFlag = ampItem.alarmFlagsSeverity.length;
    alarmsSeverity = ampItem.alarm ?? "";
  }

  String goRoutsOfDetail(int index) {
    switch (index) {
      case 0:
        return RouteHelper.deviceInfo;
      case 1:
        return RouteHelper.alarms;
      case 2:
        return RouteHelper.spectrum;
      case 3:
        return RouteHelper.auditHistory;
      case 4:
        return RouteHelper.diagnostics;
      case 5:
        return RouteHelper.configuration;
      default:
        return RouteHelper.deviceInfo;
    }
  }

  IconData getGridIconForIndex(int index) {
    switch (index) {
      case 0:
        return Icons.desktop_mac_outlined; //Device Info
      case 1:
        return Icons.alarm; // Amplifier Info/Alarms
      case 2:
        return Icons.graphic_eq; // Amplifier Config/Spectrum
      case 3:
        return Icons.tune; // DS Auto Config/Audit History
      case 4:
        return Icons.medical_services_outlined; // DS Align/Diagnostics
      case 5:
        return Icons.trending_down; //Configuration
      default:
        return Icons.device_unknown;
    }
  }
  int getCurrentIndexFromRoute(String currentDetail) {
    switch (currentDetail) {
      case RouteHelper.deviceInfo:
        return 0;
      case RouteHelper.alarms:
        return 1;
      case RouteHelper.spectrum:
        return 2;
      case RouteHelper.auditHistory:
        return 3;
      case RouteHelper.diagnostics:
        return 4;
      case RouteHelper.configuration:
        return 5;
      default:
        return 0;
    }
  }

  Future<void> handleTabOnBackWithConfiguration(BuildContext context) async {
    int index = ampPageHelper.amplifierDeviceList.result
        .indexWhere((tab) => tab.deviceEui == ampItem.deviceEui);
    if (ampPageHelper.getConfigurationMap(index) != null) {
      bool isSuccess = await ampPageHelper.checkConfigurationMap(context, index);
      if (isSuccess) {
        goBack();
      }
    } else {
      goBack();
    }
  }
}
