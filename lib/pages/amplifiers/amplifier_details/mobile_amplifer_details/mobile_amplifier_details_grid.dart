import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/pages/amplifiers/amplifier_details/mobile_amplifer_details/mobile_amplifier_halper.dart';

class MobileAmplifierDetailGrid extends StatefulWidget {
  final AmplifierDeviceItem amplifierItem;
  final AmplifierPageHelper ampPageHelper;

  const MobileAmplifierDetailGrid(
      {super.key, required this.amplifierItem, required this.ampPageHelper});

  @override
  State<MobileAmplifierDetailGrid> createState() => _MobileAmplifierDetailGridState();
}

class _MobileAmplifierDetailGridState extends State<MobileAmplifierDetailGrid>
    with TickerProviderStateMixin {
  late MobileAmplifierDetailPageHelper _helper;
  late AmplifierDeviceItem ampItem;
  late ScreenLayoutType screenLayoutType;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    ampItem = widget.amplifierItem;
    _helper =
        MobileAmplifierDetailPageHelper(ampPageHelper: widget.ampPageHelper, ampItem: ampItem);
    _helper.getAlarmsFlagCount();
    _helper.manageSubTab();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AmplifierController>(
      init: AmplifierController(),
      initState: (state) async {
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          int refreshIndex =
              widget.ampPageHelper.listTabs.indexWhere((tab) => tab.title == ampItem.deviceEui);
          await  _helper.getAmplifierInformationForBetterAmpsFWVersion(
              widget.ampPageHelper.state.context, ampItem.deviceEui, refreshIndex);
        });
      },
      builder: (AmplifierController controller) {
        return ScreenLayoutTypeBuilder(builder: (context, screenType, constraints) {
          screenLayoutType = screenType;
          return Stack(
            children: [
              Container(color: AppColorConstants.colorPrimaryLime,
                child: Padding(
                  padding: const EdgeInsets.only(top: 60), // reserve space for header
                  child: ListView(
                    padding:  EdgeInsets.symmetric(horizontal: constraints.maxWidth > 500 ? 80 : 20.0,vertical: 16),
                    children: [
                      GridView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: _helper.gridList.length,
                        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          childAspectRatio: 1.5,
                          crossAxisSpacing: 12,
                          mainAxisSpacing: 12,
                        ),
                        itemBuilder: (context, index) {
                          String ampTabItem =_helper.gridList[index];
                          String routsItem = _helper.goRoutsOfDetail(index);
                          return InkWell(
                            onTap: ()  {
                              router.push(
                                '${RouteHelper.routeMobileAMPDetailView}/$routsItem',
                                extra: AmpDetailPageParams(
                                  ampItem: widget.amplifierItem,
                                  ampPageHelper: widget.ampPageHelper,
                                ),
                              );
                            },
                            child: Card(
                              color: AppColorConstants.colorAppbar,
                              elevation: 0,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Center(
                                child: commonGridContainView(
                                    _helper.getGridIconForIndex(index), ampTabItem, index),
                              ),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
              Container(
                height: 60,
                padding: const EdgeInsets.symmetric(horizontal: 5.0),
                color: AppColorConstants.colorAppBackground,
                alignment: Alignment.center,
                child: Row(
                  children: [
                    if (_helper.apiStatus == ApiStatus.loading)
                      const SizedBox(
                        height: 40,
                        width: 40,
                        child: AppLoader(),
                      )else const SizedBox(width: 40,),
                    Expanded(
                      child: Align(
                        alignment: Alignment.center,
                        child: AppText(
                           ampItem.deviceEui,
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: getBoldFontWeight(),
                            color: AppColorConstants.colorChartLine,
                          ),
                        ),
                      ),
                    ),
                    IconButton(
                      icon:  Icon(Icons.cancel_outlined,size: 30,color: AppColorConstants.colorPrimary,),
                      onPressed: () {
                        goBack();
                      },
                    ),

                  ],
                ),
              ),
            ],
          );
        });
      },
    );
  }



  Widget commonGridContainView(IconData gridIcon, String ampTabItem, int index) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          gridIcon,
          size: 60,
          color: AppColorConstants.colorWhite,
        ),
        Row(
          mainAxisSize: MainAxisSize.min, // Prevents unnecessary expansion
          children: [
            AppText(
              isSelectableText: false,
              ampTabItem,
              style: TextStyle(
                fontFamily: AppAssetsConstants.openSans,
                fontWeight: _helper.currentSubTab == index
                    ? getMediumBoldFontWeight()
                    : getMediumFontWeight(),
                fontSize: 16,
                color: AppColorConstants.colorWhite,
              ),
            ),
            StreamBuilder<String>(
              stream: widget.ampPageHelper.updateStreamView,
              builder: (context, snapshot) {
                if (index == 0) {
                  return getDeviceInitializationCountWidget();
                } else if (index == 1) {
                  _helper.getAlarmsFlagCount();
                  return getAlarmCountWidget();
                } else {
                  return Container();
                }
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget getDeviceInitializationCountWidget() {
    if (ampItem.config?.isEmpty ?? true) {
      return Container();
    } else {
      // count number of strings which have NOT in them
      int count = 0;
      ampItem.config?.forEach((value) {
        if (value.contains("NOT")) {
          count++;
        }
      });
      if (count == 0) {
        return Container();
      } else {
        return Container(
          height: 20,
          width: 19,
          margin: const EdgeInsets.only(left: 5),
          decoration: BoxDecoration(
              color: AppColorConstants.colorCriticalLite, borderRadius: BorderRadius.circular(2)),
          alignment: Alignment.center,
          child: Center(
            child: Text("$count",
                style: TextStyle(
                  fontFamily: AppAssetsConstants.openSans,
                  fontWeight: getMediumBoldFontWeight(),
                  fontSize: 14,
                  color: AppColorConstants.colorCritical,
                )),
          ),
        );
      }
    }
  }

  Widget getAlarmCountWidget() {
    if (_helper.alarmsSeverity.isEmpty) {
      return Container();
    }

    return Container(
      height: 20,
      width: 19,
      margin: const EdgeInsets.only(left: 5),
      decoration: BoxDecoration(
          color: getSeverityColor(_helper.alarmsSeverity, true),
          borderRadius: BorderRadius.circular(2)),
      alignment: Alignment.center,
      child: Center(
        child: Text("${_helper.alarmsFlag}",
            style: TextStyle(
              fontFamily: AppAssetsConstants.openSans,
              fontWeight: getMediumBoldFontWeight(),
              fontSize: 14,
              color: getSeverityColor(_helper.alarmsSeverity, false),
            )),
      ),
    );
  }


}
