// ignore_for_file: use_build_context_synchronously


import 'package:intl/intl.dart';
import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/pages/amplifiers/amplifier_details/alarms_history/alarms_history_datasource.dart';
import 'package:quantumlink_node/pages/amplifiers/amplifier_details/amp_configuration/AmplifierConfigurationHelper.dart';
import 'package:quantumlink_node/utils/dialog_utils.dart';
import 'package:rxdart/rxdart.dart';
import 'amplifier_details/amp_telemetry/telemetry_datasource.dart';





AmplifierRepositoryImpl amplifierRepository = AmplifierRepositoryImpl();

class AmplifierPageHelper {
  late AmplifierPageState state;

  ApiStatus apiStatus = ApiStatus.initial;

  final double heightOfDataTableCell = 48;
  int recordsInPage = 0;
  int currentPageIndex = 0;
  late String deviceEui = "";
  bool isInitializedTab = false;

  late ScreenLayoutType screenLayoutType;
  AmplifierDeviceItem amplifierItem = AmplifierDeviceItem.empty();
  AmplifierDeviceList  amplifierDeviceList = AmplifierDeviceList.empty();
  AmplifierDeviceList  searchAmplifierDeviceList = AmplifierDeviceList.empty();
  String?ampErrorMessage;
  List<AmpTabItem> listTabs = [];
  TelemetryThreshold currentTelemetryThreshold = TelemetryThreshold.empty();
  TelemetryThreshold defaultTelemetryThreshold = TelemetryThreshold.empty();
  bool isShowTelemetryThreshold = false;
  ScrollController scrollController = ScrollController();
  SocketHelper socketHelper = SocketHelper();
  String selectedOption = "";
  late TabController tabController;
  AmplifierDataSource amplifierDataSource = AmplifierDataSource.empty([], (value) async {} );
  PaginatorController pageController = PaginatorController();
  late List<bool> isHovered;
  DataTableHelper dataTableHelper = DataTableHelper();
  TextEditingController searchController = TextEditingController();
  Timer? _socketTimer ;
  PaginationHelper telemetryPaginationHelper = PaginationHelper();
  int selectedTelemetryTabIndex = 0;
  Map<int,int> testPointMap = {};
  RxBool isTestPointConfigUpdating = false.obs;
  late TabController subTabController;
  List<String> subTabList = [
    AppStringConstants.deviceInfo,
    AppStringConstants.telemetry,
    AppStringConstants.alarms,
    AppStringConstants.spectrum,
    AppStringConstants.auditHistory,
    AppStringConstants.diagnostics,
    AppStringConstants.configuration,
  ];
  bool isTableView = true;
  bool isExpandAll = false;
  bool isMapTab=false;
  late ScreenLayoutType previousLayoutType = ScreenLayoutType.desktop;
  // late final AmplifierConfigurationHelper amplifierConfigurationHelper;



  //Declaration use in Amplifier Device Info
  // DateTime? onTapTimeOfAI;
  // Duration ? differenceTimeOfAI;
  // bool isShowTextOfAI = true;
  // Timer? refreshTimerOfAI;

  // Placement and Identity
  // DateTime? onTapTimeOfPAI;
  // Duration ? differenceTimeOfPAI;
  // bool isShowTextOfPAI = true;
  // Timer? refreshTimerOfPAI;

  //Power Supply Information
  // DateTime? onTapTimeOfPSI;
  // Duration ? differenceTimeOfPSI;
  // bool isShowTextOfPSI = true;
  // Timer? refreshTimerOfPSI;

  //Transponder Information
  // DateTime? onTapTimeOfTI;
  // Duration ? differenceTimeOfTI;
  // bool isShowTextOfTI = true;
  // Timer? refreshTimerOfTI;

  //Declaration use in Amplifier Telemetry
  List<TelemetryItem> telemetryDataList = [];
  TelemetryDataSource? telemetryDataSource;
  PaginatorController telemetryTableController = PaginatorController();
  DateTime startDate = DateTime.parse(DateFormat("yyyy-MM-dd").format(DateTime.now()));
  DateTime endDate = DateTime.now();
  DateTime? onTapTimeOfTelemetry;
  Duration ? differenceTimeOfTelemetry;
  bool isShowTextOfTelemetry = true;
  Timer? refreshTimerOfTelemetry;
  String selectedUnit =  AppStringConstants.fahrenheit;



  //Declaration use in alarms History
  List<AlarmHistoryData> alarmsHistoryDataList = [];
  late AlarmsHistoryDataSource alarmsHistoryDataSource;
  PaginatorController alarmsTableController = PaginatorController();
  DateTime? onTapTimeOfAlarmsHistory;
  Duration ? differenceTimeOfAlarmsHistory;
  bool isShowTextOfAlarmsHistory = true;
  Timer? refreshTimerOfAlarmsHistory;

  //Declaration use in Spectrum
  DateTime? onTapTimeOfSpectrum;
  Duration ? differenceTimeOfSpectrum;
  bool isShowTextOfSpectrum = true;
  Timer? refreshTimerOfSpectrum;
  final StreamController<List<AmplifierDeviceItem>> locationDeviceStreamController = StreamController.broadcast();

  //  Common Update Stream
  final BehaviorSubject<String> commonUpdateStream = BehaviorSubject<String>();
  Stream<String> get updateStreamView => commonUpdateStream.stream;

  // Declaration use in Location
  String initialLocationValue = "";
  // Declaration use on difference time
  DateTime ?lastUpdateTime ;
  DateTime? onTapTime;
  Duration ? differenceTime;
  bool isShowText = true;
  Timer? refreshTimer;

  RxBool isMapPickerDisplay = false.obs;
  bool isRevertDSAutoAlignmentConfiguration =true;
  String dsConfigurationDraftMessage="";
  String usConfigurationDraftMessage="";

  Future<bool> checkConfigurationMap(BuildContext context, int index) async {
    final completer = Completer<bool>();
    await confirmationDialogConfigurationMap(
      state.context,
      callBack: (isRevert) async {
        if (isRevert == true) {
          bool isSuccess = await revertAmpDSandUSAutoAlignmentConfiguration(
            isRevertDSAutoAlignmentConfiguration,
            index,
          );
          if (!completer.isCompleted) {
            completer.complete(isSuccess);
          }
        } else {
          if (!completer.isCompleted) {
            completer.complete(false);
          }
        }
      },
    );
    return await completer.future;
  }

 Future<bool> revertAmpDSandUSAutoAlignmentConfiguration(bool isDSAlignment, int index) async {
    try {
      DsManualAlignmentItem dsManualAlignmentItem =
      DsManualAlignmentItem([],
          manual_align_ctrl_type_enum: 6);
      state.amplifierController.update();
      if (isDSAlignment) {
        return await state.amplifierController.saveRevertDsManualAlignment(
            dsManualAlignmentItem: dsManualAlignmentItem,
            deviceEui: amplifierItem.deviceEui,
            context: state.context).then((value) async {
          if (value['body'].result != null) {
            clearMapValues(index);
            debugLogs("Revert Result : ${value['body'].result}");
            displayToastNotification(state.context,isDSAlignment,true);
            goBack();
            dsConfigurationDraftMessage ="";
            return true;
          } else {
            displayToastNotification(state.context,isDSAlignment,false);
            return false;
          }
        });
      }else{
        return await state.amplifierController.saveRevertUsManualAlignment(
            dsManualAlignmentItem: dsManualAlignmentItem,
            deviceEui: amplifierItem.deviceEui,
            context: state.context).then((value) async {
          if (value['body'].result != null) {
            clearMapValues(index);
            debugLogs("Revert Result : ${value['body'].result}");
            displayToastNotification(state.context,isDSAlignment,true);
            goBack();
            usConfigurationDraftMessage="";
            return true;
          } else {
            displayToastNotification(state.context,isDSAlignment,false);
            return false;
          }
        });
      }
    }  catch (e) {
      displayToastNotification(state.context,isDSAlignment,false);
      state.amplifierController.update();
      return false;
    } finally{
      state.amplifierController.update();
    }
  }
  displayToastNotification(
      context, bool isDsAlignment,bool isSuccess) {
    String msg = "";
    if (isDsAlignment)
      if(isSuccess)
        msg =  S.of(context).revertDsAlignmentCompleted;
      else
        msg =  S.of(context).revertDsAlignmentFailed;
    else
    if(isSuccess)
      msg =  S.of(context).revertUsAlignmentCompleted;
    else
      msg =  S.of(context).revertUsAlignmentFailed;

    if (isSuccess)
      msg.showSuccess(context);
    else
      msg.showError(context);
  }

  AmplifierDeviceItem? getConfigurationMap(int index) {
    final ampDeviceItem = amplifierDeviceList.result[index];
    if (ampDeviceItem.mapWrittenCtrlDS.isNotEmpty ||
        ampDeviceItem.mapWrittenCtrlUS.isNotEmpty ||
        ampDeviceItem.mapWrittenCtrlDSSpectrum.isNotEmpty) {
      return ampDeviceItem;
    }
    return null;
  }

  clearMapValues(int index){
    amplifierDeviceList.result[index].mapWrittenCtrlDS = {};
    amplifierDeviceList.result[index].mapWrittenCtrlUS = {};
    amplifierDeviceList.result[index].mapWrittenCtrlDSSpectrum = {};
  }

  Future<bool?> confirmationDialogConfigurationMap(BuildContext context,{ApiStatus ?loadingStatus,  required Future<void> Function(bool isRevert) callBack}) async {
    return await confirmationDialogConfiguration(context,callBack: callBack);
  }

  // Implement initState
  AmplifierPageHelper(this.state) {
    tabController = TabController(
      initialIndex: 0,
      length: listTabs.isEmpty ? 2 : listTabs.length,
      vsync: state,
        animationDuration: Duration.zero
    );
    tabController.addListener(_handleTabChange);
    socketHelper.connectAmplifiersChannelSocket();
    startSocketTimer();
    Future.delayed(const Duration(milliseconds: 100)).then((value) async {
      getAmplifierList();
      autoRefreshTimer();
    });
    getCurrantPageIndex();
  }
  getCurrantPageIndex() {
    pageController.addListener(() {
      currentPageIndex = (pageController.currentRowIndex / AppStringConstants.ampPrePageLimit).ceil();
      state.amplifierController.update();
    });
  }

  void _handleTabChange() async{
    final tabControllerIndex = tabController.index;
    if (tabController.index != tabController.previousIndex) {
      int deviceListIndex = amplifierDeviceList.result
          .indexWhere((tab) => tab.deviceEui == listTabs[tabController.previousIndex].ampDeviceItem.deviceEui);

      if(deviceListIndex==-1){
        return;
      }
      if (getConfigurationMap(deviceListIndex) != null) {
        tabController.index = tabController.previousIndex;
        bool isSuccess = await checkConfigurationMap(
            state.context, deviceListIndex);
        if (isSuccess) {
          tabController.index = tabControllerIndex;
          tabAmpsHeaderOnTap(tabControllerIndex);
          state.amplifierController.update();
        }else{
          return;
        }
      } else {
        tabController.index = tabControllerIndex;
        return;
      }
    }
  }

  Timer? _autoRefreshTimer;
  void autoRefreshTimer() {
    debugLogs("autoRefreshTimer ->");
    stopAutoRefreshTimer();
    _autoRefreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      debugLogs("_autoRefreshTimer -> tabController.index : ${tabController.index} currentPage : ${ampPaginationHelper.currentPage}");
      debugLogs("_autoRefreshTimer -> amplifierDeviceList.result.length: ${amplifierDeviceList.result.length} AppStringConstants.ampPrePageLimit : ${AppStringConstants.ampPrePageLimit}");
      if ((apiStatus != ApiStatus.loading) && (searchController.text.trim().isEmpty)&&(ampPaginationHelper.currentPage==0) && (amplifierDeviceList.result.length < AppStringConstants.ampPrePageLimit) && (tabController.index == 0)) {
        debugLogs("getAmplifierList -> auto refresh");
        getAmplifierList(isAutoRefresh: true);
      }
    });
  }

  void stopAutoRefreshTimer() {
    _autoRefreshTimer?.cancel();
    _autoRefreshTimer = null;
  }


  void initializeTabs(BuildContext context) {
    if (!isInitializedTab) {
      listTabs = [
        AmpTabItem(
          title: S.of(context).list,
          isCurrentOpen: true,
          icon: AppAssetsConstants.vectorIcon,
          ampDeviceItem: AmplifierDeviceItem.empty(),
        ),
        AmpTabItem(
          title: S.of(context).map,
          isCurrentOpen: false,
          icon: AppAssetsConstants.stateRegionIcon,
          ampDeviceItem: AmplifierDeviceItem.empty(),
        ),
      ];
      isHovered = List<bool>.filled(listTabs.length, false);
      isInitializedTab = true;
    }
    if(state.widget.mapWithTabIndex != null){
      for (var tabElement in listTabs) {
        tabElement.isCurrentOpen = false;
      }
      listTabs[0].isCurrentOpen = true;
      tabController.animateTo(state.widget.mapWithTabIndex!['initialTabIndex'], duration: const Duration(milliseconds: 100));
    }
  }

  // SERVICES
  Future<void> getAmplifierList({isAutoRefresh = false}) async {
    initializeTimer();
    currentPageIndex=0;
    ampPaginationHelper.initializePagination();
    amplifierDeviceList = amplifierRepository.amplifierDeviceList ?? AmplifierDeviceList.empty();
    if (!isAutoRefresh && (amplifierDeviceList.result.isNotEmpty || amplifierRepository.amplifierDeviceList == null) ) {
      apiStatus = ApiStatus.loading;
      state.amplifierController.update();
    }

    await state.amplifierController.getAmplifierList(state.context, (AmplifierDeviceList ?data) {
      if(data != null ){
        amplifierDataSource = dataSource(data.result);
        amplifierRepository.amplifierDeviceList = data;
        amplifierDeviceList = data;
        validateAndCleanupTabs();
      }else{
        amplifierDataSource = dataSource(<AmplifierDeviceItem>[]);
        ampErrorMessage= S.of(state.context).somethingWentWrong;
      }
    },ampPaginationHelper.pageOffset,AppStringConstants.ampPrePageLimit , search: searchController.text.isNotEmpty ? searchController.text : null);
    bool hasMoreData = amplifierDeviceList.result.length == AppStringConstants.ampPrePageLimit ||
        amplifierDeviceList.result.length > AppStringConstants.ampPrePageLimit;
    ampPaginationHelper.updatePagination(amplifierDeviceList.result.length, hasMore: hasMoreData,pageLimit: AppStringConstants.ampPrePageLimit);
    socketHelper.sendAmplifiersDeviceEUIList(amplifierDeviceList.result , (data) {
     // print("---STEP-0--socketHelper--${data}");
      buildDataSourceWithSocket(data);
      commonUpdateStream.sink.add("");
    });
    if (amplifierDeviceList.result.isEmpty) {
      stopSocketTimer();
    }
    getDifferenceTime();
    apiStatus = ApiStatus.success;
    lastUpdateTime = DateTime.now();
    state.amplifierController.update();
  }
  void initializeTimer() {
    ampErrorMessage= null;
    differenceTime = null;
    onTapTime = DateTime.now();
    isShowText = true;
    refreshTimer?.cancel();
  }
  getDifferenceTime() {
    differenceTime = DateTime.now().difference(onTapTime!);
    refreshTimer= Timer(const Duration(seconds: 2), () {
      isShowText = false;
      state.amplifierController.update();
    });
  }


  void startSocketTimer() {
    _socketTimer?.cancel();
    _socketTimer = Timer.periodic(const Duration(minutes:2), (Timer timer) {
      _checkAndUpdate();
    });
  }

  void _checkAndUpdate() {
    final currentTime = DateTime.now();
    final difference = currentTime.difference(lastUpdateTime!);
    if (difference.inSeconds >= 105) {
      socketHelper.sendAmplifiersDeviceEUIList(amplifierDeviceList.result, (data) {});
      lastUpdateTime = DateTime.now();
    }
    state.amplifierController.update();
  }

  void stopSocketTimer() {
    _socketTimer?.cancel();
    _socketTimer = null;
  }

  // Handle Web Socket
  buildDataSourceWithSocket(event) {
    handleWebSocketMessage(event, (data) {
      amplifierDataSource = dataSource(data);
    });
  }

  void handleWebSocketMessage(String message, Function f) {
    try {
      final data = jsonDecode(message) as Map<String, dynamic>;
      AmplifierDeviceItem updatedAmplifier = AmplifierDeviceItem.fromJson(data);
      debugLogs("Socket -> ${updatedAmplifier.deviceEui}");
       lastUpdateTime = DateTime.now();
      //state.amplifierController.update();
     // debugLogs(updatedAmplifier.toJson());
      final index = amplifierDeviceList.result.indexWhere((device) => device.deviceEui == updatedAmplifier.deviceEui);
      if (index != -1) {
        amplifierDeviceList.result[index].deviceEui = updatedAmplifier.deviceEui;
        amplifierDeviceList.result[index].lastSeen = updatedAmplifier.lastSeen;
        amplifierDeviceList.result[index].type = updatedAmplifier.type;
        amplifierDeviceList.result[index].siteId = updatedAmplifier.siteId;
        amplifierDeviceList.result[index].alarm = updatedAmplifier.alarm;
        amplifierDeviceList.result[index].status = updatedAmplifier.status;
        amplifierDeviceList.result[index].location = updatedAmplifier.location;
        amplifierDeviceList.result[index].alarmFlagsSeverity = updatedAmplifier.alarmFlagsSeverity;
        amplifierDeviceList.result[index].config = updatedAmplifier.config;
        amplifierDeviceList.result[index].configBitmask = updatedAmplifier.configBitmask;
        amplifierDeviceList.result[index].alarmFlags = updatedAmplifier.alarmFlags;
        amplifierDeviceList.result[index].lastAlarmAt = updatedAmplifier.lastAlarmAt;
        amplifierDeviceList.result[index].assetId = updatedAmplifier.assetId;
        amplifierDeviceList.result[index].lastNotificationAt = updatedAmplifier.lastNotificationAt;
        amplifierDataSource.notifyListeners();
        updateStreamControllerDevices(amplifierDeviceList.result);
        debugLogs("Amps Sink ->");
      }
      f.call(amplifierDeviceList.result);
    } catch (e) {
      debugLogs('Error parsing WebSocket message: $e');
    }
  }
  void updateStreamControllerDevices(List<AmplifierDeviceItem> newDevices) {
    locationDeviceStreamController.add(newDevices);
  }

  void validateAndCleanupTabs() {
    // Keep only tabs that are valid or fixed (first two)
    if (listTabs.length < 3) return;
    listTabs.removeWhere((tab) =>
    listTabs.indexOf(tab) >= 2 &&
        !amplifierDeviceList.result.any((device) => device.deviceEui == tab.title)
    );

    isHovered = List<bool>.filled(listTabs.length, false);
    tabController = TabController(
      initialIndex: 0,
      length: listTabs.length,
      vsync: state,
      animationDuration: Duration.zero,
    )..addListener(_handleTabChange);

    if (listTabs.isNotEmpty && !listTabs.any((tab) => tab.isCurrentOpen)) {
      listTabs.first.isCurrentOpen = true;
    }
  }
  //SEARCH AMPLIFIER
  void searchAmplifier(String searchText) {
    if(amplifierDeviceList.result.isNotEmpty){
      currentPageIndex = 0;
      ampPaginationHelper.currentPage=0;
      if(isTableView){
        pageController.goToFirstPage();
      }
      amplifierDataSource.notifyListeners();
      searchAmplifierDeviceList.result = amplifierDeviceList.result.where((element) {
        final lowerCaseValue = searchText.toLowerCase();
        final typeValue = element.type?.toLowerCase() ?? '';
        final siteName = element.site?.name?.toLowerCase() ?? '';
        final assetIdValue = element.assetId?.toLowerCase() ?? '';
        final statusValue = element.status?.toLowerCase() ?? '';

        return element.deviceEui.contains(searchText) ||
            siteName.contains(searchText) ||
            typeValue.contains(lowerCaseValue) ||
            assetIdValue.contains(lowerCaseValue) || statusValue.startsWith(lowerCaseValue);
      }).toList();
      amplifierDataSource = dataSource(searchController.text.isEmpty
          ?amplifierDeviceList.result
          : searchAmplifierDeviceList.result);
      state.amplifierController.update();
    }
  }

  // [version_info]Amplifier Information = 1, [device_identity [2]+ alarm config[32] ] Amplifier Information = 34, [user_device_info] Placement and Identity = 4, Power supply = 16 , Get Config Status = 32
  //
  List<int> staticBitmaskList = [1 , 2 , 4 , 16, 32];



 //GET DASHBOARD DATA FUNCTION
  Future<void> getDashboardData(BuildContext context, String deviceEui, int index, {bool isRefresh = false}) async {
    await Future.wait<void>([
      getPlacementAndIdentity(context, deviceEui, index),
      getConfigStatus(context, deviceEui, index),
      getAmplifierInformation(context, deviceEui, index),
      getTransponderInfo(context, deviceEui, index),
      getPowerSupplyInfo(context, deviceEui, index),
    ]);
    amplifierItem.dashboardUpdateTime = DateTime.now();
  }

  getPlacementAndIdentity(BuildContext context, String deviceEui, int index, {bool isRefresh = false}) async {
    try {
      AmplifierDeviceItem amplifierDeviceItem = listTabs[index].ampDeviceItem;
      if (amplifierDeviceItem.pAInfoRefreshStatus != ApiStatus.loading) {
        initializePAI(amplifierDeviceItem);
        amplifierDeviceItem.pAInfoRefreshStatus = ApiStatus.loading;
        state.amplifierController.update();
        final value = await state.amplifierController.getDeviceSummary(
          deviceEui: deviceEui,
          context: context,
          isRefresh: isRefresh,
          bitMask: staticBitmaskList[2],
        );
        if (value['body'] != null && value['body'] is AmpDeviceSummary) {
          AmpDeviceSummary ampDeviceSummary = value['body'];
          amplifierDeviceItem.ampDeviceSummary.result.userDeviceInfo =
              ampDeviceSummary.result.userDeviceInfo;
                amplifierDeviceItem.ampDeviceSummary.result.diplexInfo = ampDeviceSummary.result.diplexInfo;
          amplifierDeviceItem.ampDeviceSummary.result.bwMode = ampDeviceSummary.result.bwMode;
        } else {
          amplifierDeviceItem.placementAndIdentityError = value['body'];
        }
        listTabs[index].ampDeviceItem.placementAndIdentityUpdateTime =
            getLastUpdateTime(value['headers']['updated_at']);
      }
    } catch (e) {
      debugLogs('Error Get Placement And Identity : $e');
      listTabs[index].ampDeviceItem.placementAndIdentityError =
          "${S.of(context).errorRetrievingMessage}";
    } finally {
      listTabs[index].ampDeviceItem.pAInfoRefreshStatus = ApiStatus.success;
      listTabs[index].ampDeviceItem.differenceTimeOfPAI = DateTime.now().difference( listTabs[index].ampDeviceItem.onTapTimeOfPAI!);
      listTabs[index].ampDeviceItem.refreshTimerOfPAI = Timer(const Duration(seconds: 3), () {
        listTabs[index].ampDeviceItem.isShowTextOfPAI = false;
        state.amplifierController.update();
      });
      state.amplifierController.update();
    }
  }

  getAmplifierInformation(BuildContext context, String deviceEui, int index,
      {bool isRefresh = false}) async {
    try {
      AmplifierDeviceItem amplifierDeviceItem = listTabs[index].ampDeviceItem;

      if (amplifierDeviceItem.aMPInfoRefreshStatus != ApiStatus.loading) {
        initializeTimerOfDeviceInfo(amplifierDeviceItem);
        amplifierDeviceItem.aMPInfoRefreshStatus = ApiStatus.loading;
        state.amplifierController.update();

        final result = await Future.wait([
          state.amplifierController.getDeviceSummary(
            deviceEui: deviceEui,
            context: context,
            isRefresh: isRefresh,
            bitMask: staticBitmaskList[0] + 32,
          ),
          state.amplifierController.getDeviceSummary(
            deviceEui: deviceEui,
            context: context,
            isRefresh: isRefresh,
            bitMask: staticBitmaskList[1],
          ),


        ]);
        final vIValue = result[0];
        final dIValue = result[1];
        state.amplifierController.update();

        if (vIValue['body'] != null && vIValue['body'] is AmpDeviceSummary)  {
          AmpDeviceSummary ampDeviceSummary = vIValue['body'];
          amplifierDeviceItem.ampDeviceSummary.result.versionInfo =
              ampDeviceSummary.result.versionInfo;
          String ampVersion= amplifierDeviceItem.ampDeviceSummary.result.versionInfo.fwVersion ?? "";
          amplifierDeviceItem.isShowConfigurationAndDiagnostics= isBetterAmpsFWVersion(ampVersion);
          amplifierDeviceItem.isShowSwitchBankAndReboot= isBetterAmpsFWVersion(ampVersion);
          if(!amplifierDeviceItem.isShowConfigurationAndDiagnostics){
            subTabController = TabController(
                initialIndex: amplifierItem.initialAmpDetailTabValue ?? 0,
                length: 4,
                vsync: state,
                animationDuration: Duration.zero
            );
            subTabList = [
              AppStringConstants.deviceInfo,
              AppStringConstants.telemetry,
              AppStringConstants.alarms,
              AppStringConstants.spectrum,
            ];
          }
          else{
            subTabController = TabController(
                initialIndex: amplifierItem.initialAmpDetailTabValue ?? 0,
                length: 7,
                vsync: state,
                animationDuration: Duration.zero
            );
            subTabList = [
              AppStringConstants.deviceInfo,
              AppStringConstants.telemetry,
              AppStringConstants.alarms,
              AppStringConstants.spectrum,
              AppStringConstants.auditHistory,
              AppStringConstants.diagnostics,
              AppStringConstants.configuration,
            ];
          }
          state.amplifierController.update();

        } else {
          amplifierDeviceItem.ampDeviceSummaryError = vIValue['body'];
        }
        if (dIValue['body'] != null && dIValue['body'] is AmpDeviceSummary) {
          AmpDeviceSummary ampDeviceSummary = dIValue['body'];
          amplifierDeviceItem.ampDeviceSummary.result.deviceIdentity =
              ampDeviceSummary.result.deviceIdentity;
        } else {
          amplifierDeviceItem.ampDeviceSummaryError = dIValue['body'];
        }
        listTabs[index].ampDeviceItem.aMPInfoUpdateTime =  getLastUpdateTime(dIValue['headers']['updated_at']);

      }
    } catch (e) {
      debugLogs('Error Get Amplifier Information: $e');
      listTabs[index].ampDeviceItem.ampDeviceSummaryError =
          "${S.of(context).errorRetrievingMessage}";
    } finally {
      listTabs[index].ampDeviceItem.aMPInfoRefreshStatus = ApiStatus.success;
      listTabs[index].ampDeviceItem.differenceTimeOfAI = DateTime.now().difference(listTabs[index].ampDeviceItem.onTapTimeOfAI!);
      listTabs[index].ampDeviceItem.refreshTimerOfAI = Timer(const Duration(seconds: 3), () {
        listTabs[index].ampDeviceItem.isShowTextOfAI = false;
        state.amplifierController.update();
      });
      state.amplifierController.update();
      if(!isRefresh){
        Future.wait([
          getCheckTransponderFWImage(deviceEui,index),
          getCheckAmplifierFWImage(deviceEui,index)
        ]);
      }
    }
  }

// This info is fetched initially after loading amplifier details, depending on the version.

  Future<void> getCheckTransponderFWImage(String deviceEui, int index,
      {bool isRefresh = false}) async {
    AmplifierDeviceItem ampDeviceItem = listTabs[index].ampDeviceItem;
    if (ampDeviceItem.isShowSwitchBankAndReboot) {
      initializeTransponderImageINfo(ampDeviceItem);
      ampDeviceItem.transponderFWImageInfoStatus = ApiStatus.loading;
      state.amplifierController.update();
      try {
        final getTransponderFmImageInfo = await state.amplifierController.getTransponderFWImageInfo(
          deviceEui: deviceEui,
          context: state.context,
          isRefresh: isRefresh,
        );
        if (getTransponderFmImageInfo['body'] != null &&
            getTransponderFmImageInfo['body'] is FirmwareImageInfo) {
          final firmwareImageInfo = getTransponderFmImageInfo['body'] as FirmwareImageInfo;
          ampDeviceItem.isTransponderBankReboot = firmwareImageInfo.result.activeBankIndex == 1;
          ampDeviceItem.transponderFWImageInfo = firmwareImageInfo;
          state.amplifierController.update();
        } else {
          ampDeviceItem.transponderImageInfoError = getTransponderFmImageInfo['body'];
        }
      } catch (e) {
        debugLogs("getCheckTransponderFWImage error-->$e");
        ampDeviceItem.transponderImageInfoError = S.of(state.context).errorRetrievingMessage;
      } finally {
        ampDeviceItem.transponderFWImageInfoStatus = ApiStatus.success;
        ampDeviceItem.differenceTimeOfTIImageInfo =
            DateTime.now().difference(ampDeviceItem.onTapTimeOfTIImageInfo!);
        ampDeviceItem.refreshTimerOfTIImageInfo = Timer(const Duration(seconds: 3), () {
          listTabs[index].ampDeviceItem.isShowTextOfTIImageInfo = false;
          state.amplifierController.update();
        });
        state.amplifierController.update();
      }
    }
  }

  Future<void> getCheckAmplifierFWImage(String deviceEui, int index,
      {bool isRefresh = false}) async {
    AmplifierDeviceItem ampDeviceItem = listTabs[index].ampDeviceItem;
    if (ampDeviceItem.isShowSwitchBankAndReboot) {
      initializeAmpImageINfo(ampDeviceItem);
      ampDeviceItem.ampFWImageInfoStatus = ApiStatus.loading;
      state.amplifierController.update();
      try {
        final getAMPFmImageInfo = await state.amplifierController.getAMPFirmwareImageInfo(
          deviceEui: deviceEui,
          context: state.context,
          isRefresh: isRefresh,
        );

        if (getAMPFmImageInfo['body'] != null && getAMPFmImageInfo['body'] is FirmwareImageInfo) {
          final firmwareImageInfo = getAMPFmImageInfo['body'] as FirmwareImageInfo;
          ampDeviceItem.isAMPBankReboot = firmwareImageInfo.result.activeBankIndex == 1;
          ampDeviceItem.ampFirmwareImageInfo = firmwareImageInfo;
        } else {
          ampDeviceItem.ampImageInfoError = getAMPFmImageInfo['body'];
        }
      } catch (e) {
        debugLogs("getCheckTransponderFWImage error-->$e");
        ampDeviceItem.ampImageInfoError = S.of(state.context).errorRetrievingMessage;
      } finally {
        ampDeviceItem.ampFWImageInfoStatus = ApiStatus.success;
        ampDeviceItem.differenceTimeOfAmpImageInfo =
            DateTime.now().difference(ampDeviceItem.onTapTimeOfAmpImageInfo!);
        ampDeviceItem.refreshTimerOfTIImageInfo = Timer(const Duration(seconds: 3), () {
          listTabs[index].ampDeviceItem.isShowTextOfAmpImageInfo = false;
          state.amplifierController.update();
        });
        state.amplifierController.update();
      }
    }
  }

  getPowerSupplyInfo(BuildContext context, String deviceEui, int index, {bool isRefresh = false}) async {
    try {
      AmplifierDeviceItem amplifierDeviceItem = listTabs[index].ampDeviceItem;
      if (amplifierDeviceItem.pSInfoRefreshStatus != ApiStatus.loading) {
        initializePSI(amplifierDeviceItem);
        amplifierDeviceItem.pSInfoRefreshStatus = ApiStatus.loading;
        state.amplifierController.update();
        final value = await state.amplifierController.getDeviceSummary(
          deviceEui: deviceEui,
          context: context,
          isRefresh: isRefresh,
          bitMask: staticBitmaskList[3],
        );
        if (value['body'] != null && value['body'] is AmpDeviceSummary){
          AmpDeviceSummary ampDeviceSummary = value['body'];
          amplifierDeviceItem.ampDeviceSummary.result.powerSupplyInfo =
              ampDeviceSummary.result.powerSupplyInfo;
        } else {
          amplifierDeviceItem.powerSupplyError = value['body'];
        }
        listTabs[index].ampDeviceItem.powerSupplyInfoUpdateTime =getLastUpdateTime(value['headers']['updated_at']);

      }
    } catch (e) {
      debugLogs('Error Get PowerSupply Info: $e');
      listTabs[index].ampDeviceItem.powerSupplyError =
          "${S.of(context).errorRetrievingMessage}";
    } finally {

      listTabs[index].ampDeviceItem.pSInfoRefreshStatus = ApiStatus.success;
      listTabs[index].ampDeviceItem.differenceTimeOfPSI = DateTime.now().difference(listTabs[index].ampDeviceItem.onTapTimeOfPSI!);
      listTabs[index].ampDeviceItem.refreshTimerOfPSI = Timer(const Duration(seconds: 3), () {
        listTabs[index].ampDeviceItem.isShowTextOfPSI = false;
        state.amplifierController.update();
      });
      state.amplifierController.update();
    }
  }

  getConfigStatus(BuildContext context, String deviceEui, int index) async {
    try {
      AmplifierDeviceItem amplifierDeviceItem = listTabs[index].ampDeviceItem;
      if (amplifierDeviceItem.configStatusRefreshStatus != ApiStatus.loading) {
        initializeConfigStatus(amplifierDeviceItem);
        amplifierDeviceItem.configStatusRefreshStatus = ApiStatus.loading;
        state.amplifierController.update();
        final value = await state.amplifierController.getDeviceSummary(
          deviceEui: deviceEui,
          context: context,
          isRefresh: true,
          bitMask: staticBitmaskList[4],
        );
        if (value['body'] != null && value['body'] is AmpDeviceSummary){
          AmpDeviceSummary ampDeviceSummary = value['body'];
          amplifierDeviceItem.ampDeviceSummary.result.configStatus =
              ampDeviceSummary.result.configStatus;
          listTabs[index].ampDeviceItem.configStatusRefreshStatus = ApiStatus.success;
        } else {
          listTabs[index].ampDeviceItem.configStatusRefreshStatus = ApiStatus.failed;
          amplifierDeviceItem.configStatusError = value['body'];
        }
        listTabs[index].ampDeviceItem.configStatusUpdateTime =getLastUpdateTime(value['headers']['updated_at']);

      }
    } catch (e) {
      debugLogs('Error Get Config Status Info: $e');
      listTabs[index].ampDeviceItem.configStatusError =
          "${S.of(context).errorRetrievingMessage}";
      listTabs[index].ampDeviceItem.configStatusRefreshStatus = ApiStatus.failed;
    } finally {
      listTabs[index].ampDeviceItem.differenceTimeOfConfigStatus = DateTime.now().difference(listTabs[index].ampDeviceItem.onTapTimeOfConfigStatus!);
      listTabs[index].ampDeviceItem.refreshTimerOfConfigStatus = Timer(const Duration(seconds: 3), () {
        listTabs[index].ampDeviceItem.isShowTextOfConfigStatus = false;
        state.amplifierController.update();
      });
      state.amplifierController.update();
    }
  }

  getTransponderInfo(BuildContext context, String deviceEui, int index,
      {bool isRefresh = false}) async {
    try {
      AmplifierDeviceItem amplifierDeviceItem = listTabs[index].ampDeviceItem;
      if (amplifierDeviceItem.transponderRefreshStatus != ApiStatus.loading) {
        initializeTI(amplifierDeviceItem);
        amplifierDeviceItem.transponderRefreshStatus = ApiStatus.loading;
        state.amplifierController.update();

        final result = await Future.wait([
          state.amplifierController.getDeviceTransponderInfo(
            deviceEui: deviceEui,
            context: context,
            isRefresh: isRefresh,
          ),
        ]);

        final transponderValue = result [0] ;

        if (transponderValue['body'] != null && transponderValue['body'] is TransponderInfo) {
          amplifierDeviceItem.transponderInfo = transponderValue['body'];
          if (transponderValue['body'].message == "") {
            amplifierDeviceItem.ampDeviceTransponderError =
                "${S.of(context).errorRetrievingMessage}";
          }

        } else {
          amplifierDeviceItem.ampDeviceTransponderError = transponderValue['body'];
        }
        amplifierDeviceItem.transponderInfoUpdateTime =
            getLastUpdateTime(transponderValue['headers']['updated_at']);
      }
    } catch( e){
      debugLogs("getTransponderInfo-->${e.toString()}");
      listTabs[index].ampDeviceItem.ampDeviceTransponderError= S.of(context).errorRetrievingMessage;
    } finally {
      listTabs[index].ampDeviceItem.transponderRefreshStatus = ApiStatus.success;
      listTabs[index].ampDeviceItem.differenceTimeOfTI = DateTime.now().difference(listTabs[index].ampDeviceItem.onTapTimeOfTI!);
      listTabs[index].ampDeviceItem.refreshTimerOfTI = Timer(const Duration(seconds: 3), () {
        listTabs[index].ampDeviceItem.isShowTextOfTI = false;
        state.amplifierController.update();
      });
      state.amplifierController.update();
    }
  }

  ampSetSwitchBankReboot(AmplifierDeviceItem ampItem) async {
    ampItem.ampBankRebootStatus = ApiStatus.loading;
    state.amplifierController.update();
    goBack();
    ampItem.isAMPBankReboot = !ampItem.isAMPBankReboot;
    try {
      await state.amplifierController.setAMPSwitchBankAndReboot(
          deviceEui: ampItem.deviceEui,
          context: state.context,
          bankIndex: ampItem.isAMPBankReboot ? 1 : 0,
          isRefresh: true);
      final getFMImageInfo = await state.amplifierController.getAMPFirmwareImageInfo(
        deviceEui: ampItem.deviceEui,
        context: state.context,
        isRefresh: true,
      );
      if (getFMImageInfo['body'] != null && getFMImageInfo['body'] is FirmwareImageInfo) {
        FirmwareImageInfo firmwareImageInfo = getFMImageInfo['body'];
        ampItem.isAMPBankReboot = firmwareImageInfo.result.activeBankIndex == 1 ? true : false;
        ampItem.ampFirmwareImageInfo = firmwareImageInfo;
      } else {
        ampItem.ampImageInfoError = getFMImageInfo['body'];
        ampItem.isAMPBankReboot = !ampItem.isAMPBankReboot;
      }

      ampItem.aMPInfoUpdateTime =  getLastUpdateTime(getFMImageInfo['headers']['updated_at']);
    } on Exception catch (e) {
      debugLogs("setSwitchBankReboot--->${e.toString()}");
      ampItem.isAMPBankReboot = !ampItem.isAMPBankReboot;
      ampItem.ampImageInfoError = S.of(state.context).errorRetrievingMessage;
    }finally{
      ampItem.ampBankRebootStatus = ApiStatus.success;
      state.amplifierController.update();
    }
  }

  transponderSetSwitchBankReboot(AmplifierDeviceItem ampItem) async {
    ampItem.transponderBankRebootStatus = ApiStatus.loading;
    state.amplifierController.update();
    goBack();
    ampItem.isTransponderBankReboot = !ampItem.isTransponderBankReboot;
    try {
      await state.amplifierController.setTransponderSwitchBankAndReboot(
          deviceEui: ampItem.deviceEui,
          context: state.context,
          bankIndex: ampItem.isTransponderBankReboot ? 1 : 0,
          isRefresh: true);
      await Future.delayed(const Duration(seconds: 45));
      final getImageInfo = await state.amplifierController.getTransponderFWImageInfo(
        deviceEui: ampItem.deviceEui,
        context: state.context,
        isRefresh: true,
      );
      if (getImageInfo['body'] != null && getImageInfo['body'] is FirmwareImageInfo) {
        FirmwareImageInfo firmwareImageInfo = getImageInfo['body'];
        ampItem.isTransponderBankReboot =
        firmwareImageInfo.result.activeBankIndex == 1 ? true : false;
        ampItem.transponderFWImageInfo = firmwareImageInfo;
      } else {
        ampItem.transponderImageInfoError = getImageInfo['body'];
        ampItem.isTransponderBankReboot = !ampItem.isTransponderBankReboot;
      }

      ampItem.transponderInfoUpdateTime =
          getLastUpdateTime(getImageInfo['headers']['updated_at']);
    } on Exception catch (e) {
      debugLogs("setSwitchBankReboot--->${e.toString()}");
      ampItem.transponderImageInfoError = S.of(state.context).errorRetrievingMessage;
      ampItem.isTransponderBankReboot = !ampItem.isTransponderBankReboot;
    }finally{
      ampItem.transponderBankRebootStatus = ApiStatus.success;
      state.amplifierController.update();
    }
  }


  void initializeTimerOfDeviceInfo(AmplifierDeviceItem amplifierDeviceItem) {
    amplifierDeviceItem.differenceTimeOfAI = null;
    amplifierDeviceItem.onTapTimeOfAI = DateTime.now();
    amplifierDeviceItem.isShowTextOfAI = true;
    amplifierDeviceItem.refreshTimerOfAI?.cancel();
    amplifierDeviceItem.ampDeviceSummaryError = null;
    amplifierDeviceItem.aMPInfoUpdateTime=null;
  }
  void initializePAI(AmplifierDeviceItem amplifierDeviceItem) {
    amplifierDeviceItem.differenceTimeOfPAI = null;
    amplifierDeviceItem.onTapTimeOfPAI = DateTime.now();
    amplifierDeviceItem.isShowTextOfPAI = true;
    amplifierDeviceItem.refreshTimerOfPAI?.cancel();
    amplifierDeviceItem.placementAndIdentityError = null;
    amplifierDeviceItem.placementAndIdentityUpdateTime=null;
  }
  void initializeTI(AmplifierDeviceItem amplifierDeviceItem) {
    amplifierDeviceItem.differenceTimeOfTI = null;
    amplifierDeviceItem.onTapTimeOfTI = DateTime.now();
    amplifierDeviceItem.isShowTextOfTI = true;
    amplifierDeviceItem.refreshTimerOfTI?.cancel();
    amplifierDeviceItem.transponderInfoUpdateTime = null;
    amplifierDeviceItem.ampDeviceTransponderError=null;
  }
  void initializePSI(AmplifierDeviceItem amplifierDeviceItem) {
    amplifierDeviceItem.differenceTimeOfPSI = null;
    amplifierDeviceItem.onTapTimeOfPSI = DateTime.now();
    amplifierDeviceItem.isShowTextOfPSI = true;
    amplifierDeviceItem.refreshTimerOfPSI?.cancel();
    amplifierDeviceItem.powerSupplyInfoUpdateTime = null;
    amplifierDeviceItem.powerSupplyError = null;
  }
  void initializeTransponderImageINfo(AmplifierDeviceItem amplifierDeviceItem) {
    amplifierDeviceItem.differenceTimeOfTIImageInfo = null;
    amplifierDeviceItem.onTapTimeOfTIImageInfo = DateTime.now();
    amplifierDeviceItem.isShowTextOfTIImageInfo = true;
    amplifierDeviceItem.refreshTimerOfTIImageInfo?.cancel();
    amplifierDeviceItem.tiImageInfoUpdateTime = null;
    amplifierDeviceItem.transponderImageInfoError = null;
  }
  void initializeAmpImageINfo(AmplifierDeviceItem amplifierDeviceItem) {
    amplifierDeviceItem.differenceTimeOfAmpImageInfo = null;
    amplifierDeviceItem.onTapTimeOfAmpImageInfo = DateTime.now();
    amplifierDeviceItem.isShowTextOfAmpImageInfo = true;
    amplifierDeviceItem.refreshTimerOfAmpImageInfo?.cancel();
    amplifierDeviceItem.ampImageInfoUpdateTime = null;
    amplifierDeviceItem.ampImageInfoError = null;
  }
  void initializeConfigStatus(AmplifierDeviceItem amplifierDeviceItem) {
    amplifierDeviceItem.differenceTimeOfConfigStatus = null;
    amplifierDeviceItem.onTapTimeOfConfigStatus = DateTime.now();
    amplifierDeviceItem.isShowTextOfConfigStatus = true;
    amplifierDeviceItem.refreshTimerOfConfigStatus?.cancel();
    amplifierDeviceItem.configStatusUpdateTime = null;
    amplifierDeviceItem.configStatusError = null;
  }
  void initializeDeviceInfo(AmplifierDeviceItem amplifierDeviceItem) {
    amplifierDeviceItem.ampDeviceSummaryError = null;

    amplifierDeviceItem.aMPInfoUpdateTime = null;
  }

  getDifferenceTimeOfDeviceInfo(DateTime onTap, Duration? difference, bool isShow,
      Timer? refreshTimer) async {

    state.amplifierController.update();
  }

  //GET CONFIG DATA FUNCTION
  getDsConfigData(BuildContext context, String deviceEui, int index) async {
    AmplifierDeviceItem amplifierDeviceItem = listTabs[index].ampDeviceItem;
    amplifierDeviceItem.downStreamAutoAlignmentError = null;
    amplifierDeviceItem.configRefreshStatus = ApiStatus.loading;
    state.amplifierController.update();
    await getDownStreamConfig(context, deviceEui, index);
    listTabs[index].ampDeviceItem.configRefreshStatus = ApiStatus.success;
    state.amplifierController.update();
  }

  getIngressSwitchData(BuildContext context, String deviceEui, IngressSwitchItem ingressSwitchItem,int index,bool isMultiIngress
      ) async {
    AmplifierDeviceItem amplifierDeviceItem = listTabs[index].ampDeviceItem;
    amplifierDeviceItem.ingressSwitchError = null;
    amplifierDeviceItem.configRefreshStatus = ApiStatus.loading;
    state.amplifierController.update();
    await getIngressSwitchControl(context, deviceEui, ingressSwitchItem,index,isMultiIngress);
    listTabs[index].ampDeviceItem.configRefreshStatus = ApiStatus.success;
    state.amplifierController.update();
  }

  getDownStreamConfig(BuildContext context, String deviceEui, int index) async {
    await state.amplifierController
        .ampDsConfig(deviceEui: deviceEui, context: context)
        .then((value) {
      if (value != null) {
        if (value is AmpDownStreamModel) {
          listTabs[index].ampDeviceItem.ampDownStreamItem = value.result;
        } else {
          listTabs[index].ampDeviceItem.downStreamAutoAlignmentError = value;
        }
      }
    });
  }

  getIngressSwitchControl(BuildContext context, String deviceEui ,IngressSwitchItem ingressSwitchItem,int index, bool isMultiIngress) async {
   try{
     listTabs[index].ampDeviceItem.setIngressStatus = ApiStatus.loading;
     await state.amplifierController
         .getIngressSwitch(context, deviceEui,ingressSwitchItem,isMultiIngress)
         .then((value) {
        if (value['body'] != null) {
          if (value['body'] is IngressSwitchControl) {
            listTabs[index].ampDeviceItem.ingressSwitchItemList = value['body'].items;
            listTabs[index].ampDeviceItem.setIngressStatus = ApiStatus.success;
          } else {
            listTabs[index].ampDeviceItem.ingressSwitchError = value['body'];
            listTabs[index].ampDeviceItem.setIngressStatus = ApiStatus.failed;
          }
        }else{
          listTabs[index].ampDeviceItem.setIngressStatus = ApiStatus.failed;
          listTabs[index].ampDeviceItem.ingressSwitchError= S.of(context).socketExceptionMessage;
        }
        listTabs[index].ampDeviceItem.ampIngressUpdateTime = getLastUpdateTime(value['headers']['updated_at']);
      });
    }catch(e){
     debugLogs("getIngressSwitchControl exception-->$e");
     listTabs[index].ampDeviceItem.ingressSwitchError= S.of(context).socketExceptionMessage;
     listTabs[index].ampDeviceItem.setIngressStatus = ApiStatus.failed;
   }finally{
     state.amplifierController.update();
   }
  }

  //Set Ingress Switch
  setIngressSwitchAPICall(
      BuildContext context,
      int index,
      int lastIngressValue,
      String deviceEui,
      IngressSwitchItem ingressSwitchItem,
      bool isRefresh, bool isMultiIngressSwitch) async {
    try {
      await state.amplifierController
          .setIngressSwitch(context, deviceEui, ingressSwitchItem,
              isRefresh: isRefresh,isMultiIngressSwitch:isMultiIngressSwitch)
          .then((value) async {
        if (value != null) {
          if (value is IngressSwitchControl) {
            await getIngressSwitchData(context, deviceEui, ingressSwitchItem, index ,isMultiIngressSwitch);
            listTabs[index].ampDeviceItem.setIngressStatus = ApiStatus.success;
            listTabs[index].ampDeviceItem.ingressSwitchError = null;
          } else {
            setIngressFailed(context, index, lastIngressValue,
                msg: value['body']);
          }
        } else {
          setIngressFailed(context, index, lastIngressValue);
        }
      });
    } catch (e) {
      setIngressFailed(context, index, lastIngressValue);
    }finally{
      state.amplifierController.update();
    }
  }

  setIngressFailed(BuildContext context, int index, int lastIngressValue,
      {String? msg}) {
    msg = msg ?? S.of(context).ampIngressUpdateFailed;
    listTabs[index].ampDeviceItem.ingressSwitchError = msg;
    listTabs[index].ampDeviceItem.setIngressStatus = ApiStatus.failed;
  }

  // Get Test Point Config
  getTestPointConfiguration(
      BuildContext context, String deviceEui,int index, bool isRefresh) async {
    listTabs[index].ampDeviceItem.ampTestPointUpdateTime = null;
    listTabs[index].ampDeviceItem.testPointConfigError = null;
    listTabs[index].ampDeviceItem.testPointConfigRefreshStatus = ApiStatus.loading;
    state.amplifierController.update();
    try {
      await state.amplifierController
          .getTestPointConfiguration(context, deviceEui, isRefresh)
          .then((value) {
        if (value['body'] != null) {
          if (value['body'] is TestPointModel) {
            TestPointModel testPointModel = value['body'];
            listTabs[index].ampDeviceItem.testPointConfigList = testPointModel.testPointData.testPointList;
            testPointMap.clear();
            for (int i = 0; i < testPointModel.testPointData.testPointList.length; i++) {
            testPointMap[i] = testPointModel.testPointData.testPointList[i].value;
            }
            listTabs[index].ampDeviceItem.testPointConfigError = null;
            listTabs[index].ampDeviceItem.testPointConfigRefreshStatus = ApiStatus.success;
          }else{
            testPointConfigFailed(context,index,false,msg: value['body']);
          }
          listTabs[index].ampDeviceItem.ampTestPointUpdateTime= getLastUpdateTime(value['headers']['updated_at']);
        }else{
          testPointConfigFailed(context,index,false);
        }
      });
    } catch (e) {
      testPointConfigFailed(context,index,false);
    } finally {
      state.amplifierController.update();
    }
  }

  // Set Test Point Config
  setTestPointConfiguration(BuildContext context, int index, String deviceEui,
      TestPointItem testPointItem) async {
    try{
      testPointItem.isProgressing.value = true;
      isTestPointConfigUpdating.value = true;
      listTabs[index].ampDeviceItem.isSetDataInProgressing.value = true;
      await state.amplifierController
          .setTestPoint(context, deviceEui, testPointItem)
          .then((value) {
        if (value != null) {
          if (value is TestPointItem) {
            testPointMap.clear();
            listTabs[index].ampDeviceItem.testPointConfigError = null;
            for (int i = 0; i < listTabs[index].ampDeviceItem.testPointConfigList.length; i++) {
              if (value.id == listTabs[index].ampDeviceItem.testPointConfigList[i].id) {
                listTabs[index].ampDeviceItem.testPointConfigList[i] = value;
              }
              testPointMap[i] = listTabs[index].ampDeviceItem.testPointConfigList[i].value;
            }
            listTabs[index].ampDeviceItem.testPointConfigRefreshStatus = ApiStatus.success;
          } else {
            testPointConfigFailed(context,index,true,msg:getMessageString(context, testPointItem, false) );
          }
        } else {
          testPointConfigFailed(context,index,true,msg:getMessageString(context, testPointItem, false) );
        }
        isTestPointConfigUpdating.value = false;
        testPointItem.isProgressing.value = false;
      });
    }catch(e){
      debugLogs("test config error = ${e.toString()}");
      testPointConfigFailed(context,index,true,msg:getMessageString(context, testPointItem, false) );
    }finally{
      isTestPointConfigUpdating.value = false;
      testPointItem.isProgressing.value = false;
      listTabs[index].ampDeviceItem.isSetDataInProgressing.value = false;
      state.amplifierController.update();
    }
  }

  String getMessageString(
      BuildContext context, TestPointItem testPointItem, bool isSuccess) {
    String msg2 =
        isSuccess ? S.of(context).updateSuccess : S.of(context).updateFailed;
    return "${testPointItem.getName(testPointItem.id)} $msg2";
  }

  testPointConfigFailed(BuildContext context, int index, bool isSet,
      {String? msg}) {
    msg = msg ?? ((isSet)? S.of(context).ampTestPointConfigUpdateFailed : S.of(context).socketExceptionMessage);
    if(!isSet) listTabs[index].ampDeviceItem.testPointConfigList = [];
    listTabs[index].ampDeviceItem.testPointConfigError = msg;
    listTabs[index].ampDeviceItem.testPointConfigRefreshStatus = ApiStatus.failed;
  }

  // TELEMETRY
  Future<dynamic> getTelemetryData(BuildContext context, String deviceEui , int index, int startDate , int endDate, int perPageLimit) async {
    // print("getTelemetryData--- deviceEui=${deviceEui}--index=${index}----startDate=$startDate----endDate=$endDate");
    listTabs[index].ampDeviceItem.telemetryStatus = ApiStatus.loading;
    state.amplifierController.update();
    await state.amplifierController
        .getTelemetry(
            deviceEui: deviceEui, context: context, formDate: "$startDate", toDate: "$endDate" , pageOffset: telemetryPaginationHelper.pageOffset,perPageLimit:perPageLimit)
        .then((value) {
       debugLogs("value telemetry : ${value.result}");
      if (value.result != null) {
        listTabs[index].ampDeviceItem.telemetryModel = value.result;
        telemetryDataList = value.result;
        bool hasMoreData = telemetryDataList.length == AppStringConstants.telemetryPrePageLimit || telemetryDataList.length > AppStringConstants.telemetryPrePageLimit;
        telemetryPaginationHelper.updatePagination(telemetryDataList.length,
            hasMore: hasMoreData,pageLimit: AppStringConstants.telemetryPrePageLimit);
        telemetryDataSource = TelemetryDataSource(
          context,
          telemetryDataList,
          this,
          (value) {},
          selectedTabIndex: selectedTelemetryTabIndex, isShowTelemetryALSCPilots :  listTabs[index].ampDeviceItem.isShowTelemetryALSCPilots,
          selectedUnit: selectedUnit
        );
      }else{
        telemetryDataSource = TelemetryDataSource(context, telemetryDataList, this, (value) {},selectedTabIndex: selectedTelemetryTabIndex);
      }
    });

    listTabs[index].ampDeviceItem.telemetryStatus = ApiStatus.success;
    state.amplifierController.update();
  }
  getTelemetryDifferenceTime() {
    differenceTimeOfTelemetry = DateTime.now().difference(onTapTimeOfTelemetry!);
    refreshTimerOfTelemetry= Timer(const Duration(seconds: 3), () {
      isShowTextOfTelemetry = false;
      state.amplifierController.update();
    });
  }

  //Telemetry Threshold
  Future<dynamic> getTelemetryThresholdData(BuildContext context, String deviceType,int index) async {
    getTelemetryDefaultThresholdData(context, index);
    await state.amplifierController
        .getTelemetryThresholds(
        deviceType: "current-values", context: context)
        .then((value) {
      if (value != null) {
        currentTelemetryThreshold = value;
        isShowTelemetryThreshold = true;
      }
    });
  }

  //Default Telemetry Threshold Values
  Future<dynamic> getTelemetryDefaultThresholdData(BuildContext context, int index) async {
    await state.amplifierController
        .getTelemetryThresholds(
        deviceType: "default", context: context)
        .then((value) {
      if (value != null) {
        defaultTelemetryThreshold = value;
      }
    });
  }

  //Update Telemetry Threshold
  updateTelemetryThresholdData(BuildContext context, String deviceType,TelemetryThreshold telemetryThresholdData) async {
    await state.amplifierController.addTelemetryThresholds(
        context, deviceType,
        telemetryThresholdData)
        .then((value) {
      if (value != null) {
        currentTelemetryThreshold = value;
        S.of(context).thresholdSuccess.showMessage();
      }else{
        S.of(context).somethingWentWrong.showMessage();
      }
    });
    amplifierItem.telemetryUpdateTime = DateTime.now();
    state.amplifierController.update();
  }


  //ALARM History
  Future<dynamic> getAlarmsHistoryData(String deviceEui, int index ,int startDate , int endDate) async {
    listTabs[index].ampDeviceItem.alarmHistoryStatus = ApiStatus.loading;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      state.amplifierController.update();
    });
    await state.amplifierController
        .getAlarmsHistoryData(deviceEui: deviceEui, context: state.context , fromDate: "$startDate", toDate: "$endDate")
        .then((value) {
      if (value != null) {
        listTabs[index].ampDeviceItem.alarmStatusModel = value;
        alarmsHistoryDataList = listTabs[index].ampDeviceItem.alarmStatusModel;
        alarmsHistoryDataSource = AlarmsHistoryDataSource(
          state.context,
          alarmsHistoryDataList,
              (value) {},
        );
      }
    });
    listTabs[index].ampDeviceItem.alarmUpdateTime = DateTime.now();
    listTabs[index].ampDeviceItem.alarmHistoryStatus = ApiStatus.success;
    getAlarmHistoryDifferenceTime();
    state.amplifierController.update();
  }

  getAlarmHistoryDifferenceTime() {
    differenceTimeOfAlarmsHistory = DateTime.now().difference(onTapTimeOfAlarmsHistory!);
    refreshTimerOfAlarmsHistory= Timer(const Duration(seconds: 3), () {
      isShowTextOfAlarmsHistory = false;
      state.amplifierController.update();
    });
  }

  // DATA SOURCE METHOD
  AmplifierDataSource dataSource(List<AmplifierDeviceItem> result) {
    return AmplifierDataSource(result, (value) async {
      // On Tap
       if(AppConfig.shared.isOpenFromBLE) {
         updateTabAndNavigateToDetail(value) ;
       } else {
         addTabTableOnTap(value);
       }
    }, state.context , (latLng) async {
      tabAmpsHeaderOnTap(1);
      tabController.animateTo(1, duration: const Duration(milliseconds: 100));
      initialLocationValue = latLng;
    });
  }

  void updateTabAndNavigateToDetail(AmplifierDeviceItem value) {
    const int deviceIndex=2;
     amplifierItem = value;
    if (listTabs.length > deviceIndex) {
      listTabs[deviceIndex] = AmpTabItem(
        title: value.deviceEui,
        isCurrentOpen: false,
        ampDeviceItem: value,
      );
    } else {
      listTabs.add(AmpTabItem(
        title: value.deviceEui,
        isCurrentOpen: false,
        ampDeviceItem: value,
      ));
      tabController = TabController(
          initialIndex: 0,
          length: listTabs.length,
          vsync: state,
          animationDuration: Duration.zero
      );
    }
    isHovered = List<bool>.filled(listTabs.length, false);
    router.push(
      RouteHelper.routeMobileAMPDetail,
      extra: AmpDetailPageParams(
        ampItem: listTabs[deviceIndex].ampDeviceItem,
        ampPageHelper: this,
      ),
    );
  }


  // TAB ON TAP
  addTabTableOnTap(AmplifierDeviceItem value) {
      amplifierItem = value;
      bool deviceEuiExists = listTabs.any((tab) => tab.title == value.deviceEui);
      if (!deviceEuiExists) {
        if (listTabs.length <= 7) {
          addTab(value.deviceEui, true);
          int euiIndex = listTabs.indexWhere((element) => element.title == value.deviceEui);
          getDashboardData(state.context, value.deviceEui, euiIndex);
        }else{
          S.of(state.context).maxTabMessage.showError(state.context);
        }
        state.amplifierController.update();
      } else {
        for (var element in listTabs) {
          element.isCurrentOpen = false;
        }
        int euiIndex = listTabs.indexWhere((element) => element.title == value.deviceEui);
        listTabs[euiIndex].isCurrentOpen = true;
        tabController.animateTo(
          euiIndex,
          duration: const Duration(milliseconds: 100),
        );
      }

    state.amplifierController.update();
  }

  // ADD TAB FUNCTION
  addTab(t, o) {
    for (var tabElement in listTabs) {
      tabElement.isCurrentOpen = false;
    }
    listTabs.add(AmpTabItem(title: t, isCurrentOpen: o, ampDeviceItem: amplifierItem));
    isHovered = List<bool>.filled(listTabs.length, false);
    tabController = TabController(
      initialIndex: listTabs.length - 1,
      length: listTabs.length,
      vsync: state,
        animationDuration: Duration.zero
    );
    tabController.addListener(_handleTabChange);
  }

  // TAB HEADER ON TAP
  tabAmpsHeaderOnTap(int value) {
    for (var tabElement in listTabs) {
      tabElement.isCurrentOpen = false;
    }
    listTabs[value].isCurrentOpen = true;
    final currentTabTitle = listTabs
        .singleWhere((element) => element.isCurrentOpen)
        .title;
     isMapTab = currentTabTitle == S.of(state.context).map;
    // if (value > 1 &&
    //     listTabs[value].ampDeviceItem.initialAmpDetailTabValue == null || listTabs[value].ampDeviceItem.initialAmpDetailTabValue == 0) {
    //   listTabs[value].ampDeviceItem = amplifierDeviceList.result
    //       .firstWhere((element) => element.deviceEui == listTabs[value].title);
    //
    //   getDashboardData(state.context, listTabs[value].title, value);
    // }
    initialLocationValue = '';
    state.amplifierController.update();
  }

  // REMOVE TAB
  removeAmpTab(int index) {
    listTabs[index].ampDeviceItem.initialAmpDetailTabValue = 0;
    listTabs.removeAt(index);
    for (var tabElement in listTabs) {
      tabElement.isCurrentOpen = false;
    }
    tabController = TabController(
      initialIndex: listTabs.length - 1,
      length: listTabs.length,
      vsync: state,
        animationDuration: Duration.zero
    );
    tabController.addListener(_handleTabChange);
    listTabs.first.isCurrentOpen = true;
    tabController.animateTo(0);
    // if (listTabs.length > 3) {
    //   listTabs[listTabs.length - 1].isCurrentOpen = true;
    //   getDashboardData(
    //       state.context, listTabs[listTabs.length - 1].title,listTabs.length - 1);
    //   listTabs[index-1].ampDeviceItem =amplifierRepository.amplifierDeviceList!.result.firstWhere((element) =>
    //   element.deviceEui == listTabs[listTabs.length - 1].title);
    // }else{
    //   listTabs[0].isCurrentOpen = true;
    //   tabController.animateTo(0);
    // }
    state.amplifierController.update();
  }

  checkDeviceType(String? type){
    return type?? "Default";
  }

  //For Pagination Helper
  PaginationHelper  ampPaginationHelper = PaginationHelper();
  Future<void> loadPreviousLogs(BuildContext context) async {
      ampPaginationHelper.setPage(ampPaginationHelper.currentPage - 1);
      if(isTableView) {
        pageController.goToPreviousPage();
      }
      state.amplifierController.update();
  }

  Future<void> loadNextLogs(BuildContext context) async {
    if (ampPaginationHelper.canGoToNextPage) {
      ampPaginationHelper.setPage(ampPaginationHelper.currentPage + 1);
      if (ampPaginationHelper.currentPage >= ampPaginationHelper.totalPages) {
        ampPaginationHelper.pageOffset = amplifierDeviceList.result.length;
        await updateAmplifierPageData();
      } else {
        if(isTableView) {
          pageController.goToNextPage();
        }
        state.amplifierController.update();
      }
    }
  }

  updateAmplifierPageData() async {
    apiStatus = ApiStatus.loading;
    initializeTimer();
     state.amplifierController.update();
    AmplifierDeviceList ? ampDeviceList ;
    ampDeviceList = await state.amplifierController.getAmplifierList(state.context, (AmplifierDeviceList ? data) {
      ampDeviceList = data;
    },ampPaginationHelper.pageOffset,AppStringConstants.ampPrePageLimit, search: searchController.text.isNotEmpty ? searchController.text : null);
    if (ampDeviceList != null &&ampDeviceList!.result.isNotEmpty) {
      amplifierDeviceList.result.addAll(ampDeviceList!.result);
      amplifierDataSource = dataSource(amplifierDeviceList.result);
      bool hasMoreData = ampDeviceList!.result.length == AppStringConstants.ampPrePageLimit;
      ampPaginationHelper.updatePagination(amplifierDeviceList.result.length, hasMore: hasMoreData,pageLimit: AppStringConstants.ampPrePageLimit);
      socketHelper.sendAmplifiersDeviceEUIList(amplifierDeviceList.result, (data) {
        buildDataSourceWithSocket(data);
        commonUpdateStream.sink.add("");
      });
    } else {
      if(ampDeviceList == null) ampErrorMessage = S.of(state.context).somethingWentWrong;
      ampPaginationHelper.setPage(ampPaginationHelper.currentPage - 1);
      ampPaginationHelper.updatePagination(amplifierDeviceList.result.length, hasMore: false,pageLimit: AppStringConstants.ampPrePageLimit);
      Future.delayed(const Duration(milliseconds: 100)).then((value) {
        if (isTableView) {
          if (ampDeviceList != null && ampDeviceList!.result.isNotEmpty)
            pageController.goToLastPage();
        }
        amplifierDataSource.notifyListeners();
      });
    }
    getDifferenceTime();
    lastUpdateTime = DateTime.now();
    apiStatus = ApiStatus.success;
    state.amplifierController.update();
  }

  // Future<bool> compareWithOldValue() async {
  //   bool isUpdated = true;
  //   for (var element in amplifierConfigurationHelper.dsManualAlignmentItem.dsValues) {
  //     String key = "${element.ctrlType}_${element.stage}";
  //     if (amplifierConfigurationHelper.dsManualAlignmentListMap.containsKey(key)) {
  //       double oldValue;
  //       try {
  //         oldValue = double.parse(amplifierConfigurationHelper.dsManualAlignmentListMap[key].toString());
  //       } catch (e) {
  //         continue;
  //       }
  //       if (element.value != oldValue) {
  //         isUpdated = false;
  //         break;
  //       }
  //     }
  //   }
  //   return isUpdated;
  // }
  //
  // updateOriginalValue() async {
  //   amplifierConfigurationHelper.dsManualAlignmentItem.dsValues
  //       .forEach((element) {
  //     String key = "${element.ctrlType}_${element.stage}";
  //     if (amplifierConfigurationHelper.dsManualAlignmentListMap
  //         .containsKey(key)) {
  //       element.value = double.parse(amplifierConfigurationHelper
  //           .dsManualAlignmentListMap[key]
  //           .toString());
  //     }
  //   });
  // }
  //
  // Future<void> updateSelections() async {
  //   await Future.forEach(amplifierConfigurationHelper.dsManualAlignmentItem.dsValues, (element) async {
  //     element.isSelected = false;
  //   });
  // }
  //
  // Future<void> updateSelectedValue(int refreshIndex) async {
  //   int index = amplifierConfigurationHelper.dsManualAlignmentItem.dsValues
  //       .indexWhere((element) => ((element.ctrlType == listTabs[refreshIndex].ampDeviceItem.mapCtrlStage.keys.first) &&
  //       (element.stage == listTabs[refreshIndex].ampDeviceItem.mapCtrlStage.values.first)));
  //   if (index != -1) {
  //     amplifierConfigurationHelper.dsManualAlignmentItem.dsValues[index].isSelected = true;
  //   }
  // }
  //
  //
  // Future<bool?> confirmationView({
  //   required AmplifierController controller,
  //   required BuildContext context,
  // }) async {
  //   return await showManualAlignmentConfirmationDialog(
  //     context,
  //     "Changes are not Saved!!",
  //     "Are you sure you want to discard the changes?",
  //   );
  // }
}
