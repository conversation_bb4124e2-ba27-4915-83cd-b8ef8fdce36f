import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/main_ql_node.dart';

import '../../controller/dashboard_controller.dart';

class SplashPageHelper {
  late SplashPageState state;
  late AuthController authController;
  DashboardController? dashboardController;
  RxBool isPleaseWaitTextDisplay = false.obs;
  RxBool isCheckStatusTextDisplay = false.obs;
  SplashPageHelper(this.state) {
    dashboardController = Get.put(DashboardController());
    authController = Get.put(AuthController());
  }
  bool isTokenValidationInProgress = false;

  healthCheck(context) async {
    try {
      dynamic data = await dashboardController!.checkHealth(state.context);
      if (data != null) {
        state.stopTimer();
        isPleaseWaitTextDisplay.value = false;
        if (!isTokenValidationInProgress) {
          isTokenValidationInProgress = true;
          await authController.validateToken(context);
        }
      } else {
        isPleaseWaitTextDisplay.value = true;
      }
    } catch (e) {
      print("healthCheck Exception--> $e");
      isCheckStatusTextDisplay.value = true;
    }
  }
}
