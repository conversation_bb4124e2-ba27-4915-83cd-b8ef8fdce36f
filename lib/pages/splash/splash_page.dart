import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:quantumlink_node/app/constants/app_constant.dart';
import 'package:quantumlink_node/app/helper/app_ui_helper.dart';
import 'package:quantumlink_node/app/ui/app_image_assets.dart';
import 'package:quantumlink_node/app/ui/app_status_bar.dart';
import 'package:quantumlink_node/generated/l10n.dart';
import 'package:quantumlink_node/pages/splash/splash_page_helper.dart';

late BuildContext buildContext;

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => SplashPageState();
}

class SplashPageState extends State<SplashPage> with TickerProviderStateMixin {
  SplashPageHelper? splashPageHelper;
  Timer? timer;

  @override
  void initState() {
    startTimer();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    splashPageHelper ?? (splashPageHelper = SplashPageHelper(this));
    buildContext = context;
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: getAppStatusBar(),
      child: Scaffold(
        backgroundColor: AppColorConstants.colorBackground,
        body: splashLogoView(),
      ),
    );
  }

  Widget splashLogoView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          alignment: Alignment.center,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(getSize(3)),
            child: AppImageAsset(
              image: AppAssetsConstants.appLogo,
              fit: BoxFit.contain,
              width: getSize(190),
              height: getSize(190),
            ),
          ),
        ),
        Lottie.asset(
          height: 100,
          AppAssetsConstants.splashLoaderAnimation,
        ),
        SizedBox(height: 20,),
        Obx(() {
          return Column(
            children: [
              (splashPageHelper!.isPleaseWaitTextDisplay.value)
                  ? Text(
                      S.of(context).healthyServicesMessage,
                      style: TextStyle(
                          fontSize: 20,
                          color: AppColorConstants.colorPrimary,
                          fontWeight: FontWeight.bold),
                    )
                  : Container(),
              if  (splashPageHelper!.isCheckStatusTextDisplay.value)
                Text(
                  S.of(context).wifiStatusMessage,
                style: TextStyle(
                    fontSize: 18,
                    color: AppColorConstants.colorPrimary,
                    fontWeight: FontWeight.bold),
              )
            ],
          );
        }),
      ],
    );
   }

  void startTimer() {
    timer = Timer.periodic(const Duration(seconds: 5), (timer)async {
      await splashPageHelper?.healthCheck(context);
    });
  }

  void stopTimer() {
    timer?.cancel();
    timer = null;
  }

  @override
  void dispose() {
    stopTimer();
    super.dispose();
  }
}
