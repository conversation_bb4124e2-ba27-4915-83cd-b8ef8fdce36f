import 'package:quantumlink_node/app_import.dart';
import 'package:universal_html/html.dart' as html;

class SignInPage extends StatefulWidget {
  const SignInPage({super.key});

  @override
  State<SignInPage> createState() => SignInPageState();
}

class SignInPageState extends State<SignInPage> {
  SignInPageHelper? signInPageHelper;
  late AuthController authController;
  final GlobalKey<FormState> deviceFormKey = GlobalKey<FormState>();
  late StreamSubscription<html.PopStateEvent> _popStateListener;

  @override
  initState() {
    super.initState();
    onPopStateOfBrowser();
  }

  onPopStateOfBrowser(){
    _popStateListener= html.window.onPopState.listen((event) async {
      router.pushReplacement(RouteHelper.routeSignInPage);
    });
  }

  @override
  void dispose() {
    _popStateListener.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AuthController>(
      init: AuthController(),
      builder: (AuthController controller) {
        authController = controller;
        signInPageHelper ?? (signInPageHelper = SignInPageHelper(this));
        buildContext = context;
        return Scaffold(
          appBar: AppBar(
            toolbarHeight: getSize(80),
            title:
                const SizedBox(height: 60, child: AppImageAsset(image: AppAssetsConstants.appBar)),
            backgroundColor: AppColorConstants.colorAppbar,
          ),
          backgroundColor: AppColorConstants.colorBackgroundDark,
          body: Stack(
            alignment: Alignment.center,
            children: [
              Container(
                  color: AppColorConstants.colorPrimary,
                  width: double.infinity,
                  height: double.infinity,
                  child: const AppImageAsset(
                    image: AppAssetsConstants.loginBackground,
                    fit: BoxFit.fitHeight,
                  )),
              getBody(),
             // signInPageHelper!.apiStatus == ApiStatus.loading ? const AppLoader() : Container()
            ],
          ),
        );
      },
    );
  }

  Widget getBody() {
    return SingleChildScrollView(
      child: Container(
        width: double.infinity,
        padding:EdgeInsets.only(bottom: getSize(80),top: getSize(10)),
        child: Column(
          children: [
            AppText(
              S.of(context).empoweringConnectivity,
              textAlign: TextAlign.center,
              style: TextStyle(
                  color: AppColorConstants.colorWhite,
                  fontSize: getSize(31),
                  fontWeight: FontWeight.w400,
                  fontFamily: AppAssetsConstants.openSans),
            ),
            SizedBox(height: getSize(25)),
            AppText(S.of(context).pavingTheWayTitle,
                textAlign: TextAlign.center,
                style: TextStyle(
                    color: AppColorConstants.colorWhite,
                    fontSize: getSize(14),
                    fontWeight: FontWeight.w300,
                    fontFamily: AppAssetsConstants.openSans)),
            SizedBox(height: getSize(50)),
            // if (signInPageHelper?.connectedSsid != null && (signInPageHelper?.connectedSsid.isNotEmpty ?? false)) ...[
            //   Column(
            //     children: [
            //       Row(
            //         mainAxisAlignment: MainAxisAlignment.center,
            //         children: [
            //           if ( (signInPageHelper?.isConnectedWithInternet.isFalse ?? true)) ...[
            //             Container(
            //               height: getSize(16),
            //               width: getSize(16),
            //               decoration: BoxDecoration(
            //                 border: Border.all(color: AppColorConstants.colorWhite),
            //                 color: signInPageHelper?.connectedSsid['internet'] == true
            //                     ? AppColorConstants.lightGreen
            //                     : AppColorConstants.lightRed,
            //                 shape: BoxShape.circle,
            //               ),
            //             ),
            //             SizedBox(width: getSize(10)),
            //           ],
            //           if (signInPageHelper?.connectedSsid.containsKey('connected_ssid') == true && (signInPageHelper?.connectedSsid['connected_ssid'].isNotEmpty ?? true)) ... [
            //             AppText(
            //               (signInPageHelper?.isConnectedWithInternet.isTrue ?? false) ? S.of(context).connectedWith : S.of(context).notConnectedWith,
            //               style: TextStyle(
            //                 color: AppColorConstants.colorWhite,
            //                 fontSize: getSize(14),
            //                 fontWeight: FontWeight.w300,
            //                 fontFamily: AppAssetsConstants.openSans,
            //               ),
            //             ),
            //           ],
            //         ],
            //       ),
            //       if (signInPageHelper?.isConnectedWithInternet.isTrue ?? false) ...[
            //         Row(
            //           mainAxisAlignment: MainAxisAlignment.center,
            //           crossAxisAlignment: CrossAxisAlignment.center,
            //           mainAxisSize: MainAxisSize.min,
            //           children: [
            //             Container(
            //               height: getSize(16),
            //               width: getSize(16),
            //               decoration: BoxDecoration(
            //                 border: Border.all(color: AppColorConstants.colorWhite),
            //                 color: signInPageHelper?.connectedSsid['internet'] == true
            //                     ? AppColorConstants.lightGreen
            //                     : AppColorConstants.lightRed,
            //                 shape: BoxShape.circle,
            //               ),
            //             ),
            //             SizedBox(width: getSize(10)),
            //             Padding(
            //               padding: const EdgeInsets.only(top: 4),
            //               child: AppText(
            //                 (signInPageHelper?.connectedSsid.containsKey('connected_ssid') == true && (signInPageHelper?.connectedSsid['connected_ssid'].isEmpty ?? true)) ? S.of(context).connectedWithEthernet : signInPageHelper?.connectedSsid['connected_ssid'],
            //                 style: TextStyle(color: AppColorConstants.colorWhite, fontWeight: FontWeight.w500, fontSize: 18),
            //               ),
            //             ),
            //           ],
            //         ),
            //       ],
            //       Padding(
            //         padding: const EdgeInsets.only(top: 4),
            //         child: AppText(
            //           signInPageHelper?.connectedSsid['ip'] ?? "",
            //           style: TextStyle(color: AppColorConstants.colorWhite, fontWeight: FontWeight.w500, fontSize: 14),
            //         ),
            //       ),
            //       SizedBox(height: getSize(16)),
            //       AppButton(
            //         buttonHeight: getSize(43),
            //         buttonWidth: getSize(200),
            //         buttonRadius: 20,
            //         fontSize: 14,
            //         buttonColor: AppColorConstants.colorChartLine,
            //         onPressed: () => context.go(RouteHelper.routeSSIdReportPage),
            //         buttonName: S.of(context).wifiUplink,
            //         fontFamily: AppAssetsConstants.openSans,
            //         borderColor: AppColorConstants.colorAppbar,
            //       ),
            //     ],
            //   ),
            // ],
            SizedBox(height: getSize(20)),
            Form(
              key: deviceFormKey,
              child: Container(
                decoration: BoxDecoration(
                    color: AppColorConstants.colorWhite,
                    borderRadius: BorderRadius.circular(getSize(8))),
                constraints: BoxConstraints(maxWidth: getSize(320), maxHeight:getSize(320)),
                child: Column(mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      alignment: Alignment.center,
                      padding: EdgeInsets.symmetric(vertical: getSize(20), horizontal: getSize(20)),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Image.asset(
                            AppAssetsConstants.logInUserImage,
                            width: getSize(120),
                            height: getSize(100),
                          ),
                          SizedBox(height: getSize(20)),
                          emailTextFieldView(),
                          SizedBox(height: getSize(10)),
                          // if(!AppConfig.shared.isQLCentral) passwordTextFieldView(),
                          // SizedBox(height: getSize(10)),
                          authoriseButtonView(),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget emailTextFieldView() {
    return RawAutocomplete<String>(
      optionsBuilder: (TextEditingValue textEditingValue) {
        if (textEditingValue.text == '') {
          return const Iterable<String>.empty();
        }
        return signInPageHelper!.suggestionList.where((String option) {
          return option.toLowerCase().contains(textEditingValue.text.toLowerCase());
        });
      },
      onSelected: (String selection) {
        debugPrint('$selection selected');
      },
      fieldViewBuilder: (BuildContext context, TextEditingController textEditingController,
          FocusNode focusNode, VoidCallback onFieldSubmitted) {
        signInPageHelper!.txtEmail = textEditingController;
        return AppTextFormField(
          focusedBorderColor: AppColorConstants.colorPrimary,
          enabledBorderColor: AppColorConstants.colorH2.withOpacity(0.5),
          fontFamily: AppAssetsConstants.openSans,
          contentPadding: const EdgeInsets.fromLTRB(10, 13, 10, 13),
          focusNode: focusNode,
          borderRadius: 8,
          onFieldSubmitted: (text) async {
            onFieldSubmitted();
            if (!deviceFormKey.currentState!.validate()) {
              FocusScope.of(context).requestFocus(focusNode);
              return;
            }
            await signInPageHelper!.signInMethod(context, signInPageHelper!.txtEmail.text.trim());
            signInPageHelper!.saveEmail(signInPageHelper!.txtEmail.text);
          },
          hintText: S.of(context).email,
          hintTextColor: AppColorConstants.colorH2.withOpacity(0.5),
          hintFontSize: 14,
          controller: signInPageHelper!.txtEmail,
          maxLines: 1,
          textInputType: TextInputType.emailAddress,
          validator: (value) {
            if (value!.toString().trim().isEmpty) {
              return S.of(context).thisFieldIsRequired;
            } else if (!signInPageHelper!.validEmailAddress(value)) {
              return S.of(context).validEmailAddress;
            } else if (!signInPageHelper!.validEmailDomains(value)) {
              return S.of(context).validEmail;
            } else {
              return null;
            }
          },
        );
      },
      optionsViewBuilder: (BuildContext context, AutocompleteOnSelected<String> onSelected,
          Iterable<String> options) {
        return Align(
          alignment: Alignment.topLeft,
          child: Material(
            elevation: 4.0,
            child: Container(
              constraints: const BoxConstraints(maxHeight: 200, maxWidth: 340),
              child: ListView.builder(
                padding: const EdgeInsets.all(8.0),
                itemCount: options.length,
                itemBuilder: (BuildContext context, int index) {
                  final String option = options.elementAt(index);
                  return ListTile(
                      trailing: GestureDetector(
                          onTap: () {
                            signInPageHelper!.removeEmailFromList(options.elementAt(index));
                          },
                          child: const Icon(
                            Icons.close,
                            size: 15,
                          )),
                      onTap: () {
                        onSelected(option);
                      },
                      title: Text(
                        option,
                        style: const TextStyle(fontFamily: AppAssetsConstants.notoSans),
                      ));
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Widget passwordTextFieldView() {
    return AppTextFormField(
      focusedBorderColor: AppColorConstants.colorPrimary,
      enabledBorderColor: AppColorConstants.colorH2.withOpacity(0.5),
      fontFamily: AppAssetsConstants.openSans,
      focusNode: signInPageHelper?.passwordFocusNode,
      contentPadding: const EdgeInsets.fromLTRB(10, 13, 10, 13),
      borderRadius: 8,
      onFieldSubmitted: (text) async {
        if (!deviceFormKey.currentState!.validate()) {
          FocusScope.of(context).requestFocus(signInPageHelper?.passwordFocusNode);
          return;
        }
        await signInPageHelper!.signInMethod(context, signInPageHelper!.txtEmail.text.trim());
        signInPageHelper!.saveEmail(signInPageHelper!.txtEmail.text);
      },
      hintText: S.of(context).password,
      obscureText: signInPageHelper!.isObscureText,
      hintTextColor: AppColorConstants.colorH2.withOpacity(0.5),
      hintFontSize: 14,
      controller: signInPageHelper!.txtPassword,
      maxLines: 1,
      textInputType: TextInputType.visiblePassword,
      validator: (value) {
        if (value!.toString().trim().isEmpty) {
          return S.of(context).thisFieldIsRequired;
        } else if (signInPageHelper!.apiStatus == ApiStatus.failed) {
          return signInPageHelper!.responseString;
        } else {
          return null;
        }
      },
      suffixIcon: InkWell(child:Icon(
          signInPageHelper!.isObscureText ? Icons.visibility : Icons.visibility_off,
          color: AppColorConstants.colorH2.withOpacity(0.5),
        ),
        onTap: () => signInPageHelper!.togglePasswordVisibility(),
      ),
    );
  }

  Widget authoriseButtonView() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 15),
            child: AppButton(loadingStatus: signInPageHelper!.apiStatus,
                buttonHeight: getSize(43),
                buttonRadius: 20,
                fontSize: 14,
                buttonColor: AppColorConstants.colorChartLine,
                onPressed: () async {
                  if (signInPageHelper!.apiStatus != ApiStatus.loading) {
                      signInPageHelper!.apiStatus = ApiStatus.initial;
                    if (!deviceFormKey.currentState!.validate()) {
                      return;
                    }
                    await signInPageHelper!.signInMethod(context, signInPageHelper!.txtEmail.text);
                    signInPageHelper!.saveEmail(signInPageHelper!.txtEmail.text);
                  }
                },
                buttonName: S.of(context).login,
                fontFamily: AppAssetsConstants.openSans,
                borderColor: AppColorConstants.colorAppbar),
          ),
        ),
      ],
    );
  }


}
