import 'package:flutter/scheduler.dart';
import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/app/cache/app_shared_preference.dart';
import 'package:quantumlink_node/serialized/auth/auth_response.dart';

class SignInPageHelper {
  late SignInPageState state;
  ApiStatus apiStatus = ApiStatus.initial;
  TextEditingController txtEmail = TextEditingController();
  TextEditingController txtPassword = TextEditingController();
  final FocusNode passwordFocusNode = FocusNode();
  String? responseString;
  bool isObscureText = true;
  Map<String, dynamic> connectedSsid = {};
  RxBool isConnectedWithInternet = false.obs;
  SignInPageHelper(this.state) {
    SchedulerBinding.instance.addPostFrameCallback(
      (timeStamp) async {
        // if (AppConfig.shared.isQLCentral == false){
        //   await getSSIDDetails();
        // }
        state.authController.getAllDomains();
        _loadEmails();
      },
    );
  }

  validEmailDomains(String value) {
    return state.authController.isEmailDomainAllowed(value);
  }

  validEmailAddress(String email){
    final regex = RegExp(r'^[\w\.-]+@[\w\.-]+\.\w+$');
    return regex.hasMatch(email);
  }

  Future signInWithGoogle(context, email) async {
    apiStatus = ApiStatus.loading;
    updateState();
    await state.authController.signInWithGoogle(context,email);
    apiStatus = ApiStatus.success;
    updateState();
  }

  Future aadAuthSignIn(context, email) async {
    apiStatus = ApiStatus.loading;
    updateState();
    await state.authController.signIn(context, email);
    apiStatus = ApiStatus.success;
    updateState();
  }

  // Sign In Method Action For QL and Node
  Future<void> signInMethod(BuildContext context, email) async {
    await state.authController.updateMsConfig(email);
    if (!AppConfig.shared.grantAccess && !AppConfig.shared.isOpenFromBLE) {
      S.of(context).restrictSigninMessage.showError(context);
    } else {
      final isValid = await isProviderValid();
      if (isValid) {
        if (AppConfig.shared.isGoogleSignIn) {
          await signInWithGoogle(context, email);
        } else {
          await aadAuthSignIn(context, email);
        }
      } else {
        S.of(context).providerNotFound.showError(context);
      }
    }

    // if (AppConfig.shared.isQLCentral) {
    //  await signIn(context);
    // } else {
    //   if (!AppConfig.shared.aadAuth) {
    //     await state.authController.signIn(context);
    //   }
    //   else{
    //     await signInWithPassword(emailId: txtEmail.text, password: txtPassword.text);
    //   }
    // }
  }

  Future<bool> isProviderValid() async {
    return (AppConfig.shared.provider.isNotEmpty &&
        (AppConfig.shared.provider.toLowerCase() == AppStringConstants.google ||
            AppConfig.shared.provider.toLowerCase() == AppStringConstants.microsoft));
  }

  Future<void> signInWithPassword({required String emailId, required String password}) async {
    apiStatus = ApiStatus.loading;
    updateState();
    Response? responseData =
        await state.authController.signWithPassword(state.context, emailId: emailId, password: password);
    if (responseData == null) {
      apiStatus = ApiStatus.failed;
      responseString = S.of(state.context).somethingWentWrong;
      state.deviceFormKey.currentState!.validate();
      updateState();
      return;
    }
    Map<String, dynamic> jsonResponse = json.decode(responseData.body);
    if (responseData.statusCode == 400) {
      apiStatus = ApiStatus.failed;
      responseString = jsonResponse['detail'];
      state.deviceFormKey.currentState!.validate();
      updateState();
    } else if (responseData.statusCode == 200) {
      AuthResponse authResponse = AuthResponse.fromJson(jsonResponse);
      await state.authController.setAT(authResponse.accessToken);
      await state.authController.setAtRefreshToken(authResponse.refreshToken);
      await state.authController.validateToken(state.context);
    }
    apiStatus = ApiStatus.success;
    updateState();
  }

  void togglePasswordVisibility() {
    isObscureText = !isObscureText;
    updateState();
  }

  saveEmail(String email) async {
    List<String> emails = (await getPrefListValue(AppSharedPreference.emails)) ?? [];
    if (!emails.contains(email)) {
      emails.add(email);
      await setPrefListValue(AppSharedPreference.emails, emails);
      _loadEmails();
    }
  }

  List<String> suggestionList = [];
  removeEmailFromList(String email) async {
    suggestionList.remove(email);
    state.authController.update();
    await setPrefListValue(AppSharedPreference.emails, suggestionList);
  }

  _loadEmails() async {
    if (await checkPrefKey(AppSharedPreference.emails)) {
      suggestionList = (await getPrefListValue(AppSharedPreference.emails))!;
    }
    state.authController.update();
  }

  // Future<void> getSSIDDetails() async {
  //   apiStatus = ApiStatus.loading;
  //   updateState();
  //   Future.delayed(
  //     Duration(seconds: 3),
  //     () async {
  //       connectedSsid = await state.authController.ssidReport(state.context);
  //       if (connectedSsid.containsKey('internet') && connectedSsid['internet'] != true) {
  //         gotoSSIdReportPage();
  //       }
  //       isConnectedWithInternet.value = connectedSsid.containsKey('internet') && connectedSsid['internet'] == true;
  //       apiStatus = ApiStatus.success;
  //       updateState();
  //     },
  //   );
  // }

  void updateState() => state.authController.update();
}

