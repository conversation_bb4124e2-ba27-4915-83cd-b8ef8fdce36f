import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:quantumlink_node/app_import.dart';

class AccessDeniedPage extends StatelessWidget {
  const AccessDeniedPage({super.key});

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: getAppStatusBar(),
      child: Scaffold(
        backgroundColor: AppColorConstants.colorBackground,
        body: _buildAccessDeniedContent(context),
      ),
    );
  }

  Widget _buildAccessDeniedContent(BuildContext context) {
    return ScreenLayoutTypeBuilder(
      builder: (context, screenType, constraints) {
        final isWeb = screenType == ScreenLayoutType.desktop || 
                     screenType == ScreenLayoutType.tablet;
        final screenWidth = MediaQuery.of(context).size.width;
        final screenHeight = MediaQuery.of(context).size.height;
        
        return Center(
          child: SingleChildScrollView(
            child: Container(
              width: isWeb ? 500 : screenWidth * 0.9,
              padding: EdgeInsets.symmetric(
                horizontal: getSize(24),
                vertical: getSize(32),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  _buildLockIcon(),
                  SizedBox(height: getSize(40)),
                  _buildTitle(context),
                  SizedBox(height: getSize(16)),
                  _buildMainMessage(context),
                  SizedBox(height: getSize(32)),
                  _buildContactMessage(context),
                  if (!isWeb) SizedBox(height: screenHeight * 0.1),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildLockIcon() {
    return Container(
      width: getSize(130),
      height: getSize(130),
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: AppColorConstants.colorPrimary.withOpacity(0.1),
      ),
      child: Center(
        child: Container(
          width: getSize(70),
          height: getSize(70),
          decoration: BoxDecoration(
            color: AppColorConstants.colorPrimary,
            borderRadius: BorderRadius.circular(getSize(12)),
          ),
          child: Icon(
            Icons.lock,
            size: getSize(40),
            color: AppColorConstants.colorWhite,
          ),
        ),
      ),
    );
  }

  Widget _buildTitle(BuildContext context) {
    return AppText(
      S.of(context).accessDenied,
      style: TextStyle(
        fontSize: getSize(28),
        fontWeight: FontWeight.bold,
        color: AppColorConstants.colorBlack,
        fontFamily: AppAssetsConstants.openSans,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildMainMessage(BuildContext context) {
    return AppText(
      S.of(context).accessDeniedMessage,
      style: TextStyle(
        fontSize: getSize(16),
        color: AppColorConstants.colorH2,
        fontFamily: AppAssetsConstants.openSans,
        height: 1.5,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildContactMessage(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: getSize(16),
        vertical: getSize(12),
      ),
      decoration: BoxDecoration(
        color: AppColorConstants.colorBackgroundDark,
        borderRadius: BorderRadius.circular(getSize(8)),
        border: Border.all(
          color: AppColorConstants.colorDivider.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: AppText(
        S.of(context).contactAdministrator,
        style: TextStyle(
          fontSize: getSize(14),
          color: AppColorConstants.colorH3,
          fontFamily: AppAssetsConstants.openSans,
          height: 1.4,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}
