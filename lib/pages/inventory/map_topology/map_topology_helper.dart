import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/controller/map_topology_controller.dart';
import 'package:quantumlink_node/serialized/map_topology/map_layers_response.dart';

import 'map_topology_view.dart';


/// Helper class that manages ALL data, variables, and business logic
class MapTopologyHelper {
  late MapTopologyPageState state;
  late MapTopologyController mapTopologyController;

  // ====== CONFIGURATION CONSTANTS ======
  static const double deviceMarkerSize = 55.0;
  static const double deviceMarkerInnerSize = 40.0;
  static const double gatewayMarkerSize = 62.0;
  static const double gatewayMarkerInnerSize = 45.0;
  static const double gatewayMarkerLegendInnerSize = 40.0;
  static const double initialZoom = 5;
  static const double zoomStep = 1.0;
  static const Duration debounceDelay = Duration(milliseconds: 800);

  // ====== ALL DATA VARIABLES ======
  // Filter states
  bool showOffline = true;
  bool showOnline = true;
  bool showDevices = true;
  bool showGateways = true;
  bool showDeviceLinks = true;

  // Map data
  List<MapDevice> _currentDevices = [];
  List<MapGateway> _currentGateways = [];
  List<MapDeviceLink> _currentConnections = [];
  List<DeviceCluster> _deviceClusters = [];
  List<MapDeviceWithOffset> _devicesWithOffset = [];

  // Animation state for multi-device locations (only one can be expanded at a time)
  String? _currentExpandedLocation;


  // State variables
  bool _isLoading = false;
  double _currentZoom = initialZoom;
  String? _errorMessage;


  // Zoom management variables
  Timer? _debounceTimer;
  ParsedMapLayersData? _cachedData;
  List<double>? _lastBbox;
  List<String>? vendorCodeList=[];
  String selectedVendorCode=AppStringConstants.all;
  ApiStatus ampProviderStatus = ApiStatus.initial;
  late ScreenLayoutType screenLayoutType;
  // Controllers
  late MapController mapController;

  // ====== GETTERS FOR UI ======
  // List<MapDevice> get currentDevices => _currentDevices;

  List<MapGateway> get currentGateways => _currentGateways;

  List<MapDeviceLink> get currentConnections => _currentConnections;

  List<DeviceCluster> get deviceClusters => _deviceClusters;

  List<MapDeviceWithOffset> get devicesWithOffset => _devicesWithOffset;

  String? get currentExpandedLocation => _currentExpandedLocation;

  bool get isLoading => _isLoading;

  bool get isDevicesShowEnabled => _currentZoom >= 12;

  double get currentZoom => _currentZoom;

  String? get errorMessage => _errorMessage;

  // ParsedMapLayersData? get cachedData => _cachedData;

  // ====== SETTERS FOR UI ======
  void setShowDevices(bool value) {
    showDevices = value;
    updateState();
    refreshDataWithNewFilters();
  }
  void setShowGateways(bool value) {
    showGateways = value;
    updateState();
    refreshDataWithNewFilters();
  }
  void setShowDeviceLinks(bool value) {
    showDeviceLinks = value;
    updateState();
    refreshDataWithNewFilters();
  }

  // Device status filter
  final List<String> allDeviceStatuses = [AppStringConstants.online, AppStringConstants.offline, AppStringConstants.pending];
  List<String> selectedDeviceStatuses = [];

  final Map<int, String> prodIdCodeMap = {
    0:"All",
    277: "Line Extender (P2A)",
    288: "Line Extender",
    304: "Line Extender (BLE)",
    320: "High-Gain Dual System Amp",
    321: "High-Gain Dual System Amp (XL)",
    336: "Mini-Bridger System Amp",
    352: "High-Gain Balanced Triple Amp",
  };
  int selectedProdId = 0;
  final Map<String, String> deviceTypeCodeMap = {
    "Line Extender (P2A)": "LE",
    "Line Extender": "LE",
    "Line Extender (BLE)": "BLE",
    "High-Gain Dual System Amp": "HGD",
    "High-Gain Dual System Amp (XL)": "HGD",
    "Mini-Bridger System Amp": "MB Amp",
    "High-Gain Balanced Triple Amp": "HGBT",
  };
  // ====== CONSTRUCTOR ======
  MapTopologyHelper(this.state) {
    mapTopologyController = Get.put(MapTopologyController());
    mapController = MapController();
    getAmpProviderList();
    getData();
  }

  // ====== DATA MANAGEMENT METHODS ======

  /// Load initial data
  void getData() async {
     clearError();
    _setLoading(true);
    try {
      // Build layers based on filter states
      final layers = _buildLayersArray();
      final request = MapLayersRequest(
        layers: layers,
        filters: {},
        bbox: [0],
        context: {},
        autoFit: false,
      );
      final result = await mapTopologyController.getMapLayersData(context: state.context, request: request);

      if (result is ParsedMapLayersData) {
        final devices = _convertMapDevices(result.devices);
        final topologyData = _convertToTopologyData(result);

        _updateDevices(devices);
        _updateTopologyData(topologyData);
      } else {
        _handleInvalidResponse();
      }
    } catch (e) {
      _handleError(e.toString());
    } finally {
      _setLoading(false);
    }
  }
  getAmpProviderList() async {
    ampProviderStatus = ApiStatus.loading;
    updateState();
    ProvisionController  provisionController = Get.put(ProvisionController());
    List<AmplifierProviderItem>? ampProviderList  = await provisionController.getAmpProviderList(context: state.context);
    vendorCodeList = [
      AppStringConstants.all, // Index 0, default label
      ...?ampProviderList
          ?.where((e) => e.code != null && e.code!.isNotEmpty)
          .map((e) => e.code!)
    ];
    ampProviderStatus = ApiStatus.success;
    updateState();
  }

  updateState() {
    state.mapTopologyController.update();
  }

  /// Set filter states
  void setShowOffline(bool value) {
    showOffline = value;
    updateState();
  }

  void setShowOnline(bool value) {
    showOnline = value;
    updateState();
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    updateState();
  }

  /// Set error message
  void _setError(String? error) {
    _errorMessage = error;
  }

  /// Clear error message
  void clearError() {
    _setError(null);
    updateState();
  }

  /// Set current zoom
  void setCurrentZoom(double zoom) {
    _currentZoom = zoom;
  }

  /// Update devices data
  void _updateDevices(List<MapDevice> devices) {
    _currentDevices = devices;
    _createDevicesWithOffset();
    _setError(null);
  }

  /// Update topology data
  void _updateTopologyData(ParsedMapLayersData data) {
    _currentGateways = data.gateways;
    _currentConnections = data.deviceLinks;
    _deviceClusters = data.deviceClusters;
  }

  /// Handle map zoom changes with debouncing
  void onMapZoomChanged(BuildContext context, List<double> bbox) {
    _lastBbox = bbox;
    _debounceTimer?.cancel();
    _debounceTimer = Timer(debounceDelay, () {
      _fetchDevicesForZoomLevel(context, bbox);
    });
  }

  /// Fetch devices for zoom level
  Future<void> _fetchDevicesForZoomLevel(BuildContext context, List<double> bbox) async {
    clearError();
    if (_isLoading) return;
    _setLoading(true);

    try {
      final layers = _buildLayersArray();
      final request = MapLayersRequest(
        layers: layers,
        filters: buildFilters(),
        bbox: bbox,
        context: {},
        autoFit: false,
      );

      final result = await mapTopologyController.getMapLayersData(
        context: context,
        request: request,
      );

      if (result is ParsedMapLayersData) {
        _cachedData = result;
        _updateUIWithData(result);
      } else {
        _handleInvalidResponse();
      }
    } catch (e) {
      _handleError(e.toString());
    } finally {
      _setLoading(false);
      updateState();
    }
  }

  /// Update UI with data
  void _updateUIWithData(ParsedMapLayersData data) {
    final devices = _convertMapDevices(data.devices);
    _updateDevices(devices);
    final topologyData = _convertToTopologyData(data);
    _updateTopologyData(topologyData);
  }

  /// Handle invalid response
  void _handleInvalidResponse() {
    debugLogs("Invalid response type from API");
    _setError("Invalid response from server");
    _fallbackToCachedData();
  }

  /// Handle errors
  void _handleError(String error) {
    debugLogs("Error fetching devices: $error");
    _setError("Failed to fetch map data: $error");
    _fallbackToCachedData();
  }

  /// Fallback to cached data
  void _fallbackToCachedData() {
    if (_cachedData != null) {
      _updateUIWithData(_cachedData!);
    }
  }

  /// Handle zoom button press with smooth animation
  Future<void> zoomMap(int direction) async {
    _currentZoom = mapController.camera.zoom;
    final center = mapController.camera.center;
    final targetZoom = _currentZoom + direction;

    // Use smooth zoom animation for button presses
    await animatedZoomToLocation(
      center,
      targetZoom,
      duration: const Duration(milliseconds: 600)
    );
  }

  /// Smooth animated zoom to location (like Google Maps)
  Future<void> animatedZoomToLocation(LatLng targetLocation, double targetZoom, {Duration? duration}) async {
    final currentZoom = mapController.camera.zoom;
    final currentCenter = mapController.camera.center;

    // Use default duration if not provided
    final animationDuration = duration ?? const Duration(milliseconds: 500);

    // Calculate zoom difference to determine if we need smooth animation
    final zoomDifference = (targetZoom - currentZoom).abs();

    if (zoomDifference <= 0.5) {
      // Small zoom change - direct move
      mapController.move(targetLocation, targetZoom);
      return;
    }

    // Create smooth animation with multiple steps
    const int steps = 20; // Number of animation steps
    final stepDuration = Duration(milliseconds: animationDuration.inMilliseconds ~/ steps);

    for (int i = 1; i <= steps; i++) {
      final progress = i / steps;

      // Use easeInOut curve for smooth animation
      final easedProgress = _easeInOut(progress);

      // Interpolate position
      final lat = currentCenter.latitude + (targetLocation.latitude - currentCenter.latitude) * easedProgress;
      final lng = currentCenter.longitude + (targetLocation.longitude - currentCenter.longitude) * easedProgress;

      // Interpolate zoom
      final zoom = currentZoom + (targetZoom - currentZoom) * easedProgress;

      // Move map to interpolated position
      mapController.move(LatLng(lat, lng), zoom);

      // Wait for next frame
      await Future.delayed(stepDuration);
    }

    // Ensure we end exactly at target
    mapController.move(targetLocation, targetZoom);
    _currentZoom = targetZoom;
    updateState();
  }

  /// Easing function for smooth animation (easeInOut curve)
  double _easeInOut(double t) {
    return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
  }

  /// Advanced smooth zoom with Google Maps-like animation
  Future<void> smoothZoomToLocation(LatLng targetLocation, double targetZoom, {
    Duration? duration,
    Curve curve = Curves.easeInOut,
  }) async {
    final currentZoom = mapController.camera.zoom;
    final currentCenter = mapController.camera.center;

    // Use default duration if not provided
    final animationDuration = duration ?? const Duration(milliseconds: 500);

    // Calculate if we need animation
    final zoomDifference = (targetZoom - currentZoom).abs();
    final distanceKm = _calculateDistance(currentCenter, targetLocation);

    if (zoomDifference <= 0.1 && distanceKm < 0.1) {
      // Very small change - direct move
      mapController.move(targetLocation, targetZoom);
      return;
    }

    // Use high-frequency updates for smoother animation
    const int fps = 60; // 60 FPS for smooth animation
    final int totalFrames = (animationDuration.inMilliseconds * fps / 1000).round();
    final frameDuration = Duration(milliseconds: (500 / fps).round());

    for (int frame = 1; frame <= totalFrames; frame++) {
      final progress = frame / totalFrames;

      // Apply easing curve
      final easedProgress = _applyCurve(progress, curve);

      // Interpolate position with smooth transition
      final lat = _lerp(currentCenter.latitude, targetLocation.latitude, easedProgress);
      final lng = _lerp(currentCenter.longitude, targetLocation.longitude, easedProgress);

      // Interpolate zoom with smooth transition
      final zoom = _lerp(currentZoom, targetZoom, easedProgress);

      // Move map to interpolated position
      mapController.move(LatLng(lat, lng), zoom);

      // Wait for next frame
      await Future.delayed(frameDuration);
    }

    // Ensure we end exactly at target
    mapController.move(targetLocation, targetZoom);
    _currentZoom = targetZoom;
    updateState();
  }

  /// Apply curve to progress value
  double _applyCurve(double t, Curve curve) {
    if (curve == Curves.easeInOut) {
      return _easeInOut(t);
    } else if (curve == Curves.easeIn) {
      return t * t;
    } else if (curve == Curves.easeOut) {
      return 1 - (1 - t) * (1 - t);
    } else if (curve == Curves.elasticOut) {
      if (t == 0 || t == 1) return t;
      return pow(2, -10 * t) * sin((t - 0.1) * 2 * pi / 0.4) + 1;
    }
    return t; // Linear fallback
  }

  /// Linear interpolation
  double _lerp(double start, double end, double t) {
    return start + (end - start) * t;
  }

  /// Calculate distance between two points in kilometers
  double _calculateDistance(LatLng point1, LatLng point2) {
    const double earthRadius = 6371; // Earth's radius in kilometers
    final lat1Rad = point1.latitude * pi / 180;
    final lat2Rad = point2.latitude * pi / 180;
    final deltaLatRad = (point2.latitude - point1.latitude) * pi / 180;
    final deltaLngRad = (point2.longitude - point1.longitude) * pi / 180;

    final a = sin(deltaLatRad / 2) * sin(deltaLatRad / 2) +
        cos(lat1Rad) * cos(lat2Rad) * sin(deltaLngRad / 2) * sin(deltaLngRad / 2);
    final c = 2 * atan2(sqrt(a), sqrt(1 - a));

    return earthRadius * c;
  }

  /// Clear cache
  void clearCache() {
    _cachedData = null;
    _lastBbox = null;
  }

  /// Refresh data with new filter settings
  void refreshDataWithNewFilters() {
    List<double>? bbox = _lastBbox ?? [0];
    updateState();
    _fetchDevicesForZoomLevel(state.context, bbox);
  }
  // ====== DATA PROCESSING METHODS ======

  /// Create devices with offset for same location handling
  void _createDevicesWithOffset() {
    _devicesWithOffset.clear();

    // Group devices by exact location (lat, lng)
    final Map<String, List<MapDevice>> locationGroups = {};

    for (final device in _currentDevices) {
      final locationKey = '${device.position.latitude.toStringAsFixed(6)},${device.position.longitude.toStringAsFixed(6)}';
      locationGroups.putIfAbsent(locationKey, () => []).add(device);
    }
    // Create offset devices for each location
    for (final entry in locationGroups.entries) {
      final devices = entry.value;

      if (devices.length == 1) {
        // Single device - no offset needed
        _devicesWithOffset.add(MapDeviceWithOffset(
          device: devices.first,
          offsetPosition: devices.first.position,
          deviceIndex: 0,
          totalDevicesAtLocation: 1,
        ));
      } else {
        // Multiple devices - apply circular offset
        for (int i = 0; i < devices.length; i++) {
          final offsetPosition = _calculateOffsetPosition(
            devices.first.position,
            i,
            devices.length
          );

          _devicesWithOffset.add(MapDeviceWithOffset(
            device: devices[i],
            offsetPosition: offsetPosition,
            deviceIndex: i,
            totalDevicesAtLocation: devices.length,
          ));
        }
      }
    }
  }

  /// Calculate offset position for devices at same location
  LatLng _calculateOffsetPosition(LatLng basePosition, int index, int totalDevices) {
    if (totalDevices == 1) return basePosition;

    // Offset distance in degrees (adjust based on zoom level)
    final offsetDistance = _currentZoom > 15 ? 0.0001 : 0.0005;

    // Calculate angle for circular arrangement
    final angle = (2 * 3.14159 * index) / totalDevices;

    // Calculate offset
    final latOffset = offsetDistance * cos(angle);
    final lngOffset = offsetDistance * sin(angle);

    return LatLng(
      basePosition.latitude + latOffset,
      basePosition.longitude + lngOffset,
    );
  }

  /// Get filtered devices based on current filter settings
  List<MapDeviceWithOffset> getFilteredDevicesWithOffset() {
    return _devicesWithOffset.where((deviceWithOffset) {
      final device = deviceWithOffset.device;
      if (!showOffline && device.status == DetectedStatusType.offline) return false;
      if (!showOnline && device.status != DetectedStatusType.offline) return false;
      return true;
    }).toList();
  }

  /// Get filtered devices (legacy method for compatibility)
  List<MapDevice> getFilteredDevices() {
    return getFilteredDevicesWithOffset().map((d) => d.device).toList();
  }

  /// Toggle expansion state for a location (only one location can be expanded at a time)
  void toggleLocationExpansion(String locationKey) {
    if (_currentExpandedLocation == locationKey) {
      _currentExpandedLocation = null;
    } else {
      _currentExpandedLocation = locationKey;
    }
    updateState();
  }

  /// Check if a location is expanded
  bool isLocationExpanded(String locationKey) {
    return _currentExpandedLocation == locationKey;
  }

  /// Collapse all expanded locations
  void collapseAllLocations() {
    _currentExpandedLocation = null;
    updateState();
  }
  /// Get location key for a position
  String getLocationKey(LatLng position) {
    return '${position.latitude.toStringAsFixed(6)},${position.longitude.toStringAsFixed(6)}';
  }

  /// Get devices for animation display (count vs expanded)
  List<MapDeviceWithOffset> getDevicesForDisplay() {
    final Map<String, List<MapDeviceWithOffset>> locationGroups = {};

    // Group devices by location
    for (final deviceWithOffset in _devicesWithOffset) {
      final locationKey = getLocationKey(deviceWithOffset.device.position);
      locationGroups.putIfAbsent(locationKey, () => []).add(deviceWithOffset);
    }

    List<MapDeviceWithOffset> displayDevices = [];

    for (final entry in locationGroups.entries) {
      final locationKey = entry.key;
      final devices = entry.value;

      if (devices.length == 1) {
        // Single device - always show
        displayDevices.addAll(devices);
      } else {
        // Multiple devices - show based on expansion state
        if (isLocationExpanded(locationKey)) {
          // Show all devices with offset positions
          displayDevices.addAll(devices);
        } else {
          // Show only count marker at original position
          final firstDevice = devices.first;
          displayDevices.add(MapDeviceWithOffset(
            device: firstDevice.device,
            offsetPosition: firstDevice.device.position, // Original position
            deviceIndex: 0,
            totalDevicesAtLocation: devices.length,
            isCountMarker: true,
            locationKey: locationKey,
            allDevicesAtLocation: devices.map((d) => d.device).toList(),
          ));
        }
      }
    }

    return displayDevices;
  }

  /// Get filtered connections based on available devices
  List<MapDeviceLink> getFilteredConnections() {
    final deviceMap = {for (var d in _currentDevices) d.deviceEui: d};
    return _currentConnections.where((conn) {
      final fromDevice = deviceMap[conn.sourceId];
      final toDevice = deviceMap[conn.targetId];
      return fromDevice != null && toDevice != null;
    }).toList();
  }

  /// Get device count info text
  String getDeviceCountInfo(int devices, int gateways) {
    if (_isLoading) return S.of(state.context).loadingDevices;
    return S.of(state.context).devicesAndGatewaysShown(devices, gateways);
  }
  String getDeviceTypeCode(String deviceName) {
    return deviceTypeCodeMap[deviceName] ?? "";
  }

  String getBwModeVersion(int bwMode) {
    String ampDeviceMode = bwMode == 0
            ? "1.2"
            : bwMode == 1
                ? "1.8"
                    : "";
    return ampDeviceMode.isNotEmpty ? "- $ampDeviceMode" : "";
  }

  /// Get zoom level category for display
  String getZoomLevelCategory() {
    if (_currentZoom >= 16) return "Detailed View";
    if (_currentZoom >= 14) return "Standard View";
    if (_currentZoom >= 12) return "Overview";
    return "Regional View";
  }

  // ====== DATA CONVERSION METHODS ======

  /// Convert MapDevice list
  List<MapDevice> _convertMapDevices(List<MapDevice> mapDevices) {
    return mapDevices
        .map((mapDevice) => MapDevice(
              deviceEui: mapDevice.deviceEui,
              deviceAlias: mapDevice.deviceAlias,
              position: mapDevice.position,
              status: mapDevice.status,
              lastSeen: mapDevice.lastSeen,
              vendorCode: mapDevice.vendorCode,
              type: mapDevice.type,
              description: mapDevice.description,
              bwMode: mapDevice.bwMode,
              placement: mapDevice.placement,
            ))
        .toList();
  }

  /// Convert to topology data
  ParsedMapLayersData _convertToTopologyData(ParsedMapLayersData data) {
    final devices = _convertMapDevices(data.devices);

    final gateways = data.gateways
        .map((gateway) => MapGateway(
              gatewayId: gateway.gatewayId,
              position: gateway.position,
              name: gateway.name,
              type: gateway.type,
              status: gateway.status,
              lastSeen: gateway.lastSeen,
              domain: gateway.domain,
              hwVersion: gateway.hwVersion,
              swVersion: gateway.swVersion,
              userEmail: gateway.userEmail,
            ))
        .toList();

    final connections = data.deviceLinks
        .map((link) => MapDeviceLink(
              sourceId: link.sourceId,
              targetId: link.targetId,
              polylinePoints: link.polylinePoints,
              styleHint: link.styleHint,
            ))
        .toList();

    final deviceClusters = data.deviceClusters
        .map((cluster) => DeviceCluster(
     count:cluster.count ,
      lat: cluster.lat,
      lng: cluster.lng
    ))
        .toList();

    return ParsedMapLayersData(
      devices: devices,
      deviceLinks: connections,
      gateways: gateways,
      deviceClusters: deviceClusters
    );
  }

  /// Build layers array based on filter states
  List<String> _buildLayersArray() {
    final layers = <String>[];
    if (isDevicesShowEnabled) {
      if (showDevices) {
        layers.add(AppStringConstants.devices);
      }
      if (showGateways) {
        layers.add(AppStringConstants.gateways);
      }
      if (showDeviceLinks) {
        layers.add(AppStringConstants.deviceLinks);
      }
    } else {
      layers.add(AppStringConstants.deviceClusters);
    }
    return layers;
  }

  Map<String, Map<String, dynamic>> buildFilters() {
    final deviceFilters = <String, dynamic>{ };
    if (selectedDeviceStatuses.isNotEmpty) {
      deviceFilters["status"] = selectedDeviceStatuses.toList();
    }

    if (selectedVendorCode != AppStringConstants.all) {
      deviceFilters["vendor_code"] = selectedVendorCode;
    }

    if(selectedProdId !=0 ){
      deviceFilters["prod_id"] = selectedProdId;
    }

    return  {
      "devices": deviceFilters,
    };
  }

  // ====== STYLING HELPER METHODS ======

  /// Get status color based on device status
  static Color getStatusColor(DetectedStatusType status) {
    switch (status) {
      case DetectedStatusType.online:
        return AppColorConstants.colorGreen2;
      case DetectedStatusType.pending:
        return AppColorConstants.colorOrange;
      case DetectedStatusType.offline:
        return AppColorConstants.colorH2;
      case DetectedStatusType.missingKey:
        return AppColorConstants.colorH2;
      case DetectedStatusType.missingVendor:
        return AppColorConstants.colorH2;
      case DetectedStatusType.fwDownload:
        return AppColorConstants.colorPrimary;
    }
  }

  /// Get status icon based on device status
  static IconData getStatusIcon(DetectedStatusType status) {
    switch (status) {
      case DetectedStatusType.online:
        return Icons.check_circle;
      case DetectedStatusType.missingKey:
        return Icons.warning;
      case DetectedStatusType.pending:
        return Icons.power;
      case DetectedStatusType.offline:
        return Icons.power_off;
      case DetectedStatusType.missingVendor:
        return Icons.warning;
      case DetectedStatusType.fwDownload:
        return Icons.info;
    }
  }



  /// Get gateway icon
  static IconData getGatewayIcon() => Icons.router;

  /// Get gateway color
  static Color getGatewayColor(String status) {
    switch (status.toLowerCase()) {
      case 'online':
        return AppColorConstants.colorGreen2;
      case 'offline':
        return AppColorConstants.colorH2;
      case 'never_seen':
        return AppColorConstants.colorOrange;
      case 'unknown':
        return AppColorConstants.colorH3;
      default:
        return AppColorConstants.colorH2;
    }
  }

  /// Parse color from hex string
  static Color parseColor(String? colorHex, [Color fallback = Colors.grey]) {
    if (colorHex == null || colorHex.isEmpty) return fallback;
    try {
      final hex = colorHex.replaceAll('#', '');
      final hexColor = hex.length == 6 ? 'FF$hex' : hex;
      return Color(int.parse(hexColor, radix: 16));
    } catch (e) {
      return fallback;
    }
  }

  // ====== TOOLTIP CONTENT METHODS ======

  /// Build device tooltip content
  String buildDeviceTooltip(MapDeviceWithOffset deviceWithOffset) {
    final device = deviceWithOffset.device;
    String tooltip = '''
Device EUI: ${device.deviceEui}
Status: ${capitalizeFirst(device.status.name)}
Last Seen: ${getUtcTimeZone(device.lastSeen)}
Type : ${device.type},
Amplifier Mode ${getBwModeVersion(device.bwMode).isNotEmpty ? getBwModeVersion(device.bwMode) + " GHz" : "-"}, 
Description : ${device.description}
Placement : ${device.placement}
''';

    if (device.deviceAlias.isNotEmpty) {
      tooltip += 'Alias: ${device.deviceAlias}\n';
    }

    if (device.vendorCode.isNotEmpty == true) {
      tooltip += 'Vendor: ${device.vendorCode}\n';
    }

    if (deviceWithOffset.isMultiDeviceLocation) {
      tooltip += '\n📍 ${deviceWithOffset.totalDevicesAtLocation} devices at this location';
      tooltip += '\n🔢 Device ${deviceWithOffset.deviceIndex + 1} of ${deviceWithOffset.totalDevicesAtLocation}';
    }

    return tooltip.trim();
  }
  String capitalizeFirst(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1).toLowerCase();
  }


  /// Build gateway tooltip content
  String buildGatewayTooltip(MapGateway gateway) {
    return '''
ID: ${gateway.gatewayId}
Gateway: ${gateway.name}
Type: ${gateway.type}
Status: ${gateway.status}
Last Seen: ${getUtcTimeZone(gateway.lastSeen ?? 0)}
Domain: ${gateway.domain}
HW Version: ${gateway.hwVersion}
SW Version: ${gateway.swVersion}
User Email: ${gateway.userEmail}
'''
        .trim();
  }

  // ====== POLYLINE DATA METHOD ======

  /// Get polyline points for connection
  List<LatLng>? getPolylinePoints(MapDeviceLink conn) {
    if (conn.polylinePoints.isNotEmpty) {
      return conn.polylinePoints;
    } else {
      try {
        final sourceDevice = _currentDevices.firstWhere((d) => d.deviceEui == conn.sourceId);
        final targetDevice = _currentDevices.firstWhere((d) => d.deviceEui == conn.targetId);
        return [sourceDevice.position, targetDevice.position];
      } catch (e) {
        return null;
      }
    }
  }

  // ====== DISPOSE METHOD ======
  void dispose() {
    _debounceTimer?.cancel();
    clearCache();
  }
}

/// Map device with offset position for handling multiple devices at same location
class MapDeviceWithOffset {
  final MapDevice device;
  final LatLng offsetPosition;
  final int deviceIndex;
  final int totalDevicesAtLocation;
  final bool isCountMarker;
  final String? locationKey;
  final List<MapDevice>? allDevicesAtLocation;

  MapDeviceWithOffset({
    required this.device,
    required this.offsetPosition,
    required this.deviceIndex,
    required this.totalDevicesAtLocation,
    this.isCountMarker = false,
    this.locationKey,
    this.allDevicesAtLocation,
  });

  /// Check if this device is part of a multi-device location
  bool get isMultiDeviceLocation => totalDevicesAtLocation > 1;


  /// Get primary status for count marker (highest priority status)
  DetectedStatusType get primaryStatus {
    if (!isCountMarker || allDevicesAtLocation == null) {
      return device.status;
    }

    final devices = allDevicesAtLocation!;
    // Priority order: offline > pending > online
    if (devices.any((d) => d.status == DetectedStatusType.offline)) {
      return DetectedStatusType.offline;
    }
    if (devices.any((d) => d.status == DetectedStatusType.pending)) {
      return DetectedStatusType.pending;
    }
    if (devices.any((d) => d.status == DetectedStatusType.missingKey)) {
      return DetectedStatusType.missingKey;
    }
    if (devices.any((d) => d.status == DetectedStatusType.missingVendor)) {
      return DetectedStatusType.missingVendor;
    }
    if (devices.any((d) => d.status == DetectedStatusType.fwDownload)) {
      return DetectedStatusType.fwDownload;
    }
    return DetectedStatusType.online;
  }

}
