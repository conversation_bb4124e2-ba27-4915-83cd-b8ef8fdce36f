import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/controller/map_topology_controller.dart';


/// Helper class that manages ALL data, variables, and business logic
class MapTopologyHelper {
  late MapTopologyPageState state;
  late MapTopologyController mapTopologyController;

  // ====== CONFIGURATION CONSTANTS ======
  static const double deviceMarkerWidthSize = 50.0;
  static const double deviceMarkerHeightSize = 42.0;
  static const double deviceMarkerInnerSize = 40.0;
  static const double gatewayMarkerSize = 62.0;
  static const double gatewayMarkerInnerSize = 45.0;
  static const double gatewayMarkerLegendInnerSize = 40.0;
  static const double initialZoom = 5;
  static const double zoomStep = 1.0;
  static const Duration debounceDelay = Duration(milliseconds: 800);

  // ====== ALL DATA VARIABLES ======
  // Filter states
  bool showOffline = true;
  bool showOnline = true;
  bool showDevices = true;
  bool showGateways = false;
  bool showDeviceLinks = true;
  bool showNodes = true;

  // Map data
  List<MapDevice> _currentDevices = [];
  List<MapGateway> _currentGateways = [];
  List<MapDeviceLink> _currentConnections = [];
  List<MapNodeDeviceLink> _currentNodeDeviceLinks = [];
  List<MapNode> _currentNodes = [];
  List<DeviceCluster> _deviceClusters = [];
  List<MapDeviceWithOffset> _devicesWithOffset = [];



  // State variables
  bool _isLoading = false;
  double _currentZoom = initialZoom;
  String? _errorMessage;


  // Zoom management variables
  Timer? _debounceTimer;
  ParsedMapLayersData? _cachedData;
  List<double>? _lastBbox;
  List<String>? vendorCodeList=[];
  String selectedVendorCode=AppStringConstants.all;
  ApiStatus ampProviderStatus = ApiStatus.initial;
  late ScreenLayoutType screenLayoutType;
  // Controllers
  late MapController mapController;

  // ====== GETTERS FOR UI ======
  // List<MapDevice> get currentDevices => _currentDevices;

  List<MapGateway> get currentGateways => _currentGateways;

  List<MapDeviceLink> get currentConnections => _currentConnections;
  List<MapNodeDeviceLink> get currentNodeDeviceLinks => _currentNodeDeviceLinks;
  List<MapNode> get currentNodes => _currentNodes;

  List<DeviceCluster> get deviceClusters => _deviceClusters;

  List<MapDeviceWithOffset> get devicesWithOffset => _devicesWithOffset;


  bool get isLoading => _isLoading;

  bool get isDevicesShowEnabled => _currentZoom >= 12;

  double get currentZoom => _currentZoom;

  String? get errorMessage => _errorMessage;

  // ParsedMapLayersData? get cachedData => _cachedData;

  // ====== SETTERS FOR UI ======
  void setShowDevices(bool value) {
    showDevices = value;
    updateState();
    refreshDataWithNewFilters();
  }
  void setShowGateways(bool value) {
    showGateways = value;
    updateState();
    refreshDataWithNewFilters();
  }
  void setShowDeviceLinks(bool value) {
    showDeviceLinks = value;
    updateState();
    refreshDataWithNewFilters();
  }
  void setShowNodes(bool value) {
    showNodes = value;
    updateState();
    refreshDataWithNewFilters();
  }

  // Device status filter
  final List<String> allDeviceStatuses = [AppStringConstants.online, AppStringConstants.offline, AppStringConstants.pending];
  List<String> selectedDeviceStatuses = [];

  final Map<int, String> prodIdCodeMap = {
    0:"All",
    277: "Line Extender (P2A)",
    288: "Line Extender",
    304: "Line Extender (BLE)",
    320: "High-Gain Dual System Amp",
    321: "High-Gain Dual System Amp (XL)",
    336: "Mini-Bridger System Amp",
    352: "High-Gain Balanced Triple Amp",
  };
  int selectedProdId = 0;
  final Map<String, String> deviceTypeCodeMap = {
    "Line Extender (P2A)": "LE",
    "Line Extender": "LE",
    "BLine Extender": "BLE",
    "Line Extender (BLE)": "BLE",
    "High-Gain Dual System Amp": "HGD",
    "High-Gain Dual System Amp (XL)": "HGD",
    "Mini-Bridger System Amp": "MB",
    "High-Gain Balanced Triple Amp": "HGBT",
  };
  // ====== CONSTRUCTOR ======
  MapTopologyHelper(this.state) {
    mapTopologyController = Get.put(MapTopologyController());
    mapController = MapController();
    getAmpProviderList();
    getData();
  }

  // ====== DATA MANAGEMENT METHODS ======

  /// Load initial data
  void getData() async {
     clearError();
    _setLoading(true);
    try {
      // Build layers based on filter states
      final layers = _buildLayersArray();
      final request = MapLayersRequest(
        layers: layers,
        filters: {},
        bbox: [0],
        context: {},
        autoFit: false,
      );
      final result = await mapTopologyController.getMapLayersData(context: state.context, request: request);

      if (result is ParsedMapLayersData) {
        final devices = _convertMapDevices(result.devices);
        final topologyData = _convertToTopologyData(result);

        _updateDevices(devices);
        _updateTopologyData(topologyData);
      } else {
        _handleInvalidResponse();
      }
    } catch (e) {
      _handleError(e.toString());
    } finally {
      _setLoading(false);
    }
  }
  getAmpProviderList() async {
    ampProviderStatus = ApiStatus.loading;
    updateState();
    ProvisionController  provisionController = Get.put(ProvisionController());
    List<AmplifierProviderItem>? ampProviderList  = await provisionController.getAmpProviderList(context: state.context);
    vendorCodeList = [
      AppStringConstants.all, // Index 0, default label
      ...?ampProviderList
          ?.where((e) => e.code != null && e.code!.isNotEmpty)
          .map((e) => e.code!)
    ];
    ampProviderStatus = ApiStatus.success;
    updateState();
  }

  updateState() {
    state.mapTopologyController.update();
  }

  /// Set filter states
  void setShowOffline(bool value) {
    showOffline = value;
    updateState();
  }

  void setShowOnline(bool value) {
    showOnline = value;
    updateState();
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    updateState();
  }

  /// Set error message
  void _setError(String? error) {
    _errorMessage = error;
  }

  /// Clear error message
  void clearError() {
    _setError(null);
    updateState();
  }

  /// Set current zoom
  void setCurrentZoom(double zoom) {
    _currentZoom = zoom;
  }

  /// Update devices data
  void _updateDevices(List<MapDevice> devices) {
    _currentDevices = devices;
    _createDevicesWithOffset();
    _setError(null);
  }

  /// Update topology data
  void _updateTopologyData(ParsedMapLayersData data) {
    _currentGateways = data.gateways;
    _currentConnections = data.deviceLinks;
    _currentNodeDeviceLinks = data.nodeDeviceLinks;
    _currentNodes = data.nodes;
    _deviceClusters = data.deviceClusters;
  }

  /// Handle map zoom changes with debouncing
  void onMapZoomChanged(BuildContext context, List<double> bbox) {
    _lastBbox = bbox;
    _debounceTimer?.cancel();
    _debounceTimer = Timer(debounceDelay, () {
      _fetchDevicesForZoomLevel(context, bbox);
    });
  }

  /// Fetch devices for zoom level
  Future<void> _fetchDevicesForZoomLevel(BuildContext context, List<double> bbox) async {
    clearError();
    if (_isLoading) return;
    _setLoading(true);

    try {
      final layers = _buildLayersArray();
      final request = MapLayersRequest(
        layers: layers,
        filters: buildFilters(),
        bbox: bbox,
        context: {},
        autoFit: false,
      );

      final result = await mapTopologyController.getMapLayersData(
        context: context,
        request: request,
      );

      if (result is ParsedMapLayersData) {
        _cachedData = result;
        _updateUIWithData(result);
      } else {
        _handleInvalidResponse();
      }
    } catch (e) {
      _handleError(e.toString());
    } finally {
      _setLoading(false);
      updateState();
    }
  }

  /// Update UI with data
  void _updateUIWithData(ParsedMapLayersData data) {
    final devices = _convertMapDevices(data.devices);
    _updateDevices(devices);
    final topologyData = _convertToTopologyData(data);
    _updateTopologyData(topologyData);
  }

  /// Handle invalid response
  void _handleInvalidResponse() {
    debugLogs("Invalid response type from API");
    _setError("Invalid response from server");
    _fallbackToCachedData();
  }

  /// Handle errors
  void _handleError(String error) {
    debugLogs("Error fetching devices: $error");
    _setError("Failed to fetch map data: $error");
    _fallbackToCachedData();
  }

  /// Fallback to cached data
  void _fallbackToCachedData() {
    if (_cachedData != null) {
      _updateUIWithData(_cachedData!);
    }
  }

  /// Handle zoom button press with smooth animation
  Future<void> zoomMap(int direction) async {
    final currentZoom = mapController.camera.zoom;
    final center = mapController.camera.center;
    final targetZoom = currentZoom + direction;

    // Use smooth zoom animation for button presses
    await smoothZoomToCluster(
      center, // Keep same center, just change zoom
      targetZoom,
      step: 10,
      duration: const Duration(milliseconds: 800), // Faster for buttons
    );
  }

  /// Clear cache
  void clearCache() {
    _cachedData = null;
    _lastBbox = null;
  }

  /// Refresh data with new filter settings
  void refreshDataWithNewFilters() {
    List<double>? bbox = _lastBbox ?? [0];
    updateState();
    _fetchDevicesForZoomLevel(state.context, bbox);
  }
  // ====== DATA PROCESSING METHODS ======

  /// Create devices with offset for same location handling
  void _createDevicesWithOffset() {
    _devicesWithOffset.clear();

    // Group devices by exact location (lat, lng)
    final Map<String, List<MapDevice>> locationGroups = {};

    for (final device in _currentDevices) {
      final locationKey = '${device.position.latitude.toStringAsFixed(6)},${device.position.longitude.toStringAsFixed(6)}';
      locationGroups.putIfAbsent(locationKey, () => []).add(device);
    }
    // Create offset devices for each location
    for (final entry in locationGroups.entries) {
      final devices = entry.value;

      if (devices.length == 1) {
        // Single device - no offset needed
        _devicesWithOffset.add(MapDeviceWithOffset(
          device: devices.first,
          position: devices.first.position,
          deviceIndex: 0,
          totalDevicesAtLocation: 1,
        ));
      } else {
        // Multiple devices - apply circular offset
        for (int i = 0; i < devices.length; i++) {
          _devicesWithOffset.add(MapDeviceWithOffset(
            device: devices[i],
            position: devices.first.position,
            deviceIndex: i,
            totalDevicesAtLocation: devices.length,
          ));
        }
      }
    }
  }

  /// Get filtered devices based on current filter settings
  List<MapDeviceWithOffset> getFilteredDevicesWithOffset() {
    return _devicesWithOffset.where((deviceWithOffset) {
      final device = deviceWithOffset.device;
      if (!showOffline && device.status == DetectedStatusType.offline) return false;
      if (!showOnline && device.status != DetectedStatusType.offline) return false;
      return true;
    }).toList();
  }

  /// Get filtered devices (legacy method for compatibility)
  List<MapDevice> getFilteredDevices() {
    return getFilteredDevicesWithOffset().map((d) => d.device).toList();
  }

  /// Get location key for a position
  String getLocationKey(LatLng position) {
    return '${position.latitude.toStringAsFixed(6)},${position.longitude.toStringAsFixed(6)}';
  }

  /// Google Maps-style smooth zoom for clusters (simple and reliable)
  Future<void> smoothZoomToCluster(LatLng targetLocation, double targetZoom, {
    Duration? duration,int?step
  }) async {
    // Get current state
    final startZoom = mapController.camera.zoom;
    final startCenter = targetLocation;

    // Skip if very small change
    if ((targetZoom - startZoom).abs() < 0.1 &&
        (targetLocation.latitude - startCenter.latitude).abs() < 0.0001 &&
        (targetLocation.longitude - startCenter.longitude).abs() < 0.0001) {
      mapController.move(targetLocation, targetZoom);
      return;
    }

    // Simple 30-step animation for smooth Google Maps-like effect
    var steps = step ?? 25;

    for (int i = 1; i <= steps; i++) {
      final progress = i / steps;

      // Google Maps-style easing curve (smooth start and end)
      final smoothProgress = progress * progress * (3 - 2 * progress);

      // Calculate intermediate position
      final lat = startCenter.latitude +
                  (targetLocation.latitude - startCenter.latitude) * smoothProgress;
      final lng = startCenter.longitude +
                  (targetLocation.longitude - startCenter.longitude) * smoothProgress;
      final zoom = startZoom + (targetZoom - startZoom) * smoothProgress;

      // Move to intermediate position
      mapController.move(LatLng(lat, lng), zoom);

      // Wait for next step
      await Future.delayed(Duration(milliseconds: 70));
    }

    // Final exact position
    mapController.move(targetLocation, targetZoom);
    _currentZoom = targetZoom;
    updateState();
  }

  /// Get devices for animation display (count vs expanded)
  List<MapDeviceWithOffset> getDevicesForDisplay() {
    final Map<String, List<MapDeviceWithOffset>> locationGroups = {};

    // Group devices by location
    for (final deviceWithOffset in _devicesWithOffset) {
      final locationKey = getLocationKey(deviceWithOffset.device.position);
      locationGroups.putIfAbsent(locationKey, () => []).add(deviceWithOffset);
    }

    List<MapDeviceWithOffset> displayDevices = [];

    for (final entry in locationGroups.entries) {
      final locationKey = entry.key;
      final devices = entry.value;

      if (devices.length == 1) {
        // Single device - always show
        displayDevices.addAll(devices);
      } else {
          // Show only count marker at original position
          final firstDevice = devices.first;
          displayDevices.add(MapDeviceWithOffset(
            device: firstDevice.device,
            position: firstDevice.device.position, // Original position
            deviceIndex: 0,
            totalDevicesAtLocation: devices.length,
            locationKey: locationKey,
            allDevicesAtLocation: devices.map((d) => d.device).toList(),
          ));
      }
    }

    return displayDevices;
  }

  /// Get filtered connections based on available devices
  List<MapDeviceLink> getFilteredConnections() {
    final deviceMap = {for (var d in _currentDevices) d.deviceEui: d};
    return _currentConnections.where((conn) {
      final fromDevice = deviceMap[conn.sourceId];
      final toDevice = deviceMap[conn.targetId];
      return fromDevice != null && toDevice != null;
    }).toList();
  }

  /// Get filtered node-device connections based on available nodes and devices
  List<MapNodeDeviceLink> getFilteredNodeConnections() {
    final deviceMap = {for (var d in _currentDevices) d.deviceEui: d};
    final nodeMap = {for (var n in _currentNodes) n.id: n};
    return _currentNodeDeviceLinks.where((conn) {
      final fromNode = nodeMap[conn.sourceNodeId];
      final toDevice = deviceMap[conn.targetDeviceEui];
      return fromNode != null && toDevice != null;
    }).toList();
  }

  /// Get device count info text
  String getDeviceCountInfo(int devices, int nodes) {
    if (_isLoading) return S.of(state.context).loadingDevices;
    return S.of(state.context).devicesAndGatewaysShown(devices, nodes);
  }
  String getDeviceTypeCode(String deviceName) {
    return deviceTypeCodeMap[deviceName] ?? "";
  }

  String getBwModeVersion(int bwMode) {
    String ampDeviceMode = bwMode == 0
            ? "1.2"
            : bwMode == 1
                ? "1.8"
                    : "";
    return ampDeviceMode.isNotEmpty ? ampDeviceMode : "";
  }

  /// Get zoom level category for display
  String getZoomLevelCategory() {
    if (_currentZoom >= 16) return "Detailed View";
    if (_currentZoom >= 14) return "Standard View";
    if (_currentZoom >= 12) return "Overview";
    return "Regional View";
  }

  // ====== DATA CONVERSION METHODS ======

  /// Convert MapDevice list
  List<MapDevice> _convertMapDevices(List<MapDevice> mapDevices) {
    return mapDevices
        .map((mapDevice) => MapDevice(
              deviceEui: mapDevice.deviceEui,
              deviceAlias: mapDevice.deviceAlias,
              position: mapDevice.position,
              status: mapDevice.status,
              lastSeen: mapDevice.lastSeen,
              vendorCode: mapDevice.vendorCode,
              type: mapDevice.type,
              description: mapDevice.description,
              bwMode: mapDevice.bwMode,
              placement: mapDevice.placement,
            ))
        .toList();
  }

  /// Convert to topology data
  ParsedMapLayersData _convertToTopologyData(ParsedMapLayersData data) {
    final devices = _convertMapDevices(data.devices);

    final gateways = data.gateways
        .map((gateway) => MapGateway(
              gatewayId: gateway.gatewayId,
              position: gateway.position,
              name: gateway.name,
              type: gateway.type,
              status: gateway.status,
              lastSeen: gateway.lastSeen,
              domain: gateway.domain,
              hwVersion: gateway.hwVersion,
              swVersion: gateway.swVersion,
              userEmail: gateway.userEmail,
            ))
        .toList();

    final connections = data.deviceLinks
        .map((link) => MapDeviceLink(
              sourceId: link.sourceId,
              targetId: link.targetId,
              polylinePoints: link.polylinePoints,
              styleHint: link.styleHint,
            ))
        .toList();

    final nodeDeviceLinks = data.nodeDeviceLinks
        .map((link) => MapNodeDeviceLink(
              sourceNodeId: link.sourceNodeId,
              targetDeviceEui: link.targetDeviceEui,
              polylinePoints: link.polylinePoints,
              styleHint: link.styleHint,
            ))
        .toList();

    final deviceClusters = data.deviceClusters
        .map((cluster) => DeviceCluster(
     count:cluster.count ,
      lat: cluster.lat,
      lng: cluster.lng
    ))
        .toList();

    final nodes = data.nodes
        .map((n) => MapNode(
              id: n.id,
              alias: n.alias,
              description: n.description,
              placement: n.placement,
              latitude: n.latitude,
              longitude: n.longitude,
              isLocationConfirmed: n.isLocationConfirmed,
              manufacturer: n.manufacturer,
              model: n.model,
              gwEui: n.gwEui,
              position: n.position,
            ))
        .toList();

    return ParsedMapLayersData(
      devices: devices,
      deviceLinks: connections,
      gateways: gateways,
      deviceClusters: deviceClusters,
      nodes: nodes,
      nodeDeviceLinks: nodeDeviceLinks,
    );
  }

  /// Build layers array based on filter states
  List<String> _buildLayersArray() {
    final layers = <String>[];
    if (isDevicesShowEnabled) {
      if (showDevices) {
        layers.add(AppStringConstants.devices);
      }
      if (showDeviceLinks) {
        layers.add(AppStringConstants.deviceLinks);
      }
      if (showNodes) {
        layers.add(AppStringConstants.nodes);
      }
      if(showDeviceLinks&&showNodes){
        layers.add(AppStringConstants.nodeDeviceLinks);
      }
    } else {
      layers.add(AppStringConstants.deviceClusters);
    }
    return layers;
  }

  Map<String, Map<String, dynamic>> buildFilters() {
    final deviceFilters = <String, dynamic>{ };
    if (selectedDeviceStatuses.isNotEmpty) {
      deviceFilters["status"] = selectedDeviceStatuses.toList();
    }

    if (selectedVendorCode != AppStringConstants.all) {
      deviceFilters["vendor_code"] = selectedVendorCode;
    }

    if(selectedProdId !=0 ){
      deviceFilters["prod_id"] = selectedProdId;
    }

    return  {
      "devices": deviceFilters,
    };
  }

  // ====== STYLING HELPER METHODS ======

  /// Get status color based on device status
  static Color getStatusColor(DetectedStatusType status) {
    switch (status) {
      case DetectedStatusType.online:
        return AppColorConstants.colorGreen2;
      case DetectedStatusType.pending:
        return AppColorConstants.colorOrange;
      case DetectedStatusType.offline:
        return AppColorConstants.colorH2;
      case DetectedStatusType.missingKey:
        return AppColorConstants.colorH2;
      case DetectedStatusType.missingVendor:
        return AppColorConstants.colorH2;
      case DetectedStatusType.fwDownload:
        return AppColorConstants.colorPrimary;
    }
  }

  /// Get status icon based on device status
  /// Get status icon asset based on device status
  static String getStatusIcon(DetectedStatusType status) {
    switch (status) {
      case DetectedStatusType.online:
        return AppAssetsConstants.icMarkerDeviceOnline;
      case DetectedStatusType.offline:
        return AppAssetsConstants.icMarkerDeviceOffline;
      case DetectedStatusType.pending:
        return AppAssetsConstants.icMarkerDevicePending;
      case DetectedStatusType.missingKey:
        return AppAssetsConstants.icMarkerDeviceOffline;
      case DetectedStatusType.missingVendor:
        return AppAssetsConstants.icMarkerDeviceOffline;
      case DetectedStatusType.fwDownload:
        return AppAssetsConstants.icMarkerDeviceFd; // reuse FD for firmware
    }
  }




  /// Get gateway icon
  static IconData getGatewayIcon() => Icons.router;

  /// Get gateway color
  static Color getGatewayColor(String status) {
    switch (status.toLowerCase()) {
      case 'online':
        return AppColorConstants.colorGreen2;
      case 'offline':
        return AppColorConstants.colorH2;
      case 'never_seen':
        return AppColorConstants.colorOrange;
      case 'unknown':
        return AppColorConstants.colorH3;
      default:
        return AppColorConstants.colorH2;
    }
  }

  /// Parse color from hex string
  static Color parseColor(String? colorHex, [Color fallback = Colors.grey]) {
    if (colorHex == null || colorHex.isEmpty) return fallback;
    try {
      final hex = colorHex.replaceAll('#', '');
      final hexColor = hex.length == 6 ? 'FF$hex' : hex;
      return Color(int.parse(hexColor, radix: 16));
    } catch (e) {
      return fallback;
    }
  }

  String capitalizeFirst(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1).toLowerCase();
  }

  // ====== POLYLINE DATA METHOD ======

  /// Get polyline points for connection
  List<LatLng>? getPolylinePoints(MapDeviceLink conn) {
    if (conn.polylinePoints.isNotEmpty) {
      return conn.polylinePoints;
    } else {
      try {
        final sourceDevice = _currentDevices.firstWhere((d) => d.deviceEui == conn.sourceId);
        final targetDevice = _currentDevices.firstWhere((d) => d.deviceEui == conn.targetId);
        return [sourceDevice.position, targetDevice.position];
      } catch (e) {
        return null;
      }
    }
  }

  /// Get polyline points for node-device connection
  List<LatLng>? getPolylinePointsForNode(MapNodeDeviceLink conn) {
    if (conn.polylinePoints.isNotEmpty) {
      return conn.polylinePoints;
    } else {
      try {
        final sourceNode = _currentNodes.firstWhere((n) => n.id == conn.sourceNodeId);
        final targetDevice = _currentDevices.firstWhere((d) => d.deviceEui == conn.targetDeviceEui);
        return [sourceNode.position, targetDevice.position];
      } catch (e) {
        return null;
      }
    }
  }

  // ====== DISPOSE METHOD ======
  void dispose() {
    _debounceTimer?.cancel();
    clearCache();
  }
}

/// Map device for handling multiple devices at same location
class MapDeviceWithOffset {
  final MapDevice device;
  final LatLng position;
  final int deviceIndex;
  final int totalDevicesAtLocation;
  final String? locationKey;
  final List<MapDevice>? allDevicesAtLocation;

  MapDeviceWithOffset({
    required this.device,
    required this.position,
    required this.deviceIndex,
    required this.totalDevicesAtLocation,
    this.locationKey,
    this.allDevicesAtLocation,
  });

  /// Check if this device is part of a multi-device location
  bool get isMultiDeviceLocation => totalDevicesAtLocation > 1;

}
