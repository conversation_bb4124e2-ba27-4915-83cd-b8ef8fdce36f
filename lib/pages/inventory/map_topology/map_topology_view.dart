import 'dart:math' as math;
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/controller/map_topology_controller.dart';
import 'map_topology_helper.dart';
/// View class that handles ONLY UI components and widgets following GetX pattern
class MapTopologyView extends StatefulWidget {
  const MapTopologyView({super.key});

  @override
  State<MapTopologyView> createState() => MapTopologyPageState();
}

class MapTopologyPageState extends State<MapTopologyView> {
  MapTopologyHelper? mapTopologyHelper;
  MapTopologyController mapTopologyController = MapTopologyController();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    mapTopologyHelper ?? (mapTopologyHelper = MapTopologyHelper(this));
    return GetBuilder<MapTopologyController>(
      init: MapTopologyController(),
      builder: (MapTopologyController controller) {
        mapTopologyController = controller;
        return getBody();
      },
    );
  }

  Widget getBody() {
    return Column(crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(padding: const EdgeInsets.symmetric(horizontal: 15),child: getPageTitleView(S.of(context).topology),),
        Expanded(child: getMapTopologyView(),)
      ],
    );
  }

  Widget getMapTopologyView() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
      decoration: BoxDecoration(
        color: AppColorConstants.colorWhite,
        borderRadius: const BorderRadius.only(
          topRight: Radius.circular(8),
          topLeft: Radius.circular(8),
        ),
        border: Border.all(color: AppColorConstants.colorH2, width: 1),
      ),
      child: Stack(
        children: [
          Column(
            children: [
              // Header with filters
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16,vertical: 12),
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(8),
                    topLeft: Radius.circular(8),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildFilterControls(),
                  ],
                ),
              ),
              // Zoom info bar
              _buildZoomInfoBar(),
              // Map
              Expanded(
                child: FlutterMap(
                  mapController: mapTopologyHelper!.mapController,
                  options: MapOptions(
                    minZoom: 3,
                    initialCenter: const LatLng(37.678178, -97.445364), // Atlanta area
                    initialZoom: MapTopologyHelper.initialZoom,
                    onPositionChanged: _onMapZoomChanged,
                    // Disable map rotation by setting rotation to 0
                    rotation: 0.0,
                    // Disable map repeat by limiting bounds
                    maxBounds: LatLngBounds(
                      const LatLng(-85, -180), // South, West
                      const LatLng(85, 180),   // North, East
                    ),
                  ),
                  children: [
                    TileLayer(
                      urlTemplate: AppAssetsConstants.openStreetMapUrl,
                      // Disable map repeat by setting maxBounds in MapOptions instead
                      tms: false,
                    ),
                    PolylineLayer(polylines: _buildPolylines()),
                    MarkerLayer(
                      markers: _buildAllMarkers(),
                    ),
                  ],
                ),
              ),
            ],
          ),

          openStreetMapCopyRightLinkView(context),
          // Legend Box
          Positioned(
            top: 105,
            right: 8,
            child: _buildLegendBox(),
          ),
          // Zoom controls
          Positioned(
            bottom: 35.0,
            right: 10,
            child: _buildZoomControls(),
          ),
          // Error overlay
          if (_buildErrorOverlay() != null) _buildErrorOverlay()!,
        ],
      ),
    );
  }

  // ====== UI BUILDING METHODS ======

  /// Handle map zoom changes
  void _onMapZoomChanged(MapPosition position, bool hasGesture) {
    debugLogs("onPositionChanged ->");
    debugLogs(hasGesture);
    debugLogs(position.center);
    debugLogs(position.zoom);
    final bbox = [
      position.bounds!.west,
      position.bounds!.south,
      position.bounds!.east,
      position.bounds!.north,
    ];
    debugLogs(bbox);
    mapTopologyHelper!.onMapZoomChanged(context, bbox);
    mapTopologyHelper!.setCurrentZoom(position.zoom ?? mapTopologyHelper!.currentZoom);
  }
  /// Build animated device markers with count/expand functionality
  List<Marker> _buildAnimatedDeviceMarkers() {
    final devicesForDisplay = mapTopologyHelper!.getDevicesForDisplay();
    List<Marker> markers = [];

    for (final deviceWithOffset in devicesForDisplay) {
      if (deviceWithOffset.isCountMarker) {
        // Show count marker for collapsed multi-device location
        markers.add(_buildCountMarker(deviceWithOffset));
      } else {
        // Show individual device marker (connection lines handled by polylines)
        markers.add(_buildDeviceMarkerWithOffset(deviceWithOffset));
      }
    }

    return markers;
  }

  /// Build count marker for multiple devices at same location
  Marker _buildCountMarker(MapDeviceWithOffset deviceWithOffset) {
    return Marker(
      point: deviceWithOffset.offsetPosition,
      width: MapTopologyHelper.deviceMarkerInnerSize,
      height: MapTopologyHelper.deviceMarkerInnerSize,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        transform: Matrix4.identity(),
        child: InkWell(
          onTap: () {
            if (deviceWithOffset.locationKey != null) {
              mapTopologyHelper!.toggleLocationExpansion(deviceWithOffset.locationKey!);
            }
          },
          child: AnimatedContainer(alignment: Alignment.center,
            duration: const Duration(milliseconds: 300),
            width: MapTopologyHelper.deviceMarkerInnerSize,
            height: MapTopologyHelper.deviceMarkerInnerSize,
            decoration: BoxDecoration(
              color:AppColorConstants.colorBlue,
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.white,
                width: 3,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 6,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: AppText(isSelectableText: false,
              '${deviceWithOffset.totalDevicesAtLocation}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
    );
  }

  /// Build device marker with offset position
  Marker _buildDeviceMarkerWithOffset(MapDeviceWithOffset deviceWithOffset) {
    final device = deviceWithOffset.device;
    final tooltipContent = mapTopologyHelper!.buildDeviceTooltip(deviceWithOffset);

    return Marker(
      point: deviceWithOffset.offsetPosition,
      width: MapTopologyHelper.deviceMarkerSize,
      height: MapTopologyHelper.deviceMarkerSize,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 500),
        curve: Curves.elasticOut,
        child: Tooltip(
          textStyle: TextStyle(color: AppColorConstants.colorBlackBlue),
          padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
          decoration: BoxDecoration(
            color: AppColorConstants.colorWhite,
            borderRadius: const BorderRadius.all(Radius.circular(8)),
          ),
          message: tooltipContent,
          child: InkWell(
            onTap: deviceWithOffset.isMultiDeviceLocation ? () {
              if (deviceWithOffset.isMultiDeviceLocation) {
                mapTopologyHelper!.collapseAllLocations();
              }
            }:null,
            child: Column(
              children: [
                // Device type code with multi-device indicator
                if (mapTopologyHelper!.getDeviceTypeCode(device.type).isNotEmpty ||
                    mapTopologyHelper!.getBwModeVersion(device.bwMode).isNotEmpty)
                  Container(
                    decoration: BoxDecoration(
                        color:  AppColorConstants.colorWhite,
                        shape: BoxShape.rectangle,
                        border: Border.all(
                          color: Colors.grey,
                          width: 1,
                        ),
                        borderRadius: const BorderRadius.all(Radius.circular(2))
                    ),
                    child: AppText(
                      " ${mapTopologyHelper!.getDeviceTypeCode(device.type)}"
                          "${mapTopologyHelper!.getBwModeVersion(device.bwMode)}",
                      style: TextStyle(fontSize: 8, fontWeight: FontWeight.bold),
                      textAlign: TextAlign.center,
                    ),
                  ),
                Stack(
                  alignment: Alignment.topCenter,
                  children: [
                    Container(
                      width: MapTopologyHelper.deviceMarkerInnerSize,
                      height: MapTopologyHelper.deviceMarkerInnerSize,
                      child: SvgPicture.asset(
                        AppAssetsConstants.icMarkerDevice,
                        colorFilter: ColorFilter.mode(
                          MapTopologyHelper.getStatusColor(device.status),
                          BlendMode.srcIn,
                        ),
                        width: 22,
                        height: 22,
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: 5),
                      child: ClipOval(
                        child: Container(
                          color: Colors.white,
                          child: Icon(
                            MapTopologyHelper.getStatusIcon(device.status),
                            color: MapTopologyHelper.getStatusColor(device.status),
                            size: 23,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }


  /// Build gateway marker
  Marker _buildGatewayMarker(MapGateway gateway) {
    final tooltipContent = mapTopologyHelper!.buildGatewayTooltip(gateway);
    
    return Marker(
      point: gateway.position,
      width: MapTopologyHelper.gatewayMarkerSize,
      height: MapTopologyHelper.gatewayMarkerSize,
      child: Tooltip(
        textStyle: TextStyle(color: AppColorConstants.colorBlackBlue),
        padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
        decoration: BoxDecoration(
          color: AppColorConstants.colorWhite,
          borderRadius: const BorderRadius.all(Radius.circular(8)),
        ),
        message: tooltipContent,
        child: Column(
          children: [
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.rectangle,
                border: Border.all(color: Colors.grey, width: 1),
              ),
              child: AppText(" "+gateway.name, style: TextStyle(fontSize: 8, fontWeight: FontWeight.bold), textAlign: TextAlign.center),
            ),
            SizedBox(height: 3,),
            Stack(
              alignment: Alignment.topCenter,
              children: [
                Container(
                  width: MapTopologyHelper.gatewayMarkerInnerSize,
                  height: MapTopologyHelper.gatewayMarkerInnerSize,

                  child: SvgPicture.asset(
                    AppAssetsConstants.icGwDevice,
                    colorFilter: ColorFilter.mode(
                      MapTopologyHelper.getGatewayColor(gateway.status),
                      BlendMode.srcIn,
                    ),
                    width: 22,
                    height: 22,
                  ),
                ),

                Padding(
                  padding:  EdgeInsets.only(top: 5),
                  child: SvgPicture.asset(
                    AppAssetsConstants.icGwDeviceIcon,
                    width: 22,
                    height: 22,
                  ),
                ),
              ],
            ),



          ],
        ),
      ),
    );
  }

  /// Build all markers
  List<Marker> _buildAllMarkers() {
    final deviceMarkers = _buildAnimatedDeviceMarkers();
    final gatewayMarkers = _buildGatewayMarkers();
    final clusterMarkers = _buildClusterMarkers();
    return [...deviceMarkers, ...gatewayMarkers, ...clusterMarkers];
  }

  /// Build connection lines for multi-device locations
  List<Polyline> _buildConnectionLines() {
    final devicesForDisplay = mapTopologyHelper!.getDevicesForDisplay();
    List<Polyline> polylines = [];

    // Group devices by location to find expanded locations
    final Map<String, List<MapDeviceWithOffset>> locationGroups = {};
    for (final device in devicesForDisplay) {
      if (device.isMultiDeviceLocation && !device.isCountMarker) {
        final locationKey = mapTopologyHelper!.getLocationKey(device.device.position);
        locationGroups.putIfAbsent(locationKey, () => []).add(device);
      }
    }

    // Create connection lines for each expanded location
    for (final entry in locationGroups.entries) {
      final devices = entry.value;
      if (devices.length > 1) {
        final centerPoint = devices.first.device.position;

        for (final device in devices) {
          polylines.add(
            Polyline(
              points: [centerPoint, device.offsetPosition],
              strokeWidth: 3.0,
              color: Colors.deepOrange,
              isDotted: true,
            ),
          );
        }
      }
    }

    return polylines;
  }

  /// Build device markers list
  List<Marker> _buildDeviceMarkers() {
    final filteredDevicesWithOffset = mapTopologyHelper!.getFilteredDevicesWithOffset();
    List<Marker> markers = [];
    for (final deviceWithOffset in filteredDevicesWithOffset) {
      markers.add(_buildDeviceMarkerWithOffset(deviceWithOffset));
    }
    return markers;
  }

  /// Build gateway markers list
  List<Marker> _buildGatewayMarkers() {
    return mapTopologyHelper!.currentGateways.map((gateway) => _buildGatewayMarker(gateway)).toList();
  }
  /// Build cluster markers for device_clusters
  List<Marker> _buildClusterMarkers() {
    return mapTopologyHelper!.deviceClusters.map((cluster) {
      return Marker(
        point: LatLng(cluster.lat, cluster.lng),
        width: 40,
        height: 40,
        child: AnimatedClusterMarker(
          count: cluster.count,
          color: Colors.deepPurple,
          onTap: () {
            final currentZoom = mapTopologyHelper!.currentZoom;
            final targetZoom = currentZoom < 12.5 ? 12.5 : currentZoom;
            mapTopologyHelper!.mapController.move(
              LatLng(cluster.lat, cluster.lng),
              targetZoom ,
            );
          },
        ),
      );
    }).toList();
  }

  /// Build polylines for connections
  List<Polyline> _buildPolylines() {
    List<Polyline> polylines = [];

    final filteredConnections = mapTopologyHelper!.getFilteredConnections();
    polylines.addAll(filteredConnections.map((conn) {
      final points = mapTopologyHelper!.getPolylinePoints(conn);
      if (points == null) return null;

      return Polyline(
        points: points,
        color: AppColorConstants.colorPolyLine,
        strokeWidth: 2,
      );
    }).where((polyline) => polyline != null).cast<Polyline>());

    // Add connection lines for multi-device locations
    polylines.addAll(_buildConnectionLines());

    return polylines;
  }

  // /// Build polylines for connections
  // List<Polyline> _buildPolylines() {
  //   final filteredConnections = mapTopologyHelper!.getFilteredConnections();
  //
  //   return filteredConnections.map((conn) {
  //     final points = mapTopologyHelper!.getPolylinePoints(conn);
  //     if (points == null) return null;
  //
  //     return Polyline(
  //       points: points,
  //       color: AppColorConstants.colorPolyLine,
  //       strokeWidth: 2,
  //     );
  //   }).where((polyline) => polyline != null).cast<Polyline>().toList();
  // }

  /// Build filter chip
  Widget _buildFilterChip({
    required String label,
    required IconData icon,
    required Color iconColor,
    required bool selected,
    required ValueChanged<bool> ?onSelected,
  }) {
    return FilterChip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: iconColor, size: 16),
          const SizedBox(width: 4),
          AppText(label,isSelectableText: false),
        ],
      ),
      selected: selected,
      onSelected: onSelected,
      selectedColor: iconColor.withOpacity(0.1),
    );
  }

  /// Build filter controls
  Widget _buildFilterControls() {
    return Row(mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const SizedBox(width: 12),
        _buildLayersFilterView(),
        _buildProdCodeFilterDropdown(),
        const SizedBox(width: 12),
        _buildStatusFilterDropdown(),
        const SizedBox(width: 12),
        _buildVendorCodeFilterDropdown(),
      ],
    );
  }


  Widget _buildStatusFilterDropdown() {
    return buildCustomDropdown<String>(
      label:
      "${S.of(context).status}${mapTopologyHelper!.selectedDeviceStatuses.isEmpty ? "" : " : ${mapTopologyHelper?.selectedDeviceStatuses.join(', ')}"}",
      selectedValue: null,
      height: 50,
      width: 180,
      isEnabled: mapTopologyHelper!.isDevicesShowEnabled,
      items: mapTopologyHelper!.allDeviceStatuses.map((item) {
        return DropdownMenuItem<String>(
          value: item,
          enabled: false,
          child: StatefulBuilder(
            builder: (context, menuSetState) {
              final isSelected =
              mapTopologyHelper!.selectedDeviceStatuses.contains(item);
              return CheckboxListTile(
                title: AppText(isSelectableText: false, item, style: const TextStyle(fontSize: 14)),
                value: isSelected,
                onChanged: (checked) {
                  if (checked == null) return;
                  if (checked) {
                    mapTopologyHelper!.selectedDeviceStatuses.add(item);
                  } else {
                    mapTopologyHelper!.selectedDeviceStatuses.remove(item);
                  }
                  menuSetState(() {
                    mapTopologyHelper?.refreshDataWithNewFilters();
                  });
                },
              );
            },
          ),
        );
      }).toList(),
      onChanged: (_) {},
    );
  }


  Widget _buildVendorCodeFilterDropdown() {
    return buildCustomDropdown<String>(
      label: "${S.of(context).vendor} : ${mapTopologyHelper?.selectedVendorCode ?? ""}",
      selectedValue: null,
      width: 150,
      isEnabled: mapTopologyHelper!.isDevicesShowEnabled,
      items: mapTopologyHelper!.vendorCodeList!.map((entry) {
        return DropdownMenuItem<String>(
          value: entry,
          child: AppText(entry, style: const TextStyle(fontSize: 14), isSelectableText: false),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          mapTopologyHelper!.selectedVendorCode = value;
          mapTopologyHelper?.refreshDataWithNewFilters();
          mapTopologyController.update();
        }
      },
    );
  }


  Widget _buildLayersFilterView() {
   bool isEnable =  mapTopologyHelper!.isDevicesShowEnabled ;
    return  Expanded(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          AppText(
            "${S.of(context).layers} :",
            style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 15),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildFilterChip(
                  label: 'Devices',
                  icon: Icons.device_hub,
                  iconColor: Colors.blue,
                  selected: mapTopologyHelper!.showDevices,
                  onSelected: isEnable ? (value) => mapTopologyHelper!.setShowDevices(value) : null,
                ),
                // _buildFilterChip(
                //   label: 'Gateways',
                //   icon: Icons.router,
                //   iconColor: Colors.orange,
                //   selected: mapTopologyHelper!.showGateways,
                //   onSelected:isEnable ?  (value) => mapTopologyHelper!.setShowGateways(value) : null,
                // ),
                _buildFilterChip(
                  label: 'Device Links',
                  icon: Icons.link,
                  iconColor: Colors.purple,
                  selected: mapTopologyHelper!.showDeviceLinks,
                  onSelected: isEnable ? (value) => mapTopologyHelper!.setShowDeviceLinks(value) : null,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }


  Widget _buildProdCodeFilterDropdown() {
    return buildCustomDropdown<int>(
      label: "${S.of(context).product} : ${mapTopologyHelper!.prodIdCodeMap[mapTopologyHelper?.selectedProdId] ?? ""}",
      selectedValue: null,
      isEnabled: mapTopologyHelper!.isDevicesShowEnabled,
      width: 200,
      items: mapTopologyHelper!.prodIdCodeMap.entries.map((entry) {
        return DropdownMenuItem<int>(
          value: entry.key,
          child: AppText(entry.value, style: const TextStyle(fontSize: 14), isSelectableText: false),
        );
      }).toList(),
      onChanged: (code) {
        if (code != null) {
          mapTopologyHelper!.selectedProdId = code;
          mapTopologyHelper?.refreshDataWithNewFilters();
          mapTopologyController.update();
        }
      },
    );
  }

  Widget buildCustomDropdown<T>({
    required String label,
    required T? selectedValue,
    required List<DropdownMenuItem<T>> items,
    required ValueChanged<T?>? onChanged,
    double? width,
    double? height,
    bool isEnabled = true,
  }) {
    return DropdownButtonHideUnderline(
      child: DropdownButton2<T>(
        isExpanded: true,
        customButton: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              AppText(
                isSelectableText: false,
                label,
                style: TextStyle(
                  fontSize: 14,
                  color: isEnabled
                      ? AppColorConstants.colorBlackBlue
                      : AppColorConstants.colorH2,
                ),
              ),
              const SizedBox(width: 5),
              Icon(
                Icons.keyboard_arrow_down_outlined,
                color: isEnabled
                    ? AppColorConstants.colorBlackBlue
                    : AppColorConstants.colorH2,
                size: 20,
              ),
            ],
          ),
        ),
        value: selectedValue,
        buttonStyleData: ButtonStyleData(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          height: height ?? 40,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        dropdownStyleData: DropdownStyleData(
          width: width ?? 180,
          openInterval: const Interval(0.25, 0.70),
          maxHeight: 200,
          decoration: BoxDecoration(
            color: AppColorConstants.colorWhite,
            borderRadius: BorderRadius.circular(8),
          ),
          scrollbarTheme: ScrollbarThemeData(
            radius: const Radius.circular(40),
            thickness: MaterialStateProperty.all<double>(6),
            thumbVisibility: MaterialStateProperty.all<bool>(true),
          ),
        ),
        iconStyleData: IconStyleData(
          openMenuIcon: Icon(
            Icons.keyboard_arrow_up_sharp,
            color: AppColorConstants.colorH3,
          ),
          icon: Icon(
            Icons.keyboard_arrow_down_outlined,
            color: AppColorConstants.colorH3,
          ),
          iconSize: 20,
        ),
        items: items,
        onChanged: isEnabled ? onChanged : null,
        menuItemStyleData: const MenuItemStyleData(
          height: 45,
        ),
      ),
    );
  }


  /// Build zoom info bar
  Widget _buildZoomInfoBar() {
    final deviceMarkers = _buildDeviceMarkers();
    final gatewayMarkers = _buildGatewayMarkers();
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: AppColorConstants.colorBackgroundDark,
        border: Border(
          top: BorderSide(color: AppColorConstants.colorDivider.withOpacity(0.3)),
          bottom: BorderSide(color: AppColorConstants.colorDivider.withOpacity(0.3)),
        ),
      ),
      child: Row(
        children: [
          if (mapTopologyHelper!.isLoading) ...[
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
            const SizedBox(width: 8),
            Text(S.of(context).loadingDevices),
          ] else ...[
            Icon(Icons.zoom_in, size: 16, color: AppColorConstants.colorH3),
            const SizedBox(width: 8),
            Text(
              '${S.of(context).zoom}: ${mapTopologyHelper!.currentZoom.toStringAsFixed(1)} - ${mapTopologyHelper!.getZoomLevelCategory()}',
              style: TextStyle(
                fontSize: 12,
                color: AppColorConstants.colorH3,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
          const Spacer(),
          if (mapTopologyHelper!.isDevicesShowEnabled)
            Text(
              mapTopologyHelper!.getDeviceCountInfo(deviceMarkers.length, gatewayMarkers.length),
              style: TextStyle(
                fontSize: 12,
                color: AppColorConstants.colorH3,
              ),
            ),
        ],
      ),
    );
  }

  /// Build zoom control button
  Widget _buildZoomButton({
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        maximumSize: const Size(40, 40),
        minimumSize: const Size(24, 32),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        backgroundColor: Colors.white,
        padding: EdgeInsets.zero,
      ),
      onPressed: onPressed,
      child: Center(child: Icon(icon)),
    );
  }

  /// Build zoom controls
  Widget _buildZoomControls() {
    return Column(
      children: [
        _buildZoomButton(
          icon: Icons.add,
          onPressed: () => mapTopologyHelper!.zoomMap(1),
        ),
        const SizedBox(height: 16),
        _buildZoomButton(
          icon: Icons.remove,
          onPressed: () => mapTopologyHelper!.zoomMap(-1),
        ),
      ],
    );
  }

  /// Build error overlay
  Widget? _buildErrorOverlay() {
    if (mapTopologyHelper!.errorMessage == null) return null;
    
    return Positioned(
      bottom: 10.0,
      left: 16,
      right: 60,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.red[100],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.red),
        ),
        child: Row(
          children: [
            const Icon(Icons.error, color: Colors.red),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                mapTopologyHelper!.errorMessage!,
                style: const TextStyle(color: Colors.red),
              ),
            ),
            IconButton(
              icon: const Icon(Icons.close, color: Colors.red),
              onPressed: () => mapTopologyHelper!.clearError(),
            ),
          ],
        ),
      ),
    );
  }

  /// Build legend box showing device and gateway statuses
  Widget _buildLegendBox() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Title
            const AppText(
              'Legend',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 8),

            // Device Statuses
            const AppText(
              'Device',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 4),

            ...DetectedStatusType.values.map((status) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Stack(
                        alignment: Alignment.topCenter,
                        children: [
                          Container(
                            width: MapTopologyHelper.deviceMarkerInnerSize,
                            height: MapTopologyHelper.deviceMarkerInnerSize,

                            child: SvgPicture.asset(
                              AppAssetsConstants.icMarkerDevice,
                              colorFilter: ColorFilter.mode(
                                MapTopologyHelper.getStatusColor(status),
                                BlendMode.srcIn,
                              ),
                              width: 22,
                              height: 22,
                            ),
                          ),

                          Padding(
                            padding:  EdgeInsets.only(top: 5),
                            child: ClipOval(
                              child: Container(
                                color: Colors.white,
                                child: Icon(
                                  MapTopologyHelper.getStatusIcon(status),
                                  color: MapTopologyHelper.getStatusColor(status),
                                  size: 23,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(width: 6),
                      AppText(
                        status.displayName,
                        style: const TextStyle(fontSize: 11),
                      ),
                    ],
                  ),
                )),

            const SizedBox(height: 8),
/*
            // Gateway Statuses
            const AppText(
              'Gateway',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 4),
            _buildLegendItem(
              icon: MapTopologyHelper.getGatewayIcon(),
              color: MapTopologyHelper.getGatewayColor('online'),
              label: S.of(context).online,
            ),
            _buildLegendItem(
              icon: MapTopologyHelper.getGatewayIcon(),
              color: MapTopologyHelper.getGatewayColor('offline'),
              label: S.of(context).offline,
            ),
            _buildLegendItem(
              icon: MapTopologyHelper.getGatewayIcon(),
              color: MapTopologyHelper.getGatewayColor('never_seen'),
              label:  S.of(context).neverSeen,
            ),
            _buildLegendItem(
              icon: MapTopologyHelper.getGatewayIcon(),
              color: MapTopologyHelper.getGatewayColor('unknown'),
              label: S.of(context).unknown,
            ),*/
          ],
        ),
      ),
    );
  }

  /// Build individual legend item
  Widget _buildLegendItem({
    required IconData icon,
    required Color color,
    required String label,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            alignment: Alignment.topCenter,
            children: [
              Container(
                width: MapTopologyHelper.gatewayMarkerLegendInnerSize,
                height: MapTopologyHelper.gatewayMarkerLegendInnerSize,

                child: SvgPicture.asset(
                  AppAssetsConstants.icGwDevice,
                  colorFilter: ColorFilter.mode(
                    color,
                    BlendMode.srcIn,
                  ),
                  width: 22,
                  height: 22,
                ),
              ),

              Padding(
                padding:  EdgeInsets.only(top: 5),
                child: SvgPicture.asset(
                  AppAssetsConstants.icGwDeviceIcon,
                  width: 22,
                  height: 22,
                ),
              ),
            ],
          ),
          const SizedBox(width: 6),
          AppText(
            label,
            style: const TextStyle(fontSize: 11),
          ),
        ],
      ),
    );
  }
}

// Animated cluster marker widget
class AnimatedClusterMarker extends StatefulWidget {
  final int count;
  final VoidCallback onTap;
  final Color color;
  const AnimatedClusterMarker({required this.count, required this.onTap, required this.color, super.key});

  @override
  State<AnimatedClusterMarker> createState() => _AnimatedClusterMarkerState();
}

class _AnimatedClusterMarkerState extends State<AnimatedClusterMarker> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scale;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
      lowerBound: 1.0,
      upperBound: 1.3,
    );
    _scale = _controller.drive(Tween(begin: 1.0, end: 1.3));
  }

  void _onTap() async {
    await _controller.forward();
    await _controller.reverse();
    widget.onTap();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _onTap,
      child: ScaleTransition(
        scale: _scale,
        child: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: widget.color,
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white, width: 2),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          alignment: Alignment.center,
          child: AppText(
            '${widget.count}',
            isSelectableText: false,
            style: const TextStyle(color: Colors.white, fontWeight: FontWeight.w700, fontSize: 14),
          ),
        ),
      ),
    );
  }
}