import 'dart:math' as math;
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/controller/map_topology_controller.dart';
import 'map_topology_helper.dart';
/// View class that handles ONLY UI components and widgets following GetX pattern
class MapTopologyView extends StatefulWidget {
  const MapTopologyView({super.key});

  @override
  State<MapTopologyView> createState() => MapTopologyPageState();
}

class MapTopologyPageState extends State<MapTopologyView> {
  MapTopologyHelper? mapTopologyHelper;
  MapTopologyController mapTopologyController = MapTopologyController();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    mapTopologyHelper ?? (mapTopologyHelper = MapTopologyHelper(this));
    return GetBuilder<MapTopologyController>(
      init: MapTopologyController(),
      builder: (MapTopologyController controller) {
        mapTopologyController = controller;
        return getBody();
      },
    );
  }

  Widget getBody() {
    return Column(crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(padding: const EdgeInsets.symmetric(horizontal: 15),child: getPageTitleView(S.of(context).topology),),
        Expanded(child: getMapTopologyView(),)
      ],
    );
  }

  Widget getMapTopologyView() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
      decoration: BoxDecoration(
        color: AppColorConstants.colorWhite,
        borderRadius: const BorderRadius.only(
          topRight: Radius.circular(8),
          topLeft: Radius.circular(8),
        ),
        border: Border.all(color: AppColorConstants.colorH2, width: 1),
      ),
      child: Stack(
        children: [
          Column(
            children: [
              // Header with filters
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16,vertical: 12),
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(8),
                    topLeft: Radius.circular(8),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildFilterControls(),
                  ],
                ),
              ),
              // Zoom info bar
              _buildZoomInfoBar(),
              // Map
              Expanded(
                child: FlutterMap(
                  mapController: mapTopologyHelper!.mapController,
                  options: MapOptions(
                    minZoom: 3,
                    initialCenter: const LatLng(37.068851, -88.669345), // Atlanta area
                    initialZoom: MapTopologyHelper.initialZoom,
                    onPositionChanged: _onMapZoomChanged,
                    // Disable map rotation by setting rotation to 0
                    rotation: 0.0,
                    // Disable map repeat by limiting bounds
                    maxBounds: LatLngBounds(
                      const LatLng(-85, -180), // South, West
                      const LatLng(85, 180),   // North, East
                    ),
                  ),
                  children: [
                    TileLayer(
                      urlTemplate: AppAssetsConstants.openStreetMapUrl,
                      // Disable map repeat by setting maxBounds in MapOptions instead
                      tms: false,
                    ),
                    PolylineLayer(polylines: _buildPolylines()),
                    MarkerLayer(
                      markers: _buildAllMarkers(),
                    ),
                  ],
                ),
              ),
            ],
          ),

          openStreetMapCopyRightLinkView(context),
          // Legend Box
          Positioned(
            top: 105,
            right: 8,
            child: _buildLegendBox(),
          ),
          // Zoom controls
          Positioned(
            bottom: 35.0,
            right: 10,
            child: _buildZoomControls(),
          ),
          // Error overlay
          if (_buildErrorOverlay() != null) _buildErrorOverlay()!,
        ],
      ),
    );
  }

  // ====== UI BUILDING METHODS ======

  /// Handle map zoom changes
  void _onMapZoomChanged(MapPosition position, bool hasGesture) {
    debugLogs("onPositionChanged ->");
    debugLogs(hasGesture);
    debugLogs(position.center);
    debugLogs(position.zoom);
    final bbox = [
      position.bounds!.west,
      position.bounds!.south,
      position.bounds!.east,
      position.bounds!.north,
    ];
    debugLogs(bbox);
    mapTopologyHelper!.onMapZoomChanged(context, bbox);
    mapTopologyHelper!.setCurrentZoom(position.zoom ?? mapTopologyHelper!.currentZoom);
  }
  /// Build animated device markers with count/expand functionality
  List<Marker> _buildAnimatedDeviceMarkers() {
    final devicesForDisplay = mapTopologyHelper!.getDevicesForDisplay();
    List<Marker> markers = [];

    for (final deviceWithOffset in devicesForDisplay) {
      markers.add(_buildDeviceMarker(deviceWithOffset));
    }

    return markers;
  }

  /// Build device marker with offset position
  Marker _buildDeviceMarker(MapDeviceWithOffset deviceWithOffset) {
    final device = deviceWithOffset.device;

    return Marker(
      point: deviceWithOffset.position,
      width: MapTopologyHelper.deviceMarkerWidthSize,
      height: MapTopologyHelper.deviceMarkerHeightSize,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 500),
        curve: Curves.elasticOut,
        child: Tooltip(
          padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
          decoration: BoxDecoration(
            color: AppColorConstants.colorWhite,
            borderRadius: const BorderRadius.all(Radius.circular(8)),
          ),
          richMessage: TextSpan(
            children: [
              WidgetSpan(
                child:_buildDeviceTooltipWidget(deviceWithOffset),
              ),
            ],
          ),
          child: InkWell(
            onTap: deviceWithOffset.isMultiDeviceLocation ? () {} : null,
            child: Column(
              children: [
                Stack(
                  alignment: Alignment.center,
                  children: [
                    AppImageAsset(
                        fit: BoxFit.cover,
                        height: 42,
                        width: 42,
                        image: MapTopologyHelper.getStatusIcon(device.status)),
                    if (mapTopologyHelper!.getDeviceTypeCode(device.type).isNotEmpty ||
                        mapTopologyHelper!.getBwModeVersion(device.bwMode).isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 3,right: 1),
                        child: AppText(
                          "${mapTopologyHelper!.getDeviceTypeCode(device.type)}\n"
                          "${mapTopologyHelper!.getBwModeVersion(device.bwMode)}",
                          isSelectableText: false,
                          style: TextStyle(
                              fontSize: 9,
                              fontWeight: FontWeight.bold,
                              color: AppColorConstants.colorWhite),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    if (deviceWithOffset.isMultiDeviceLocation)
                      Positioned(
                        top: -1,
                        right: -1,
                        child: Container(
                          height: 18,
                          width: 18,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.white, width: 2),
                          ),
                          child: Text(
                            "${deviceWithOffset.totalDevicesAtLocation}",
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 11,
                              fontWeight: FontWeight.bold,
                              height: 1.0, 
                            ),
                          ),
                        ),
                      )

                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }


  /// Build gateway marker
  Marker _buildGatewayMarker(MapGateway gateway) {

    return Marker(
      point: gateway.position,
      width: MapTopologyHelper.gatewayMarkerSize,
      height: MapTopologyHelper.gatewayMarkerSize,
              child: Tooltip(
          padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
          decoration: BoxDecoration(
            color: AppColorConstants.colorWhite,
            borderRadius: const BorderRadius.all(Radius.circular(8)),
          ),
          richMessage: TextSpan(
            children: [
              WidgetSpan(
                child: _buildGatewayTooltipWidget(gateway),
              ),
            ],
          ),
        child: Column(
          children: [
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.rectangle,
                border: Border.all(color: Colors.grey, width: 1),
              ),
              child: AppText(" "+gateway.name, style: TextStyle(fontSize: 8, fontWeight: FontWeight.bold), textAlign: TextAlign.center),
            ),
            SizedBox(height: 3,),
            Stack(
              alignment: Alignment.topCenter,
              children: [
                Container(
                  width: MapTopologyHelper.gatewayMarkerInnerSize,
                  height: MapTopologyHelper.gatewayMarkerInnerSize,

                  child: SvgPicture.asset(
                    AppAssetsConstants.icGwDevice,
                    colorFilter: ColorFilter.mode(
                      MapTopologyHelper.getGatewayColor(gateway.status),
                      BlendMode.srcIn,
                    ),
                    width: 22,
                    height: 22,
                  ),
                ),

                Padding(
                  padding:  EdgeInsets.only(top: 5),
                  child: SvgPicture.asset(
                    AppAssetsConstants.icGwDeviceIcon,
                    width: 22,
                    height: 22,
                  ),
                ),
              ],
            ),



          ],
        ),
      ),
    );
  }

  /// Build all markers
  List<Marker> _buildAllMarkers() {
    final deviceMarkers = _buildAnimatedDeviceMarkers();
    // final gatewayMarkers = _buildGatewayMarkers();
    final nodeMarkers = _buildNodeMarkers();
    final clusterMarkers = _buildClusterMarkers();
    final connectionArrows = _buildConnectionLineArrows();
    return [...deviceMarkers, ...nodeMarkers, ...clusterMarkers, ...connectionArrows];
  }

  /// Build connection lines for multi-device locations
  List<Polyline> _buildConnectionLines() {
    final devicesForDisplay = mapTopologyHelper!.getDevicesForDisplay();
    List<Polyline> polylines = [];

    // Group devices by location to find expanded locations
    final Map<String, List<MapDeviceWithOffset>> locationGroups = {};
    for (final device in devicesForDisplay) {
      if (device.isMultiDeviceLocation) {
        final locationKey = mapTopologyHelper!.getLocationKey(device.device.position);
        locationGroups.putIfAbsent(locationKey, () => []).add(device);
      }
    }

    // Create connection lines for each expanded location
    for (final entry in locationGroups.entries) {
      final devices = entry.value;
      if (devices.length > 1) {
        final centerPoint = devices.first.device.position;

        for (final device in devices) {
          polylines.add(
            Polyline(
              points: [centerPoint, device.position],
              strokeWidth: 3.0,
              color: Colors.orange,
            ),
          );
        }
      }
    }

    return polylines;
  }

  /// Build arrow markers for connection lines
  List<Marker> _buildConnectionLineArrows() {
    final devicesForDisplay = mapTopologyHelper!.getFilteredConnections();
    List<Marker> arrowMarkers = [];

    // Group devices by location to find expanded locations
    final Map<String, List<MapDeviceWithOffset>> locationGroups = {};
    for (final devices in devicesForDisplay) {
      final targetPoint = devices.targetId;
      final angle = _calculateAngle(centerPoint, targetPoint);

      // Add arrow at the end of the line (at target device)
      arrowMarkers.add(_createArrowMarker(
        targetPoint,
        angle,
        Colors.orange,
        isEndArrow: true,
      ));

      // Add arrow at midpoint for better visibility
      final midPoint = LatLng(
        (centerPoint.latitude + targetPoint.latitude) / 2,
        (centerPoint.longitude + targetPoint.longitude) / 2,
      );
      arrowMarkers.add(_createArrowMarker(
        midPoint,
        angle,
        Colors.orange,
        isEndArrow: false,
      ));
    }

    // Create arrows for each connection line
    for (final entry in locationGroups.entries) {

    }

    return arrowMarkers;
  }

  /// Create a single arrow marker
  Marker _createArrowMarker(LatLng position, double angle, Color color, {required bool isEndArrow}) {
    return Marker(
      point: position,
      width: isEndArrow ? 20 : 16,
      height: isEndArrow ? 20 : 16,
      child: Transform.rotate(
        angle: angle,
        child: Icon(
          Icons.arrow_forward,
          color: color,
          size: 100,
        ),
      ),
    );
  }

  /// Calculate angle between two points for arrow rotation
  double _calculateAngle(LatLng from, LatLng to) {
    final deltaX = to.longitude - from.longitude;
    final deltaY = to.latitude - from.latitude;
    return math.atan2(deltaY, deltaX);
  }

  /// Build device markers list
  List<Marker> _buildDeviceMarkers() {
    final filteredDevicesWithOffset = mapTopologyHelper!.getFilteredDevicesWithOffset();
    List<Marker> markers = [];
    for (final deviceWithOffset in filteredDevicesWithOffset) {
      markers.add(_buildDeviceMarker(deviceWithOffset));
    }
    return markers;
  }

  /// Build gateway markers list
  List<Marker> _buildGatewayMarkers() {
    return mapTopologyHelper!.currentGateways.map((gateway) => _buildGatewayMarker(gateway)).toList();
  }

  /// Build node marker
  Marker _buildNodeMarker(MapNode node) {
    return Marker(
      point: node.position,
      width: 36,
      height: 36,
      child: Tooltip(
        padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
        decoration: BoxDecoration(
          color: AppColorConstants.colorWhite,
          borderRadius: const BorderRadius.all(Radius.circular(8)),
        ),
        richMessage: TextSpan(
          children: [
            WidgetSpan(
              child: _buildNodeTooltipWidget(node),
            ),
          ],
        ),
        child: const AppImageAsset(image: AppAssetsConstants.starIcon),
      ),
    );
  }

  /// Build node markers list
  List<Marker> _buildNodeMarkers() {
    if (!mapTopologyHelper!.showNodes) return [];
    return mapTopologyHelper!.currentNodes.map((node) => _buildNodeMarker(node)).toList();
  }
  /// Build cluster markers for device_clusters
  List<Marker> _buildClusterMarkers() {
    return mapTopologyHelper!.deviceClusters.map((cluster) {
      return Marker(
        point: LatLng(cluster.lat, cluster.lng),
        width: 40,
        height: 40,
        child: AnimatedClusterMarker(
          count: cluster.count,
          color: Colors.deepPurple,
          onTap: () async {
            final currentZoom = mapTopologyHelper!.currentZoom;
            final targetZoom = currentZoom < 12.5 ? 12.5 : currentZoom + 2.0;
            await mapTopologyHelper!.smoothZoomToCluster(
              LatLng(cluster.lat, cluster.lng),
              targetZoom,
              duration: const Duration(milliseconds: 1500),
            );
          },
        ),
      );
    }).toList();
  }

  /// Build polylines for connections
  List<Polyline> _buildPolylines() {
    List<Polyline> polylines = [];

    final filteredConnections = mapTopologyHelper!.getFilteredConnections();
    polylines.addAll(filteredConnections.map((conn) {
      final points = mapTopologyHelper!.getPolylinePoints(conn);
      if (points == null) return null;

      return Polyline(
        points: points,
        color: AppColorConstants.colorPolyLine,
        strokeWidth: 2,
      );
    }).where((polyline) => polyline != null).cast<Polyline>());

    // Node-Device links
    if (mapTopologyHelper!.showNodes && mapTopologyHelper!.showDeviceLinks) {
      final filteredNodeConnections = mapTopologyHelper!.getFilteredNodeConnections();
      polylines.addAll(filteredNodeConnections.map((conn) {
        final points = mapTopologyHelper!.getPolylinePointsForNode(conn);
        if (points == null) return null;

        return Polyline(
          points: points,
          color: Colors.purpleAccent,
          strokeWidth: 2,
        );
      }).where((polyline) => polyline != null).cast<Polyline>());
    }

    // Add connection lines for multi-device locations
    polylines.addAll(_buildConnectionLines());

    return polylines;
  }

  // /// Build polylines for connections
  // List<Polyline> _buildPolylines() {
  //   final filteredConnections = mapTopologyHelper!.getFilteredConnections();
  //
  //   return filteredConnections.map((conn) {
  //     final points = mapTopologyHelper!.getPolylinePoints(conn);
  //     if (points == null) return null;
  //
  //     return Polyline(
  //       points: points,
  //       color: AppColorConstants.colorPolyLine,
  //       strokeWidth: 2,
  //     );
  //   }).where((polyline) => polyline != null).cast<Polyline>().toList();
  // }

  /// Build filter chip
  Widget _buildFilterChip({
    required String label,
    required IconData icon,
    required Color iconColor,
    required bool selected,
    required ValueChanged<bool> ?onSelected,
  }) {
    return FilterChip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: iconColor, size: 16),
          const SizedBox(width: 4),
          AppText(label,isSelectableText: false),
        ],
      ),
      selected: selected,
      onSelected: onSelected,
      selectedColor: iconColor.withOpacity(0.1),
    );
  }

  /// Build filter controls
  Widget _buildFilterControls() {
    return Row(mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const SizedBox(width: 12),
        _buildLayersFilterView(),
        _buildProdCodeFilterDropdown(),
        const SizedBox(width: 12),
        _buildStatusFilterDropdown(),
        const SizedBox(width: 12),
        _buildVendorCodeFilterDropdown(),
      ],
    );
  }


  Widget _buildStatusFilterDropdown() {
    return buildCustomDropdown<String>(
      label:
      "${S.of(context).status}${mapTopologyHelper!.selectedDeviceStatuses.isEmpty ? "" : " : ${mapTopologyHelper?.selectedDeviceStatuses.join(', ')}"}",
      selectedValue: null,
      height: 50,
      width: 180,
      isEnabled: mapTopologyHelper!.isDevicesShowEnabled,
      items: mapTopologyHelper!.allDeviceStatuses.map((item) {
        return DropdownMenuItem<String>(
          value: item,
          enabled: false,
          child: StatefulBuilder(
            builder: (context, menuSetState) {
              final isSelected =
              mapTopologyHelper!.selectedDeviceStatuses.contains(item);
              return CheckboxListTile(
                title: AppText(isSelectableText: false, item, style: const TextStyle(fontSize: 14)),
                value: isSelected,
                onChanged: (checked) {
                  if (checked == null) return;
                  if (checked) {
                    mapTopologyHelper!.selectedDeviceStatuses.add(item);
                  } else {
                    mapTopologyHelper!.selectedDeviceStatuses.remove(item);
                  }
                  menuSetState(() {
                    mapTopologyHelper?.refreshDataWithNewFilters();
                  });
                },
              );
            },
          ),
        );
      }).toList(),
      onChanged: (_) {},
    );
  }


  Widget _buildVendorCodeFilterDropdown() {
    return buildCustomDropdown<String>(
      label: "${S.of(context).vendor} : ${mapTopologyHelper?.selectedVendorCode ?? ""}",
      selectedValue: null,
      width: 150,
      isEnabled: mapTopologyHelper!.isDevicesShowEnabled,
      items: mapTopologyHelper!.vendorCodeList!.map((entry) {
        return DropdownMenuItem<String>(
          value: entry,
          child: AppText(entry, style: const TextStyle(fontSize: 14), isSelectableText: false),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          mapTopologyHelper!.selectedVendorCode = value;
          mapTopologyHelper?.refreshDataWithNewFilters();
          mapTopologyController.update();
        }
      },
    );
  }


  Widget _buildLayersFilterView() {
   bool isEnable =  mapTopologyHelper!.isDevicesShowEnabled ;
    return  Expanded(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          AppText(
            "${S.of(context).layers} :",
            style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 15),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildFilterChip(
                  label: 'Devices',
                  icon: Icons.device_hub,
                  iconColor: Colors.blue,
                  selected: mapTopologyHelper!.showDevices,
                  onSelected: isEnable ? (value) => mapTopologyHelper!.setShowDevices(value) : null,
                ),
                // _buildFilterChip(
                //   label: 'Gateways',
                //   icon: Icons.router,
                //   iconColor: Colors.orange,
                //   selected: mapTopologyHelper!.showGateways,
                //   onSelected:isEnable ?  (value) => mapTopologyHelper!.setShowGateways(value) : null,
                // ),
                _buildFilterChip(
                  label: 'Device Links',
                  icon: Icons.link,
                  iconColor: AppColorConstants.colorPink,
                  selected: mapTopologyHelper!.showDeviceLinks,
                  onSelected: isEnable ? (value) => mapTopologyHelper!.setShowDeviceLinks(value) : null,
                ),
                _buildFilterChip(
                  label: 'Nodes',
                  icon: Icons.device_hub,
                  iconColor: Colors.purpleAccent,
                  selected: mapTopologyHelper!.showNodes,
                  onSelected: isEnable ? (value) => mapTopologyHelper!.setShowNodes(value) : null,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }


  Widget _buildProdCodeFilterDropdown() {
    return buildCustomDropdown<int>(
      label: "${S.of(context).product} : ${mapTopologyHelper!.prodIdCodeMap[mapTopologyHelper?.selectedProdId] ?? ""}",
      selectedValue: null,
      isEnabled: mapTopologyHelper!.isDevicesShowEnabled,
      width: 200,
      items: mapTopologyHelper!.prodIdCodeMap.entries.map((entry) {
        return DropdownMenuItem<int>(
          value: entry.key,
          child: AppText(entry.value, style: const TextStyle(fontSize: 14), isSelectableText: false),
        );
      }).toList(),
      onChanged: (code) {
        if (code != null) {
          mapTopologyHelper!.selectedProdId = code;
          mapTopologyHelper?.refreshDataWithNewFilters();
          mapTopologyController.update();
        }
      },
    );
  }

  Widget buildCustomDropdown<T>({
    required String label,
    required T? selectedValue,
    required List<DropdownMenuItem<T>> items,
    required ValueChanged<T?>? onChanged,
    double? width,
    double? height,
    bool isEnabled = true,
  }) {
    return DropdownButtonHideUnderline(
      child: DropdownButton2<T>(
        isExpanded: true,
        customButton: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              AppText(
                isSelectableText: false,
                label,
                style: TextStyle(
                  fontSize: 14,
                  color: isEnabled
                      ? AppColorConstants.colorBlackBlue
                      : AppColorConstants.colorH2,
                ),
              ),
              const SizedBox(width: 5),
              Icon(
                Icons.keyboard_arrow_down_outlined,
                color: isEnabled
                    ? AppColorConstants.colorBlackBlue
                    : AppColorConstants.colorH2,
                size: 20,
              ),
            ],
          ),
        ),
        value: selectedValue,
        buttonStyleData: ButtonStyleData(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          height: height ?? 40,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        dropdownStyleData: DropdownStyleData(
          width: width ?? 180,
          openInterval: const Interval(0.25, 0.70),
          maxHeight: 200,
          decoration: BoxDecoration(
            color: AppColorConstants.colorWhite,
            borderRadius: BorderRadius.circular(8),
          ),
          scrollbarTheme: ScrollbarThemeData(
            radius: const Radius.circular(40),
            thickness: MaterialStateProperty.all<double>(6),
            thumbVisibility: MaterialStateProperty.all<bool>(true),
          ),
        ),
        iconStyleData: IconStyleData(
          openMenuIcon: Icon(
            Icons.keyboard_arrow_up_sharp,
            color: AppColorConstants.colorH3,
          ),
          icon: Icon(
            Icons.keyboard_arrow_down_outlined,
            color: AppColorConstants.colorH3,
          ),
          iconSize: 20,
        ),
        items: items,
        onChanged: isEnabled ? onChanged : null,
        menuItemStyleData: const MenuItemStyleData(
          height: 45,
        ),
      ),
    );
  }


  /// Build zoom info bar
  Widget _buildZoomInfoBar() {
    final deviceMarkers = _buildDeviceMarkers();
    final nodeMarkers = _buildNodeMarkers();
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: AppColorConstants.colorBackgroundDark,
        border: Border(
          top: BorderSide(color: AppColorConstants.colorDivider.withOpacity(0.3)),
          bottom: BorderSide(color: AppColorConstants.colorDivider.withOpacity(0.3)),
        ),
      ),
      child: Row(
        children: [
          if (mapTopologyHelper!.isLoading) ...[
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
            const SizedBox(width: 8),
            Text(S.of(context).loadingDevices),
          ] else ...[
            Icon(Icons.zoom_in, size: 16, color: AppColorConstants.colorH3),
            const SizedBox(width: 8),
            Text(
              '${S.of(context).zoom}: ${mapTopologyHelper!.currentZoom.toStringAsFixed(1)} - ${mapTopologyHelper!.getZoomLevelCategory()}',
              style: TextStyle(
                fontSize: 12,
                color: AppColorConstants.colorH3,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
          const Spacer(),
          if (mapTopologyHelper!.isDevicesShowEnabled)
            Text(
              mapTopologyHelper!.getDeviceCountInfo(deviceMarkers.length, nodeMarkers.length),
              style: TextStyle(
                fontSize: 12,
                color: AppColorConstants.colorH3,
              ),
            ),
        ],
      ),
    );
  }

  /// Build zoom control button
  Widget _buildZoomButton({
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        maximumSize: const Size(40, 40),
        minimumSize: const Size(24, 32),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        backgroundColor: Colors.white,
        padding: EdgeInsets.zero,
      ),
      onPressed: onPressed,
      child: Center(child: Icon(icon)),
    );
  }

  /// Build zoom controls
  Widget _buildZoomControls() {
    return Column(
      children: [
        _buildZoomButton(
          icon: Icons.add,
          onPressed: () => mapTopologyHelper!.zoomMap(1),
        ),
        const SizedBox(height: 16),
        _buildZoomButton(
          icon: Icons.remove,
          onPressed: () => mapTopologyHelper!.zoomMap(-1),
        ),
      ],
    );
  }

  /// Build device tooltip widget
  Widget _buildDeviceTooltipWidget(MapDeviceWithOffset deviceWithOffset) {
    final device = deviceWithOffset.device;
    final bool isMultiDevice = deviceWithOffset.isMultiDeviceLocation;
    
    // Get all devices at this location if it's a multi-device location
    List<MapDevice> devicesAtLocation = [];
    if (isMultiDevice && deviceWithOffset.allDevicesAtLocation != null) {
      devicesAtLocation = deviceWithOffset.allDevicesAtLocation!;
    } else {
      devicesAtLocation = [device];
    }

    return Container(
      constraints: const BoxConstraints(
        minWidth: 350,
        maxWidth: 500,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (isMultiDevice) ...[
            // Multiple devices table
            _buildMultipleDevicesTable(devicesAtLocation),
          ] else ...[
            // Single device details
            _buildMultipleDevicesTable([device]),
          ],
        ],
      ),
    );
  }

  /// Build table for multiple devices
  Widget _buildMultipleDevicesTable(List<MapDevice> devices) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Table header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
              border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
            ),
            child: const Row(
              children: [
                Expanded(flex: 2, child: Text('Device ID', style: TextStyle(fontWeight: FontWeight.w600, fontSize: 13))),
                Expanded(flex: 2, child: Text('Type', style: TextStyle(fontWeight: FontWeight.w600, fontSize: 13))),
                Expanded(flex: 1, child: Text('Mode', style: TextStyle(fontWeight: FontWeight.w600, fontSize: 13))),
                Expanded(flex: 1, child: Text('Status', style: TextStyle(fontWeight: FontWeight.w600, fontSize: 13))),
              ],
            ),
          ),
          // Table rows
          ...devices.map((device) => Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              border: Border(bottom: BorderSide(color: Colors.grey.shade200)),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Text(
                    device.deviceEui,
                    style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    device.type,
                    style: const TextStyle(fontSize: 12),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    mapTopologyHelper!.getBwModeVersion(device.bwMode),
                    style: const TextStyle(fontSize: 12),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Container(
                    margin: const EdgeInsets.only(right: 16),
                    padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
                    decoration: BoxDecoration(
                      color: MapTopologyHelper.getStatusColor(device.status),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                          device.status.name == "fwDownload"
                              ? S.of(context).fwdnld
                              : device.status.displayName,
                          style: const TextStyle(
                              fontSize: 10, color: Colors.white, fontWeight: FontWeight.w600),
                          textAlign: TextAlign.center,
                        ),
                      ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }


  /// Build detail row for single device
  Widget _buildDetailRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(
          width: 105,
          child: Text(
            '$label :',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 13,
              color: Colors.grey.shade700,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(fontSize: 13),
          ),
        ),
      ],
    );
  }

  /// Build gateway tooltip widget
  Widget _buildGatewayTooltipWidget(MapGateway gateway) {
    return Container(
      constraints: const BoxConstraints(
        minWidth: 200,
        maxWidth: 250,
      ),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.15),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: _buildGatewayDetails(gateway),
    );
  }

  /// Build gateway details
  Widget _buildGatewayDetails(MapGateway gateway) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDetailRow('Gateway ID', gateway.gatewayId),
        _buildDetailRow('Type', gateway.type),
        _buildDetailRow('Status', gateway.status),
        if (gateway.domain!.isNotEmpty)
          _buildDetailRow('Domain', gateway.domain!),
        if (gateway.hwVersion.isNotEmpty)
          _buildDetailRow('HW Version', gateway.hwVersion),
        if (gateway.swVersion.isNotEmpty)
          _buildDetailRow('SW Version', gateway.swVersion),
        if (gateway.userEmail.isNotEmpty)
          _buildDetailRow('User Email', gateway.userEmail),
        _buildDetailRow('Last Seen', getUtcTimeZone(gateway.lastSeen ?? 0)),
      ],
    );
  }

  /// Build gateway tooltip widget
  Widget _buildNodeTooltipWidget(MapNode node) {
    return Container(
      constraints: const BoxConstraints(
        minWidth: 200,
        maxWidth: 250,
      ),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.15),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: _buildNodeDetails(node),
    );
  }

  /// Build gateway details
  Widget _buildNodeDetails(MapNode node) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (node.gwEui != null && node.gwEui!.isNotEmpty) _buildDetailRow('Gateway EUI', node.gwEui!),
        if (node.alias.isNotEmpty) _buildDetailRow('Alias', node.alias),
        if (node.description.isNotEmpty) _buildDetailRow('Description', node.description),
        if (node.placement.isNotEmpty) _buildDetailRow('Placement', node.placement),
        _buildDetailRow('Location Confirmed', node.isLocationConfirmed ? 'Yes' : 'No'),
        if (node.manufacturer.isNotEmpty) _buildDetailRow('Manufacturer', node.manufacturer),
        if (node.model.isNotEmpty) _buildDetailRow('Model', node.model),
      ],
    );
  }

  /// Build error overlay
  Widget? _buildErrorOverlay() {
    if (mapTopologyHelper!.errorMessage == null) return null;
    
    return Positioned(
      bottom: 10.0,
      left: 16,
      right: 60,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.red[100],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.red),
        ),
        child: Row(
          children: [
            const Icon(Icons.error, color: Colors.red),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                mapTopologyHelper!.errorMessage!,
                style: const TextStyle(color: Colors.red),
              ),
            ),
            IconButton(
              icon: const Icon(Icons.close, color: Colors.red),
              onPressed: () => mapTopologyHelper!.clearError(),
            ),
          ],
        ),
      ),
    );
  }

  /// Build legend box showing device and gateway statuses
  Widget _buildLegendBox() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Title
            const AppText(
              'Legend',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 8),

            // Device Statuses
            const AppText(
              'Device',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 4),

            ...DetectedStatusType.values.map((status) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Stack(
                        alignment: Alignment.topCenter,
                        children: [
                          Padding(
                            padding:  EdgeInsets.only(top: 5),
                            child: AppImageAsset(
                                fit: BoxFit.fill,
                                height: 25,
                                width: 25,
                                image: MapTopologyHelper.getStatusIcon(status)
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(width: 6),
                      AppText(
                        status.displayName,
                        style: const TextStyle(fontSize: 11),
                      ),
                    ],
                  ),
                )),
            const SizedBox(height: 8),
            const AppText(
              'Nodes',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 4),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 4.0),
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Stack(
                    alignment: Alignment.topCenter,
                    children: [
                      Padding(
                        padding:  EdgeInsets.only(top: 5),
                        child: AppImageAsset(
                            fit: BoxFit.fill,
                            height: 25,
                            width: 25,
                            image: AppAssetsConstants.starIcon
                        ),
                      ),
                    ],
                  ),
                  SizedBox(width:8),
                  AppText(
                   "Node",
                    style: TextStyle(fontSize: 11),
                  ),
                ],
              ),
            )
/*
            // Gateway Statuses
            const AppText(
              'Gateway',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 4),
            _buildLegendItem(
              icon: MapTopologyHelper.getGatewayIcon(),
              color: MapTopologyHelper.getGatewayColor('online'),
              label: S.of(context).online,
            ),
            _buildLegendItem(
              icon: MapTopologyHelper.getGatewayIcon(),
              color: MapTopologyHelper.getGatewayColor('offline'),
              label: S.of(context).offline,
            ),
            _buildLegendItem(
              icon: MapTopologyHelper.getGatewayIcon(),
              color: MapTopologyHelper.getGatewayColor('never_seen'),
              label:  S.of(context).neverSeen,
            ),
            _buildLegendItem(
              icon: MapTopologyHelper.getGatewayIcon(),
              color: MapTopologyHelper.getGatewayColor('unknown'),
              label: S.of(context).unknown,
            ),*/
          ],
        ),
      ),
    );
  }

  /// Build individual legend item
  Widget _buildLegendItem({
    required IconData icon,
    required Color color,
    required String label,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            alignment: Alignment.topCenter,
            children: [
              Container(
                width: MapTopologyHelper.gatewayMarkerLegendInnerSize,
                height: MapTopologyHelper.gatewayMarkerLegendInnerSize,

                child: SvgPicture.asset(
                  AppAssetsConstants.icGwDevice,
                  colorFilter: ColorFilter.mode(
                    color,
                    BlendMode.srcIn,
                  ),
                  width: 22,
                  height: 22,
                ),
              ),

              Padding(
                padding:  EdgeInsets.only(top: 5),
                child: SvgPicture.asset(
                  AppAssetsConstants.icGwDeviceIcon,
                  width: 22,
                  height: 22,
                ),
              ),
            ],
          ),
          const SizedBox(width: 6),
          AppText(
            label,
            style: const TextStyle(fontSize: 11),
          ),
        ],
      ),
    );
  }
}

// Animated cluster marker widget
class AnimatedClusterMarker extends StatefulWidget {
  final int count;
  final VoidCallback onTap;
  final Color color;
  const AnimatedClusterMarker({required this.count, required this.onTap, required this.color, super.key});

  @override
  State<AnimatedClusterMarker> createState() => _AnimatedClusterMarkerState();
}

class _AnimatedClusterMarkerState extends State<AnimatedClusterMarker> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scale;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
      lowerBound: 1.0,
      upperBound: 1.3,
    );
    _scale = _controller.drive(Tween(begin: 1.0, end: 1.3));
  }

  void _onTap() async {
    await _controller.forward();
    await _controller.reverse();
    widget.onTap();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _onTap,
      child: ScaleTransition(
        scale: _scale,
        child: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: widget.color,
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white, width: 2),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          alignment: Alignment.center,
          child: AppText(
            '${widget.count}',
            isSelectableText: false,
            style: const TextStyle(color: Colors.white, fontWeight: FontWeight.w700, fontSize: 14),
          ),
        ),
      ),
    );
  }
}