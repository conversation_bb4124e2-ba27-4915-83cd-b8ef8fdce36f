import 'package:flutter/widgets.dart';
import 'package:lottie/lottie.dart';
import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/controller/dashboard_controller.dart';
import 'inventory_dashboard_page_helper.dart';

class InventoryDashboardPage extends StatefulWidget {
  const InventoryDashboardPage({super.key});

  @override
  State<InventoryDashboardPage> createState() => InventoryDashboardPageState();
}

class InventoryDashboardPageState extends State<InventoryDashboardPage> {
  InventoryDashboardPageHelper? _helper;
  late ScreenLayoutType screenLayoutType;


  @override
  void initState() {
    _helper ?? (_helper = InventoryDashboardPageHelper(this));
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        getBodyView(),
      ],
    );
  }

  Widget getBodyView() {
    return ScreenLayoutTypeBuilder(
      builder: (context, screenType, constraints) {
        screenLayoutType = screenType;
        return Center(
            child: Padding(
              padding: const EdgeInsets.only(top: 50),
              child: AppText("We're working on dashboard screen...",
                  style: TextStyle(
                      color: AppColorConstants.colorH2,
                      fontSize: 14,
                      fontFamily: AppAssetsConstants.openSans,
                      fontWeight: FontWeight.w600)),
            ));
      },
    );
  }

}
