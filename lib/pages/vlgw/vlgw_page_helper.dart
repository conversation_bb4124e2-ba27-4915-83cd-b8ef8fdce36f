import 'package:quantumlink_node/app_import.dart';

import '../../controller/vlgw_controller.dart';
import 'vlgw_datasource.dart';

class VLGWPageHelper {
  late VLGWPageState state;
  late SiteRegionsController siteRegionsController;

  bool isInitialized = false;
  VLGWs vlgWs = VLGWs.empty();
  late VLGW vLGWItem = VLGW.empty();
  late VLGWDataSource vLGWDataSource;
  ApiStatus apiStatus = ApiStatus.initial;
  final double heightOfDataTableCell = 48;
  PaginatorController paginatorController = PaginatorController();
  int recordsInPage = 0;
  int currentPageIndex = 0;
  List<VGWTabItem> vLGWListTabs = [];
  List<SiteDataModel> listSiteSensorData = [];
  VLGWFSKStats vlgwfskStats = VLGWFSKStats.empty();
  SessionConfig ndrConfig = SessionConfig.empty();
  SessionConfig ndfConfig = SessionConfig.empty();
  Version version = Version.empty();
  List<Channel>? configChannels = [];
  String configError="";
  String versionError="";
  String vlgwfskStatsError="";
  late List<bool> isHovered;
  bool isInitializedTab = false;
  late TabController tabController;
  DataTableHelper dataTableHelper = DataTableHelper();
  DateTime? vlgwUpdateTime;
  DateTime? onTapTime;
  Duration? differenceTime;
  bool isShowText = true;
  Timer? refreshTimer;
  late VLGWController vlgwController;


  //Declaration use in VLGW Info
  DateTime? lastUpdatedTimeVLGWInfo;
  Duration? differenceTimeVLGWInfo;
  bool isShowTextOfVI = true;

  //Declaration use in VLGW FSKStats
  DateTime? lastUpdatedTimeVLGWFSKStats;
  Duration? differenceTimeVLGWFSKStats;
  bool isShowTextOfVFSKStats = true;
  ApiStatus apiStatusVLGWFSKStats = ApiStatus.initial;
  bool isRefreshVLGWInfo = false;


  void getData() async {
    initializeTimer();
    vlgWs = await state.vlgwController.getVlGwList(state.context);
    vLGWDataSource = VLGWDataSource(
        context: state.context,
        vlgwPageHelper: this,
        vlgWs: vlgWs,
        onTap: (VLGW value) {
          addTabTableOnTap(value);
        });
    vlgwUpdateTime = DateTime.now();
    apiStatus = ApiStatus.success;
    getDifferenceTime();
    state.vlgwController.update();
  }

  VLGWPageHelper(this.state) {
    siteRegionsController = Get.put(SiteRegionsController());
    tabController = TabController(
      initialIndex: 0,
      length: 2,
      vsync: state,
      animationDuration: Duration.zero
    );
    getSitesList();
    getData();
    getCurrantPageIndex();
  }

  VLGWPageHelper.infoPage() {
    vlgwController = Get.find<VLGWController>();
    if (vlgwController.isClosed) {
      vlgwController = Get.put(VLGWController());
    }
  }

  VLGWPageHelper.ampsPage() {
    vlgwController = Get.find<VLGWController>();
    if (vlgwController.isClosed) {
      vlgwController = Get.put(VLGWController());
    }
  }

  getSitesList() async {
    await siteRegionsController
        .getSiteRegionsDeviceList(context: state.context)
        .then(
      (value) {
        listSiteSensorData = value.cast<SiteDataModel>();
      },
    );
  }

  Future getVLGWFInfo(BuildContext context, VLGW vLGWItem1) async {
    var sessionId = "80001FF2";
    print("sessionId=$sessionId");
    int decimalId = int.parse(sessionId, radix: 16);
    //apiStatusVLGWInfo = ApiStatus.loading;
    await vlgwController.getVLGWInfo(context, vLGWItem1.eui!).then(
      (value) {
        differenceTimeVLGWInfo =
            DateTime.now().difference(lastUpdatedTimeVLGWInfo!);
        lastUpdatedTimeVLGWInfo = DateTime.now();
        if (value != null) {
          vLGWItem = value;
        } else {
           //apiStatusVLGWInfo = ApiStatus.failed;
        }
      },
    );

    Future.delayed(Duration(seconds: 3)).then((value) {
      isShowTextOfVI = false;
      vlgwController.update();
    });

    vlgwController.update();
  }

  Future getVLGWFSKStats(BuildContext context, VLGW vLGWItem) async {
    vlgwfskStatsError="";
    await vlgwController.getVLGWFSKStats(context, vLGWItem.eui!).then(
      (value) {
        if (value.demodGoodPkts != null) {
          vlgwfskStats = value;
        } else {
          apiStatusVLGWFSKStats = ApiStatus.failed;
          vlgwfskStatsError =S.of(context).socketExceptionMessage;
        }
      },
    );
    vlgwController.update();
  }

  Future<void> getConfigsAndFSKStats(BuildContext context, VLGW vLGWItem) async {
    initializeVLGWInfoTimer();
    await getVLGWFSKStats(context, vLGWItem);
    await getConfigData(context, vLGWItem.eui ?? "");
    await getVersionOfConfig(context, vLGWItem.eui ?? "");
    getDifferenceTimeVLGWInfo();
    vlgwController.update();
  }


 Future<void> getConfigData(BuildContext context,String euiId) async {

    await vlgwController.getConfig(context, euiId).then(
          (value) {
            configError= "";
        VLGWConfig vlgwConfig = value;
        if (vlgwConfig.result != null) {
          ndrConfig = value.result.ndr;
          ndfConfig = value.result.ndf;
          configChannels = value.result.channels;
        } else {
          apiStatusVLGWFSKStats = ApiStatus.failed;
          configError = S.of(context).socketExceptionMessage;
        }
      },
    );
    vlgwController.update();
  }

  Future<void> getVersionOfConfig(BuildContext context, String euiID) async {
    await vlgwController.getVersionOfConfig(context, euiID).then(
      (value) {
        versionError = "";
        print("jeson--${jsonEncode(value)}");
        ConfigVersion configVersion = value;
        if (configVersion.result != null) {
          version = configVersion.result!;
        } else {
          apiStatusVLGWFSKStats = ApiStatus.failed;
          versionError = S.of(context).socketExceptionMessage;
        }
      },
    );
  }

  Future resetVLGWFSKStats(BuildContext context, VLGW vLGWItem) async {
    initializeVLGWInfoTimer();
    apiStatusVLGWFSKStats = ApiStatus.loading;
    vlgwController.update();
    await vlgwController.resetVLGWFSKStats(context, vLGWItem.eui!).then(
      (value) async {
        if (value != null) {
          vlgwfskStats = value;
          await getVLGWFSKStats(context, vLGWItem);
        } else {
          apiStatusVLGWFSKStats = ApiStatus.failed;
        }
        getDifferenceTimeVLGWInfo();
      },
    );
    vlgwController.update();
  }

  initializeVLGWInfoTimer() {
    isRefreshVLGWInfo = true;
    lastUpdatedTimeVLGWFSKStats = DateTime.now();
    differenceTimeVLGWFSKStats = null;
    isShowTextOfVFSKStats = true;
  }

  void getDifferenceTimeVLGWInfo() {
    isRefreshVLGWInfo = false;
    differenceTimeVLGWFSKStats =
        DateTime.now().difference(lastUpdatedTimeVLGWFSKStats!);
    lastUpdatedTimeVLGWFSKStats = DateTime.now();
    if (apiStatusVLGWFSKStats == ApiStatus.failed) {
      apiStatusVLGWFSKStats = ApiStatus.failed;
    } else {
      apiStatusVLGWFSKStats = ApiStatus.success;
    }
    Future.delayed(Duration(seconds: 2)).then((value) {
      isShowTextOfVFSKStats = false;
      vlgwController.update();
    });
  }

  void initializeTimer() {
    differenceTime = null;
    onTapTime = DateTime.now();
    isShowText = true;
    refreshTimer?.cancel();
  }

  getDifferenceTime() {
    differenceTime = DateTime.now().difference(onTapTime!);
    refreshTimer = Timer(const Duration(seconds: 2), () {
      isShowText = false;
      state.vlgwController.update();
    });
  }

  //-------------> This Function is used to get current page data length of Multiple VLGW

  // int getCurrentPageDataLength(List<VLGW> itemDataList, int currentPageIndex, int rowsPerPage) {
  //   int totalRows = 0;
  //   for (var vlgw in itemDataList) {
  //     totalRows += vlgw.deviceDetail?.length ?? 0;
  //   }
  //   int startIndex = currentPageIndex * 10;
  //   int endIndex = (currentPageIndex + 1) * 10;
  //   if (endIndex > totalRows) {
  //     endIndex = totalRows;
  //   }
  //   return endIndex - startIndex;
  // }
  getCurrantPageIndex() {
    paginatorController.addListener(() {
      currentPageIndex = (paginatorController.currentRowIndex / 10).ceil();
      state.vlgwController.update();
    });
  }

  void initializeVgwTabs(BuildContext context) {
    if (!isInitializedTab) {
      vLGWListTabs = [
        VGWTabItem(
          title: S.of(context).list,
          isCurrentOpen: true,
          icon: AppAssetsConstants.vectorIcon,
        ),
        VGWTabItem(
          title: S.of(context).topology,
          isCurrentOpen: false,
          icon: AppAssetsConstants.topologyIcon,
        ),
      ];
      isInitializedTab = true;
    }
    isHovered = List<bool>.filled(vLGWListTabs.length, false);
  }

  addTabTableOnTap(VLGW value) {
    bool deviceEuiExists = vLGWListTabs.any((tab) => tab.title == value.eui);
    vLGWItem = value;
    if (!deviceEuiExists) {
      if (vLGWListTabs.length <= 6) {
        addTab(value.eui, true);
      }else{
        S.of(state.context).maxTabMessage.showError(state.context);
      }
      state.vlgwController.update();
    } else {
      for (var element in vLGWListTabs) {
        element.isCurrentOpen = false;
      }
      int euiIndex =
          vLGWListTabs.indexWhere((element) => element.title == value.eui);
      vLGWListTabs[euiIndex].isCurrentOpen = true;
      tabController.animateTo(
        euiIndex,
        duration: Duration.zero,
      );
    }
    manageVLGWRefreshTimer();
    state.vlgwController.update();
  }

  addTab(t, o) {
    for (var tabElement in vLGWListTabs) {
      tabElement.isCurrentOpen = false;
    }
    vLGWListTabs.add(
      VGWTabItem(
        title: t,
        isCurrentOpen: o,
      ),
    );
    isHovered = List<bool>.filled(vLGWListTabs.length, false);
    tabController = TabController(
      initialIndex: vLGWListTabs.length - 1,
      length: vLGWListTabs.length,
      vsync: state,animationDuration: Duration.zero
    );
  }

  removeVLGWTab(int index) {
    vLGWListTabs.removeAt(index);
    for (var tabElement in vLGWListTabs) {
      tabElement.isCurrentOpen = false;
    }
    tabController = TabController(
      initialIndex: vLGWListTabs.length - 1,
      length: vLGWListTabs.length,
      vsync: state,
      animationDuration: Duration.zero
    );
    vLGWListTabs.first.isCurrentOpen = true;
    tabController.animateTo(0);
    manageVLGWRefreshTimer();
    state.vlgwController.update();
  }

  tabVlGwHeaderOnTap(int value) {
    for (var tabElement in vLGWListTabs) {
      tabElement.isCurrentOpen = false;
    }
    if (vLGWListTabs[value].title != S.of(state.context).list && vLGWListTabs[value].title != S.of(state.context).topology) {
      vLGWItem = vLGWDataSource.vlgWs.result
          .firstWhere((element) => element.eui == vLGWListTabs[value].title);
    }
    vLGWListTabs[value].isCurrentOpen = true;
    manageVLGWRefreshTimer();
    state.vlgwController.update();
  }

  manageVLGWRefreshTimer(){
    if(tabController.index ==0){
      state.stopTimer();
      state.startTimer();
    }else{
      state.stopTimer();
    }
  }

  updateVLGW(BuildContext context, String gwEui, String siteId) async {
    VLGW vlgw = VLGW.empty();
    vlgw.siteId = siteId;
    vlgw.eui = gwEui;
    var value = await state.vlgwController.updateSite(state.context, vlgw);
    if (value != null) {
      S.of(context).siteUpdateSuccess.showSuccess(context);
      getData();
    } else {
      S.of(context).somethingWentWrong.showError(context);
    }
  }
}
