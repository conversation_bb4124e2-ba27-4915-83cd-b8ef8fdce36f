import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/controller/vlgw_controller.dart';
import 'package:quantumlink_node/pages/vlgw/vlgw_page_helper.dart';

import 'vlgw_detail/vlgw_details.dart';

class VLGWPage extends StatefulWidget {
  const VLGWPage({super.key});

  @override
  State<StatefulWidget> createState() => VLGWPageState();
}

class VLGWPageState extends State<VLGWPage> with TickerProviderStateMixin {
  VLGWPageHelper? vlgwPageHelper;
  VLGWController vlgwController = VLGWController();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    vlgwPageHelper ?? (vlgwPageHelper = VLGWPageHelper(this));
    vlgwPageHelper!.initializeVgwTabs(context);
    return GetBuilder<VLGWController>(
      init: VLGWController(),
      builder: (VLGWController controller) {
        vlgwController = controller;
        return getBody();
      },
    );
  }

  Widget getBody() {
    return getVLGWView();
  }

  Widget getVLGWView() {
    return ScreenLayoutTypeBuilder(
      builder: (context, screenType, constraints) {
        return Container(
          height: (double.infinity),
          child: ClipRRect(
            borderRadius: const BorderRadius.all(Radius.circular(5)),
            child: Container(
              color: AppColorConstants.colorWhite,
              width: double.infinity,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header sections (fixed)
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 15),
                    child: getPageTitleView(S.of(context).virtualGW),
                  ),
                  SizedBox(height: getSize(10)),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 15),
                    child: getTabsOnVlgwHeader(),
                  ),
                  // Scrollable content area
                  Expanded(
                    child: Scrollbar(
                      controller: vlgwPageHelper!.mainScrollController,
                      thumbVisibility: true,
                      child: SingleChildScrollView(
                        controller: vlgwPageHelper!.mainScrollController,
                        physics: const ClampingScrollPhysics(),
                        child: ConstrainedBox(
                          constraints: BoxConstraints(
                            minHeight: MediaQuery.of(context).size.height, // Ensure content is scrollable
                          ),
                          child: getTabsContent(),
                        ),
                      ),
                    ),
                  )
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget getTabsContent() {
    return TabBarView(
      physics: const NeverScrollableScrollPhysics(),
      controller: vlgwPageHelper!.tabController,
      children: List.generate(vlgwPageHelper!.vLGWListTabs.length, (index) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            (index == 0) ? getVLGWContent() : VlGWDetail(vLGWItem: vlgwPageHelper!.vLGWItem),
            // Add extra space to ensure scrollability
            SizedBox(height: 100),
          ],
        );
      }),
    );
  }

  Widget getVLGWContent() {
    if (vlgwPageHelper!.apiStatus != ApiStatus.success) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15),
        child: ClipRRect(
          borderRadius:
          const BorderRadius.only(topRight: Radius.circular(8), topLeft: Radius.circular(8)),
          child: Container(
              alignment: Alignment.center,
              height: 300,
              decoration: BoxDecoration(
                borderRadius:
                const BorderRadius.only(topRight: Radius.circular(8), topLeft: Radius.circular(8)),
                border: Border.all(color: AppColorConstants.colorH2, width: 0.8),
              ),
              width: double.infinity,
              child: const AppLoader()),
        ),
      );
    }
    // Widget table = getVLGWTableView();
    // int itemsPerPage = vlgwPageHelper!.getCurrentPageDataLength(
    //     vlgwPageHelper!.vLGWDataSource.vlgWs, vlgwPageHelper!.currentPageIndex , vlgwPageHelper!.recordsInPage);
    // int totalDeviceDetailsLength = vlgwPageHelper!.vLGWDataSource.vlgWs
    //     .fold(0, (sum, item) => sum + (item.deviceDetail?.length ?? 0));
    // vlgwPageHelper!.recordsInPage =
    //     (totalDeviceDetailsLength > 10) ? itemsPerPage : totalDeviceDetailsLength;
    Widget table = getVLGWTableView();
    int itemsPerPage = vlgwPageHelper!.dataTableHelper.getCurrentPageDataLength(
        vlgwPageHelper!.vLGWDataSource.vlgWs.result, vlgwPageHelper!.currentPageIndex,perPageLimit:   AppStringConstants.vlgwPerPageLimit,);
    vlgwPageHelper!.recordsInPage = (vlgwPageHelper!.vLGWDataSource.vlgWs.result.length >  AppStringConstants.vlgwPerPageLimit)
        ? itemsPerPage
        : vlgwPageHelper!.vLGWDataSource.vlgWs.result.length;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15),
      child: ClipRRect(
        borderRadius:
        const BorderRadius.only(topRight: Radius.circular(8), topLeft: Radius.circular(8)),
        child: Container(
          color: AppColorConstants.colorWhite,
          width: double.infinity,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
                table,
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    buildLastSeenView(),
                    AppRefresh(
                      buttonColor: AppColorConstants.colorPrimary,
                      loadingStatus: vlgwPageHelper!.apiStatus,
                      onPressed: () {
                        vlgwPageHelper!.getData();
                      },
                    )
                  ],
                )
              ],
            ),
          ),
        ),
      );
  }
  Widget buildLastSeenView() {
    if (vlgwPageHelper!.isShowText) {
      return getTimeDurationView(
        refreshStatus: vlgwPageHelper!.apiStatus,
        updateTime: vlgwPageHelper!.vlgwUpdateTime,
        onTapTime: vlgwPageHelper!.onTapTime,
        difference: vlgwPageHelper!.differenceTime,
      );
    } else {
      if (vlgwPageHelper!.vlgwUpdateTime != null) {
        return getLastSeenView(vlgwPageHelper!.vlgwUpdateTime);
      } else {
        return Container();
      }
    }
  }



  Widget getTabsOnVlgwHeader() {
    return SelectionArea(
      child: TabBar(
          controller: vlgwPageHelper!.tabController,
          dividerColor: AppColorConstants.colorWhite,
          labelPadding: EdgeInsets.zero,
          labelColor: Colors.white,
          padding: EdgeInsets.zero,
          isScrollable: true,
          indicatorColor: AppColorConstants.colorWhite,
          tabAlignment: TabAlignment.start,
          onTap: (value) => vlgwPageHelper!.tabVlGwHeaderOnTap(value),
          tabs: List.generate(vlgwPageHelper!.vLGWListTabs.length, (index) {
            VGWTabItem vLGWTabItem = vlgwPageHelper!.vLGWListTabs[index];
            return MouseRegion(
              onEnter: (event) {
                vlgwPageHelper!.isHovered[index] = true;
                vlgwController.update();
              },
              onExit: (event) {
                vlgwPageHelper!.isHovered[index] = false;
                vlgwController.update();
              },
              child: Tab(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 5),
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: getSize(25)),
                    height: getSize(42),
                    alignment: Alignment.center,
                    decoration: vLGWTabItem.getDeco(vlgwPageHelper!.isHovered[index]),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (index == 0)
                          Padding(
                            padding: EdgeInsets.all((index == 0 ? 9.5 : 0.0)),
                            child: AppImageAsset(width: 25,
                                image: vLGWTabItem.icon ?? "",
                                color: vlgwPageHelper!.isHovered[index] && !vLGWTabItem.isCurrentOpen
                                    ? AppColorConstants.colorBlackBlue
                                    : vLGWTabItem.isCurrentOpen
                                    ? AppColorConstants.colorLightBlue
                                    : AppColorConstants.colorH2),
                          ),
                        AppText(
                          isSelectableText: false,
                          vLGWTabItem.title,
                          style: TextStyle(
                              fontSize: getSize(16),
                              fontFamily: AppAssetsConstants.poppins,
                              fontWeight: FontWeight.w600,
                              color: vlgwPageHelper!.isHovered[index] && !vLGWTabItem.isCurrentOpen
                                  ? AppColorConstants.colorBlackBlue
                                  : vLGWTabItem.isCurrentOpen
                                  ? AppColorConstants.colorLightBlue
                                  : AppColorConstants.colorH2),
                        ),
                        if (vlgwPageHelper!.vLGWListTabs.length > 1 && index > 0) ...[
                          SizedBox(width: getSize(10)),
                          GestureDetector(
                              onTap: () => vlgwPageHelper!.removeVLGWTab(index),
                              child: CircleAvatar(
                                  maxRadius: 10,
                                  backgroundColor: vLGWTabItem.isCurrentOpen
                                      ? AppColorConstants.colorLightBlue
                                      : AppColorConstants.colorH2.withOpacity(0.3),
                                  child: Icon(Icons.close,
                                      size: getSize(16),
                                      color: vLGWTabItem.isCurrentOpen
                                          ? AppColorConstants.colorWhite
                                          : AppColorConstants.colorH3))),
                        ]
                      ],
                    ),
                  ),
                ),
              ),
            );
          })),
    );
  }
  Widget getVLGWTableView() {
    return Expanded(
      child: Container(
        decoration: vlgwPageHelper!.dataTableHelper.tableBorderDeco(),
        child: Column(
          children: [
            // Table
            Expanded(
              child: PaginatedDataTable2(
                rowsPerPage: AppStringConstants.vlgwPerPageLimit,
                initialFirstRowIndex: vlgwPageHelper!.paginationHelper.currentPage *
                    AppStringConstants.vlgwPerPageLimit,
                columnSpacing: 2,
                controller: vlgwPageHelper!.vlgwPageController,
                headingCheckboxTheme:
                    CheckboxThemeData(side: BorderSide(width: 2, color: AppColorConstants.colorWhite)),
                headingTextStyle: vlgwPageHelper!.dataTableHelper.headingTextStyle(),
                wrapInCard: false,
                datarowCheckboxTheme:
                    const CheckboxThemeData(side: BorderSide(width: 2, color: Colors.grey)),
                border: vlgwPageHelper!.dataTableHelper.tableBorder(),
                renderEmptyRowsInTheEnd: false,
                // ignore: deprecated_member_use
                headingRowColor: vlgwPageHelper!.dataTableHelper.headingRowColor(),
                source: vlgwPageHelper!.vLGWDataSource,
                minWidth: 1500,
                hidePaginator: true,
                columns: [
                  DataColumn2(
                    fixedWidth: 150,
                    label: AppText(S.of(context).gwEUI),
                  ),
                  DataColumn2(
                    fixedWidth: 180,
                    label: Center(child: AppText(S.of(context).lastSeen)),
                  ),
                  DataColumn2(
                    fixedWidth: 100,
                    label: Container(alignment: Alignment.center, child: AppText(S.of(context).status)),
                  ),
                  DataColumn2(
                    fixedWidth: getSize(100),
                    label: Center(
                      child: AppText(S.of(context).rpdIp),
                    ),
                  ),
                  DataColumn2(
                    fixedWidth: getSize(120),
                    size: ColumnSize.L,
                    label: Center(
                      child: AppText(S.of(context).siteID),
                    ),
                  ),
                  DataColumn2(
                    fixedWidth: getSize(80),
                    label: Center(
                      child: AppText("#${S.of(context).aMPS}"),
                    ),
                  ),
                  DataColumn2(
                    fixedWidth: getSize(120),
                    label: Center(
                      child: AppText(S.of(context).interface),
                    ),
                  ),
                  DataColumn2(
                    fixedWidth: getSize(120),
                    label: Center(
                      child: AppText(S.of(context).vlgwHostname),
                    ),
                  ),
                  DataColumn2(
                    fixedWidth: getSize(120),
                    label: Center(
                      child: AppText(S.of(context).ndrSessionId),
                    ),
                  ),
                  DataColumn2(
                    fixedWidth: getSize(120),
                    label: Center(
                      child: AppText(S.of(context).ndfSessionId),
                    ),
                  ),
                  DataColumn2(
                    label: Container(),
                  ),
                ],
                empty: vlgwPageHelper!.dataTableHelper.getEmptyTableContent(context),
              ),
            ),
            // Custom Pagination
            Container(
              padding: const EdgeInsets.only(left: 16, top: 15),
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppColorConstants.colorBackgroundDark,
                borderRadius: const BorderRadius.only(bottomRight: Radius.circular(5), bottomLeft: Radius.circular(5)),
              ),
              child:  customPaginationArrowView(),
            ),
          ],
        ),
      ),
    );
  }
  Widget customPaginationArrowView() {
    return AppPaginationWidget(
      apiStatus: vlgwPageHelper!.apiStatus,
      paginationHelper: vlgwPageHelper!.paginationHelper,
      onLoadNext: () async {
        await vlgwPageHelper!.loadNextLogs(context);
        vlgwController.update();
      },
      onLoadPrevious: () async {
        await vlgwPageHelper!.loadPreviousLogs(context);
        vlgwController.update();
      },
      onGoToFirstPage: () {
        vlgwPageHelper!.goToFirstPage();
      },
      onGoToLastPage: () {
        vlgwPageHelper!.goToLastPage();
      },
      itemsPerPage: AppStringConstants.vlgwPerPageLimit,
      onChanged: (value) {
        vlgwPageHelper!.onPageLimitChanged(int.parse(value));
      },
    );
  }

  Widget getMultipleVLGWTableView() {
    return Expanded(
      child: Container(
        decoration: vlgwPageHelper!.dataTableHelper.tableBorderDeco(),
        child: PaginatedDataTable2(
          dividerThickness: 0,
          columnSpacing: 0,
          horizontalMargin: 0,
          controller: vlgwPageHelper!.paginatorController,
          headingCheckboxTheme:
              CheckboxThemeData(side: BorderSide(width: 2, color: AppColorConstants.colorWhite)),
          headingTextStyle: vlgwPageHelper!.dataTableHelper.headingTextStyle(),
          wrapInCard: false,
          border: vlgwPageHelper!.dataTableHelper.tableBorder(),
          renderEmptyRowsInTheEnd: false,
          headingRowColor: vlgwPageHelper!.dataTableHelper.headingRowColor(),
          source: vlgwPageHelper!.vLGWDataSource,
          minWidth: 1400,
          columns: [
            DataColumn2(
              fixedWidth: 150,
              label: Padding(
                padding: const EdgeInsets.only(left: 20),
                child: AppText(S.of(context).gwEUI),
              ),
            ),
            DataColumn2(
              fixedWidth: 200,
              label: Padding(
                padding: const EdgeInsets.only(left: 8),
                child: AppText(S.of(context).lastSeen),
              ),
            ),
            DataColumn2(
              fixedWidth: 110,
              label: Padding(
                padding: const EdgeInsets.only(left: 20),
                child: AppText(S.of(context).status),
              ),
            ),
            DataColumn2(
              fixedWidth: getSize(120),
              size: ColumnSize.L,
              label: Container(
                alignment: Alignment.center,
                child: AppText(S.of(context).rpdIp),
              ),
            ),
            DataColumn2(
              fixedWidth: getSize(120),
              size: ColumnSize.L,
              label: Container(
                alignment: Alignment.center,
                child: AppText(S.of(context).siteID),
              ),
            ),
            DataColumn2(
              label: Container(
                alignment: Alignment.center,
                child: AppText("#${S.of(context).aMPS}"),
              ),
            ),
            DataColumn2(
              label: Container(
                alignment: Alignment.center,
                child: AppText(S.of(context).interface),
              ),
            ),
            DataColumn2(
              label: Container(
                alignment: Alignment.center,
                child: AppText(S.of(context).vlgwHostname),
              ),
            ),
            DataColumn2(
              label: Container(
                alignment: Alignment.center,
                child: AppText(S.of(context).ndrSessionId),
              ),
            ),
            DataColumn2(
              label: Container(
                alignment: Alignment.center,
                child: AppText(S.of(context).ndfSessionId),
              ),
            ),
            DataColumn2(
              fixedWidth: getSize(120),
              label: Container(
                alignment: Alignment.center,
                child: AppText(S.of(context).ndfMulticastIp),
              ),
            ),
          ],
          empty: vlgwPageHelper!.dataTableHelper.getEmptyTableContent(context),
          onPageChanged: (rowIndex) {
            vlgwPageHelper!.currentPageIndex = (rowIndex / 10).ceil();
            vlgwController.update();
          },
        ),
      ),
    );
  }


  @override
  void dispose() {
    super.dispose();
  }
}
