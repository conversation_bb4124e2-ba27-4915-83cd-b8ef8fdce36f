// ignore_for_file: deprecated_member_use
import 'package:intl/intl.dart';
import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/pages/vlgw/vlgw_page_helper.dart';
import 'package:quantumlink_node/utils/dialog_utils.dart';

class VLGWDataSource extends DataTableSource {
  VLGWs vlgWs;
  final Function(VLGW) onTap;
  final BuildContext context;
  final VLGWPageHelper vlgwPageHelper;
  List<bool> _hoveredIndices = [];
  VLGWDataSource(
      {required this.vlgWs,
      required this.vlgwPageHelper,
      required this.onTap,
      required this.context}){
    _hoveredIndices = List.generate(vlgWs.result.length, (_) => false);
  }

  DataTableHelper dataTableHelper = DataTableHelper();

  @override
  DataRow? getRow(int index) {
    if (index >= vlgWs.result.length) {
      return null;
    }
    return DataRow(
        cells: getCells(index),
        color: MaterialStateProperty.all(AppColorConstants.colorWhite));
  }

  List<DataCell> getCells(int index) {
    debugPrint("{${vlgWs.result[index].toJson()}");

    VLGW vlgw = vlgWs.result[index];
    String eui = vlgw.eui ?? '';
    String chirpStackServerHost = vlgw.chirpStackServerHost ?? '';
    String rpdIp = vlgw.rpdIp ?? '';
    String ampsCount = vlgw.ampsCount != null ? vlgWs.result[index].ampsCount.toString() : '';
    String siteName = vlgw.site?.name ?? '-';
    String ndrSessionId = vlgw.ndrSessionId ?? '';
    String ndfSessionId = vlgw.ndfSessionId ?? '';
    String ndfDestIp = vlgw.ndfDestIp ?? '';
    String lastSeen = vlgw.lastSeen != null
        ? DateFormat(lastSeenFormat).format(DateTime.parse(vlgw.lastSeen ?? ''))
        : '';
    String interface = vlgw.interface ?? '';
    String vlgwIp = vlgw.vlgwIp ?? "";

    DetectedStatusType? detectedStatusType =
        getDetectedStatusType(vlgWs.result[index].status);
    List<DataCell> cells = [
      DataCell(
          onTap: () => onTap(vlgWs.result[index]),
          MouseRegion(
            onEnter: (_) {
              _hoveredIndices[index] = true;
              notifyListeners();
            },
            onExit: (_) {
              _hoveredIndices[index] = false;
              notifyListeners();
            },
            child: AppText(
              isSelectableText: false,
              eui,
              style: TextStyle(
                  fontFamily: AppAssetsConstants.openSans,
                  fontSize: getSize(14),
                  fontWeight: getMediumBoldFontWeight(),
                  color: AppColorConstants.colorLightBlue,
                  decoration:
                      _hoveredIndices[index] ? TextDecoration.underline : TextDecoration.none,
                  decorationThickness: 4,
                  decorationColor: AppColorConstants.colorLightBlue),
            ),
          )),
      DataCell(Container(
          alignment: Alignment.center,
          decoration: _tableBorderDeco(),
          child: AppText(lastSeen ?? '',
              style: dataTableHelper.dataRowTextStyle))),
      DataCell(
        Container(
          padding: EdgeInsets.symmetric(
              horizontal: getSize(15), vertical: getSize(8)),
          height: double.infinity,
          width: double.infinity,
          child: Container(
            alignment: Alignment.center,
            height: getSize(30),
            width: getSize(70),
            decoration: BoxDecoration(
                color: detectedStatusType == DetectedStatusType.online
                    ? AppColorConstants.colorGreen2
                    : AppColorConstants.colorH2.withOpacity(0.4),
                borderRadius: BorderRadius.circular(getSize(18))),
            child: AppText(
              detectedStatusType == DetectedStatusType.online
                  ? S.of(context).online
                  : S.of(context).offline,
              style: TextStyle(
                  color: (detectedStatusType == DetectedStatusType.offline ||
                          vlgWs.result[index].status == null)
                      ? AppColorConstants.colorH3
                      : AppColorConstants.colorWhite,
                  fontFamily: AppAssetsConstants.sourceSans,
                  fontSize: 14,
                  fontWeight: FontWeight.w500),
            ),
          ),
        ),
      ),
      DataCell(Center(
        child: AppText(chirpStackServerHost,
            style: dataTableHelper.dataRowTextStyle),
      )),

      DataCell(Container(
          alignment: Alignment.center,
          decoration: _tableBorderDeco(),
          child: AppText(rpdIp,
              style: dataTableHelper.dataRowTextStyle))),
      DataCell(Container(
          alignment: Alignment.center,
          decoration: _tableBorderDeco(),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Flexible(
                  child: AppText(
                      siteName,
                      style: _textStyle)),
              InkWell(
                  onTap: () {
                    SiteDataModel siteModel =
                        (vlgWs.result[index].siteId != null)
                            ? vlgwPageHelper.listSiteSensorData.singleWhere(
                                (element) =>
                                    element.id == vlgWs.result[index].siteId)
                            : vlgwPageHelper.listSiteSensorData.first;

                    editSiteVLGW(context, vlgWs.result[index].eui!, siteModel,
                        vlgwPageHelper.listSiteSensorData,
                        (SiteDataModel? siteModel) {
                      if (siteModel != null) {
                        //update clicked
                        debugLogs(
                            "siteModel -----${siteModel.name} ----- SiteId -----${siteModel.id}");
                        vlgwPageHelper.updateVLGW(
                            context, vlgWs.result[index].eui!, siteModel.id);
                      }
                    });
                  },
                  child: const Icon(
                    Icons.edit,
                    size: 14,
                  ))
            ],
          ))),
      DataCell(Container(
          alignment: Alignment.center,
          decoration: _tableBorderDeco(),
          child: AppText(ampsCount, style: dataTableHelper.dataRowTextStyle))),
      DataCell(Container(
          alignment: Alignment.center,
          decoration: _tableBorderDeco(),
          child: AppText(interface,
              style: dataTableHelper.dataRowTextStyle))),
      DataCell(Container(
          alignment: Alignment.center,
          decoration: _tableBorderDeco(),
          child: AppText(vlgwIp, style: dataTableHelper.dataRowTextStyle))),
      DataCell(Container(
          alignment: Alignment.center,
          decoration: _tableBorderDeco(),
          child: AppText(ndrSessionId,
              style: dataTableHelper.dataRowTextStyle))),
      DataCell(Container(
          alignment: Alignment.center,
          decoration: _tableBorderDeco(),
          child: AppText(ndfSessionId,
              style: dataTableHelper.dataRowTextStyle))),
      DataCell(AppText(ndfDestIp,
          style: dataTableHelper.dataRowTextStyle)),
    ];
    return cells;
  }

  @override
  int get rowCount => vlgWs.result.length;

  @override
  bool get isRowCountApproximate => false;

  @override
  int get selectedRowCount => 0;

  static TextStyle get _textStyle => TextStyle(
        fontFamily: AppAssetsConstants.roboto,
        fontSize: getSize(14),
        fontWeight: getMediumFontWeight(),
      );

  static BoxDecoration _tableBorderDeco() {
    return BoxDecoration(
      color: AppColorConstants.colorWhite,
      border: Border(
          bottom: BorderSide(color: AppColorConstants.colorH2, width: 0.8)),
    );
  }

  editSiteVLGW(context, String gwEui, SiteDataModel siteModel,
      List<SiteDataModel> listTypeDDItem, Function function) {
    TextEditingController txtGWEui = TextEditingController();
    txtGWEui.text = gwEui;
    SiteDataModel siteItem = siteModel;

    DialogUtils().regularDialog(context, S.of(context).updateSiteVLGW,null,
        StatefulBuilder(builder: (context, snapshot) {
      return Padding(
        padding: EdgeInsets.only(
            left: 24,
            right: 24,
            bottom: MediaQuery.of(context).viewInsets.bottom),
        //padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 500,
              padding: const EdgeInsets.only(left: 16, right: 16, top: 16),
              child: Column(
                children: [
                  const SizedBox(height: 25),
                  dialogBoxEdit(
                      title: S.of(context).gwEUI,
                      hintText: S.of(context).gwEUI,
                      isReadOnly: true,
                      controller: txtGWEui),
                  const SizedBox(height: 25),
                  Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: AppText(S.of(context).selectSite,
                            style: TextStyle(
                                fontFamily: AppAssetsConstants.openSans,
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: AppColorConstants.colorBlack)),
                      ),
                      const SizedBox(
                        width: 20,
                      ),
                      Expanded(
                        flex: 5,
                        child: getAppDropDown(siteItem, listTypeDDItem,
                            (value) {
                          siteItem = listTypeDDItem
                              .singleWhere((element) => element.name == value);
                          snapshot(() {});
                        },
                            fontColor: AppColorConstants.colorBlack,
                            dropdownButtonColor: AppColorConstants.colorWhite,
                            dropdownColor: AppColorConstants.colorWhite,
                            borderRadius: 4),
                      )
                    ],
                  ),
                  const SizedBox(height: 25),
                ],
              ),
            ),
            const SizedBox(height: 24),
          ],
        ),
      );
    }), () {
      function.call(siteItem);
      goBack();
    });
  }
}
// --------------------->Multiple VLGW Details Model Datasource <----------------------------------------------
// class VLGWDataSource extends DataTableSource {
//   final List<DataRow> _rows;
//   final List<VLGW> vlgWs;
//   final Function(VLGW) onTap;
//   final BuildContext context;
//   VLGWDataSource(this. context, {required this.vlgWs, required this.onTap})
//       : _rows = _buildDataRows(vlgWs, onTap, context);
//
//   static List<DataRow> _buildDataRows(List<VLGW> vlgWs, Function(VLGW) onTap,BuildContext context) {
//     List<DataRow> rows = [];
//     for (var result in vlgWs) {
//       DetectedStatusType? detectedStatusType = getDetectedStatusType(result.status);
//       for (var i = 0; i < (result.deviceDetail?.length ?? 0); i++) {
//         var detailItem = result.deviceDetail![i];
//         rows.add(DataRow(cells: [
//           DataCell(
//             MouseRegion(
//               cursor: SystemMouseCursors.click,
//               child: GestureDetector(
//                 onTap: () => onTap(result),
//                 child: Container(
//                   alignment: Alignment.centerLeft,
//                   padding: const EdgeInsets.only(left: 8),
//                   height: double.infinity,
//                   width: double.infinity,
//                   decoration: BoxDecoration(
//                       border: Border(
//                           bottom: BorderSide(
//                               color: i == result.deviceDetail!.length - 1 &&
//                                       result.deviceDetail!.length > 1
//                                   ? AppColorConstants.colorH2
//                                   : result.deviceDetail!.length == 1
//                                       ? AppColorConstants.colorH2
//                                       : AppColorConstants.colorWhite,
//                               width: 0.8))),
//                   child: AppText(
//                     isSelectableText: false,
//                     i == 0 ? result.eui ?? '' : '',
//                     style: TextStyle(
//                         fontFamily: AppAssetsConstants.roboto,
//                         fontSize: getSize(14),
//                         fontWeight: getMediumFontWeight(),
//                         color: AppColorConstants.colorLightBlue),
//                   ),
//                 ),
//               ),
//             ),
//           ),
//           DataCell(
//             Container(
//               alignment: Alignment.centerLeft,
//               padding: const EdgeInsets.only(left: 8),
//               height: double.infinity,
//               width: double.infinity,
//               decoration: BoxDecoration(
//                   border: Border(
//                       bottom: BorderSide(
//                           color: i == result.deviceDetail!.length - 1 &&
//                                   result.deviceDetail!.length > 1
//                               ? AppColorConstants.colorH2
//                               : result.deviceDetail!.length == 1
//                                   ? AppColorConstants.colorH2
//                                   : AppColorConstants.colorWhite,
//                           width: 0.8))),
//               child: AppText(
//                 isSelectableText: false,
//                 i == 0 ? result.lastSeen ?? '' : '',
//                 style: _textStyle,
//               ),
//             ),
//           ),
//           DataCell(
//             Container(
//               padding: EdgeInsets.symmetric(horizontal: getSize(15), vertical: getSize(8)),
//               height: double.infinity,
//               width: double.infinity,
//               decoration: BoxDecoration(
//                 border: Border(
//                     bottom: BorderSide(
//                         color:
//                             i == result.deviceDetail!.length - 1 && result.deviceDetail!.length > 1
//                                 ? AppColorConstants.colorH2
//                                 : result.deviceDetail!.length == 1
//                                     ? AppColorConstants.colorH2
//                                     : AppColorConstants.colorWhite,
//                         width: 0.8)),
//               ),
//               child: i == 0
//                   ? Container(
//                       alignment: Alignment.center,
//                       height: getSize(30),
//                       width: getSize(70),
//                       decoration: BoxDecoration(
//                           color: detectedStatusType == DetectedStatusType.online
//                               ? AppColorConstants.colorGreen2
//                               : AppColorConstants.colorH2.withOpacity(0.4),
//                           borderRadius: BorderRadius.circular(getSize(18))),
//                       child: AppText(
//                         detectedStatusType == DetectedStatusType.online
//                             ? S.of(context).online
//                             : S.of(context).offline,
//                         style: TextStyle(
//                             color: (detectedStatusType == DetectedStatusType.offline ||
//                                     result.status == null)
//                                 ? AppColorConstants.colorH3
//                                 : AppColorConstants.colorWhite,
//                             fontFamily: AppAssetsConstants.sourceSans,
//                             fontSize: 14,
//                             fontWeight: FontWeight.w500),
//                       ),
//                     )
//                   : Container(),
//             ),
//           ),
//           DataCell(
//             Container(
//               alignment: Alignment.centerLeft,
//               padding: const EdgeInsets.only(left: 8),
//               height: double.infinity,
//               width: double.infinity,
//               decoration: BoxDecoration(
//                   border: Border(
//                       right: BorderSide(color: AppColorConstants.colorH2, width: 0.8),
//                       bottom: BorderSide(
//                           color: i == result.deviceDetail!.length - 1 &&
//                                   result.deviceDetail!.length > 1
//                               ? AppColorConstants.colorH2
//                               : result.deviceDetail!.length == 1
//                                   ? AppColorConstants.colorH2
//                                   : AppColorConstants.colorWhite,
//                           width: 0.8))),
//               child: AppText(
//                 isSelectableText: false,
//                 i == 0 ? result.chirpstackBridgeIp ?? '' : '',
//                 style: _textStyle,
//               ),
//             ),
//           ),
//           DataCell(Container(
//               alignment: Alignment.center,
//               decoration: _tableBorderDeco(),
//               child: AppText(detailItem.lat ?? '', style: _textStyle))),
//           DataCell(Container(
//               alignment: Alignment.center,
//               decoration: _tableBorderDeco(),
//               child: AppText('0.0', style: _textStyle))),
//           DataCell(Container(
//               alignment: Alignment.center,
//               decoration: _tableBorderDeco(),
//               child: AppText(detailItem.alt ?? '', style: _textStyle))),
//           DataCell(Container(
//               alignment: Alignment.center,
//               decoration: _tableBorderDeco(),
//               child: AppText(detailItem.ndfConfig?.interface ?? '', style: _textStyle))),
//           DataCell(Container(
//               alignment: Alignment.center,
//               decoration: _tableBorderDeco(),
//               child: AppText(detailItem.alt ?? '', style: _textStyle))),
//           DataCell(Container(
//               alignment: Alignment.center,
//               decoration: _tableBorderDeco(),
//               child: AppText(detailItem.ndrConfig?.sessionId ?? '', style: _textStyle))),
//           DataCell(Container(
//               alignment: Alignment.center,
//               decoration: _tableBorderDeco(),
//               child: AppText(detailItem.ndfConfig?.sessionId?? '', style: _textStyle))),
//           DataCell(Container(
//               alignment: Alignment.center,
//               decoration: _tableBorderDeco(),
//               child: AppText(detailItem.ndrConfig?.apiPort ?? '', style: _textStyle))),
//         ]));
//       }
//     }
//     return rows;
//   }
//
//   @override
//   DataRow? getRow(int index) {
//     return index < _rows.length ? _rows[index] : null;
//   }
//
//   @override
//   int get rowCount => _rows.length;
//
//   @override
//   bool get isRowCountApproximate => false;
//
//   @override
//   int get selectedRowCount => 0;
//
//   static TextStyle get _textStyle => TextStyle(
//     fontFamily: AppAssetsConstants.roboto,
//     fontSize: getSize(14),
//     fontWeight: getMediumFontWeight(),
//   );
//
//   static BoxDecoration _tableBorderDeco() {
//     return BoxDecoration(
//       color: AppColorConstants.colorWhite,
//       border: Border(
//           bottom: BorderSide(color: AppColorConstants.colorH2, width: 0.8)),
//     );
//   }
// }
