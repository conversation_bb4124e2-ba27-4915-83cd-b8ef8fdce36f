import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/controller/vlgw_controller.dart';

class VlGWTopologyView extends StatefulWidget {
  const VlGWTopologyView({super.key});

  @override
  State<VlGWTopologyView> createState() => _VlGWTopologyViewState();
}

class _VlGWTopologyViewState extends State<VlGWTopologyView> with TickerProviderStateMixin {
  late ScreenLayoutType screenLayoutType;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<VLGWController>(
      init: VLGWController(),
      builder: (VLGWController controller) {
        return ScreenLayoutTypeBuilder(builder: (context, screenType, constraints) {
          screenLayoutType = screenType;
          return SingleChildScrollView(
            padding: EdgeInsets.only(bottom: getSize(40)),
            child: Center(
                child: Padding(
              padding: const EdgeInsets.only(top: 50),
              child: AppText("We're working on topology screen...",
                  style: TextStyle(
                      color: AppColorConstants.colorH2,
                      fontSize: 14,
                      fontFamily: AppAssetsConstants.openSans,
                      fontWeight: FontWeight.w600)),
            )),
          );
        });
      },
    );
  }
}
