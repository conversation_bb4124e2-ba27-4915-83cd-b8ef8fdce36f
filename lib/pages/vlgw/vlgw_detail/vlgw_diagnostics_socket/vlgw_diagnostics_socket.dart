import 'package:lottie/lottie.dart';
import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/controller/vlgw_controller.dart';
import 'package:quantumlink_node/pages/vlgw/vlgw_detail/vlgw_diagnostics_socket/vlgw_diagnostics_socket_datasource.dart';
import 'package:rxdart/rxdart.dart';

class VLGwDiagnosticsSocketPage extends StatefulWidget {
  final VLGW vLGWItem;
  const VLGwDiagnosticsSocketPage({super.key, required this.vLGWItem});

  @override
  State<VLGwDiagnosticsSocketPage> createState() => VLGwDiagnosticsSocketPageState();
}

class VLGwDiagnosticsSocketPageState extends State<VLGwDiagnosticsSocketPage> {

  VLGWController vlgwController = VLGWController();
  RxList<String> selectedListDeviceEUI = <String>[].obs;
  late ScreenLayoutType screenLayoutType;
  final double heightOfDataTableCell = 48;
  int recordsInPage = 0; // For Set Fixed and Static Height Of Table
  int currentPageIndex = 0;
  int perPageLimit=10;
  PaginatorController pageController = PaginatorController();
  SocketHelper socketHelper = SocketHelper();
  BehaviorSubject<bool> isStreamLoading = BehaviorSubject();
  DataTableHelper dataTableHelper = DataTableHelper();
  double tableHeight = 0;
  bool _isTimeout = false;
  Timer? _timeoutTimer;
  List<DiagnosticsDataModel> listDiagnosticsData = [];
  late VlgwDiagnosticsDataSource vlgwDiagnosticsDataSource;
  DateTime ?lastUpdateTime;

  @override
  void initState() {
    super.initState();
    socketHelper.connectDiagnosticsSocket();
    getSocketData([widget.vLGWItem.eui ?? ""]);
    _startTimeoutWatcher();
  }

  // Start Changing From Hear
  getSocketData(List<String> selectedListDeviceEUI, {bool isDeviceEuiSend = false}) async {
    listDiagnosticsData.clear();
    debugLogs("search text value  -> $selectedListDeviceEUI");
    await socketHelper.sendDiagnosticsDeviceEUIList(isDeviceEui: isDeviceEuiSend, selectedListDeviceEUI, (message) {
      handleWebSocketMessage(message);
    });
    lastUpdateTime=DateTime.now();
    vlgwController.update();
  }
  void _startTimeoutWatcher() {
    _timeoutTimer?.cancel();
    _timeoutTimer = Timer(const Duration(seconds: 10), () {
        _isTimeout = true;
        vlgwController.update();
    });
  }

  handleWebSocketMessage(String message) {
    final data = jsonDecode(message) as Map<String, dynamic>;

    DiagnosticsDataModel diagnosticsDataItem =
    DiagnosticsDataModel.fromJson(data);
    debugLogs("VLGW DiagnosticsSocket Msg ->${diagnosticsDataItem.properties?.gatewayID ?? ""}");
    bool isUnique =
    listDiagnosticsData.every((item) => item.id != diagnosticsDataItem.id);
    if (isUnique) {
      listDiagnosticsData.add(diagnosticsDataItem);
      listDiagnosticsData.sort((a, b) => a.time.compareTo(b.time));
      vlgwDiagnosticsDataSource = VlgwDiagnosticsDataSource(
          context, listDiagnosticsData.reversed.toList(), (dataMap) {
        jsonTreeDataHandling(dataMap);
      });
      handleTableHeightChange();
    }
  }

  jsonTreeDataHandling(DiagnosticsDataModel dataMap) {
    String newJsonData = jsonEncode(dataMap);
    Map<String, dynamic> jsonData = jsonDecode(newJsonData);
    HomeController homeController = Get.put(HomeController());
    homeController.updateJsonData(jsonData);
    scaffoldKey.currentState?.openEndDrawer();
  }

  handleTableHeightChange() {
    int itemsPerPage = dataTableHelper.getCurrentPageDataLength(
        listDiagnosticsData, currentPageIndex);
    int recordsInPage = (listDiagnosticsData.length > 10)
        ? itemsPerPage
        : listDiagnosticsData.length;
    double height = (listDiagnosticsData.isNotEmpty)
        ? (recordsInPage * heightOfDataTableCell) + (recordsInPage * 0.1) + 190
        : (recordsInPage * heightOfDataTableCell) + (recordsInPage * 0.1) + 300;

    tableHeight = height;
  }

  @override
  void dispose() {
    socketHelper.diagnosticsOnDispose();
    _timeoutTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<VLGWController>(
      init: VLGWController(),
      builder: (VLGWController controller) {
        vlgwController = controller;
        return getBodyView();
      },
    );
  }

  getBodyView() {
    return ScreenLayoutTypeBuilder(
      builder: (context, screenType, constraints) {
        screenLayoutType = screenType;
        return getDiagnosticsBoardView();
      },
    );
  }

  getDiagnosticsBoardView() {
    return Container(
      color: AppColorConstants.colorWhite,
      width: double.infinity,
      child: ListView(padding: const EdgeInsets.only(top: 15),
        shrinkWrap: true,
        physics: const ClampingScrollPhysics(),
        children: [
          getPageAppBar(),
          diagnosticsBoardView(),
          lastUpdateTimeWithRefreshView()
        ],
      ),
    );
  }

  getPageAppBar() {
    return Container(
      // margin: const EdgeInsets.symmetric(horizontal: 5),
      alignment: Alignment.centerLeft,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.only(topRight: Radius.circular(getSize(7)), topLeft: Radius.circular(getSize(7))),
          border: Border.all(
            color: AppColorConstants.colorH2,
          ),
          color: AppColorConstants.colorWhite),
      padding: EdgeInsets.only(left: getSize(18)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: getSize(15)),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Obx(
                () {
                  return SizedBox(
                    height: 40,
                    child: ListView.builder(
                      itemCount: selectedListDeviceEUI.length,
                      scrollDirection: Axis.horizontal,
                      shrinkWrap: true,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 4.0),
                          child: Chip(
                            backgroundColor: Colors.white,
                            label: Text(selectedListDeviceEUI[index]),
                            deleteIcon: CircleAvatar(maxRadius: 10, backgroundColor: AppColorConstants.colorLightBlue, child: Icon(Icons.close, size: getSize(16), color: AppColorConstants.colorWhite)),
                            onDeleted: () {
                              pageController.goToFirstPage();
                              selectDevEUI(selectedListDeviceEUI[index], false);
                            },
                          ),
                        );
                      },
                    ),
                  );
                },
              ),
              if (screenLayoutType == ScreenLayoutType.desktop)
              SizedBox(width: MediaQuery.of(context).size.width * 0.3, child: searchAutoTextView())
            ],
          ),
          if (screenLayoutType != ScreenLayoutType.desktop) searchAutoTextView(),
          SizedBox(height: getSize(8)),
        ],
      ),
    );
  }

  searchAutoTextView() {
    TextEditingController textEditingController = TextEditingController();
    return Container(
      margin: EdgeInsets.only(right: getSize(13)),
      height: 40,
      child: AppTextFormField(
        controller: textEditingController,
        onFieldSubmitted: (value) {
          debugLogs("search text value :- $value");
          if (value.isEmpty) return;
          selectDevEUI(value, true);
          pageController.goToFirstPage();
          textEditingController.clear();
        },
        hintText: S.of(context).searchByDeviceEUI,
        contentPadding: const EdgeInsets.symmetric(vertical: 0, horizontal: 20),
        focusedBorderColor: AppColorConstants.colorPrimary,
        enabledBorderColor: AppColorConstants.colorBlackBlue,
        maxLines: 1,
        textInputType: TextInputType.text,
        borderRadius: getSize(8),
        hintTextColor: AppColorConstants.colorDarkBlue,
        suffixIcon: const Padding(
          padding: EdgeInsets.all(10),
          child: AppImageAsset(image: AppAssetsConstants.searchIcon),
        ),
        hintFontSize: 17,
        fontFamily: AppAssetsConstants.openSans,
        fontWeight: FontWeight.w400,
      ),
    );
  }

  selectDevEUI(String devEUI, bool isAdd) {
    if (isAdd) {
      if (!selectedListDeviceEUI.contains(devEUI)) {
        selectedListDeviceEUI.add(devEUI);
      }
    } else {
      selectedListDeviceEUI.remove(devEUI);
    }
    if (selectedListDeviceEUI.isEmpty) {
      getSocketData([widget.vLGWItem.eui ?? ""]);
    } else {
      getSocketData(selectedListDeviceEUI, isDeviceEuiSend: true);
    }
  }

  lastUpdateTimeWithRefreshView(){
    return  Row(mainAxisAlignment: MainAxisAlignment.end,
      children: [
        getTimeDurationView(
          onTapTime: DateTime.now(),
          refreshStatus: ApiStatus.success,
          updateTime: lastUpdateTime,),
        AppRefresh(onPressed: () {
          _isTimeout=false;
          _startTimeoutWatcher();
          getSocketData([widget.vLGWItem.eui ?? ""]);
        },loadingStatus:  ApiStatus.success),
      ],
    );
  }
  diagnosticsBoardView() {
    return StreamBuilder<dynamic>(
      stream: socketHelper.diagnosticsStreamView,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting && !_isTimeout) {
          return _buildLoading();
        }else if (snapshot.connectionState == ConnectionState.waiting && _isTimeout) {
          return _buildTimeoutMessage();
        }
        else if (snapshot.connectionState == ConnectionState.active) {
          if (snapshot.hasData) {
            isStreamLoading.add(true);
          } else {
            isStreamLoading.add(false);
          }
        }
        return Container(
            height: tableHeight,
            width: double.infinity,
            decoration: BoxDecoration(
                border: Border(
                    bottom: BorderSide(color: AppColorConstants.colorH2, width: 1),
                    left: BorderSide(color: AppColorConstants.colorH2, width: 1),
                    right: BorderSide(color: AppColorConstants.colorH2, width: 1)),
                // borderRadius: const BorderRadius.only(
                //     topLeft: Radius.circular(8), topRight: Radius.circular(8)),
                color: AppColorConstants.colorWhite),
            child: buildVlgwDiagnosticsDataTableView());
      },
    );
  }
  Widget _buildTimeoutMessage() {
    return ClipRRect(
      child: Container(
        alignment: Alignment.center,
        height: 400,
        decoration: BoxDecoration(
          color:  AppColorConstants.colorWhite,
            border: Border.all(color: AppColorConstants.colorH2)
        ),
        width: double.infinity,
        child:  Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const AppImageAsset(
              image: AppAssetsConstants.emptyLogo,
              height: 50,
            ),
            AppText(
              S.of(context).socketTimeOutMessage,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 16, color: Colors.black87),
            ),
          ],
        ),
      ),
    );
  }
  Widget _buildLoading() {
    return ClipRRect(
      child: Container(
          alignment: Alignment.center,
          height: 400,
          decoration: BoxDecoration(
            color: AppColorConstants.colorWhite,
            border: Border.all(color: AppColorConstants.colorH2, width: 1),
          ),
          width: double.infinity,
          child: const AppLoader()),
    );
  }

  _socketLoader(){
    if(isStreamLoading.value){
      return Container(alignment: Alignment.centerRight,
          width: double.infinity,
          height: 50,
          child: Lottie.asset(
            AppAssetsConstants.loaderAnimation,
            delegates: LottieDelegates(
              values: [
                ValueDelegate.color(
                  ['**'],
                  value: Colors.white, // Replace with your desired color
                ),
              ],
            ),
          ));
    }else{
      return SizedBox();
    }

  }

  buildVlgwDiagnosticsDataTableView() {
    return PaginatedDataTable2(
      isVerticalScrollBarVisible: false,
      columnSpacing: 8,
      showCheckboxColumn: false,
      headingTextStyle:
          dataTableHelper.headingTextStyle(),
      wrapInCard: false,
      renderEmptyRowsInTheEnd: false,
      dividerThickness: 0.3,
      //ignore: deprecated_member_use
      headingRowColor: dataTableHelper.headingRowColor(),
      columns: [
        DataColumn2(
          fixedWidth: 180,
          label: AppText(S.of(context).timestamp),
        ),
        DataColumn2(
          fixedWidth: 250,
          label: AppText(S.of(context).description),
        ),
        DataColumn2(
          fixedWidth: 150,
          label: AppText(S.of(context).devAddress),
        ),
        DataColumn2(
          fixedWidth: 170,
          label: AppText(S.of(context).devEUI),
        ),
        DataColumn2(
          fixedWidth: 200,
          label: AppText(S.of(context).gatWayId),
        ),
        DataColumn2(fixedWidth: 100,
          label: AppText(S.of(context).rssi+"\n(${S.of(context).dBm})"),
        ),
        DataColumn2(fixedWidth: 100,
          label: AppText(S.of(context).fPort),
        ),
        DataColumn2(fixedWidth: 150,
          label: AppText(S.of(context).frequency),
        ),
         DataColumn2(
          label: _socketLoader(),
        ),
      ],
      controller: pageController,
      source: vlgwDiagnosticsDataSource,
      minWidth: 1400,
      dataRowHeight: 55,
      // For progress indicator
      hidePaginator: false,
      empty:
          dataTableHelper.getEmptyTableContent(context),
    );
  }
}
