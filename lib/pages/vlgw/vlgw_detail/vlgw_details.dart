import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/controller/vlgw_controller.dart';
import 'package:quantumlink_node/pages/vlgw/vlgw_detail/vlgw_diagnostics_socket/vlgw_diagnostics_socket.dart';
import 'vlgw_amplifier_page/vlgw_amplifier_page.dart';
import 'vlgw_info/vlgw_info_view.dart';
import 'vlgw_topology/vlgw_topology_view.dart';

class VlGWDetail extends StatefulWidget {
  final VLGW vLGWItem;

  const VlGWDetail({
    super.key,
    required this.vLGWItem,
  });

  @override
  State<VlGWDetail> createState() => _VlGWDetailState();
}

class _VlGWDetailState extends State<VlGWDetail> with TickerProviderStateMixin {
  int currentSubTab = 0;
  String previousDeviceEui = "";
  List<String> subTabList = [];
  late TabController tabController;
  late ScreenLayoutType screenLayoutType;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    tabController = TabController(
      initialIndex: 0,
      length: 2,
      vsync: this,
      animationDuration: Duration.zero
    );
  }

  @override
  Widget build(BuildContext context) {
    if (subTabList.isEmpty) {
      subTabList = [
        S.of(context).info,
        S.of(context).diagnostics,
      ];
    }
    return GetBuilder<VLGWController>(
      init: VLGWController(),
      builder: (VLGWController controller) {
        return ScreenLayoutTypeBuilder(builder: (context, screenType, constraints) {
          screenLayoutType = screenType;
          return Container(
            padding: EdgeInsets.only(
                left: getSize(16), right: getSize(16), top: getSize(20), bottom: getSize(40)),
            decoration: BoxDecoration(
              color: AppColorConstants.colorWhite,
              borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(getSize(7)),
                  bottomRight: Radius.circular(getSize(7))),
            ),
            child: Column(
              children: [
                  Divider(
                    height: getSize(1),
                    color: AppColorConstants.colorDotLine,
                  ),
                  Row(
                    children: [
                      Flexible(
                        child: TabBar(
                            controller: tabController,
                            dividerColor: AppColorConstants.colorWhite,
                            labelPadding: EdgeInsets.only(left: getSize(5)),
                            labelColor: Colors.white,
                            padding: EdgeInsets.zero,
                            isScrollable: true,
                            indicatorColor: AppColorConstants.colorLightBlue,
                            tabAlignment: TabAlignment.start,
                            onTap: (value) {
                              onTapChangeSubTab(value, controller);
                              controller.update();
                            },
                            tabs: List.generate(subTabList.length, (index) {
                              String ampTabItem = subTabList[index];
                              return Tab(
                                child: Padding(
                                  padding: EdgeInsets.symmetric(horizontal: getSize(30)),
                                  child: AppText(
                                    isSelectableText: false,
                                    ampTabItem,
                                    style: TextStyle(
                                      fontFamily: AppAssetsConstants.openSans,
                                      fontWeight: currentSubTab == index
                                          ? getMediumBoldFontWeight()
                                          : getMediumFontWeight(),
                                      fontSize: 15,
                                      color: currentSubTab == index
                                          ? AppColorConstants.colorLightBlue
                                          : AppColorConstants.colorBlack,
                                    ),
                                  ),
                                ),
                              );
                            })),
                      ),
                    ],
                  ),
                  Divider(
                    height: getSize(1),
                    color: AppColorConstants.colorDotLine,
                  ),
                  if (subTabList[currentSubTab] == S.of(context).info) VlGWInfoView(vLGWItem: widget.vLGWItem),
                  //if (subTabList[currentSubTab] == S.of(context).topology) VlgwAmplifierPage(vLGWItem: widget.vLGWItem),
                  if (subTabList[currentSubTab] == S.of(context).diagnostics) VLGwDiagnosticsSocketPage(vLGWItem: widget.vLGWItem),
                ],
            )
          );
        });
      },
    );
  }

  getRefreshButtonView(VLGWController controller) {
    return AppButton(
        onPressed: () {},
        buttonName: S.of(context).refresh,
        buttonHeight: getSize(30),
        buttonWidth: getSize(90),
        borderColor: AppColorConstants.colorPrimary,
        buttonColor: AppColorConstants.colorPrimary,
        fontSize: getSize(13.5));
  }

  onTapChangeSubTab(
    int index,
    VLGWController controller,
  ) {
    currentSubTab = index;
    if (currentSubTab == 1) {
    } else if (currentSubTab == 2) {}
  }
}
