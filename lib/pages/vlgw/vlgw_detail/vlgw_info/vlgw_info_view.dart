import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/controller/vlgw_controller.dart';
import 'package:quantumlink_node/pages/vlgw/vlgw_page_helper.dart';

BoxDecoration infoViewDecoration = BoxDecoration(
  color: AppColorConstants.colorWhite,
  border: Border.all(color: AppColorConstants.colorDotLine, width: 1.5),
  borderRadius: BorderRadius.circular(
    getSize(10),
  ),
);

class VlGWInfoView extends StatefulWidget {
  final VLGW vLGWItem;

  const VlGWInfoView({
    super.key,
    required this.vLGWItem,
  });

  @override
  State<VlGWInfoView> createState() => _VlGWInfoViewState();
}

class _VlGWInfoViewState extends State<VlGWInfoView>
    with TickerProviderStateMixin {
  late ScreenLayoutType screenLayoutType;
  late VLGWPageHelper vlgwPageHelper = VLGWPageHelper.infoPage();
  Timer? _timer;
  DataTableHelper dataTableHelper = DataTableHelper();

  @override
  void initState() {
    vlgwPageHelper.vLGWItem = widget.vLGWItem;
    vlgwPageHelper.lastUpdatedTimeVLGWFSKStats = DateTime.now();
    vlgwPageHelper.apiStatusVLGWFSKStats = ApiStatus.loading;
    vlgwPageHelper.getConfigsAndFSKStats(context,widget.vLGWItem);
    vlgwPageHelper.lastUpdatedTimeVLGWInfo = DateTime.now();
    _startTimer();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<VLGWController>(
      init: VLGWController(),
      builder: (VLGWController controller) {
        return ScreenLayoutTypeBuilder(
            builder: (context, screenType, constraints) {
          screenLayoutType = screenType;
          return MergeSemantics(
            child: Stack(
              children: [
                ListView(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  children: [
                    SizedBox(height: 20),
                    responsiveBoxView(
                        Column(
                          children: [
                            getVLGWInfo(
                                title: S.of(context).vLGWInfo, vlgw: vlgwPageHelper.vLGWItem),
                            getNDRBox(
                                title: S.of(context).nDRSession,
                                ndrConfig: vlgwPageHelper.ndrConfig),
                            getNDFBox(
                                title: S.of(context).ndfSession,
                                ndfConfig: vlgwPageHelper.ndfConfig),
                          ],
                        ),
                        Column(
                          children: [
                            getVLGWFSKStats(
                                title: S.of(context).vlgwFSKStats,
                                vlgwFSKStats: vlgwPageHelper.vlgwfskStats),
                            getConfigChannels(
                                title: S.of(context).configChannels,
                                configChannels: vlgwPageHelper.configChannels ?? [])
                          ],
                        )),
                  ],
                ),
                if (vlgwPageHelper.apiStatusVLGWFSKStats == ApiStatus.loading)
                  Padding(
                    padding: EdgeInsets.only(top: getSize(20)),
                    child: Container(
                      height: 350,
                      child: const AppLoader(),
                    ),
                  )
              ],
            ),
          );
        });
      },
    );
  }

  Widget responsiveBoxView(Column widget1, Widget widget2) {
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Flexible(flex: 1, child: widget1),
            if (screenLayoutType == ScreenLayoutType.desktop) ...[
              SizedBox(width: getSize(20)),
              Flexible(flex: 1, child: widget2),
            ]
          ],
        ),
        if (screenLayoutType != ScreenLayoutType.desktop)
          widget2
      ],
    );
  }

  Widget getResetButtonView() {
    return Padding(
      padding: EdgeInsets.only(right: getSize(20),bottom: 5),
      child: AppButton(
        buttonHeight: 35,
        fontSize: 14.5,
        buttonRadius: 9,
        buttonName: "${S.of(context).reset}",
        onPressed: () async {
          vlgwPageHelper.resetVLGWFSKStats(context, widget.vLGWItem);
        },
        fontFamily: AppAssetsConstants.openSans,
      ),
    );
  }

  Widget getVLGWFSKStats(
      {required String title, required VLGWFSKStats vlgwFSKStats}) {
    return Padding(
      padding: EdgeInsets.only(top: getSize(20)),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: getSize(20)).copyWith(bottom: getSize(5)),
        decoration: infoViewDecoration,
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.only(left: getSize(25)),
                    child: AppText(
                      title,
                      style: TextStyle(
                          fontFamily: AppAssetsConstants.openSans,
                          fontWeight: getMediumBoldFontWeight(),
                          letterSpacing: 0.32,
                          color: AppColorConstants.colorLightBlue,
                          fontSize: getSize(18)),
                    ),
                  ),
                ),
                getResetButtonView(),
              ],
            ),
            commonSubTitleView(
                subTitle: S.of(context).demodGoodPkts,
                value: "${vlgwFSKStats.demodGoodPkts ?? ""}"),
            commonSubTitleView(
                subTitle: S.of(context).demodBadPkts,
                value: "${vlgwFSKStats.demodBadPkts ?? ""}"),
            commonSubTitleView(
                subTitle: S.of(context).demodDroppedPkts,
                value: vlgwFSKStats.demodDroppedPkts ?? ""),
            commonSubTitleView(
                subTitle: S.of(context).demodNotFoundPkts,
                value: "${vlgwFSKStats.demodNotFoundPkts ?? ""}"),
            commonSubTitleView(
                subTitle: S.of(context).demodDiagnostic,
                value: "${vlgwFSKStats.demodDiagnostic ?? ""}"),
            commonSubTitleView(
                subTitle: S.of(context).demoInputLevel,
                value: "${vlgwFSKStats.demodInputLevel ?? ""}"),
            commonSubTitleView(
                subTitle: S.of(context).modDiagnostic,
                value: "${vlgwFSKStats.modDiagnostic ?? ""}"),
            commonSubTitleView(
                subTitle: S.of(context).modOutputLevel,
                value: "${vlgwFSKStats.modOutputLevel ?? ""}"),
            commonSubTitleView(
                subTitle: S.of(context).modPkts,
                value: "${vlgwFSKStats.modPkts ?? ""}"),
            commonSubTitleView(
                subTitle: S.of(context).badChannels,
                value: "${vlgwFSKStats.badChannels ?? ""}"),
            commonSubTitleView(
                subTitle: S.of(context).framesDropped,
                value: "${vlgwFSKStats.framesDropped ?? ""}"),
            Align(
                alignment: Alignment.centerRight,
                child: vlgwPageHelper.vlgwfskStatsError.isEmpty
                    ? const SizedBox(height: 37)
                    : errorMessageView(errorMessage: vlgwPageHelper.vlgwfskStatsError,padding: 3))
          ],
        ),
      ),
    );
  }

  Widget getConfigChannels(
      {required String title, required List<Channel> configChannels}) {

    TextStyle headingTextStyle() => TextStyle(
        color: AppColorConstants.colorWhite,
        fontWeight: FontWeight.w600,
        fontFamily: AppAssetsConstants.openSans,
        fontSize: 15);
    TextStyle  dataRowTextStyle(bool isEnabled) => TextStyle(
        fontFamily: AppAssetsConstants.openSans,
        fontWeight: FontWeight.w600,
        color: isEnabled ? AppColorConstants.colorBlackBlue : AppColorConstants.colorH2,
        fontSize: getSize(14));
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.only(top: getSize(20)),
          child: Container(
            padding: EdgeInsets.only(bottom: getSize(5)),
            decoration: infoViewDecoration,
            child: Column(
              children: [
                commonTitleView(title: title),
                SizedBox(height: 5),
                if (configChannels.isEmpty)
                  SizedBox(height: 200,child: dataTableHelper.getEmptyTableContent(context))
                else
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Container(
                      constraints:
                      BoxConstraints(minWidth: MediaQuery.of(context).size.width/2.6),
                      child: DataTable(decoration: BoxDecoration(
                        border: Border.all(color: AppColorConstants.colorDotLine, width: 1.5)
                      ) ,
                        headingTextStyle: headingTextStyle(),

                        columnSpacing: 30,
                        border: TableBorder(
                            bottom: BorderSide(color: AppColorConstants.colorDotLine, width: 1.5)),
                        headingRowHeight: 50,
                        headingRowColor: MaterialStateProperty.all(AppColorConstants.colorPrimary),
                        columns: [
                          DataColumn(
                              label: Container(alignment: Alignment.center,
                            child: AppText(S.of(context).channel),
                          )),
                          DataColumn(
                              label: Center(child: AppText(S.of(context).upstream+" (MHz)"))),
                          DataColumn(
                              label: Center(child: AppText(S.of(context).downstream+" (MHz)"))),
                          DataColumn(
                              label: Center(child: AppText(S.of(context).state))),
                        ],
                        rows: configChannels.map((channel) {
                           double us = channel.us / 1000000;
                           double ds = channel.ds / 1000000;
                           String state = channel.state ?? "" ;
                           bool isEnabled = state.toLowerCase().contains("enabled");

                          return DataRow(
                              color:isEnabled
                                  ? MaterialStateProperty.all(AppColorConstants.colorWhite)
                                  : MaterialStateProperty.all(AppColorConstants.colorDotLine.withOpacity(0.7)),
                              cells: [
                                DataCell(AppText(channel.ch.toString(), style: dataRowTextStyle(isEnabled))),
                            DataCell(AppText(us.toStringAsFixed(2), style: dataRowTextStyle(isEnabled))),
                            DataCell(AppText(ds.toStringAsFixed(2), style: dataRowTextStyle(isEnabled))),
                                DataCell(Container(
                                  decoration: BoxDecoration(
                                      border: Border.all(
                                          color: isEnabled
                                              ? AppColorConstants.colorGreen3.withOpacity(0.5)
                                              : AppColorConstants.colorBlack12,
                                          width: 1.5),
                                      borderRadius: BorderRadius.circular(5)),
                                  padding: const EdgeInsets.symmetric(horizontal: 4,vertical: 1),

                                  child: AppText(
                                      state
                                          .replaceFirst("ChannelState.ENABLED",S.of(context).enabled)
                                          .replaceFirst("ChannelState.DISABLED", S.of(context).disabled),
                                      style: TextStyle(
                                          fontFamily: AppAssetsConstants.openSans,
                                          fontWeight: FontWeight.w600,
                                          color: isEnabled? AppColorConstants.colorGreen2 : AppColorConstants.colorH2,
                                          fontSize: getSize(13))),
                                )),
                              ]);
                        }).toList(),
                      ),
                    ),
                  ),
                Align(
                    alignment: Alignment.centerRight,
                    child: vlgwPageHelper.configError.isEmpty
                        ? const SizedBox(height: 37)
                        : errorMessageView(errorMessage: vlgwPageHelper.configError,padding: 5))
              ],
            ),
          ),
        ),
        refreshFooterVLGWFSKStats()
      ],
    );
  }

  Widget getVLGWInfo({required String title, required VLGW vlgw}) {
    return Padding(
      padding: EdgeInsets.only(top: getSize(20)),
      child: Container(
        padding: EdgeInsets.only(bottom: getSize(20)),
        decoration: infoViewDecoration,
        child: Column(
          children: [
            commonTitleView(title: title),
            commonSubTitleView(
                subTitle: S.of(context).ipAddress, value: vlgw.vlgwIp ?? ""),
            commonSubTitleView(
                subTitle: S.of(context).port, value: "${vlgw.vlgwPort ?? ""}"),
            commonSubTitleView(
                subTitle: S.of(context).networkServerIP,
                value: "${vlgw.chirpStackServerHost ?? ""}"),
            commonSubTitleView(
                subTitle: S.of(context).networkServerPort,
                value: "${vlgw.chirpStackServerPort ?? ""}"),
          ],
        ),
      ),
    );
  }

  Widget getNDRBox({required String title, required SessionConfig ndrConfig}) {
    return Padding(
      padding: EdgeInsets.only(top: getSize(20),bottom: getSize(5)),
      child: Container(
        decoration: infoViewDecoration,
        child: Column(
          children: [
            commonTitleView(title: title),
            commonSubTitleView(
                subTitle: S.of(context).interface,
                value:ndrConfig.interface ?? ""),
            commonSubTitleView(
                subTitle: S.of(context).sourceIP,
                value: ndrConfig.src ?? ""),
            commonSubTitleView(
                subTitle: S.of(context).destinationIP,
                value: ndrConfig.dst ?? ""),
            commonSubTitleView(
                subTitle: S.of(context).sessionID,
                value:ndrConfig.sessionId  ?? ""),
            commonSubTitleView(
                subTitle: S.of(context).version,
                value: vlgwPageHelper.version.ndrVersion  ?? ""),
            commonSubTitleView(
                subTitle: S.of(context).description,
                value: ndrConfig.activeConfig?.description ?? "",
                isExpandValue:  ndrConfig.activeConfig?.description != null ? true : false),
            Align(
              alignment: Alignment.centerRight,
              child: (vlgwPageHelper.configError.isNotEmpty ||
                      vlgwPageHelper.versionError.isNotEmpty)
                  ? errorMessageView(errorMessage: S.of(context).socketExceptionMessage, padding: 5)
                  : const SizedBox(height: 37),
            )
          ],
        ),
      ),
    );
  }

  Widget getNDFBox({required String title, required SessionConfig ndfConfig}) {
    return Padding(
      padding: EdgeInsets.only(top: getSize(20),bottom: getSize(5)),
      child: Container(
        decoration: infoViewDecoration,
        child: Column(
          children: [
            commonTitleView(title: title),
            commonSubTitleView(subTitle: S.of(context).interface, value: ndfConfig.interface ?? ""),
            commonSubTitleView(subTitle: S.of(context).sourceIP, value: ndfConfig.src ?? ""),
            commonSubTitleView(subTitle: S.of(context).destinationIP, value: ndfConfig.dst ?? ""),
            commonSubTitleView(subTitle: S.of(context).sessionID, value: ndfConfig.sessionId ?? ""),
            commonSubTitleView(
                subTitle: S.of(context).version,
                value: vlgwPageHelper.version.ndfVersion  ?? ""),
            commonSubTitleView(
                subTitle: S.of(context).description,
                value: ndfConfig.activeConfig?.description ?? "",
                isExpandValue: ndfConfig.activeConfig?.description != null ? true : false),
            Align(
              alignment: Alignment.centerRight,
              child: (vlgwPageHelper.configError.isNotEmpty ||
                      vlgwPageHelper.versionError.isNotEmpty)
                  ? errorMessageView(errorMessage: S.of(context).socketExceptionMessage, padding: 5)
                  : const SizedBox(height: 37),
            )
          ],
        ),
      ),
    );
  }

  refreshFooterVLGWFSKStats() {
    return Padding(
      padding: EdgeInsets.only(
        right: getSize(5),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(
            child: buildLastSeenView(
                isShow: vlgwPageHelper.isShowTextOfVFSKStats,
                difference: vlgwPageHelper.differenceTimeVLGWFSKStats,
                onTapTime: vlgwPageHelper.lastUpdatedTimeVLGWFSKStats,
                apiStatus: vlgwPageHelper.isRefreshVLGWInfo ? ApiStatus.loading : ApiStatus.success,
                updateTime: vlgwPageHelper.lastUpdatedTimeVLGWFSKStats,
                textColor:
                    vlgwPageHelper.apiStatusVLGWFSKStats == ApiStatus.success
                        ? AppColorConstants.colorGrn
                        : AppColorConstants.colorRed,
                differenceMessage:
                    vlgwPageHelper.apiStatusVLGWFSKStats == ApiStatus.failed
                        ? S.of(context).refreshFailedMessage
                        : null),
          ),
          Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: AppRefresh(
              buttonColor: AppColorConstants.colorPrimary,
              loadingStatus:  vlgwPageHelper.isRefreshVLGWInfo ?ApiStatus.loading : ApiStatus.success,
              onPressed: () {
                if(vlgwPageHelper.apiStatusVLGWFSKStats != ApiStatus.loading) {
                  vlgwPageHelper.isShowTextOfVFSKStats = true;
                  vlgwPageHelper.apiStatusVLGWFSKStats = ApiStatus.loading;
                  vlgwPageHelper.vlgwController.update();
                  vlgwPageHelper.getConfigsAndFSKStats(context,widget.vLGWItem);
                }
              },
              enabled: !isOffline(),
            ),
          )
        ],
      ),
    );
  }
/*
  refreshVLGWInfoScreen() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Flexible(
          child: buildLastSeenView(
              isShow: vlgwPageHelper.isShowTextOfVI,
              difference: vlgwPageHelper.differenceTimeVLGWInfo,
              onTapTime: vlgwPageHelper.lastUpdatedTimeVLGWInfo,
              apiStatus: vlgwPageHelper.apiStatusVLGWInfo,
              updateTime: vlgwPageHelper.lastUpdatedTimeVLGWInfo,
              textColor: vlgwPageHelper.apiStatusVLGWInfo == ApiStatus.success
                  ? AppColorConstants.colorGrn
                  : AppColorConstants.colorRed,
              differenceMessage:
                  vlgwPageHelper.apiStatusVLGWInfo == ApiStatus.failed
                      ? S.of(context).refreshFailedMessage
                      : null),
        ),
        Padding(
          padding: const EdgeInsets.only(right: 8.0),
          child: AppRefresh(
            buttonColor: AppColorConstants.colorPrimary,
            loadingStatus: vlgwPageHelper.apiStatusVLGWInfo,
            onPressed: () {
              vlgwPageHelper.isShowTextOfVI = true;
              vlgwPageHelper.getVLGWFInfo(context, widget.vLGWItem);
            },
            enabled: !isOffline(),
          ),
        )
      ],
    );
  }*/

  Widget buildLastSeenView(
      {ApiStatus? apiStatus,
      DateTime? onTapTime,
      Duration? difference,
      bool isShow = true,
      DateTime? updateTime,
      Color? textColor,
      String? differenceMessage}) {
    if (isShow) {
      return Padding(
        padding: const EdgeInsets.only(bottom: 8.0),
        child: getTimeDurationView(
            differenceMessage: differenceMessage,
            refreshStatus: apiStatus,
            updateTime: updateTime,
            onTapTime: onTapTime,
            difference: difference,
            textColor: textColor),
      );
    } else {
      if (updateTime != null) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 8.0),
          child: getLastSeenView(updateTime,
              textColor: textColor, offline: isOffline()),
        );
      } else {
        return Container(height: 35);
      }
    }
  }

  bool isOffline() {
    return getDetectedStatusType(widget.vLGWItem.status) ==
        DetectedStatusType.offline;
  }

  Widget commonTitleView(
      {required String title, double? width = double.infinity}) {
    return Container(
      alignment: Alignment.center,
      height: getSize(50),
      width: width,
      padding: EdgeInsets.only(top: getSize(13), left: getSize(20)),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(getSize(7)),
          topLeft: Radius.circular(getSize(7)),
        ),
      ),
      child: AppText(
        title,
        style: TextStyle(
            fontFamily: AppAssetsConstants.openSans,
            fontWeight: getMediumBoldFontWeight(),
            letterSpacing: 0.32,
            color: AppColorConstants.colorLightBlue,
            fontSize: getSize(18)),
      ),
    );
  }

  Widget emptySpace(int count) {
    return (screenLayoutType != ScreenLayoutType.desktop)
        ? Container()
        : ListView.builder(
            itemCount: count,
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            itemBuilder: (context, idx) {
              return Container(
                padding: EdgeInsets.only(
                    left: getSize(25),
                    right: getSize(20),
                    top: getSize(4),
                    bottom: getSize(10)),
                child: AppText(
                  "",
                  style: TextStyle(
                      color: AppColorConstants.colorBlackBlue,
                      fontFamily: AppAssetsConstants.openSans,
                      fontWeight: FontWeight.w500,
                      fontSize: getSize(14)),
                ),
              );
            });
  }

  Widget commonSubTitleView(
      {required String subTitle, required dynamic value, bool ?isExpandValue}) {
    return Padding(
        padding: EdgeInsets.only(
            left: getSize(25),
            right: getSize(20),
            top: getSize(4),
            bottom: getSize(10)),
        child: Row(
          crossAxisAlignment: isExpandValue==true ? CrossAxisAlignment.start : CrossAxisAlignment.center,
          children: [
            AppText(
              subTitle,
              style: TextStyle(
                  color: AppColorConstants.colorBlackBlue,
                  fontFamily: AppAssetsConstants.openSans,
                  fontWeight: FontWeight.w500,
                  fontSize: getSize(14)),
            ),
            Expanded(
              child: Padding(
                padding:  EdgeInsets.symmetric(horizontal: 12,vertical: isExpandValue==true ? 8 : 0),
                child: DottedLine(color: AppColorConstants.colorDotLine),
              ),
            ),
            isExpandValue == true
                ? Expanded(
                    child: AppText(
                      "$value",
                      textAlign:TextAlign.end,
                      style: TextStyle(
                          fontFamily: AppAssetsConstants.openSans,
                          fontWeight: FontWeight.w500,
                          color: AppColorConstants.colorBlackBlue,
                          fontSize: getSize(14)),
                    ),
                  )
                : AppText(
                    "$value",
                    style: TextStyle(
                        fontFamily: AppAssetsConstants.openSans,
                        fontWeight: FontWeight.w500,
                        color: AppColorConstants.colorBlackBlue,
                        fontSize: getSize(14)),
                  ),
          ],
        ));
  }

  void _startTimer() {
    _timer = Timer.periodic(
        const Duration(seconds: AppStringConstants.refreshTimeIntervalSeconds30),
        (timer) {
          vlgwPageHelper.getConfigsAndFSKStats(context,widget.vLGWItem);
    });
  }

  void _stopTimer() {
    _timer?.cancel();
    _timer = null;
  }

  @override
  void dispose() {
    _stopTimer();
    super.dispose();
  }
}
