import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/gestures.dart';
import 'package:graphview/GraphView.dart';
import 'package:graphview/GraphView.dart' as graphview;
import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/controller/vlgw_controller.dart';
import 'package:quantumlink_node/pages/vlgw/vlgw_page_helper.dart';

import 'vlgw_amps_datasource.dart';

class VlgwAmplifierPage extends StatefulWidget {
  final VLGW vLGWItem;
  const VlgwAmplifierPage({super.key, required this.vLGWItem});

  @override
  State<VlgwAmplifierPage> createState() => VlgwAmplifierPageState();
}

class VlgwAmplifierPageState extends State<VlgwAmplifierPage> {
  late ScreenLayoutType screenLayoutType;
  late VLGWPageHelper vlgwPageHelper = VLGWPageHelper.ampsPage();
  VLGWController vlgwController = VLGWController();
  DataTableHelper dataTableHelper = DataTableHelper();
  PaginatorController paginatorController = PaginatorController();
  ApiStatus apiStatus = ApiStatus.initial;
  int recordsInPage = 0; // For Set Fixed and Static Height Of Table
  int currentPageIndex = 0;
  final double heightOfDataTableCell = 48;
  ProvisioningDeviceList listAmpsDeviceItem =
  ProvisioningDeviceList.empty();
  late VlgwAmpsDataSource ampsDeviceDataSource;
  PaginationHelper  paginationHelper = PaginationHelper();
  String ampsErrorMessage= "";

  //Declaration use in Topology Scan
  bool isTopologyScanLoading = false;
  bool isGetScanLoading = false;
  Timer? autoRefreshTimer;
  TopologyScanModel ?latestScan;

  /*final Map<String, Set<String>> adjacencyList = {
    'VLGW': {'A', 'B', 'C'},
    'B': {'D'},
    'C': {'D','E'},
    'D': {'F'},
    'F': {'G', 'H', 'I'},
  };*/

  Map<String, Set<String>> adjacencyList= {};
  Map<String, TopologyDeviceModel> deviceModels = {}; // Store device models by EUI
  DateTime? lastUpdateTime;
  DateTime? scanDataLastUpdateTime;

  @override
  void initState() {
    super.initState();
    //getDeviceListData();
    getTopologyDeviceListData();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      getTopologyScanData();
    });
  }

  @override
  void dispose() {
    autoRefreshTimer?.cancel();
    super.dispose();
  }

  update(){
    vlgwController.update();
  }

  Future<void> getDeviceListData() async {
    apiStatus = ApiStatus.loading;
    paginationHelper.initializePagination();
    update();
    try {
      listAmpsDeviceItem = await vlgwController.getVLGWAmpsDeviceList(
          context: context,
          perPageLimit: AppStringConstants.ampPrePageLimit,
          pageOffset: paginationHelper.pageOffset,
          gwEui: widget.vLGWItem.eui);
      bool hasMoreData = listAmpsDeviceItem.result.length == AppStringConstants.ampPrePageLimit ||
          listAmpsDeviceItem.result.length > AppStringConstants.ampPrePageLimit;
      paginationHelper.updatePagination(listAmpsDeviceItem.result.length,
          hasMore: hasMoreData, pageLimit: AppStringConstants.ampPrePageLimit);
      if (mounted) {
        ampsDeviceDataSource = VlgwAmpsDataSource(context, listAmpsDeviceItem.result);
      }
    } catch (e) {
      ampsErrorMessage = S.of(context).somethingWentWrong;
      ampsDeviceDataSource = VlgwAmpsDataSource(context, []);
      apiStatus = ApiStatus.success;
      update();
    } finally {
      apiStatus = ApiStatus.success;
      update();
    }
  }

  Future<void> getTopologyDeviceListData() async {
    ampsErrorMessage= "";
    apiStatus = ApiStatus.loading;
    update();
    try {
      List<TopologyDeviceModel> ?devices = await vlgwController.getVlgwTopology(
          context: context,
          gwEui: widget.vLGWItem.eui);
      if(devices != null){
        final result = buildDeviceEuiAdjacencyListFromModels(devices);
        adjacencyList = result['adjacencyList'] as Map<String, Set<String>>;
        deviceModels = result['deviceModels'] as Map<String, TopologyDeviceModel>;
      }else{
        ampsErrorMessage = S.of(context).somethingWentWrong;
      }

    } catch (e) {
      debugLogs("getTopologyDeviceListData-->${e}");
      ampsErrorMessage = S.of(context).somethingWentWrong;
      apiStatus = ApiStatus.success;
      update();
    } finally {
      lastUpdateTime=DateTime.now();
      apiStatus = ApiStatus.success;
      update();
    }
  }

  Map<String, dynamic> buildDeviceEuiAdjacencyListFromModels(
      List<TopologyDeviceModel> devices) {
    final Map<String, Set<String>> adjacencyList = {};
    final Map<String, TopologyDeviceModel> deviceModels = {};

    void traverse(TopologyDeviceModel node) {
      final String? parentEui = node.deviceEui;
      final List<TopologyDeviceModel> children = node.children ?? [];
      if (parentEui == null) return;

      // Store the device model
      deviceModels[parentEui] = node;

      if (children.isNotEmpty) {
        adjacencyList.putIfAbsent(parentEui, () => <String>{});
      }

      for (final child in children) {
        final String? childEui = child.deviceEui;
        if (childEui != null) {
          // Store child device model
          deviceModels[childEui] = child;
          adjacencyList[parentEui]!.add(childEui);
          traverse(child); // recursively handle child's children
        }
      }
    }

    for (final device in devices) {
      traverse(device);
    }
    print("adjacencyList-->${adjacencyList}");
    return {
      'adjacencyList': adjacencyList,
      'deviceModels': deviceModels,
    };
  }

  Future<void> startTopologyScan(BuildContext context, String gwEui) async {
    if (gwEui.isEmpty) {
      S.of(context).gatewayEUIIsRequired.showError(context);
      return;
    }

    isTopologyScanLoading = true;
    vlgwPageHelper.vlgwController.update();

    try {
      final response = await vlgwPageHelper.vlgwController.startTopologyScan(context, gwEui);
      if (response != null) {
        final Map<String, dynamic>? scanData = jsonDecode(response.body);
        if (response.statusCode == 200 || response.statusCode == 201) {
          if (scanData != null && scanData['id'] != null) {
            S.of(context).scanTriggeredMessage.showSuccess(context);
            getTopologyScanData();
          }
        }
      }
    } catch (e) {
      debugLogs('catch exception in startTopologyScan ---> $e');
      "Failed to start topology scan: ${e.toString()}".showError(context);
    } finally {
      isTopologyScanLoading = false;
      vlgwPageHelper.vlgwController.update();
    }
  }

  Future<void> getTopologyScanData() async {
    isGetScanLoading = true;
    vlgwPageHelper.vlgwController.update();
    try {
      final response =
      await vlgwPageHelper.vlgwController.getTopologyScan(context, widget.vLGWItem.eui ?? "");
      if (response != null) {
        List<TopologyScanModel> scanDataList = response;
        latestScan = scanDataList.reduce(
                (a, b) =>
            DateTime.parse(a.createdAt ?? "").isAfter(DateTime.parse(b.createdAt ?? "")) ? a : b);
      }
    } catch (e) {
      debugLogs('catch exception in getTopologyScanData ---> $e');
    } finally {
      scanDataLastUpdateTime = DateTime.now();
      isGetScanLoading = false;
      vlgwPageHelper.vlgwController.update();
      startAutoRefresh();
    }
  }
  void startAutoRefresh() {
    autoRefreshTimer?.cancel();
    autoRefreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (mounted) {
        getTopologyScanData();
      } else {
        timer.cancel();
      }
    });
  }
  @override
  Widget build(BuildContext context) {
    return GetBuilder<VLGWController>(
      init: VLGWController(),
      builder: (VLGWController controller) {
        vlgwController = controller;
        return getBody();
      },
    );
  }

  Widget getBody() {
    return ScreenLayoutTypeBuilder(
      builder: (context, screenType, constraints) {
        screenLayoutType = screenType;
        return Column(
          children: [
            buildTopologyScanStatusBox(),
            getTableBoardView(),
          ],
        );
      },
    );
  }

  Widget getTableBoardView() {
    if (apiStatus == ApiStatus.loading) {
      return ListView(padding: const EdgeInsets.only(top: 20),
        shrinkWrap: true,
        children: [
          Align(
              alignment: Alignment.center,
              child: Container(
                  height: 400,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: AppColorConstants.colorWhite,
                    borderRadius:   const BorderRadius.only(
                      topRight: Radius.circular(8),
                      topLeft: Radius.circular(8),
                    ),
                    border: Border.all(color: AppColorConstants.colorH2, width: 1),
                  ),
                  child: const AppLoader())),

        ],
      );
    }
    int itemsPerPage =
        dataTableHelper.getCurrentPageDataLength(listAmpsDeviceItem.result, currentPageIndex);
    recordsInPage =
        (listAmpsDeviceItem.result.length > 10) ? itemsPerPage : listAmpsDeviceItem.result.length;
    return Column( mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // ClipRRect(
        //   borderRadius:
        //       const BorderRadius.only(topRight: Radius.circular(8), topLeft: Radius.circular(8)),
        //   child: Container(
        //       width: double.infinity,
        //       decoration: dataTableHelper.tableBorderDeco(),
        //       child: Column(
        //         crossAxisAlignment: CrossAxisAlignment.start,
        //         children: [
        //           // Table
        //           SizedBox(
        //             height: (listAmpsDeviceItem.result.isNotEmpty)
        //                 ? (recordsInPage * heightOfDataTableCell) + (recordsInPage * 0.1) + 150
        //                 : (recordsInPage * heightOfDataTableCell) + (recordsInPage * 0.1) + 350,
        //             child: getDataTableWithPageBoardView(),
        //           ),
        //           AppPaginationWidget(
        //             apiStatus: vlgwPageHelper.apiStatus,
        //             paginationHelper:paginationHelper,
        //             onLoadNext: () async {
        //               await  loadNextLogs(context);
        //             },
        //             onLoadPrevious: () async {
        //               await  loadPreviousLogs(context);
        //               vlgwController.update();
        //             },
        //             onGoToFirstPage: () {
        //               paginationHelper.setPage(0);
        //               paginatorController.goToFirstPage();
        //
        //               vlgwController.update();
        //             },
        //             onGoToLastPage: () {
        //               paginatorController.goToLastPage();
        //
        //               vlgwController.update();
        //             },
        //             itemsPerPage: AppStringConstants.ampPrePageLimit,
        //             onChanged: (value) {
        //               if (vlgwPageHelper.apiStatus != ApiStatus.loading) {
        //                 AppStringConstants.ampPrePageLimit = int.parse(value);
        //                 getDeviceListData();
        //                 vlgwController.update();
        //               }
        //             },
        //           ),
        //           SizedBox(height: getSize(20)),
        //           Container(
        //             height: 1,
        //             width: double.infinity,
        //             color: AppColorConstants.colorBlack12,
        //           ),
        //         ],
        //       )),
        // ),
        Align(
            alignment: Alignment.centerRight,
            child:ampsErrorMessage.isEmpty
                ? const SizedBox(height: 20)
                : errorMessageView(errorMessage: ampsErrorMessage,padding: 5)),
        Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: AppColorConstants.colorWhite,
              borderRadius: const BorderRadius.only(
                topRight: Radius.circular(8),
                topLeft: Radius.circular(8),
              ),
              border: Border.all(color: AppColorConstants.colorH2, width: 1),
            ),
            child: adjacencyList.isEmpty
                ? SizedBox(height:300,child: dataTableHelper.getEmptyTableContent(context),)
                : topologyView().paddingAll(10)),
        buildLastSeenView(),

      ],
    );
  }

  Widget buildTopologyScanStatusBox() {
    final card = Container(
      margin: const EdgeInsets.only(top: 20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColorConstants.colorWhite,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColorConstants.colorH2, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: buildTopologyScanDataContent(),
    );
    if (screenLayoutType == ScreenLayoutType.mobile) {
      return card;
    }
    return Row(
      children: [
        Expanded(
          child: card,
        ),
        const Spacer(),
        const SizedBox(width: 100)
      ],
    );
  }

  Widget buildTopologyScanDataContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              AppText(
                S.of(context).topologyScan,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColorConstants.colorBlackBlue,
                ),
              ),
              Row(
                children: [
                  // Auto-refresh toggle
                  AppButton(
                    buttonHeight: 32,
                    buttonWidth: 80,
                    fontSize: 14,
                    buttonRadius: 8,
                    buttonName: S.of(context).startScan,
                    borderColor: AppColorConstants.colorPrimary,
                    buttonColor: AppColorConstants.colorPrimary,
                    loadingStatus: isTopologyScanLoading ? ApiStatus.loading : ApiStatus.success,
                    onPressed: isTopologyScanLoading
                        ? null
                        : () => startTopologyScan(context, widget.vLGWItem.eui ?? ""),
                    fontFamily: AppAssetsConstants.openSans,
                  ),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        // Content
        if (isGetScanLoading && latestScan == null)
          buildLoadingContent()
        else if (latestScan != null)
          buildScanDataContent()
        else
          buildNoDataContent(),
        buildScanDataLastSeenView()
      ],
    );
  }

  /// Build loading content
  Widget buildLoadingContent() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10,vertical: 20),
      child: Row(
        children: [
          const SizedBox(
            width: 30,
            height: 30,
            child: AppLoader(),
          ),
          const SizedBox(width: 12),
          AppText(
            S.of(context).loading,
            style: TextStyle(
              fontSize: 14,
              color: AppColorConstants.colorBlack,
            ),
          ),
        ],
      ),
    );
  }

  /// Build scan data content
  Widget buildScanDataContent() {
    return Column(
      children: [
        commonSubTitleView(
            subTitle: S.of(context).createdAt, value: formatIsoDate(latestScan?.createdAt ?? "")),
        commonSubTitleView(subTitle: S.of(context).status, value: latestScan?.displayStatus),
      ],
    );
  }

  /// Build no data content
  Widget buildNoDataContent() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10,vertical: 20),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: AppColorConstants.colorBlack,
            size: 20,
          ),
          const SizedBox(width: 12),
          AppText(
            S.of(context).noDataFound,
            style: TextStyle(
              fontSize: 14,
              color: AppColorConstants.colorBlack,
            ),
          ),
        ],
      ),
    );
  }

  Widget topologyView(){
    final Graph graph = Graph();
    final Map<String, graphview.Node> nodes = {};

    final String mainNodeId = adjacencyList.keys.first;
    adjacencyList.forEach((parent, children) {
      final parentNode = nodes.putIfAbsent(parent, () => graphview.Node.Id(parent));
      for (var child in children) {
        final childNode = nodes.putIfAbsent(child, () => graphview.Node.Id(child));
        graph.addEdge(parentNode, childNode, paint: _getEdgePaint(parent, child));
      }
    });
    final int nodeCount = nodes.length;
    final builder = SugiyamaConfiguration()
      ..coordinateAssignment = CoordinateAssignment.Average
      ..levelSeparation = 40
      ..nodeSeparation = 40
      ..orientation = SugiyamaConfiguration.ORIENTATION_LEFT_RIGHT;
      
    return SizedBox(
      width: double.infinity,
      height:  math.max(300, nodeCount * 100.0),
      child: InteractiveViewer(
        scaleEnabled: false,
        constrained: false,
        boundaryMargin: const EdgeInsets.all(100),
        minScale: 0.01,
        maxScale: 5.0,
        child: GraphView(
          graph: graph,
          algorithm: SugiyamaAlgorithm(builder),
          paint: Paint()
            ..color = AppColorConstants.colorBlack
            ..strokeWidth = 2
            ..style = PaintingStyle.stroke,
          builder: (graphview.Node node) {
            var nodeId = node.key!.value as String;
            bool isMain = nodeId == mainNodeId;
            return rectangleWidget(nodeId, isMain: isMain);
          },
        ),
      ),
    );
  }
  Paint _getEdgePaint(String parent, String child) {
      return Paint()
        ..color = AppColorConstants.colorBlack
        ..strokeWidth = 2
        ..style = PaintingStyle.stroke;
  }

  Widget rectangleWidget(String nodeId, {required bool isMain}) {

    final TopologyDeviceModel? deviceModel = deviceModels[nodeId];
    final String deviceType = deviceModel?.type ?? '';
    final int? bwMode = deviceModel?.bwMode;

    String displayText = nodeId;
    String bwModeText = _getBwModeText(bwMode);

    return Container(
      alignment: Alignment.center,
      height: 70,
      width: 180,
      decoration: BoxDecoration(
        color: isMain ? AppColorConstants.colorPrimaryLime : AppColorConstants.colorTopology,
        border: Border.all(
            color:
            isMain ? AppColorConstants.colorChartLine : AppColorConstants.colorTopologyBorder,
            width: 3),
        borderRadius: BorderRadius.circular(30),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Device type and alias
          AppText(
            displayText,
            style: TextStyle(
              color: AppColorConstants.colorBlack,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
            maxLines: 1,
          ),
          if (bwModeText.isNotEmpty) ...[
            const SizedBox(height: 2),
            AppText(
              bwModeText,
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
          if (deviceType.isNotEmpty) ...[
            const SizedBox(height: 2),
            AppText(
              deviceType,
              style:  TextStyle(
                color:  AppColorConstants.colorBlackBlue,
                fontSize: 11,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ],
      ),
    );
  }


  /// Get bandwidth mode text
  String _getBwModeText(int? bwMode) {
    String ampDeviceMode = bwMode == 0
        ? "1.2 GHz"
        : bwMode == 1
        ? "1.8 GHz"
        : "";
    return ampDeviceMode.isNotEmpty ? ampDeviceMode : "";
  }
  Widget buildScanDataLastSeenView() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        getLastSeenView(scanDataLastUpdateTime),
        AppRefresh(
          buttonColor: AppColorConstants.colorPrimary,
          loadingStatus: isGetScanLoading ? ApiStatus.loading : ApiStatus.success,
          onPressed: () {
            getTopologyScanData();
          },
        )
      ],
    );
  }

  Widget buildLastSeenView() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        getLastSeenView(lastUpdateTime),
        AppRefresh(
          buttonColor: AppColorConstants.colorPrimary,
          loadingStatus:apiStatus,
          onPressed: () {
            getTopologyDeviceListData();
          },
        )
      ],
    );
  }
  Widget commonSubTitleView(
      {required String subTitle, required dynamic value}) {
    return Padding(
        padding: EdgeInsets.only(
            left: getSize(10),
            right: getSize(10),
            top: getSize(4),
            bottom: getSize(10)),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            AppText(
              subTitle,
              style: TextStyle(
                  color: AppColorConstants.colorBlackBlue,
                  fontFamily: AppAssetsConstants.openSans,
                  fontWeight: FontWeight.w500,
                  fontSize: getSize(14)),
            ),
            Expanded(
              child: Padding(
                padding:  EdgeInsets.symmetric(horizontal: 12),
                child: DottedLine(color: AppColorConstants.colorDotLine),
              ),
            ),
            AppText(
              "$value",
              style: TextStyle(
                  fontFamily: AppAssetsConstants.openSans,
                  fontWeight: FontWeight.w500,
                  color: AppColorConstants.colorBlackBlue,
                  fontSize: getSize(14)),
            ),
          ],
        ));
  }

  Widget getDataTableWithPageBoardView() {
    return PaginatedDataTable2(
      columnSpacing: 20,
      rowsPerPage: AppStringConstants.ampPrePageLimit,
      initialFirstRowIndex:
      paginationHelper.currentPage * AppStringConstants.ampPrePageLimit,
      headingCheckboxTheme: CheckboxThemeData(
          side: BorderSide(width: 2, color: AppColorConstants.colorWhite)),
      headingTextStyle:
      dataTableHelper.headingTextStyle(),
      wrapInCard: false,
      datarowCheckboxTheme: const CheckboxThemeData(
          side: BorderSide(width: 2, color: Colors.grey)),
      border: dataTableHelper.tableBorder(),
      renderEmptyRowsInTheEnd: false,
      headingRowColor:
      dataTableHelper.headingRowColor(),
      columns: _getDataColumns(),
      controller: paginatorController,
      source: ampsDeviceDataSource,
      onSelectAll:
      ampsDeviceDataSource.selectAll,
      minWidth: 1170,
      dataRowHeight: 51,
      // For progress indicator
      hidePaginator: true,
      empty:
      dataTableHelper.getEmptyTableContent(context),
    );
  }

  List<DataColumn> _getDataColumns() {
    return [
      DataColumn2(fixedWidth: 90, label: AppText(S.of(context).alarmStatus)),
      DataColumn2(fixedWidth: getSize(220),label: AppText(S.of(context).deviceEUI)),
      DataColumn2(fixedWidth: getSize(200), label: Center(child: AppText(S.of(context).type))),
      DataColumn2(fixedWidth: getSize(130), label: Center(child: AppText(S.of(context).status))),
      DataColumn2(fixedWidth: getSize(180), label: Center(child: AppText(S.of(context).assetID))),
      if(AppConfig.shared.isQLCentral) DataColumn2(fixedWidth: getSize(120), label: AppText(S.of(context).site)),
      DataColumn2( label: AppText(S.of(context).lastSeen)),
    ];
  }

  Future<void> loadPreviousLogs(BuildContext context) async {
    paginationHelper.setPage(paginationHelper.currentPage - 1);
    paginatorController.goToPreviousPage();
    vlgwController.update();
  }

  Future<void> loadNextLogs(BuildContext context) async {
    if (paginationHelper.canGoToNextPage) {
      paginationHelper.setPage(paginationHelper.currentPage + 1);
      if (paginationHelper.currentPage >= paginationHelper.totalPages) {
        paginationHelper.pageOffset = listAmpsDeviceItem.result.length;
        await updateAmplifierPageData();
      } else {
        paginatorController.goToNextPage();
        vlgwController.update();
      }
    }
  }

  updateAmplifierPageData() async {
    apiStatus = ApiStatus.loading;
    vlgwController.update();
    ProvisioningDeviceList ampDeviceList = ProvisioningDeviceList.empty();
    ampDeviceList = await vlgwController.getVLGWAmpsDeviceList(
        context: context,
        perPageLimit: AppStringConstants.ampPrePageLimit,
        pageOffset: paginationHelper.pageOffset,
        gwEui: widget.vLGWItem.eui);
    if (ampDeviceList.result.isNotEmpty) {
      listAmpsDeviceItem.result.addAll(ampDeviceList.result);
      ampsDeviceDataSource = VlgwAmpsDataSource(context, listAmpsDeviceItem.result);
      bool hasMoreData = ampDeviceList.result.length == AppStringConstants.ampPrePageLimit;
      paginationHelper.updatePagination(listAmpsDeviceItem.result.length, hasMore: hasMoreData,pageLimit: AppStringConstants.ampPrePageLimit);
    } else {
      paginationHelper.setPage(paginationHelper.currentPage - 1);
      paginationHelper.updatePagination(listAmpsDeviceItem.result.length, hasMore: false,pageLimit: AppStringConstants.ampPrePageLimit);
      Future.delayed(const Duration(milliseconds: 100)).then((value) {
          if (ampDeviceList.result.isNotEmpty) paginatorController.goToLastPage();
        ampsDeviceDataSource.notifyListeners();
      });
    }
    apiStatus = ApiStatus.success;
    vlgwController.update();
  }
  
}
