// ignore_for_file: deprecated_member_use

import 'package:dotted_border/dotted_border.dart';
import 'package:quantumlink_node/app_import.dart';


class VlgwAmpsDataSource extends DataTableSource {
  int selectedCount = 0;
  VlgwAmpsDataSource.empty(this.context, this.list );

  VlgwAmpsDataSource(this.context, this.list,
      [sortedByEUI = false,
      this.hasRowHeightOverrides = false,
      this.hasZebraStripes = false]) {
    if (sortedByEUI) {
      sort((d) => d.deviceEui, true);
    }
  }

  final BuildContext context;
  final List<ProvisioningDeviceItem> list;
  // Override height values for certain rows
  bool hasRowHeightOverrides = false;

  // Color each Row by index's parity
  bool hasZebraStripes = false;

  void sort<T>(Comparable<T> Function(ProvisioningDeviceItem d) getField, bool ascending) {
    list.sort((a, b) {
      final aValue = getField(a);
      final bValue = getField(b);
      return ascending ? Comparable.compare(aValue, bValue) : Comparable.compare(bValue, aValue);
    });
    notifyListeners();
  }
  DataTableHelper dataTableHelper = DataTableHelper();

  @override
  DataRow2 getRow(int index, [Color? color]) {
    assert(index >= 0);
    if (index >= list.length) throw 'index > _desserts.length';
    final dessert = list[index];
    DetectedStatusType? detectedStatusType = getDetectedStatusType(dessert.status);
    return DataRow2.byIndex(
      index: index,
      color:  index.isEven
          ? MaterialStateProperty.all(AppColorConstants.colorWhite)
          : MaterialStateProperty.all(AppColorConstants.colorBackgroundDark),
      cells: [
        DataCell(Container(
          alignment: Alignment.center,
          child: (detectedStatusType == DetectedStatusType.offline)
              ? const AppImageAsset(image: AppAssetsConstants.deviceOffline)
              : ((dessert.alarm == null)
              ? Icon(
            Icons.check_circle_outline,
            color:(detectedStatusType == DetectedStatusType.pending) ?AppColorConstants.colorH2 : AppColorConstants.colorGreen2,
          )
              : Icon(
            Icons.error_outline,
            color: getSeverityColor(dessert.alarm, false),
          )),
        )),
        DataCell(Row(mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            AppText(
              "${dessert.deviceEui ?? ""}",
              maxLines: 1,
              isSelectableText: false,
              style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontFamily: AppAssetsConstants.openSans,
                  color: AppColorConstants.colorLightBlue,
                  fontSize: getSize(14),
                  decorationThickness: 4,
                  decorationColor: AppColorConstants.colorLightBlue),
            ),
            SizedBox(width: getSize(10)),
            if (dessert.location != null && dessert.location.isNotEmpty && dessert.location.toString().trim() !=",")
              DottedBorder(
                padding: EdgeInsets.symmetric(
                    vertical: getSize(2.5), horizontal: getSize(2.5)),
                radius: Radius.circular(getSize(4)),
                strokeWidth: 0.5,
                borderType: BorderType.RRect,
                color: AppColorConstants.colorH1Grey,
                child: Icon(
                  Icons.location_on,
                  size: getSize(18),
                  color: AppColorConstants.colorPrimary,
                ),
              )
            else
              DottedBorder(
                padding: EdgeInsets.symmetric(
                    vertical: getSize(2.5), horizontal: getSize(2.5)),
                radius: Radius.circular(getSize(4)),
                strokeWidth: 0.5,
                borderType: BorderType.RRect,
                color: AppColorConstants.colorH1Grey,
                child: InkWell(
                  child: Icon(
                    Icons.location_on,
                    size: getSize(18),
                    color: AppColorConstants.colorH2,
                  ),
                ),
              ),
          ],
        )),
        DataCell(Container(
          alignment: Alignment.center,
          child: AppText(dessert.type ?? "-",
            style: dataTableHelper.dataRowTextStyle,textAlign: TextAlign.center,),
        )),
        DataCell(Center(
          child: Container(
            alignment: Alignment.center,
            height: getSize(30),
            width: getSize(70),
            decoration: BoxDecoration(
                color: detectedStatusType == DetectedStatusType.online
                    ? AppColorConstants.colorGreen2
                    : detectedStatusType == DetectedStatusType.offline
                    ? AppColorConstants.colorH2.withOpacity(0.4)
                    : (detectedStatusType == DetectedStatusType.pending)
                    ? AppColorConstants.colorOrange
                    : AppColorConstants.colorH2.withOpacity(0.4),
                borderRadius: BorderRadius.circular(getSize(18))),
            child: AppText(
              detectedStatusType == DetectedStatusType.online
                  ? S.of(context).online
                  : detectedStatusType == DetectedStatusType.offline
                  ? S.of(context).offline
                  : detectedStatusType == DetectedStatusType.pending
                  ? S.of(context).pending
                  : S.of(context).offline,
              style: TextStyle(
                  color: (detectedStatusType == DetectedStatusType.offline ||
                      dessert.status == null)
                      ? AppColorConstants.colorH3
                      : AppColorConstants.colorWhite,
                  fontFamily: AppAssetsConstants.sourceSans,
                  fontSize: 14,
                  fontWeight: FontWeight.w500),
            ),
          ),
        )),
        DataCell(Container(
          alignment: Alignment.center,
          child: AppText(dessert.assetId ?? "-",
              style: dataTableHelper.dataRowTextStyle),
        )),
        if(AppConfig.shared.isQLCentral) DataCell(AppText("${(dessert.site!=null)?dessert.site!.name : ""}",
            style: dataTableHelper.dataRowTextStyle)),
        DataCell(AppText(
          dessert.lastSeen != null ? getUtcTimeZone(dessert.lastSeen) : '',
          style: dataTableHelper.dataRowTextStyle,
        )),
      ],
    );
  }

  @override
  int get rowCount => list.length;

  @override
  bool get isRowCountApproximate => false;

  @override
  int get selectedRowCount => selectedCount;

  void selectAll(bool? checked) {
    for (final dessert in list) {
      dessert.selected = checked ?? false;
    }
    selectedCount = (checked ?? false) ? list.length : 0;
    notifyListeners();
  }

}
