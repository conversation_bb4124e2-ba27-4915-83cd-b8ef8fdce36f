import 'package:latlong2/latlong.dart';
import 'package:quantumlink_node/app_import.dart';

import 'vlgw_map_topology_view.dart';

/// Mock data manager that provides different device sets based on zoom levels
class MockDeviceDataManager {
  
  /// All devices - shown at highest zoom levels (16+)
  static List<DeviceNode> getAllDevices() {
    return [
      // Zone A - High density area
      DeviceNode(
        id: "DEV001", name: "Quantum18 BLE", position: const LatLng(37.7749, -122.4194),
        status: DeviceStatus.healthy, lastSeen: DateTime.now().subtract(const Duration(minutes: 5)),
        activeAlarms: [], temperature: 45.2, loraRssi: -65.0, contractor: "TechCorp",
        isCommissioned: true, isDiscovered: true, zone: "Zone A", type: AmplifierType.ble,
        snr: 12.5, packetLoss: 0.5,
      ),
      DeviceNode(
        id: "DEV002", name: "Quantum18 MB", position: const LatLng(37.7759, -122.4204),
        status: DeviceStatus.warning, lastSeen: DateTime.now().subtract(const Duration(minutes: 15)),
        activeAlarms: ["High Temperature"], temperature: 78.5, loraRssi: -72.0, contractor: "TechCorp",
        isCommissioned: true, isDiscovered: true, zone: "Zone A", type: AmplifierType.mb,
        snr: 8.0, packetLoss: 1.2,
      ),
      DeviceNode(
        id: "DEV003", name: "Quantum18 LE", position: const LatLng(37.7769, -122.4214),
        status: DeviceStatus.healthy, lastSeen: DateTime.now().subtract(const Duration(minutes: 8)),
        activeAlarms: [], temperature: 42.1, loraRssi: -68.0, contractor: "TechCorp",
        isCommissioned: true, isDiscovered: true, zone: "Zone A", type: AmplifierType.le,
        snr: 11.0, packetLoss: 0.3,
      ),
      DeviceNode(
        id: "DEV004", name: "Quantum18 BLE", position: const LatLng(37.7779, -122.4224),
        status: DeviceStatus.warning, lastSeen: DateTime.now().subtract(const Duration(minutes: 25)),
        activeAlarms: ["Low Battery"], temperature: 52.3, loraRssi: -75.0, contractor: "TechCorp",
        isCommissioned: true, isDiscovered: true, zone: "Zone A", type: AmplifierType.ble,
        snr: 9.5, packetLoss: 1.8,
      ),
      
      // Zone B - Medium density
      DeviceNode(
        id: "DEV005", name: "Quantum18 MB", position: const LatLng(37.7849, -122.4294),
        status: DeviceStatus.critical, lastSeen: DateTime.now().subtract(const Duration(hours: 2)),
        activeAlarms: ["Input Overvoltage", "Communication Loss"], temperature: 95.0, loraRssi: -85.0,
        contractor: "NetBuild", isCommissioned: false, isDiscovered: true, zone: "Zone B",
        type: AmplifierType.mb, snr: 5.0, packetLoss: 3.5,
      ),
      DeviceNode(
        id: "DEV006", name: "Quantum18 LE", position: const LatLng(37.7859, -122.4304),
        status: DeviceStatus.healthy, lastSeen: DateTime.now().subtract(const Duration(minutes: 12)),
        activeAlarms: [], temperature: 44.8, loraRssi: -70.0, contractor: "NetBuild",
        isCommissioned: true, isDiscovered: true, zone: "Zone B", type: AmplifierType.le,
        snr: 10.2, packetLoss: 0.7,
      ),
      
      // Zone C - Sparse area
      DeviceNode(
        id: "DEV007", name: "Quantum18 BLE", position: const LatLng(37.7949, -122.4094),
        status: DeviceStatus.offline, lastSeen: DateTime.now().subtract(const Duration(days: 1)),
        activeAlarms: ["Power Loss"], temperature: 0.0, loraRssi: -100.0, contractor: "FiberNet",
        isCommissioned: false, isDiscovered: false, zone: "Zone C", type: AmplifierType.ble,
        snr: 0.0, packetLoss: 10.0,
      ),
      DeviceNode(
        id: "DEV008", name: "Quantum18 MB", position: const LatLng(37.7959, -122.4104),
        status: DeviceStatus.healthy, lastSeen: DateTime.now().subtract(const Duration(minutes: 3)),
        activeAlarms: [], temperature: 41.0, loraRssi: -60.0, contractor: "FiberNet",
        isCommissioned: true, isDiscovered: true, zone: "Zone C", type: AmplifierType.mb,
        snr: 13.0, packetLoss: 0.3,
      ),
      
      // Additional devices for high zoom detail
      DeviceNode(
        id: "DEV009", name: "Quantum18 LE", position: const LatLng(37.7739, -122.4184),
        status: DeviceStatus.warning, lastSeen: DateTime.now().subtract(const Duration(minutes: 45)),
        activeAlarms: ["Temperature Fluctuation"], temperature: 65.5, loraRssi: -78.0,
        contractor: "TechCorp", isCommissioned: true, isDiscovered: true, zone: "Zone A",
        type: AmplifierType.le, snr: 7.8, packetLoss: 2.1,
      ),
      DeviceNode(
        id: "DEV010", name: "Quantum18 BLE", position: const LatLng(37.7729, -122.4174),
        status: DeviceStatus.healthy, lastSeen: DateTime.now().subtract(const Duration(minutes: 7)),
        activeAlarms: [], temperature: 43.6, loraRssi: -66.0, contractor: "TechCorp",
        isCommissioned: true, isDiscovered: true, zone: "Zone A", type: AmplifierType.ble,
        snr: 11.8, packetLoss: 0.4,
      ),
      
      // Remote devices
      DeviceNode(
        id: "DEV011", name: "Quantum18 MB", position: const LatLng(37.8049, -122.3894),
        status: DeviceStatus.critical, lastSeen: DateTime.now().subtract(const Duration(hours: 4)),
        activeAlarms: ["Hardware Fault", "High Packet Loss"], temperature: 88.2, loraRssi: -92.0,
        contractor: "WaveTech", isCommissioned: false, isDiscovered: true, zone: "Zone D",
        type: AmplifierType.mb, snr: 3.5, packetLoss: 6.2,
      ),
      DeviceNode(
        id: "DEV012", name: "Quantum18 LE", position: const LatLng(37.8149, -122.3794),
        status: DeviceStatus.offline, lastSeen: DateTime.now().subtract(const Duration(days: 2)),
        activeAlarms: ["System Unresponsive"], temperature: 0.0, loraRssi: -110.0,
        contractor: "WaveTech", isCommissioned: false, isDiscovered: false, zone: "Zone D",
        type: AmplifierType.le, snr: 0.0, packetLoss: 12.0,
      ),
    ];
  }

  /// Important devices - shown at medium zoom levels (14-16)
  static List<DeviceNode> getImportantDevices() {
    return getAllDevices().where((device) => 
      device.isCommissioned || 
      device.status == DeviceStatus.critical ||
      device.status == DeviceStatus.warning
    ).toList();
  }

  /// Critical devices only - shown at low zoom levels (12-14)
  static List<DeviceNode> getCriticalDevices() {
    return getAllDevices().where((device) => 
      device.status == DeviceStatus.critical ||
      device.status == DeviceStatus.offline ||
      device.activeAlarms.isNotEmpty
    ).toList();
  }

  /// Cluster representatives - shown at very low zoom levels (<12)
  static List<DeviceNode> getClusterRepresentatives() {
    // Return one representative device per zone
    final zones = <String, DeviceNode>{};
    for (final device in getAllDevices()) {
      if (!zones.containsKey(device.zone) || 
          device.status == DeviceStatus.critical) {
        zones[device.zone] = device;
      }
    }
    return zones.values.toList();
  }

  /// Get device statistics for zoom level
  static Map<String, int> getDeviceStats(List<DeviceNode> devices) {
    final stats = <String, int>{
      'total': devices.length,
      'healthy': 0,
      'warning': 0,
      'critical': 0,
      'offline': 0,
      'commissioned': 0,
      'discovered': 0,
    };

    for (final device in devices) {
      switch (device.status) {
        case DeviceStatus.healthy:
          stats['healthy'] = (stats['healthy'] ?? 0) + 1;
          break;
        case DeviceStatus.warning:
          stats['warning'] = (stats['warning'] ?? 0) + 1;
          break;
        case DeviceStatus.critical:
          stats['critical'] = (stats['critical'] ?? 0) + 1;
          break;
        case DeviceStatus.offline:
          stats['offline'] = (stats['offline'] ?? 0) + 1;
          break;
      }
      
      if (device.isCommissioned) {
        stats['commissioned'] = (stats['commissioned'] ?? 0) + 1;
      }
      if (device.isDiscovered) {
        stats['discovered'] = (stats['discovered'] ?? 0) + 1;
      }
    }

    return stats;
  }
}
