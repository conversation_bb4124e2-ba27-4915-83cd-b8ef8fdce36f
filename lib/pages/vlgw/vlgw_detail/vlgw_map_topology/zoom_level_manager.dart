import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:quantumlink_node/app_import.dart';
import 'geojson_data_parser.dart';

import 'vlgw_map_topology_view.dart';

/// Manages zoom level changes and API calls with debouncing
class ZoomLevelManager {
  static const Duration _debounceDelay = Duration(milliseconds: 800);

  Timer? _debounceTimer;
  double _currentZoomLevel = 13.0;
  bool _isLoading = false;
  MapTopologyData? _cachedData;

  // Callbacks
  Function(double zoomLevel)? onZoomChanged;
  Function(List<DeviceNode> devices)? onDevicesUpdated;
  Function(MapTopologyData data)? onTopologyDataUpdated;
  Function(bool isLoading)? onLoadingChanged;
  
  ZoomLevelManager({
    this.onZoomChanged,
    this.onDevicesUpdated,
    this.onTopologyDataUpdated,
    this.onLoadingChanged,
  }) {
    // Initialize with sample GeoJSON data
    _initializeWithSampleData();
  }

  double get currentZoomLevel => _currentZoomLevel;
  bool get isLoading => _isLoading;

  /// Called when map zoom changes
  void onMapZoomChanged(double newZoomLevel) {
    if (_currentZoomLevel == newZoomLevel) return;
    
    _currentZoomLevel = newZoomLevel;
    onZoomChanged?.call(newZoomLevel);
    
    // Cancel previous timer
    _debounceTimer?.cancel();
    
    // Start new debounce timer
    _debounceTimer = Timer(_debounceDelay, () {
      _fetchDevicesForZoomLevel(newZoomLevel);
    });
  }

  /// Initialize with sample GeoJSON data
  void _initializeWithSampleData() {
    final sampleGeoJson = _getSampleGeoJsonData();
    _cachedData = GeoJsonDataParser.parseGeoJsonResponse(sampleGeoJson);

    // Notify with initial data
    if (_cachedData != null) {
      final filteredDevices = GeoJsonDataParser.filterDevicesByZoom(
        _cachedData!.devices,
        _currentZoomLevel
      );
      onDevicesUpdated?.call(filteredDevices);
      onTopologyDataUpdated?.call(_cachedData!);
    }
  }

  /// Fetches devices based on zoom level with API simulation
  Future<void> _fetchDevicesForZoomLevel(double zoomLevel) async {
    if (_isLoading) return;

    _setLoading(true);

    try {
      debugLogs("Fetching devices for zoom level: $zoomLevel");

      // Simulate API call delay
      await Future.delayed(const Duration(milliseconds: 500));

      // In real implementation, make API call here:
      // final response = await apiService.getTopologyData(zoomLevel);
      // _cachedData = GeoJsonDataParser.parseGeoJsonResponse(response);

      // For now, use cached data with zoom filtering
      if (_cachedData != null) {
        final filteredDevices = GeoJsonDataParser.filterDevicesByZoom(
          _cachedData!.devices,
          zoomLevel
        );

        debugLogs("Fetched ${filteredDevices.length} devices for zoom level $zoomLevel");
        onDevicesUpdated?.call(filteredDevices);
      }

    } catch (e) {
      debugLogs("Error fetching devices: $e");
    } finally {
      _setLoading(false);
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    onLoadingChanged?.call(loading);
  }

  /// Get device count info for current zoom
  String getDeviceCountInfo(List<DeviceNode> currentDevices) {
    if (_cachedData == null) return "Loading...";
    final total = _cachedData!.devices.length;
    final current = currentDevices.length;
    return "$current of $total devices shown";
  }

  /// Get sample GeoJSON data (your provided data)
  Map<String, dynamic> _getSampleGeoJsonData() {
    return {
      "layers": {
        "devices": {
          "type": "point",
          "features": [
            {
              "type": "Feature",
              "geometry": {
                "type": "Point",
                "coordinates": [-84.40125595666186, 33.70915173670323]
              },
              "properties": {
                "device_eui": "002926a000000001",
                "device_alias": "dev-001",
                "status": "offline",
                "rssi": 80,
                "style_hint": {
                  "color": "#FF0000",
                  "icon": "sensor",
                  "marker_size": 10
                }
              }
            },
            {
              "type": "Feature",
              "geometry": {
                "type": "Point",
                "coordinates": [-84.35892290590768, 33.792928588588026]
              },
              "properties": {
                "device_eui": "002926a000000002",
                "device_alias": "dev-002",
                "status": "online",
                "rssi": 64,
                "style_hint": {
                  "color": "#00FF00",
                  "icon": "sensor",
                  "marker_size": 10
                }
              }
            },
            {
              "type": "Feature",
              "geometry": {
                "type": "Point",
                "coordinates": [-84.40538996700117, 33.77375639705986]
              },
              "properties": {
                "device_eui": "002926a000000003",
                "device_alias": "dev-003",
                "status": "online",
                "rssi": 52,
                "style_hint": {
                  "color": "#00FF00",
                  "icon": "sensor",
                  "marker_size": 10
                }
              }
            },
            {
              "type": "Feature",
              "geometry": {
                "type": "Point",
                "coordinates": [-84.371946604469, 33.73555126052873]
              },
              "properties": {
                "device_eui": "002926a000000004",
                "device_alias": "dev-004",
                "status": "online",
                "rssi": 86,
                "style_hint": {
                  "color": "#00FF00",
                  "icon": "sensor",
                  "marker_size": 10
                }
              }
            },
            {
              "type": "Feature",
              "geometry": {
                "type": "Point",
                "coordinates": [-84.37467502488337, 33.774483872822856]
              },
              "properties": {
                "device_eui": "002926a000000005",
                "device_alias": "dev-005",
                "status": "offline",
                "rssi": 85,
                "style_hint": {
                  "color": "#FF0000",
                  "icon": "sensor",
                  "marker_size": 10
                }
              }
            },
            {
              "type": "Feature",
              "geometry": {
                "type": "Point",
                "coordinates": [-84.38787135977438, 33.74973517747811]
              },
              "properties": {
                "device_eui": "002926a000000006",
                "device_alias": "dev-006",
                "status": "offline",
                "rssi": 67,
                "style_hint": {
                  "color": "#FF0000",
                  "icon": "sensor",
                  "marker_size": 10
                }
              }
            },
            {
              "type": "Feature",
              "geometry": {
                "type": "Point",
                "coordinates": [-84.42993477124199, 33.707132094497716]
              },
              "properties": {
                "device_eui": "002926a000000007",
                "device_alias": "dev-007",
                "status": "online",
                "rssi": 68,
                "style_hint": {
                  "color": "#FF0000",
                  "icon": "sensor",
                  "marker_size": 10
                }
              }
            },
            {
              "type": "Feature",
              "geometry": {
                "type": "Point",
                "coordinates": [-84.3827583367776, 33.71002658869651]
              },
              "properties": {
                "device_eui": "002926a000000016",
                "device_alias": "dev-022",
                "status": "offline",
                "rssi": 83,
                "style_hint": {
                  "color": "#00FF00",
                  "icon": "sensor",
                  "marker_size": 10
                }
              }
            },
            {
              "type": "Feature",
              "geometry": {
                "type": "Point",
                "coordinates": [-84.39282644764262, 33.733496197144305]
              },
              "properties": {
                "device_eui": "002926a000000017",
                "device_alias": "dev-023",
                "status": "offline",
                "rssi": 74,
                "style_hint": {
                  "color": "#00FF00",
                  "icon": "sensor",
                  "marker_size": 10
                }
              }
            },
            {
              "type": "Feature",
              "geometry": {
                "type": "Point",
                "coordinates": [-84.34118988893903, 33.791250068611596]
              },
              "properties": {
                "device_eui": "002926a000000018",
                "device_alias": "dev-024",
                "status": "offline",
                "rssi": 78,
                "style_hint": {
                  "color": "#00FF00",
                  "icon": "sensor",
                  "marker_size": 10
                }
              }
            }
          ]
        },

        "device_links": {
          "type": "line",
          "features": [
            {
              "type": "Feature",
              "geometry": {
                "type": "LineString",
                "coordinates": [
                  [-84.40125595666186, 33.70915173670323],
                  [-84.37467502488337, 33.774483872822856]
                ]
              },
              "properties": {
                "source_id": "dev-001",
                "target_id": "dev-005",
                "status": "linked",
                "style_hint": {
                  "color": "#888888",
                  "stroke_width": 2
                }
              }
            },
            {
              "type": "Feature",
              "geometry": {
                "type": "LineString",
                "coordinates": [
                  [-84.35892290590768, 33.792928588588026],
                  [-84.34118988893903, 33.791250068611596]
                ]
              },
              "properties": {
                "source_id": "dev-002",
                "target_id": "dev-024",
                "status": "linked",
                "style_hint": {
                  "color": "#888888",
                  "stroke_width": 2
                }
              }
            }
          ]
        },
        "gateways": {
          "type": "point",
          "features": [
            {
              "type": "Feature",
              "geometry": {
                "type": "Point",
                "coordinates": [-84.34020049959972, 33.79323027373673]
              },
              "properties": {
                "gateway_id": "gw-001",
                "site_name": "Gateway 1",
                "style_hint": {
                  "color": "#0000FF",
                  "icon": "gateway",
                  "marker_size": 14
                }
              }
            },
            {
              "type": "Feature",
              "geometry": {
                "type": "Point",
                "coordinates": [-84.36281241503298, 33.76303908935327]
              },
              "properties": {
                "gateway_id": "gw-002",
                "site_name": "Gateway 2",
                "style_hint": {
                  "color": "#0000FF",
                  "icon": "gateway",
                  "marker_size": 14
                }
              }
            }
          ]
        }
      }
    };
  }

  /// Get zoom level category for UI display
  String getZoomLevelCategory(double zoomLevel) {
    if (zoomLevel >= 16) return "Detailed View";
    if (zoomLevel >= 14) return "Standard View";
    if (zoomLevel >= 12) return "Overview";
    return "Regional View";
  }

  void dispose() {
    _debounceTimer?.cancel();
  }
}
