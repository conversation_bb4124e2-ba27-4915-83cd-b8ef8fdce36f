import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/pages/vlgw/vlgw_detail/vlgw_map_topology/mock_device_data_manager.dart';

import 'vlgw_map_topology_view.dart';

/// Manages zoom level changes and API calls with debouncing
class ZoomLevelManager {
  static const Duration _debounceDelay = Duration(milliseconds: 800);
  
  Timer? _debounceTimer;
  double _currentZoomLevel = 13.0;
  bool _isLoading = false;
  
  // Callbacks
  Function(double zoomLevel)? onZoomChanged;
  Function(List<DeviceNode> devices)? onDevicesUpdated;
  Function(bool isLoading)? onLoadingChanged;
  
  ZoomLevelManager({
    this.onZoomChanged,
    this.onDevicesUpdated,
    this.onLoadingChanged,
  });

  double get currentZoomLevel => _currentZoomLevel;
  bool get isLoading => _isLoading;

  /// Called when map zoom changes
  void onMapZoomChanged(double newZoomLevel) {
    if (_currentZoomLevel == newZoomLevel) return;
    
    _currentZoomLevel = newZoomLevel;
    onZoomChanged?.call(newZoomLevel);
    
    // Cancel previous timer
    _debounceTimer?.cancel();
    
    // Start new debounce timer
    _debounceTimer = Timer(_debounceDelay, () {
      _fetchDevicesForZoomLevel(newZoomLevel);
    });
  }

  /// Fetches devices based on zoom level with mock API simulation
  Future<void> _fetchDevicesForZoomLevel(double zoomLevel) async {
    if (_isLoading) return;
    
    _setLoading(true);
    
    try {
      debugLogs("Fetching devices for zoom level: $zoomLevel");
      
      // Simulate API call delay
      await Future.delayed(const Duration(milliseconds: 500));
      
      // Get devices based on zoom level
      final devices = _getDevicesForZoomLevel(zoomLevel);
      
      debugLogs("Fetched ${devices.length} devices for zoom level $zoomLevel");
      onDevicesUpdated?.call(devices);
      
    } catch (e) {
      debugLogs("Error fetching devices: $e");
    } finally {
      _setLoading(false);
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    onLoadingChanged?.call(loading);
  }

  /// Returns different device sets based on zoom level
  List<DeviceNode> _getDevicesForZoomLevel(double zoomLevel) {
    if (zoomLevel >= 16) {
      // High zoom - show all devices with detailed info
      return _getAllDevices();
    } else if (zoomLevel >= 14) {
      // Medium zoom - show important devices
      return _getImportantDevices();
    } else if (zoomLevel >= 12) {
      // Low zoom - show critical devices only
      return _getCriticalDevices();
    } else {
      // Very low zoom - show cluster representatives
      return _getClusterRepresentatives();
    }
  }

  /// Get zoom level category for UI display
  String getZoomLevelCategory(double zoomLevel) {
    if (zoomLevel >= 16) return "Detailed View";
    if (zoomLevel >= 14) return "Standard View";
    if (zoomLevel >= 12) return "Overview";
    return "Regional View";
  }

  /// Get device count info for current zoom
  String getDeviceCountInfo(List<DeviceNode> devices) {
    final total = _getAllDevices().length;
    final current = devices.length;
    return "$current of $total devices shown";
  }

  void dispose() {
    _debounceTimer?.cancel();
  }
}

// Extension to add zoom level specific device filtering
extension ZoomLevelDevices on ZoomLevelManager {
  
  List<DeviceNode> _getAllDevices() {
    return MockDeviceDataManager.getAllDevices();
  }

  List<DeviceNode> _getImportantDevices() {
    return MockDeviceDataManager.getImportantDevices();
  }

  List<DeviceNode> _getCriticalDevices() {
    return MockDeviceDataManager.getCriticalDevices();
  }

  List<DeviceNode> _getClusterRepresentatives() {
    return MockDeviceDataManager.getClusterRepresentatives();
  }
}
