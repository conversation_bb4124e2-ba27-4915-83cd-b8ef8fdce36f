import 'dart:convert';
import 'package:latlong2/latlong.dart';
import 'package:quantumlink_node/app_import.dart';
import 'vlgw_map_topology_view.dart';

/// Parser for GeoJSON response data
class GeoJsonDataParser {
  
  /// Parse the complete GeoJSON response
  static MapTopologyData parseGeoJsonResponse(Map<String, dynamic> response) {
    final layers = response['layers'] as Map<String, dynamic>;
    
    final devices = _parseDevices(layers['devices']);
    final connections = _parseConnections(layers['device_links']);
    final gateways = _parseGateways(layers['gateways']);
    
    return MapTopologyData(
      devices: devices,
      connections: connections,
      gateways: gateways,
    );
  }

  /// Parse devices from GeoJSON features
  static List<DeviceNode> _parseDevices(Map<String, dynamic> deviceLayer) {
    final features = deviceLayer['features'] as List<dynamic>;
    
    return features.map((feature) {
      final geometry = feature['geometry'];
      final properties = feature['properties'];
      final coordinates = geometry['coordinates'] as List<dynamic>;
      final styleHint = properties['style_hint'] as Map<String, dynamic>;
      
      return DeviceNode(
        id: properties['device_eui'] as String,
        name: properties['device_alias'] as String,
        position: LatLng(coordinates[1], coordinates[0]), // Note: lat, lng order
        status: _parseDeviceStatus(properties['status'] as String),
        lastSeen: DateTime.now().subtract(Duration(
          minutes: properties['status'] == 'offline' ? 120 : 5
        )),
        activeAlarms: properties['status'] == 'offline' ? ['Connection Lost'] : [],
        temperature: _generateTemperature(properties['status'] as String),
        loraRssi: -(properties['rssi'] as int).toDouble(),
        contractor: _assignContractor(properties['device_eui'] as String),
        isCommissioned: properties['status'] == 'online',
        isDiscovered: true,
        zone: _assignZone(coordinates[1], coordinates[0]),
        type: AmplifierType.ble, // Default type, can be enhanced
        snr: _generateSNR(properties['rssi'] as int),
        packetLoss: properties['status'] == 'offline' ? 10.0 : 0.5,
      );
    }).toList();
  }

  /// Parse device connections from GeoJSON features
  static List<TopologyConnection> _parseConnections(Map<String, dynamic> linkLayer) {
    final features = linkLayer['features'] as List<dynamic>;
    
    return features.map((feature) {
      final properties = feature['properties'];
      
      return TopologyConnection(
        fromId: _findDeviceEuiByAlias(properties['source_id'] as String),
        toId: _findDeviceEuiByAlias(properties['target_id'] as String),
        status: _parseConnectionStatus(properties['status'] as String),
      );
    }).toList();
  }

  /// Parse gateways from GeoJSON features
  static List<GatewayNode> _parseGateways(Map<String, dynamic> gatewayLayer) {
    final features = gatewayLayer['features'] as List<dynamic>;
    
    return features.map((feature) {
      final geometry = feature['geometry'];
      final properties = feature['properties'];
      final coordinates = geometry['coordinates'] as List<dynamic>;
      
      return GatewayNode(
        id: properties['gateway_id'] as String,
        name: properties['site_name'] as String,
        position: LatLng(coordinates[1], coordinates[0]),
        status: GatewayStatus.online, // Default status
      );
    }).toList();
  }

  /// Convert string status to DeviceStatus enum
  static DeviceStatus _parseDeviceStatus(String status) {
    switch (status.toLowerCase()) {
      case 'online':
        return DeviceStatus.healthy;
      case 'offline':
        return DeviceStatus.offline;
      case 'warning':
        return DeviceStatus.warning;
      case 'critical':
        return DeviceStatus.critical;
      default:
        return DeviceStatus.offline;
    }
  }

  /// Convert string status to ConnectionStatus enum
  static ConnectionStatus _parseConnectionStatus(String status) {
    switch (status.toLowerCase()) {
      case 'linked':
        return ConnectionStatus.functioning;
      case 'cut':
        return ConnectionStatus.cut;
      case 'planned':
        return ConnectionStatus.planned;
      default:
        return ConnectionStatus.functioning;
    }
  }

  /// Generate temperature based on device status
  static double _generateTemperature(String status) {
    switch (status.toLowerCase()) {
      case 'online':
        return 40.0 + (DateTime.now().millisecond % 20);
      case 'offline':
        return 0.0;
      case 'warning':
        return 70.0 + (DateTime.now().millisecond % 15);
      case 'critical':
        return 90.0 + (DateTime.now().millisecond % 10);
      default:
        return 45.0;
    }
  }

  /// Generate SNR based on RSSI
  static double _generateSNR(int rssi) {
    if (rssi < 70) return 15.0;
    if (rssi < 80) return 10.0;
    if (rssi < 90) return 5.0;
    return 2.0;
  }

  /// Assign contractor based on device EUI pattern
  static String _assignContractor(String deviceEui) {
    final lastDigit = int.parse(deviceEui.substring(deviceEui.length - 1));
    switch (lastDigit % 4) {
      case 0:
        return 'TechCorp';
      case 1:
        return 'NetBuild';
      case 2:
        return 'FiberNet';
      default:
        return 'WaveTech';
    }
  }

  /// Assign zone based on coordinates
  static String _assignZone(double lat, double lng) {
    if (lat > 33.77) return 'Zone North';
    if (lat > 33.74) return 'Zone Central';
    if (lat > 33.71) return 'Zone South';
    return 'Zone Remote';
  }

  /// Find device EUI by alias (for connections)
  static String _findDeviceEuiByAlias(String alias) {
    // Extract number from alias like "dev-001" and convert to EUI format
    final match = RegExp(r'dev-(\d+)').firstMatch(alias);
    if (match != null) {
      final number = int.parse(match.group(1)!);
      return '002926a0000000${number.toString().padLeft(2, '0')}';
    }
    return alias; // Fallback
  }

  /// Filter devices based on zoom level
  static List<DeviceNode> filterDevicesByZoom(List<DeviceNode> allDevices, double zoomLevel) {
    if (zoomLevel >= 16) {
      // Detailed View - show all devices
      return allDevices;
    } else if (zoomLevel >= 14) {
      // Standard View - show online and critical devices
      return allDevices.where((device) => 
        device.status == DeviceStatus.healthy || 
        device.status == DeviceStatus.critical ||
        device.isCommissioned
      ).toList();
    } else if (zoomLevel >= 12) {
      // Overview - show critical and offline devices only
      return allDevices.where((device) => 
        device.status == DeviceStatus.critical || 
        device.status == DeviceStatus.offline ||
        device.activeAlarms.isNotEmpty
      ).toList();
    } else {
      // Regional View - show one device per zone
      final zoneRepresentatives = <String, DeviceNode>{};
      for (final device in allDevices) {
        if (!zoneRepresentatives.containsKey(device.zone) || 
            device.status == DeviceStatus.critical) {
          zoneRepresentatives[device.zone] = device;
        }
      }
      return zoneRepresentatives.values.toList();
    }
  }
}

/// Data structure to hold parsed map topology data
class MapTopologyData {
  final List<DeviceNode> devices;
  final List<TopologyConnection> connections;
  final List<GatewayNode> gateways;

  MapTopologyData({
    required this.devices,
    required this.connections,
    required this.gateways,
  });
}

/// Gateway node data structure
class GatewayNode {
  final String id;
  final String name;
  final LatLng position;
  final GatewayStatus status;

  GatewayNode({
    required this.id,
    required this.name,
    required this.position,
    required this.status,
  });
}

/// Gateway status enum
enum GatewayStatus { online, offline, maintenance }
