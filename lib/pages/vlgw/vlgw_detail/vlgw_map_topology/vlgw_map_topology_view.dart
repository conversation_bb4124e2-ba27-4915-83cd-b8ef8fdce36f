import 'package:flutter_map/flutter_map.dart';
import 'package:intl/intl.dart';
import 'package:latlong2/latlong.dart';
import 'package:flutter_map_marker_cluster/flutter_map_marker_cluster.dart';
import 'package:quantumlink_node/app_import.dart';
import 'zoom_level_manager.dart';
import 'geojson_data_parser.dart';

enum DeviceStatus { healthy, warning, critical, offline }

enum ConnectionStatus { functioning, cut, planned }

enum AmplifierType { ble, mb, le }

class DeviceNode {
  final String id;
  final String name;
  final LatLng position;
  final DeviceStatus status;
  final DateTime lastSeen;
  final List<String> activeAlarms;
  final double temperature;
  final double loraRssi;
  final String contractor;
  final bool isCommissioned;
  final bool isDiscovered;
  final String zone;
  final AmplifierType type;
  final double snr;
  final double packetLoss;

  DeviceNode({
    required this.id,
    required this.name,
    required this.position,
    required this.status,
    required this.lastSeen,
    required this.activeAlarms,
    required this.temperature,
    required this.loraRssi,
    required this.contractor,
    required this.isCommissioned,
    required this.isDiscovered,
    required this.zone,
    required this.type,
    required this.snr,
    required this.packetLoss,
  });
}

class TopologyConnection {
  final String fromId;
  final String toId;
  final ConnectionStatus status;

  TopologyConnection({
    required this.fromId,
    required this.toId,
    required this.status,
  });
}

class VlgwMapTopologyView extends StatefulWidget {
  const VlgwMapTopologyView({Key? key}) : super(key: key);

  @override
  State<VlgwMapTopologyView> createState() => _VlgwMapTopologyViewState();
}

class _VlgwMapTopologyViewState extends State<VlgwMapTopologyView> {
  // Filter states
  bool showDiscovered = true;
  bool showCommissioned = true;
  bool showOffline = true;
  bool showOnline = true;
  String? selectedContractor;
  String? selectedZone;

  // Zoom level management
  late ZoomLevelManager _zoomManager;
  late MapController _mapController;
  List<DeviceNode> _currentDevices = [];
  List<GatewayNode> _currentGateways = [];
  bool _isLoading = false;
  double _currentZoom = 13.0;

  @override
  void initState() {
    super.initState();
    _mapController = MapController();
    _initializeZoomManager();
  }

  void _initializeZoomManager() {
    _zoomManager = ZoomLevelManager(
      onZoomChanged: (zoomLevel) {
        setState(() {
          _currentZoom = zoomLevel;
        });
      },
      onDevicesUpdated: (devices) {
        setState(() {
          _currentDevices = devices;
        });
      },
      onTopologyDataUpdated: (data) {
        setState(() {
          _currentGateways = data.gateways;
        });
      },
      onLoadingChanged: (isLoading) {
        setState(() {
          _isLoading = isLoading;
        });
      },
    );
  }

  @override
  void dispose() {
    _zoomManager.dispose();
    super.dispose();
  }

  // Mock topology connections
  final List<TopologyConnection> connections = [
    TopologyConnection(fromId: "DEV001", toId: "DEV002", status: ConnectionStatus.functioning),
    TopologyConnection(fromId: "DEV002", toId: "DEV003", status: ConnectionStatus.cut),
    TopologyConnection(fromId: "DEV003", toId: "DEV004", status: ConnectionStatus.planned),
    TopologyConnection(fromId: "DEV004", toId: "DEV005", status: ConnectionStatus.functioning),
    TopologyConnection(fromId: "DEV005", toId: "DEV006", status: ConnectionStatus.functioning),
    TopologyConnection(fromId: "DEV001", toId: "DEV006", status: ConnectionStatus.planned),
  ];

  Color getStatusColor(DeviceStatus status) {
    switch (status) {
      case DeviceStatus.healthy:
        return Colors.green;
      case DeviceStatus.warning:
        return Colors.orange;
      case DeviceStatus.critical:
        return Colors.red;
      case DeviceStatus.offline:
        return Colors.grey;
    }
  }

  IconData getStatusIcon(DeviceStatus status) {
    switch (status) {
      case DeviceStatus.healthy:
        return Icons.check_circle;
      case DeviceStatus.warning:
        return Icons.warning;
      case DeviceStatus.critical:
        return Icons.error;
      case DeviceStatus.offline:
        return Icons.offline_bolt;
    }
  }

  IconData getAmpTypeIcon(AmplifierType type) {
    switch (type) {
      case AmplifierType.ble:
        return Icons.bluetooth;
      case AmplifierType.mb:
        return Icons.memory;
      case AmplifierType.le:
        return Icons.electrical_services;
    }
  }

  Color getAmpTypeColor(AmplifierType type) {
    switch (type) {
      case AmplifierType.ble:
        return Colors.blue;
      case AmplifierType.mb:
        return Colors.purple;
      case AmplifierType.le:
        return Colors.teal;
    }
  }

  List<DeviceNode> getFilteredDevices() {
    return _currentDevices.where((device) {
      if (selectedContractor != null && device.contractor != selectedContractor) {
        return false;
      }
      if (selectedZone != null && device.zone != selectedZone) {
        return false;
      }

      if (!showOffline && device.status == DeviceStatus.offline) return false;
      if (!showOnline && device.status != DeviceStatus.offline) return false;
      if (!showDiscovered && device.isDiscovered) return false;
      if (!showCommissioned && device.isCommissioned) return false;

      return true;
    }).toList();
  }

  List<TopologyConnection> getFilteredConnections() {
    return connections.where((conn) {
      // Check if both devices exist in current device list
      final fromExists = _currentDevices.any((d) => d.id == conn.fromId);
      final toExists = _currentDevices.any((d) => d.id == conn.toId);

      if (!fromExists || !toExists) return false;

      final fromDevice = _currentDevices.firstWhere((d) => d.id == conn.fromId);
      final toDevice = _currentDevices.firstWhere((d) => d.id == conn.toId);

      if (selectedContractor != null) {
        if (fromDevice.contractor != selectedContractor ||
            toDevice.contractor != selectedContractor) {
          return false;
        }
      }
      if (selectedZone != null) {
        if (fromDevice.zone != selectedZone || toDevice.zone != selectedZone) {
          return false;
        }
      }
      return true;
    }).toList();
  }

  List<String> getAvailableZones() {
    return _currentDevices.map((d) => d.zone).toSet().toList()..sort();
  }

  List<String> getAvailableContractors() {
    return _currentDevices.map((d) => d.contractor).toSet().toList()..sort();
  }

  Color getGatewayStatusColor(GatewayStatus status) {
    switch (status) {
      case GatewayStatus.online:
        return Colors.blue;
      case GatewayStatus.offline:
        return Colors.grey;
      case GatewayStatus.maintenance:
        return Colors.orange;
    }
  }

  IconData getGatewayIcon(GatewayStatus status) {
    switch (status) {
      case GatewayStatus.online:
        return Icons.router;
      case GatewayStatus.offline:
        return Icons.router_outlined;
      case GatewayStatus.maintenance:
        return Icons.build;
    }
  }

  @override
  Widget build(BuildContext context) {
    final filteredDevices = getFilteredDevices();
    final filteredConnections = getFilteredConnections();

    // Device markers
    final deviceMarkers = filteredDevices
        .map((device) => Marker(
              point: device.position,
              width: 50,
              height: 50,
              child: GestureDetector(
                onTap: () => _showDeviceDetails(device),
                child: Tooltip(
                  textStyle: TextStyle(color: AppColorConstants.colorBlackBlue),
                  padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
                  decoration: BoxDecoration(
                      color: AppColorConstants.colorWhite,
                      borderRadius: const BorderRadius.all(Radius.circular(8))),
                  message: _buildTooltipContent(device),
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      if (device.snr > 0)
                        Container(
                          width: 50 + device.snr * 2,
                          height: 50 + device.snr * 2,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.blue.withOpacity(0.08),
                          ),
                        ),
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                          border: Border.all(color: getStatusColor(device.status), width: 3),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.2),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        width: 40,
                        height: 40,
                        child: Icon(
                          getAmpTypeIcon(device.type),
                          color: getAmpTypeColor(device.type),
                          size: 22,
                        ),
                      ),
                      // Health status overlay
                      Positioned(
                        bottom: 2,
                        right: 2,
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.white, // Optional background color
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.black, width: 1), // Black border
                          ),
                          child: Icon(
                            getStatusIcon(device.status),
                            color: getStatusColor(device.status),
                            size: 16,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ))
        .toList();

    // Gateway markers
    final gatewayMarkers = _currentGateways
        .map((gateway) => Marker(
              point: gateway.position,
              width: 60,
              height: 60,
              child: GestureDetector(
                onTap: () => _showGatewayDetails(gateway),
                child: Tooltip(
                  textStyle: TextStyle(color: AppColorConstants.colorBlackBlue),
                  padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
                  decoration: BoxDecoration(
                      color: AppColorConstants.colorWhite,
                      borderRadius: const BorderRadius.all(Radius.circular(8))),
                  message: 'Gateway: ${gateway.name}\nID: ${gateway.id}\nStatus: ${gateway.status.name.toUpperCase()}',
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                      border: Border.all(color: getGatewayStatusColor(gateway.status), width: 4),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.3),
                          blurRadius: 6,
                          offset: const Offset(0, 3),
                        ),
                      ],
                    ),
                    width: 50,
                    height: 50,
                    child: Icon(
                      getGatewayIcon(gateway.status),
                      color: getGatewayStatusColor(gateway.status),
                      size: 28,
                    ),
                  ),
                ),
              ),
            ))
        .toList();

    // Combine all markers
    final allMarkers = [...deviceMarkers, ...gatewayMarkers];

    final polylines = filteredConnections.map((conn) {
      final fromDevice = _currentDevices.firstWhere((d) => d.id == conn.fromId);
      final toDevice = _currentDevices.firstWhere((d) => d.id == conn.toId);

      Color lineColor;
      double strokeWidth;
      List<double> dashArray;

      switch (conn.status) {
        case ConnectionStatus.functioning:
          lineColor = Colors.black;
          strokeWidth = 3.0;
          dashArray = [];
          break;
        case ConnectionStatus.cut:
          lineColor = Colors.red;
          strokeWidth = 3.0;
          dashArray = [];
          break;
        case ConnectionStatus.planned:
          lineColor = Colors.black;
          strokeWidth = 2.0;
          dashArray = [10, 5];
          break;
      }

      return Polyline(
        points: [fromDevice.position, toDevice.position],
        color: lineColor,
        strokeWidth: strokeWidth,
        isDotted: conn.status == ConnectionStatus.planned,
      );
    }).toList();

    return Scaffold(
      body: Container(
        margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
        decoration: BoxDecoration(
          color: AppColorConstants.colorWhite,
          borderRadius: const BorderRadius.only(
            topRight: Radius.circular(8),
            topLeft: Radius.circular(8),
          ),
          border: Border.all(color: AppColorConstants.colorH2, width: 1),
        ),
        child: Stack(
          children: [
            Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(16).copyWith(top: 5),
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                      topRight: Radius.circular(8),
                      topLeft: Radius.circular(8),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 12),
                      // Status filters
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: [
                          FilterChip(
                            label: const Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(Icons.check_circle, color: Colors.green, size: 16),
                                SizedBox(width: 4),
                                Text('Online'),
                              ],
                            ),
                            selected: showOnline,
                            onSelected: (value) => setState(() => showOnline = value),
                            selectedColor: Colors.green[100],
                          ),
                          FilterChip(
                            label: const Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(Icons.offline_bolt, color: Colors.grey, size: 16),
                                SizedBox(width: 4),
                                Text('Offline'),
                              ],
                            ),
                            selected: showOffline,
                            onSelected: (value) => setState(() => showOffline = value),
                            selectedColor: Colors.grey[100],
                          ),
                          FilterChip(
                            label: const Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(Icons.search, color: Colors.blue, size: 16),
                                SizedBox(width: 4),
                                Text('Discovered'),
                              ],
                            ),
                            selected: showDiscovered,
                            onSelected: (value) => setState(() => showDiscovered = value),
                            selectedColor: Colors.blue[100],
                          ),
                          FilterChip(
                            label: const Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(Icons.check_circle, color: Colors.green, size: 16),
                                SizedBox(width: 4),
                                Text('Commissioned'),
                              ],
                            ),
                            selected: showCommissioned,
                            onSelected: (value) => setState(() => showCommissioned = value),
                            selectedColor: Colors.green[100],
                          ),
                        ],
                      ),
                      const SizedBox(height: 15),
                      // Contractor and Zone filters
                      Row(
                        children: [
                          Expanded(
                            child: DropdownButtonFormField<String>(
                              decoration: const InputDecoration(
                                labelText: 'Contractor',
                                border: OutlineInputBorder(),
                                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                              ),
                              value: selectedContractor,
                              items: [
                                const DropdownMenuItem(value: null, child: Text('All Contractors')),
                                ...getAvailableContractors().map((contractor) =>
                                    DropdownMenuItem(value: contractor, child: Text(contractor))),
                              ],
                              onChanged: (value) => setState(() => selectedContractor = value),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: DropdownButtonFormField<String>(
                              decoration: const InputDecoration(
                                labelText: 'Zone',
                                border: OutlineInputBorder(),
                                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                              ),
                              value: selectedZone,
                              items: [
                                const DropdownMenuItem(value: null, child: Text('All Zones')),
                                ...getAvailableZones().map(
                                    (zone) => DropdownMenuItem(value: zone, child: Text(zone))),
                              ],
                              onChanged: (value) => setState(() => selectedZone = value),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      // Legend and Zoom Controls
                      const Row(
                        children: [
                          Expanded(
                            child: Wrap(
                              spacing: 8,
                              runSpacing: 4,
                              children: [
                                Text('Legend: ', style: TextStyle(fontWeight: FontWeight.bold)),
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(Icons.bluetooth, color: Colors.blue, size: 16),
                                    Text(' BLE'),
                                  ],
                                ),
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(Icons.memory, color: Colors.purple, size: 16),
                                    Text(' MB'),
                                  ],
                                ),
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(Icons.electrical_services, color: Colors.teal, size: 16),
                                    Text(' LE'),
                                  ],
                                ),
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(Icons.check_circle, color: Colors.green, size: 16),
                                    Text(' Healthy'),
                                  ],
                                ),
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(Icons.warning, color: Colors.orange, size: 16),
                                    Text(' Warning'),
                                  ],
                                ),
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(Icons.error, color: Colors.red, size: 16),
                                    Text(' Critical'),
                                  ],
                                ),
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(Icons.offline_bolt, color: Colors.grey, size: 16),
                                    Text(' Offline'),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                // Zoom level info bar
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: AppColorConstants.colorBackgroundDark,
                    border: Border(
                      top: BorderSide(color: AppColorConstants.colorDivider.withOpacity(0.3)),
                      bottom: BorderSide(color: AppColorConstants.colorDivider.withOpacity(0.3)),
                    ),
                  ),
                  child: Row(
                    children: [
                      if (_isLoading) ...[
                        const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                        const SizedBox(width: 8),
                        const Text('Loading devices...'),
                      ] else ...[
                        Icon(Icons.zoom_in, size: 16, color: AppColorConstants.colorH3),
                        const SizedBox(width: 8),
                        Text(
                          'Zoom: ${_currentZoom.toStringAsFixed(1)} - ${_zoomManager.getZoomLevelCategory(_currentZoom)}',
                          style: TextStyle(
                            fontSize: 12,
                            color: AppColorConstants.colorH3,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                      const Spacer(),
                      Text(
                        _zoomManager.getDeviceCountInfo(filteredDevices),
                        style: TextStyle(
                          fontSize: 12,
                          color: AppColorConstants.colorH3,
                        ),
                      ),
                    ],
                  ),
                ),
                // Map
                Expanded(
                  child: FlutterMap(
                    mapController: _mapController,
                    options: MapOptions(
                      initialCenter: const LatLng(33.7490, -84.3880), // Atlanta area
                      initialZoom: 13,
                      onPositionChanged: (position, hasGesture) {
                        if (hasGesture && position.zoom != null) {
                          _zoomManager.onMapZoomChanged(position.zoom!);
                        }
                      },
                    ),
                    children: [
                      TileLayer(
                        urlTemplate: "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
                      ),
                      PolylineLayer(polylines: polylines),
                      MarkerClusterLayerWidget(
                        options: MarkerClusterLayerOptions(
                          maxClusterRadius: 45,
                          size: const Size(50, 50),
                          markers: allMarkers,
                          builder: (context, clusterMarkers) {
                            return Container(
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                color: Colors.blue,
                                shape: BoxShape.circle,
                                border: Border.all(color: Colors.white, width: 2),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.3),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Text(
                                '${clusterMarkers.length}',
                                style: const TextStyle(
                                    color: Colors.white, fontWeight: FontWeight.bold),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            Positioned(
              bottom: 10.0,
              right: 10,
              child: zoomInAndOutWidget(),
            ),
          ],
        ),
      ),
    );
  }

  zoomInAndOutWidget() {
    return Column(
      children: [
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            maximumSize: const Size(40, 40),
            minimumSize: const Size(24, 32),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            backgroundColor: Colors.white,
            padding: EdgeInsets.zero,
          ),
          child: const Center(
            child: Icon(Icons.add),
          ),
          onPressed: () async {
            _currentZoom = _mapController.camera.zoom;
            _zoomManager.onMapZoomChanged(_currentZoom);
            final center = _mapController.camera.center;
            _mapController.move(center, _currentZoom + 1);
            setState(() {});
          },
        ),
        const SizedBox(
          height: 16,
        ),
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            maximumSize: const Size(40, 40),
            minimumSize: const Size(24, 32),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            backgroundColor: Colors.white,
            padding: EdgeInsets.zero,
          ),
          child: const Center(
            child: Icon(Icons.remove),
          ),
          onPressed: () async {
            _currentZoom = _mapController.camera.zoom;
            _zoomManager.onMapZoomChanged(_currentZoom);
            final center = _mapController.camera.center;
            _mapController.move(center, _currentZoom - 1);
          },
        ),
      ],
    );
  }

  String _buildTooltipContent(DeviceNode device) {
    return '''
Device: ${device.name}
ID: ${device.id}
Type: ${device.type.name.toUpperCase()}
Status: ${device.status.name.toUpperCase()}
${device.activeAlarms.isNotEmpty ? 'Alarms: ${device.activeAlarms.join(", ")}' : ''}
Last Seen: ${DateFormat(lastSeenFormat).format(device.lastSeen)}
''';
  }

  void _showDeviceDetails(DeviceNode device) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(getStatusIcon(device.status), color: getStatusColor(device.status)),
            const SizedBox(width: 8),
            Expanded(child: Text(device.name)),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDetailRow('Device ID', device.id),
              _buildDetailRow('Type', device.type.name.toUpperCase()),
              _buildDetailRow('Status', device.status.name.toUpperCase()),
              _buildDetailRow('Last Seen', DateFormat(lastSeenFormat).format(device.lastSeen)),
              _buildDetailRow('Temperature', '${device.temperature}°C'),
              _buildDetailRow('LoRa RSSI', '${device.loraRssi} dBm'),
              _buildDetailRow('SNR', '${device.snr} dB'),
              _buildDetailRow('Packet Loss', '${device.packetLoss}%'),
              _buildDetailRow('Contractor', device.contractor),
              _buildDetailRow('Zone', device.zone),
              _buildDetailRow('Commissioned', device.isCommissioned ? 'Yes' : 'No'),
              _buildDetailRow('Discovered', device.isDiscovered ? 'Yes' : 'No'),
              if (device.activeAlarms.isNotEmpty) ...[
                const SizedBox(height: 12),
                const Text('Active Alarms:',
                    style: TextStyle(fontWeight: FontWeight.bold, color: Colors.red)),
                ...device.activeAlarms.map((alarm) => Padding(
                      padding: const EdgeInsets.only(left: 16, top: 4),
                      child: Row(
                        children: [
                          const Icon(Icons.warning, color: Colors.red, size: 16),
                          const SizedBox(width: 8),
                          Expanded(child: Text(alarm)),
                        ],
                      ),
                    )),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  void _showGatewayDetails(GatewayNode gateway) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(getGatewayIcon(gateway.status), color: getGatewayStatusColor(gateway.status)),
            const SizedBox(width: 8),
            Expanded(child: Text(gateway.name)),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDetailRow('Gateway ID', gateway.id),
              _buildDetailRow('Site Name', gateway.name),
              _buildDetailRow('Status', gateway.status.name.toUpperCase()),
              _buildDetailRow('Position', '${gateway.position.latitude.toStringAsFixed(6)}, ${gateway.position.longitude.toStringAsFixed(6)}'),
              _buildDetailRow('Type', 'LoRaWAN Gateway'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
