import 'package:flutter_map/flutter_map.dart';
import 'package:intl/intl.dart';
import 'package:latlong2/latlong.dart';
import 'package:flutter_map_marker_cluster/flutter_map_marker_cluster.dart';
import 'package:quantumlink_node/app_import.dart';

enum DeviceStatus { healthy, warning, critical, offline }

enum ConnectionStatus { functioning, cut, planned }

enum AmplifierType { ble, mb, le }

class DeviceNode {
  final String id;
  final String name;
  final LatLng position;
  final DeviceStatus status;
  final DateTime lastSeen;
  final List<String> activeAlarms;
  final double temperature;
  final double loraRssi;
  final String contractor;
  final bool isCommissioned;
  final bool isDiscovered;
  final String zone;
  final AmplifierType type;
  final double snr;
  final double packetLoss;

  DeviceNode({
    required this.id,
    required this.name,
    required this.position,
    required this.status,
    required this.lastSeen,
    required this.activeAlarms,
    required this.temperature,
    required this.loraRssi,
    required this.contractor,
    required this.isCommissioned,
    required this.isDiscovered,
    required this.zone,
    required this.type,
    required this.snr,
    required this.packetLoss,
  });
}

class TopologyConnection {
  final String fromId;
  final String toId;
  final ConnectionStatus status;

  TopologyConnection({
    required this.fromId,
    required this.toId,
    required this.status,
  });
}

class VlgwMapTopologyView extends StatefulWidget {
  const VlgwMapTopologyView({Key? key}) : super(key: key);

  @override
  State<VlgwMapTopologyView> createState() => _VlgwMapTopologyViewState();
}

class _VlgwMapTopologyViewState extends State<VlgwMapTopologyView> {
  // Filter states
  bool showDiscovered = true;
  bool showCommissioned = true;
  bool showOffline = true;
  bool showOnline = true;
  String? selectedContractor;
  String? selectedZone;

  // Mock device data with zones and types
  final List<DeviceNode> devices = [
    DeviceNode(
      id: "DEV001",
      name: "Quantum18 BLE",
      position: const LatLng(37.7749, -122.4194),
      status: DeviceStatus.healthy,
      lastSeen: DateTime.now().subtract(const Duration(minutes: 5)),
      activeAlarms: [],
      temperature: 45.2,
      loraRssi: -65.0,
      contractor: "TechCorp",
      isCommissioned: true,
      isDiscovered: true,
      zone: "Zone A",
      type: AmplifierType.ble,
      snr: 12.5,
      packetLoss: 0.5,
    ),
    DeviceNode(
      id: "DEV002",
      name: "Quantum18 MB",
      position: const LatLng(37.7849, -122.4294),
      status: DeviceStatus.warning,
      lastSeen: DateTime.now().subtract(const Duration(minutes: 15)),
      activeAlarms: ["High Temperature"],
      temperature: 78.5,
      loraRssi: -72.0,
      contractor: "TechCorp",
      isCommissioned: true,
      isDiscovered: true,
      zone: "Zone A",
      type: AmplifierType.mb,
      snr: 8.0,
      packetLoss: 1.2,
    ),
    DeviceNode(
      id: "DEV003",
      name: "Quantum18 LE",
      position: const LatLng(37.7799, -122.4194),
      status: DeviceStatus.critical,
      lastSeen: DateTime.now().subtract(const Duration(hours: 2)),
      activeAlarms: ["Input Overvoltage", "Communication Loss"],
      temperature: 95.0,
      loraRssi: -85.0,
      contractor: "NetBuild",
      isCommissioned: false,
      isDiscovered: true,
      zone: "Zone B",
      type: AmplifierType.le,
      snr: 5.0,
      packetLoss: 3.5,
    ),
    DeviceNode(
      id: "DEV004",
      name: "Quantum18 BLE",
      position: const LatLng(37.7699, -122.4294),
      status: DeviceStatus.offline,
      lastSeen: DateTime.now().subtract(const Duration(days: 1)),
      activeAlarms: ["Power Loss"],
      temperature: 0.0,
      loraRssi: -100.0,
      contractor: "NetBuild",
      isCommissioned: false,
      isDiscovered: false,
      zone: "Zone B",
      type: AmplifierType.ble,
      snr: 0.0,
      packetLoss: 10.0,
    ),
    DeviceNode(
      id: "DEV005",
      name: "Quantum18 MB",
      position: const LatLng(37.7649, -122.4194),
      status: DeviceStatus.healthy,
      lastSeen: DateTime.now().subtract(const Duration(minutes: 2)),
      activeAlarms: [],
      temperature: 42.1,
      loraRssi: -68.0,
      contractor: "TechCorp",
      isCommissioned: true,
      isDiscovered: false,
      zone: "Zone C",
      type: AmplifierType.mb,
      snr: 15.0,
      packetLoss: 0.2,
    ),
    DeviceNode(
      id: "DEV006",
      name: "Quantum18 LE",
      position: const LatLng(37.7899, -122.4094),
      status: DeviceStatus.warning,
      lastSeen: DateTime.now().subtract(const Duration(minutes: 30)),
      activeAlarms: ["Low Battery"],
      temperature: 52.3,
      loraRssi: -75.0,
      contractor: "TechCorp",
      isCommissioned: true,
      isDiscovered: true,
      zone: "Zone C",
      type: AmplifierType.le,
      snr: 10.0,
      packetLoss: 2.0,
    ),
    DeviceNode(
      id: "DEV007",
      name: "Quantum18 BLE",
      position: const LatLng(37.7900, -122.4050),
      status: DeviceStatus.healthy,
      lastSeen: DateTime.now().subtract(const Duration(minutes: 3)),
      activeAlarms: [],
      temperature: 41.0,
      loraRssi: -60.0,
      contractor: "FiberNet",
      isCommissioned: true,
      isDiscovered: true,
      zone: "Zone D",
      type: AmplifierType.ble,
      snr: 13.0,
      packetLoss: 0.3,
    ),
    DeviceNode(
      id: "DEV0078",
      name: "Quantum18 BLE",
      position: const LatLng(37.7949, -122.3894), // ~3.5 km NE
      status: DeviceStatus.healthy,
      lastSeen: DateTime.now().subtract(const Duration(minutes: 3)),
      activeAlarms: [],
      temperature: 41.0,
      loraRssi: -60.0,
      contractor: "FiberNet",
      isCommissioned: true,
      isDiscovered: true,
      zone: "Zone D",
      type: AmplifierType.ble,
      snr: 13.0,
      packetLoss: 0.3,
    ),
    DeviceNode(
      id: "DEV008",
      name: "Quantum18 MB",
      position: const LatLng(37.8149, -122.3694), // ~5.6 km NE
      status: DeviceStatus.warning,
      lastSeen: DateTime.now().subtract(const Duration(minutes: 20)),
      activeAlarms: ["Temperature Fluctuation"],
      temperature: 65.5,
      loraRssi: -70.0,
      contractor: "WaveTech",
      isCommissioned: false,
      isDiscovered: true,
      zone: "Zone D",
      type: AmplifierType.mb,
      snr: 9.5,
      packetLoss: 1.8,
    ),
    DeviceNode(
      id: "DEV009",
      name: "Quantum18 LE",
      position: const LatLng(37.8349, -122.3494), // ~7 km NE
      status: DeviceStatus.critical,
      lastSeen: DateTime.now().subtract(const Duration(hours: 3)),
      activeAlarms: ["Hardware Fault"],
      temperature: 98.2,
      loraRssi: -88.0,
      contractor: "SkyNet",
      isCommissioned: false,
      isDiscovered: false,
      zone: "Zone E",
      type: AmplifierType.le,
      snr: 4.0,
      packetLoss: 4.5,
    ),
    DeviceNode(
      id: "DEV010",
      name: "Quantum18 BLE",
      position: const LatLng(37.8549, -122.3294), // ~8.4 km NE
      status: DeviceStatus.offline,
      lastSeen: DateTime.now().subtract(const Duration(days: 2)),
      activeAlarms: ["Power Failure", "No Signal"],
      temperature: 0.0,
      loraRssi: -105.0,
      contractor: "TechCorp",
      isCommissioned: false,
      isDiscovered: false,
      zone: "Zone E",
      type: AmplifierType.ble,
      snr: 0.0,
      packetLoss: 9.8,
    ),
    DeviceNode(
      id: "DEV011",
      name: "Quantum18 MB",
      position: const LatLng(37.8888, -122.4140),
      status: DeviceStatus.healthy,
      lastSeen: DateTime.now().subtract(const Duration(minutes: 7)),
      activeAlarms: [],
      temperature: 43.6,
      loraRssi: -66.0,
      contractor: "WaveTech",
      isCommissioned: true,
      isDiscovered: true,
      zone: "Zone F",
      type: AmplifierType.mb,
      snr: 14.0,
      packetLoss: 0.4,
    ),
    DeviceNode(
      id: "DEV012",
      name: "Quantum18 LE",
      position: const LatLng(37.7698, -122.4210),
      status: DeviceStatus.warning,
      lastSeen: DateTime.now().subtract(const Duration(minutes: 50)),
      activeAlarms: ["Voltage Irregularity"],
      temperature: 67.8,
      loraRssi: -74.0,
      contractor: "FiberNet",
      isCommissioned: false,
      isDiscovered: true,
      zone: "Zone F",
      type: AmplifierType.le,
      snr: 6.8,
      packetLoss: 2.5,
    ),
    DeviceNode(
      id: "DEV013",
      name: "Quantum18 BLE",
      position: const LatLng(37.7996, -122.4120),
      status: DeviceStatus.critical,
      lastSeen: DateTime.now().subtract(const Duration(hours: 5)),
      activeAlarms: ["Communication Timeout"],
      temperature: 91.3,
      loraRssi: -90.0,
      contractor: "SkyNet",
      isCommissioned: false,
      isDiscovered: false,
      zone: "Zone G",
      type: AmplifierType.ble,
      snr: 3.2,
      packetLoss: 5.9,
    ),
    DeviceNode(
      id: "DEV014",
      name: "Quantum18 MB",
      position: const LatLng(37.7710, -122.4060),
      status: DeviceStatus.offline,
      lastSeen: DateTime.now().subtract(const Duration(days: 1, hours: 3)),
      activeAlarms: ["System Unresponsive"],
      temperature: 0.0,
      loraRssi: -110.0,
      contractor: "NetBuild",
      isCommissioned: false,
      isDiscovered: false,
      zone: "Zone G",
      type: AmplifierType.mb,
      snr: 0.0,
      packetLoss: 11.0,
    ),
  ];


  // Mock topology connections
  final List<TopologyConnection> connections = [
    TopologyConnection(fromId: "DEV001", toId: "DEV002", status: ConnectionStatus.functioning),
    TopologyConnection(fromId: "DEV002", toId: "DEV003", status: ConnectionStatus.cut),
    TopologyConnection(fromId: "DEV003", toId: "DEV004", status: ConnectionStatus.planned),
    TopologyConnection(fromId: "DEV004", toId: "DEV005", status: ConnectionStatus.functioning),
    TopologyConnection(fromId: "DEV005", toId: "DEV006", status: ConnectionStatus.functioning),
    TopologyConnection(fromId: "DEV001", toId: "DEV006", status: ConnectionStatus.planned),
  ];

  Color getStatusColor(DeviceStatus status) {
    switch (status) {
      case DeviceStatus.healthy:
        return Colors.green;
      case DeviceStatus.warning:
        return Colors.orange;
      case DeviceStatus.critical:
        return Colors.red;
      case DeviceStatus.offline:
        return Colors.grey;
    }
  }

  IconData getStatusIcon(DeviceStatus status) {
    switch (status) {
      case DeviceStatus.healthy:
        return Icons.check_circle;
      case DeviceStatus.warning:
        return Icons.warning;
      case DeviceStatus.critical:
        return Icons.error;
      case DeviceStatus.offline:
        return Icons.offline_bolt;
    }
  }

  IconData getAmpTypeIcon(AmplifierType type) {
    switch (type) {
      case AmplifierType.ble:
        return Icons.bluetooth;
      case AmplifierType.mb:
        return Icons.memory;
      case AmplifierType.le:
        return Icons.electrical_services;
    }
  }

  Color getAmpTypeColor(AmplifierType type) {
    switch (type) {
      case AmplifierType.ble:
        return Colors.blue;
      case AmplifierType.mb:
        return Colors.purple;
      case AmplifierType.le:
        return Colors.teal;
    }
  }

  List<DeviceNode> getFilteredDevices() {
    return devices.where((device) {
      if (selectedContractor != null && device.contractor != selectedContractor) {
        return false;
      }
      if (selectedZone != null && device.zone != selectedZone) {
        return false;
      }

      if (!showOffline && device.status == DeviceStatus.offline) return false;
      if (!showOnline && device.status != DeviceStatus.offline) return false;
      if (!showDiscovered && device.isDiscovered) return false;
      if (!showCommissioned && device.isCommissioned) return false;

      return true;
    }).toList();
  }

  List<TopologyConnection> getFilteredConnections() {
    return connections.where((conn) {
      final fromDevice = devices.firstWhere((d) => d.id == conn.fromId);
      final toDevice = devices.firstWhere((d) => d.id == conn.toId);
      if (selectedContractor != null) {
        if (fromDevice.contractor != selectedContractor ||
            toDevice.contractor != selectedContractor) {
          return false;
        }
      }
      if (selectedZone != null) {
        if (fromDevice.zone != selectedZone || toDevice.zone != selectedZone) {
          return false;
        }
      }
      return true;
    }).toList();
  }

  List<String> getAvailableZones() {
    return devices.map((d) => d.zone).toSet().toList()..sort();
  }

  List<String> getAvailableContractors() {
    return devices.map((d) => d.contractor).toSet().toList()..sort();
  }

  @override
  Widget build(BuildContext context) {
    final filteredDevices = getFilteredDevices();
    final filteredConnections = getFilteredConnections();

    final markers = filteredDevices
        .map((device) => Marker(
              point: device.position,
              width: 50,
              height: 50,
              child: GestureDetector(
                onTap: () => _showDeviceDetails(device),
                child: Tooltip(textStyle: TextStyle(color: AppColorConstants.colorBlackBlue),
                  padding: const EdgeInsets.symmetric(horizontal: 15,vertical: 15),
                  decoration: BoxDecoration(
                      color: AppColorConstants.colorWhite,
                      borderRadius: const BorderRadius.all(Radius.circular(8))),
                  message: _buildTooltipContent(device),
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      if (device.snr > 0)
                        Container(
                          width: 50 + device.snr * 2,
                          height: 50 + device.snr * 2,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.blue.withOpacity(0.08),
                          ),
                        ),
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                          border: Border.all(color: getStatusColor(device.status), width: 3),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.2),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        width: 40,
                        height: 40,
                        child: Icon(
                          getAmpTypeIcon(device.type),
                          color: getAmpTypeColor(device.type),
                          size: 22,
                        ),
                      ),
                      // Health status overlay
                      Positioned(
                        bottom: 2,
                        right: 2,
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.white, // Optional background color
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.black, width: 1), // Black border
                          ),
                          child: Icon(
                            getStatusIcon(device.status),
                            color: getStatusColor(device.status),
                            size: 16,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ))
        .toList();

    final polylines = filteredConnections.map((conn) {
      final fromDevice = devices.firstWhere((d) => d.id == conn.fromId);
      final toDevice = devices.firstWhere((d) => d.id == conn.toId);

      Color lineColor;
      double strokeWidth;
      List<double> dashArray;

      switch (conn.status) {
        case ConnectionStatus.functioning:
          lineColor = Colors.black;
          strokeWidth = 3.0;
          dashArray = [];
          break;
        case ConnectionStatus.cut:
          lineColor = Colors.red;
          strokeWidth = 3.0;
          dashArray = [];
          break;
        case ConnectionStatus.planned:
          lineColor = Colors.black;
          strokeWidth = 2.0;
          dashArray = [10, 5];
          break;
      }

      return Polyline(
        points: [fromDevice.position, toDevice.position],
        color: lineColor,
        strokeWidth: strokeWidth,
        isDotted: conn.status == ConnectionStatus.planned,
      );
    }).toList();

    return Scaffold(
      body: Container(
        margin: const EdgeInsets.symmetric(horizontal: 15,vertical: 8),
        decoration: BoxDecoration(
          color: AppColorConstants.colorWhite,
          borderRadius: const BorderRadius.only(
            topRight: Radius.circular(8),
            topLeft: Radius.circular(8),
          ),
          border: Border.all(color: AppColorConstants.colorH2, width: 1),
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16).copyWith(top: 5),
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(8),
                  topLeft: Radius.circular(8),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 12),
                  // Status filters
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: [
                      FilterChip(
                        label: const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.check_circle, color: Colors.green, size: 16),
                            SizedBox(width: 4),
                            Text('Online'),
                          ],
                        ),
                        selected: showOnline,
                        onSelected: (value) => setState(() => showOnline = value),
                        selectedColor: Colors.green[100],
                      ),
                      FilterChip(
                        label: const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.offline_bolt, color: Colors.grey, size: 16),
                            SizedBox(width: 4),
                            Text('Offline'),
                          ],
                        ),
                        selected: showOffline,
                        onSelected: (value) => setState(() => showOffline = value),
                        selectedColor: Colors.grey[100],
                      ),
                      FilterChip(
                        label: const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.search, color: Colors.blue, size: 16),
                            SizedBox(width: 4),
                            Text('Discovered'),
                          ],
                        ),
                        selected: showDiscovered,
                        onSelected: (value) => setState(() => showDiscovered = value),
                        selectedColor: Colors.blue[100],
                      ),
                      FilterChip(
                        label: const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.check_circle, color: Colors.green, size: 16),
                            SizedBox(width: 4),
                            Text('Commissioned'),
                          ],
                        ),
                        selected: showCommissioned,
                        onSelected: (value) => setState(() => showCommissioned = value),
                        selectedColor: Colors.green[100],
                      ),
                    ],
                  ),
                  const SizedBox(height: 15),
                  // Contractor and Zone filters
                  Row(
                    children: [
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          decoration: const InputDecoration(
                            labelText: 'Contractor',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          ),
                          value: selectedContractor,
                          items: [
                            const DropdownMenuItem(value: null, child: Text('All Contractors')),
                            ...getAvailableContractors().map((contractor) =>
                                DropdownMenuItem(value: contractor, child: Text(contractor))),
                          ],
                          onChanged: (value) => setState(() => selectedContractor = value),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          decoration: const InputDecoration(
                            labelText: 'Zone',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          ),
                          value: selectedZone,
                          items: [
                            const DropdownMenuItem(value: null, child: Text('All Zones')),
                            ...getAvailableZones()
                                .map((zone) => DropdownMenuItem(value: zone, child: Text(zone))),
                          ],
                          onChanged: (value) => setState(() => selectedZone = value),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  // Legend
                  const Row(
                    children: [
                      Text('Legend: ', style: TextStyle(fontWeight: FontWeight.bold)),
                      SizedBox(width: 8),
                      Row(
                        children: [
                          Icon(Icons.bluetooth, color: Colors.blue, size: 16),
                          Text(' BLE'),
                          SizedBox(width: 8),
                          Icon(Icons.memory, color: Colors.purple, size: 16),
                          Text(' MB'),
                          SizedBox(width: 8),
                          Icon(Icons.electrical_services, color: Colors.teal, size: 16),
                          Text(' LE'),
                          SizedBox(width: 12),
                          Icon(Icons.check_circle, color: Colors.green, size: 16),
                          Text(' Healthy'),
                          SizedBox(width: 12),
                          Icon(Icons.warning, color: Colors.orange, size: 16),
                          Text(' Warning'),
                          SizedBox(width: 12),
                          Icon(Icons.error, color: Colors.red, size: 16),
                          Text(' Critical'),
                          SizedBox(width: 12),
                          Icon(Icons.offline_bolt, color: Colors.grey, size: 16),
                          Text(' Offline'),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
            // Map
            Expanded(
              child: FlutterMap(
                options: MapOptions(
                  initialCenter: devices.first.position,
                  initialZoom: 13,
                ),
                children: [
                  TileLayer(
                    urlTemplate: "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
                  ),
                  PolylineLayer(polylines: polylines),
                  MarkerClusterLayerWidget(
                    options: MarkerClusterLayerOptions(
                      maxClusterRadius: 45,
                      size: const Size(50, 50),
                      markers: markers,
                      builder: (context, clusterMarkers) {
                        return Container(
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: Colors.blue,
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.white, width: 2),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.3),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Text(
                            '${clusterMarkers.length}',
                            style:
                                const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _buildTooltipContent(DeviceNode device) {
    return '''
Device: ${device.name}
ID: ${device.id}
Type: ${device.type.name.toUpperCase()}
Status: ${device.status.name.toUpperCase()}
${device.activeAlarms.isNotEmpty ? 'Alarms: ${device.activeAlarms.join(", ")}' : ''}
Last Seen: ${DateFormat(lastSeenFormat).format(device.lastSeen)}
''';
  }
  void _showDeviceDetails(DeviceNode device) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(getStatusIcon(device.status), color: getStatusColor(device.status)),
            const SizedBox(width: 8),
            Expanded(child: Text(device.name)),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDetailRow('Device ID', device.id),
              _buildDetailRow('Type', device.type.name.toUpperCase()),
              _buildDetailRow('Status', device.status.name.toUpperCase()),
              _buildDetailRow('Last Seen', DateFormat(lastSeenFormat).format(device.lastSeen)),
              _buildDetailRow('Temperature', '${device.temperature}°C'),
              _buildDetailRow('LoRa RSSI', '${device.loraRssi} dBm'),
              _buildDetailRow('SNR', '${device.snr} dB'),
              _buildDetailRow('Packet Loss', '${device.packetLoss}%'),
              _buildDetailRow('Contractor', device.contractor),
              _buildDetailRow('Zone', device.zone),
              _buildDetailRow('Commissioned', device.isCommissioned ? 'Yes' : 'No'),
              _buildDetailRow('Discovered', device.isDiscovered ? 'Yes' : 'No'),
              if (device.activeAlarms.isNotEmpty) ...[
                const SizedBox(height: 12),
                const Text('Active Alarms:',
                    style: TextStyle(fontWeight: FontWeight.bold, color: Colors.red)),
                ...device.activeAlarms.map((alarm) => Padding(
                      padding: const EdgeInsets.only(left: 16, top: 4),
                      child: Row(
                        children: [
                          const Icon(Icons.warning, color: Colors.red, size: 16),
                          const SizedBox(width: 8),
                          Expanded(child: Text(alarm)),
                        ],
                      ),
                    )),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}
