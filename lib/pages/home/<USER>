// ignore_for_file: deprecated_member_use

import 'package:json_view/json_view.dart';
import 'package:quantumlink_node/app_import.dart';
class HomePage extends StatefulWidget {
  final Widget child;
  final Uri uri;

  const HomePage({super.key, required this.child, required this.uri});

  @override
  State<HomePage> createState() => HomePageState();
}

class HomePageState extends State<HomePage> with TickerProviderStateMixin {
  HomePageHelper? homePageHelper;
  late ScreenLayoutType screenLayoutType;
  late HomeController homeController;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    homePageHelper ?? (homePageHelper = HomePageHelper(this));
    buildContext = context;
    if (AppConfig.shared.isQLCentral) {
      refreshCentralIndex(widget.uri.path);
    } else {
      refreshNodeIndex(widget.uri.path);
    }

    debugLogs("pageIndex -> ${homePageHelper!.pageIndex}");
    return GetBuilder<HomeController>(
      init: HomeController(),
      builder: (HomeController controller) {
        homeController = controller;
        return ScreenLayoutTypeBuilder(builder: (context, screenType, constraints) {
          screenLayoutType = screenType;
          return AnnotatedRegion<SystemUiOverlayStyle>(
            value: getAppStatusBar(),
            child: Scaffold(
              key: scaffoldKey,
              endDrawer: jsonTreeEndDrawerView(),
              drawer: !AppConfig.shared.isOpenFromBLE ? getDrawer() : null,
              backgroundColor: AppColorConstants.colorBackgroundDark,
              drawerScrimColor: AppColorConstants.colorH1.withOpacity(0.5),
              appBar: AppConfig.shared.isOpenFromBLE
                  ? null
                  : getDefaultAppBarWithAction(
                ssidText: homePageHelper?.connectedSsid['connected_ssid'] ?? '',
                wifiIconColor: homePageHelper?.isConnectedWithInternet == true ? AppColorConstants
                    .colorGreen : AppColorConstants.colorH1Grey,
                leadingWidth: screenLayoutType == ScreenLayoutType.desktop ? 225 : 56,
                AppConfig.shared.appName,
                profileFunction: () => goOtherTab(context, RouteHelper.userInfoPage, 9),
                // wifiConnectionFunction: () {
                //   AppConfig.shared.isCheckedWifi = false;
                //   return gotoSSIdReportPage();
                // },
                homePageHelper!.userProfileName.isEmpty
                    ? const Icon(Icons.person)
                    : AppText(homePageHelper!.userProfileName, isSelectableText: false),
                context,
                leading: screenLayoutType == ScreenLayoutType.desktop
                    ? InkWell(
                  onTap: () => goOtherTab(context, RouteHelper.routeDashboard, 0),
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 15, vertical: 16),
                    child: AppImageAsset(
                      image: AppAssetsConstants.appBar,
                      fit: BoxFit.fitHeight,
                    ),
                  ),
                )
                    : IconButton(
                  icon: Icon(
                    Icons.menu,
                    color: AppColorConstants.colorWhite,
                  ),
                  onPressed: () => scaffoldKey.currentState!.openDrawer(),
                ),
                notificationFunction: () {},
                helpFunction: () {},
                ipText: homePageHelper?.connectedSsid['ip'] ?? '',
              ),
              body: Stack(
                children: [
                  getHomeView(),
                  getHealthCheckView(),
                ],
              ),
            ),
          );
        });
      },
    );
  }

  AppBar nodeGwAppBar() {
    return AppBar(
      backgroundColor: AppColorConstants.colorWhite,
      surfaceTintColor: AppColorConstants.colorTransparent,
      toolbarHeight: 45,
      leading: InkWell(onTap: () => scaffoldKey.currentState!.openDrawer(),
        child: Padding(
          padding: const EdgeInsets.all(10),
          child: AppImageAsset(
            width: 23,
            image: AppAssetsConstants.openMenuIcon,
            color: AppColorConstants.colorGreen3,
            // color: AppColorConstants.colorH1,
          ),
        ),
      ),
      actions: [
        GestureDetector(
          onTap: () => goOtherTab(context, RouteHelper.userInfoPage, 9),
          child: MouseRegion(
            cursor: SystemMouseCursors.click, // Change cursor type here
            child: Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: AppColorConstants.colorPrimary, // Border color
                  width: 2, // Border width
                ),
              ),
              child: CircleAvatar(
                  maxRadius: 12,
                  backgroundColor: AppColorConstants.colorWhite,
                  child: homePageHelper!.userProfileName.isEmpty
                      ? const Icon(Icons.person)
                      : AppText(homePageHelper!.userProfileName,
                      style: TextStyle(fontSize: 13), isSelectableText: false)),
            ),
          ),
        ).paddingOnly(right: 10)
      ],
    );
  }

  Widget getHealthCheckView() {
    return StreamBuilder<bool>(
        stream: AppConfig.shared.apiHealthCheckStreamView,
        builder: (context, snapshot) {
          if (snapshot.data != null && snapshot.data == false) {
            return SizedBox(
              height: 30,
              child: AnimatedContainer(
                  curve: Curves.easeInOut,
                  decoration: BoxDecoration(
                    color: AppColorConstants.colorRedLight,
                  ),
                  duration: const Duration(milliseconds: 100),
                  height: 30,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.error_outline,
                          color: AppColorConstants.colorWhite, size: 20),
                      const SizedBox(width: 5),
                      AppText(
                        "Please Wait - Starting Services",
                        style: TextStyle(
                            fontWeight: getMediumFontWeight(),
                            fontSize: getSize(14),
                            fontFamily: AppAssetsConstants.openSans,
                            color: AppColorConstants.colorWhite),
                      ),
                      Padding(
                          padding: const EdgeInsets.only(left: 10),
                          child: AppRefresh(
                            buttonColor: AppColorConstants.colorWhite,
                            loadingStatus: homePageHelper!.apiStatus,
                            onPressed: () async {
                              homePageHelper!.apiStatus = ApiStatus.loading;
                              homeController.update();
                              await RestHelper.instance.apiHealthCheck(context, null);
                              homePageHelper!.apiStatus = ApiStatus.success;
                              homeController.update();
                            },
                          ))
                    ],
                  )),
            );
          } else {
            return Container();
          }
        });
  }

  Drawer jsonTreeEndDrawerView() {
    return Drawer(
      width: 550,
      backgroundColor: AppColorConstants.colorBackground,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(0)),
      child: ValueListenableBuilder<Map<String, dynamic>>(
          valueListenable: homeController.jsonDataListener,
          builder: (context, jsonData, child) {
            return Column(
              children: [
                jsonConfigHeaderView(jsonData: jsonData),
                jsonConfigView(jsonData: jsonData),
              ],
            );
          }),
    );
  }

  Container jsonConfigHeaderView({required Map<String, dynamic> jsonData}) {
    return Container(
      height: 90,
      decoration: BoxDecoration(
          border: Border(
              bottom: BorderSide(
                  color: AppColorConstants.colorChart, width: 0.6))),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            children: [
              IconButton(
                  onPressed: () {
                    scaffoldKey.currentState!.closeEndDrawer();
                  },
                  icon: Icon(
                    Icons.close,
                    color: AppColorConstants.colorGray,
                  )),
              AppText(
                "${S
                    .of(context)
                    .details}: ${getUtcTimeZone(jsonData['time'], dateFormat: formatter)}",
                style: TextStyle(
                    color: AppColorConstants.colorBlack,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    fontFamily: AppAssetsConstants.notoSans),
              ),
              const Spacer(),
              if(screenLayoutType == ScreenLayoutType.desktop)
                MouseRegion(
                  onEnter: (_) {
                    homePageHelper!.isHovered = true;
                    homeController.update();
                  },
                  onExit: (_) {
                    homePageHelper!.isHovered = false;
                    homeController.update();
                  },
                  child: AppButton(
                      buttonName: S
                          .of(context)
                          .download,
                      onPressed: () async {
                        homePageHelper!.downloadJsonFile(jsonData);
                      },
                      buttonHeight: 30,
                      padding: MaterialStateProperty.all(
                          const EdgeInsets.symmetric(
                              horizontal: 10, vertical: 5)),
                      buttonWidth: 80,
                      buttonRadius: 5,
                      buttonColor: AppColorConstants.colorBackground,
                      borderColor: homePageHelper!.isHovered
                          ? AppColorConstants.colorBlue
                          : AppColorConstants.colorChart,
                      fontFamily: AppAssetsConstants.openSans,
                      fontColor: homePageHelper!.isHovered
                          ? AppColorConstants.colorBlue
                          : AppColorConstants.colorBlack,
                      fontSize: 14.5),
                ),
              const SizedBox(width: 20)
            ],
          ),
          if(screenLayoutType == ScreenLayoutType.mobile)
            Padding(
              padding: const EdgeInsets.only(left: 8.0),
              child: MouseRegion(
                onEnter: (_) {
                  homePageHelper!.isHovered = true;
                  homeController.update();
                },
                onExit: (_) {
                  homePageHelper!.isHovered = false;
                  homeController.update();
                },
                child: AppButton(
                    buttonName: S
                        .of(context)
                        .download,
                    onPressed: () async {
                      homePageHelper!.downloadJsonFile(jsonData);
                    },
                    buttonHeight: 30,
                    padding: MaterialStateProperty.all(
                        const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 5)),
                    buttonWidth: 80,
                    buttonRadius: 5,
                    buttonColor: AppColorConstants.colorBackground,
                    borderColor: homePageHelper!.isHovered
                        ? AppColorConstants.colorBlue
                        : AppColorConstants.colorChart,
                    fontFamily: AppAssetsConstants.openSans,
                    fontColor: homePageHelper!.isHovered
                        ? AppColorConstants.colorBlue
                        : AppColorConstants.colorBlack,
                    fontSize: 14.5),
              ),
            ),
        ],
      ),
    );
  }

  Widget jsonConfigView({required Map<String, dynamic> jsonData}) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.only(top: 30, left: 20),
        child: JsonConfig(
          data: JsonConfigData(
            gap: 100,
            style: const JsonStyleScheme(
                keysStyle: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    fontFamily: AppAssetsConstants.openSans),
                valuesStyle: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    fontFamily: AppAssetsConstants.openSans),
                openAtStart: true,
                quotation: JsonQuotation.same('"'),
                arrow: Icon(Icons.arrow_right, size: 20)),
            color: JsonColorScheme(
              normalColor: AppColorConstants.colorBlueLight,
              numColor: AppColorConstants.colorOrangeDark,
              boolColor: AppColorConstants.colorOrangeDark,
              stringColor: AppColorConstants.colorGreen,
              nullColor: AppColorConstants.colorRedAccent,
            ),
          ),
          child: JsonView(json: jsonData['body']),
        ),
      ),
    );
  }

  Widget getHomeView() {
    return Row(
      children: [
        if (!AppConfig.shared.isOpenFromBLE && screenLayoutType != ScreenLayoutType.mobile) getDrawer(),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                  child: Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: screenLayoutType == ScreenLayoutType.desktop
                              ? getSize(4)
                              : getSize(0)),
                      child: Container(
                        color:  Theme.of(context).scaffoldBackgroundColor,
                        child: widget.child,
                      )))
            ],
          ),
        ),
      ],
    );
  }

  SideMenuItemDataTile sideMenuView({required int indexOf,
    required Function() onTap,
    required String title,
    required String iconImage, double ?padding, BoxFit? fit}) {
    return SideMenuItemDataTile(
      margin: EdgeInsetsDirectional.symmetric(horizontal: getSize(12), vertical: getSize(5)),
      hasSelectedLine: false,
      selectedTitleStyle: TextStyle(
          color: AppColorConstants.colorLightBlue,
          fontWeight: FontWeight.w600,
          fontSize: getSize(13.5),
          fontFamily: AppAssetsConstants.roboto),
      isSelected: homePageHelper!.pageIndex == indexOf,
      onTap: onTap,
      title: title,
      hoverColor: AppColorConstants.colorLightBlue1,
      titleStyle: TextStyle(
          color: AppColorConstants.colorH1,
          fontWeight: FontWeight.w600,
          fontSize: getSize(13.5),
          fontFamily: AppAssetsConstants.roboto),
      icon: Padding(
        padding: EdgeInsets.all(padding ?? getSize(8)),
        child: AppImageAsset(
          color: AppColorConstants.colorGreen3,
          image: iconImage,
          fit: fit ?? BoxFit.cover,
        ),
      ),
      selectedIcon: Padding(
        padding: EdgeInsets.all(padding ?? getSize(8)),
        child: AppImageAsset(
          color: AppColorConstants.colorLightBlue,
          image: iconImage,
          fit: fit ?? BoxFit.cover,
        ),
      ),
      borderRadius: const BorderRadius.all(Radius.circular(1)),
      highlightSelectedColor: AppColorConstants.colorLightBlue.withOpacity(0.2),
    );
  }

  SideMenuItemDataTile disableSideMenuView({required int indexOf,
    required Function() onTap,
    required String title,
    required String iconImage}) {
    return SideMenuItemDataTile(
      margin: EdgeInsetsDirectional.symmetric(horizontal: getSize(12), vertical: getSize(5)),
      hasSelectedLine: false,
      isSelected: homePageHelper!.pageIndex == indexOf,
      onTap: onTap,
      title: title,
      titleStyle: TextStyle(
          color: AppColorConstants.colorH1.withOpacity(0.5),
          fontWeight: FontWeight.w500,
          fontSize: getSize(13.5),
          fontFamily: AppAssetsConstants.roboto),
      icon: Padding(
        padding: EdgeInsets.all(getSize(9)),
        child: AppImageAsset(
          color: AppColorConstants.colorH1.withOpacity(0.7),
          image: iconImage,
          fit: BoxFit.cover,
        ),
      ),
      selectedIcon: Padding(
        padding: EdgeInsets.all(getSize(9)),
        child: AppImageAsset(
          color: AppColorConstants.colorH1.withOpacity(0.5),
          image: iconImage,
          fit: BoxFit.cover,
        ),
      ),
      borderRadius: const BorderRadius.all(Radius.circular(1)),
    );
  }

  Widget getDrawer() {
    return SideMenu(
      hasResizer: false,
      maxWidth: 220,
      minWidth: 80,
      //resizerData: ResizerData(resizerColor: AppColorConstants.colorBackground, resizerWidth: getSize(2)),
      hasResizerToggle: false,
      controller: homePageHelper!.sideMenuController,
      mode: SideMenuMode.open,
      backgroundColor: AppColorConstants.colorWhite,
      builder: (data) {
        return SideMenuData(
          footer: Padding(
            padding: const EdgeInsets.only(bottom: 40),
            child: homePageHelper!.isDrawerOpen ? buildVersionTextView() : Container(),
          ),
          header: (screenLayoutType != ScreenLayoutType.mobile)
              ? InkWell(
            onTap: () {
              homePageHelper!.sideMenuController.toggle();
              homePageHelper!.isDrawerOpen = !homePageHelper!.isDrawerOpen;
              homeController.update();
            },
            child: Align(
              alignment:
              homePageHelper!.isDrawerOpen ? Alignment.centerRight : Alignment.center,
              child: Padding(
                padding: const EdgeInsets.only(top: 30, right: 15, left: 15, bottom: 10),
                child: AppImageAsset(
                  width: 23,
                  image: homePageHelper!.isDrawerOpen
                      ? AppAssetsConstants.closeMenuIcon
                      : AppAssetsConstants.openMenuIcon,
                  color: AppColorConstants.colorGreen3,
                  // color: AppColorConstants.colorH1,
                ),
              ),
            ),
          )
              : SizedBox(height: getSize(10)),
          /* Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Padding(
                padding: EdgeInsets.all(getSize(10)),
                child: AppImageAsset(
                  height: getSize(90),
                  image: AppAssetsConstants.homeAppLogo,
                  fit: BoxFit.fill,
                ),
              ),
              getHomeAppDivider(),
            ],
          ),*/
          items: (AppConfig.shared.isQLCentral)
              ? centralSideMenuView()
              : nodeSideMenuView(),
          //footer: const Text('Footer'),
        );
      },
    );
  }

  FutureBuilder<String> buildVersionTextView() {
    return FutureBuilder<String>(
      future: homePageHelper!.getVersionNumber(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return AppText(S
              .of(context)
              .loading,
              style: TextStyle(
                  color: AppColorConstants.colorH2,
                  fontSize: 11,
                  fontFamily: AppAssetsConstants.openSans,
                  fontWeight: FontWeight.w600));
        } else if (snapshot.hasError) {
          return AppText('Error: ${snapshot.error}',
              style: TextStyle(
                  color: AppColorConstants.colorH2,
                  fontSize: 11,
                  fontFamily: AppAssetsConstants.openSans,
                  fontWeight: FontWeight.w600));
        } else {
          homePageHelper!.versionNumber = snapshot.data!;
          return getVersionsWidgets();
        }
      },
    );
  }

  Widget getVersionsWidgets() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        AppText("${S
            .of(context)
            .version} ${homePageHelper!.versionNumber}",
            style: TextStyle(
                color: AppColorConstants.colorH3,
                fontSize: 11,
                fontFamily: AppAssetsConstants.openSans,
                fontWeight: FontWeight.w600)),
        // const SizedBox(height: 5,),
        // ...homePageHelper!.versions.map((key, value) {
        //   return MapEntry(
        //       key,
        //       AppText("$key: $value",
        //           style: TextStyle(
        //               color: AppColorConstants.colorH2,
        //               fontSize: 8,
        //               fontFamily: AppAssetsConstants.openSans,
        //               fontWeight: FontWeight.w600)));
        // }).values.toList(),
      ],
    );
  }

  List<SideMenuItemData>? centralSideMenuView() {
    return [
      //QL Dashboard
      sideMenuView(
          onTap: () {
            goOtherTab(context, RouteHelper.routeDashboard, 0);
            homePageHelper!.closeDrawer();
          },
          iconImage: AppAssetsConstants.dashboardIcon,
          indexOf: 0,
          title: S
              .of(context)
              .qlDashboard),
      //Provision
      sideMenuView(
          onTap: () {
            goOtherTab(context, RouteHelper.routeProvisionPage, 1);
            homePageHelper!.closeDrawer();
          },
          iconImage: AppAssetsConstants.addIcon,
          indexOf: 1,
          title: S
              .of(context)
              .discoveredDevice),
      //Amplifiers
      sideMenuView(
          onTap: () {
            //goOtherTab(context, RouteHelper.routeAmplifierPage, 2);
            router.go(RouteHelper.routeAmplifierPage, extra: {'initialTabIndex': 0});
            homePageHelper!.closeDrawer();
          },
          iconImage: AppAssetsConstants.amplifiersIcon,
          indexOf: 2,
          title: S
              .of(context)
              .amplifiers),
      //Sites/ Regions
      sideMenuView(
          onTap: () {
            goOtherTab(context, RouteHelper.routeSiteRegion, 3);
            homePageHelper!.closeDrawer();
          },
          iconImage: AppAssetsConstants.stateRegionIcon,
          indexOf: 3,
          title: S
              .of(context)
              .siteRegions),
      //Virtual GW
      sideMenuView(
          onTap: () {
            goOtherTab(context, RouteHelper.routeVLGWPage, 4);
            homePageHelper!.closeDrawer();
          },
          iconImage: AppAssetsConstants.monitorIcon,
          indexOf: 4,
          title: S
              .of(context)
              .virtualGW),


      //Firmware Management
      sideMenuView(
          onTap: () {
            goOtherTab(context, RouteHelper.routeFirmwarePage, 5);
            homePageHelper!.closeDrawer();
          },
          iconImage: AppAssetsConstants.downloadIcon,
          indexOf: 5,
          title: S
              .of(context)
              .firmwareDownload),

      //Diagnostics
      sideMenuView(
          onTap: () {
            goOtherTab(context, RouteHelper.routeDiagnostics, 6);
            homePageHelper!.closeDrawer();
          },
          iconImage: AppAssetsConstants.diagnosticsIcon,
          indexOf: 6,
          title: S
              .of(context)
              .diagnostics),
      //Audit Logs
      sideMenuView(
          onTap: () {
            goOtherTab(context, RouteHelper.routeAuditLogs, 8);
            homePageHelper!.closeDrawer();
          },
          iconImage: AppAssetsConstants.fileTextIcon,
          indexOf: 8,
          title: S
              .of(context)
              .auditLogs),
      //GWSettingPage
      /*  sideMenuView(
          onTap: () {
            goOtherTab(context, RouteHelper.routeGWSettingPage, 7);
            homePageHelper!.closeDrawer();
          },
          iconImage: AppAssetsConstants.settingIcon ,
          indexOf: 7,
          title: S.of(context).gwSetting),*/
      //Software Update
      // sideMenuView(
      //     onTap: () {
      //       goOtherTab(context, RouteHelper.routeSoftwareUpdate, 9);
      //       homePageHelper!.closeDrawer();
      //     },
      //     iconImage: AppAssetsConstants.settingIcon ,
      //     indexOf: 9,
      //     title: S.of(context).softwareUpdate),

      //Logout
      sideMenuView(
          onTap: () {
            logoutView(context, () async {
              await homePageHelper!.authController.logOut();
              goBack();
            });
          },
          iconImage: AppAssetsConstants.logoutIcon,
          indexOf: 10,
          padding: 10,
          fit: BoxFit.fitHeight,
          title: S
              .of(context)
              .logout),
    ];
  }

  List<SideMenuItemData>? nodeSideMenuView() {
    return [
      //QL Dashboard
      sideMenuView(
          onTap: () {
            goOtherTab(context, RouteHelper.routeDashboard, 0);
            homePageHelper!.closeDrawer();
          },
          iconImage: AppAssetsConstants.dashboardIcon,
          indexOf: 0,
          title: S
              .of(context)
              .qlDashboard),
      //Provision
      sideMenuView(
          onTap: () {
            goOtherTab(context, RouteHelper.routeProvisionPage, 1);
            homePageHelper!.closeDrawer();
          },
          iconImage: AppAssetsConstants.addIcon,
          indexOf: 1,
          title: S
              .of(context)
              .discoveredDevice),
      //Amplifiers
      sideMenuView(
          onTap: () {
            // goOtherTab(context, RouteHelper.routeAmplifierPage, 1);
            router.go(RouteHelper.routeAmplifierPage, extra: {'initialTabIndex': 0});
            homePageHelper!.closeDrawer();
            homePageHelper!.closeDrawer();
          },
          iconImage: AppAssetsConstants.amplifiersIcon,
          indexOf: 2,
          title: S
              .of(context)
              .amplifiers),

      //APIs
      sideMenuView(
          onTap: () {
            goOtherTab(context, RouteHelper.routeAPIs, 3);
            homePageHelper!.closeDrawer();
          },
          iconImage: AppAssetsConstants.apiIcon,
          indexOf: 3,
          title: S
              .of(context)
              .apis),

      //APIs
      sideMenuView(
          onTap: () {
            goOtherTab(context, RouteHelper.routeNodeGWPage, 8);
            homePageHelper!.closeDrawer();
          },
          iconImage: AppAssetsConstants.nodeGWIcon,
          indexOf: 8,
          title: S
              .of(context)
              .nodeGW),

      //Firmware Management
      sideMenuView(
          onTap: () {
            goOtherTab(context, RouteHelper.routeFirmwarePage, 5);
            homePageHelper!.closeDrawer();
          },
          iconImage: AppAssetsConstants.downloadIcon,
          indexOf: 5,
          title: S
              .of(context)
              .firmwareDownload),
      sideMenuView(
          onTap: () {
            goOtherTab(context, RouteHelper.routeSettingsPage, 6);
            homePageHelper!.closeDrawer();
          },
          iconImage: AppAssetsConstants.settingIcon ,
          indexOf: 6,
          title: S.of(context).homeSettings),
      // spacer
      //Software Update
      // sideMenuView(
      //     onTap: () {
      //       goOtherTab(context, RouteHelper.routeSoftwareUpdate, 4);
      //       homePageHelper!.closeDrawer();
      //     },
      //     iconImage: AppAssetsConstants.softwareUpdateIcon ,
      //     indexOf: 4,
      //     title: S.of(context).softwareUpdate),
      // if (homePageHelper!.gwIDFormWebHost.isNotEmpty)
      //   sideMenuView(
      //       onTap: () {
      //         goOtherTab(context, RouteHelper.routeGWSettingPage, 6);
      //         homePageHelper!.closeDrawer();
      //       },
      //       iconImage: AppAssetsConstants.settingIcon ,
      //       indexOf: 6,
      //       title: S.of(context).gwSetting),

      if (!AppConfig.shared.isOpenFromBLE)
        sideMenuView(
            onTap: () {
              logoutView(context, () async {
                await homePageHelper!.authController.logOut();
                goBack();
                homeController.update();
              });
            },
            iconImage: AppAssetsConstants.logoutIcon,
            indexOf: 7,
            padding: 10,
            fit: BoxFit.fitHeight,
            title: S
                .of(context)
                .logout),
    ];
  }

  void refreshCentralIndex(String uriPath) {
    if (uriPath == RouteHelper.routeDashboard) {
      homePageHelper!.pageIndex = 0;
    } else if (uriPath == RouteHelper.routeProvisionPage) {
      homePageHelper!.pageIndex = 1;
    } else if (uriPath == RouteHelper.routeAmplifierPage) {
      homePageHelper!.pageIndex = 2;
    } else if (uriPath == RouteHelper.routeSiteRegion) {
      homePageHelper!.pageIndex = 3;
    } else if (uriPath == RouteHelper.routeVLGWPage) {
      homePageHelper!.pageIndex = 4;
    } else if (uriPath == RouteHelper.routeFirmwarePage) {
      homePageHelper!.pageIndex = 5;
    } else if (uriPath == RouteHelper.routeDiagnostics) {
      homePageHelper!.pageIndex = 6;
    } else if (uriPath == RouteHelper.routeSoftwareUpdate) {
      homePageHelper!.pageIndex = 9;
    } else if (uriPath == RouteHelper.routeAuditLogs) {
      homePageHelper!.pageIndex = 8;
    } else if (uriPath == RouteHelper.routeGWSettingPage) {
      homePageHelper!.pageIndex = 7;
    } else if (uriPath == RouteHelper.userInfoPage) {
      homePageHelper!.pageIndex = 10;
    }
  }

  void refreshNodeIndex(String uriPath) {
    if (uriPath == RouteHelper.routeDashboard) {
      homePageHelper!.pageIndex = 0;
    } else if (uriPath == RouteHelper.routeProvisionPage) {
      homePageHelper!.pageIndex = 1;
    } else if (uriPath == RouteHelper.routeAmplifierPage) {
      homePageHelper!.pageIndex = 2;
    } else if (uriPath == RouteHelper.routeAPIs) {
      homePageHelper!.pageIndex = 3;
    } else if (uriPath == RouteHelper.routeSoftwareUpdate) {
      homePageHelper!.pageIndex = 4;
    } else if (uriPath == RouteHelper.routeFirmwarePage) {
      homePageHelper!.pageIndex = 5;
    } else if (uriPath == RouteHelper.routeSettingsPage) {
      homePageHelper!.pageIndex = 6;
    } else if (uriPath == RouteHelper.userInfoPage) {
      homePageHelper!.pageIndex = 7;
    } else if (uriPath == RouteHelper.routeNodeGWPage) {
      homePageHelper!.pageIndex = 8;
    }
  }

  void goOtherTab(BuildContext context, String location, index) async {
    debugLogs("Drawer Tapped");
    debugLogs("Drawer Index : $index");

    if (getConfigurationListMap() != null) {
      AmplifierDeviceItem ? ampItem= getConfigurationListMap();
      bool isSuccess = await checkConfigurationMap(context,ampItem);
      if (!isSuccess) {
        return;
      } else {
        clearMapValues();
      }
    }

    if (homePageHelper!.pageIndex == index) return;
    GoRouter router = GoRouter.of(context);
    router.go(location);
    homePageHelper!.pageIndex = index;
    homeController.update();
  }

  AmplifierDeviceItem? getConfigurationListMap() {
    AmplifierDeviceItem? ampDeviceItem;
    amplifierRepository.amplifierDeviceList?.result.forEach((element) {
      if (element.mapWrittenCtrlDS.isNotEmpty ||
          element.mapWrittenCtrlUS.isNotEmpty ||
          element.mapWrittenCtrlDSSpectrum.isNotEmpty) {
        ampDeviceItem = element;

      }
    });
    return ampDeviceItem;
  }

  clearMapValues() {
    amplifierRepository.amplifierDeviceList?.result.forEach((element) {
      element.mapWrittenCtrlDS = {};
      element.mapWrittenCtrlUS = {};
      element.mapWrittenCtrlDSSpectrum = {};
    });
  }

  Future<bool> checkConfigurationMap(BuildContext context, AmplifierDeviceItem? ampItem) async {
    final completer = Completer<bool>();
    await confirmationDialogConfigurationMap(
      context,
      callBack: (isRevert) async {
        if (isRevert == true) {
          bool isSuccess = await revertAmpDSandUSAutoAlignmentConfiguration(
              ampItem!.mapWrittenCtrlDS.isNotEmpty, ampItem
          );
          if (!completer.isCompleted) {
            completer.complete(isSuccess);
          }
        } else {
          if (!completer.isCompleted) {
            completer.complete(false);
          }
        }
      },
    );
    return await completer.future;
  }

  Future<bool?> confirmationDialogConfigurationMap(BuildContext context,
      {ApiStatus ? loadingStatus,  required Future<void> Function(bool isRevert) callBack}) async {
    return await confirmationDialogConfiguration(
        context, callBack: callBack);
  }

  Future<bool> revertAmpDSandUSAutoAlignmentConfiguration(bool isDSAlignment, AmplifierDeviceItem? ampItem) async {
    AmplifierController ampController = Get.put(AmplifierController());
    try {
      DsManualAlignmentItem dsManualAlignmentItem =
      DsManualAlignmentItem([],
          manual_align_ctrl_type_enum: 6);
      if (isDSAlignment) {
        return await ampController.saveRevertDsManualAlignment(
            dsManualAlignmentItem: dsManualAlignmentItem,
            deviceEui: ampItem?.deviceEui,
            context: context).then((value) async {
          if (value['body'].result != null) {
            clearMapValues();
            debugLogs("Revert Result : ${value['body'].result}");
            displayToastNotification(context,isDSAlignment,true);
            goBack();
            return true;
          } else {
            displayToastNotification(context,isDSAlignment,false);
            return false;
          }
        });
      }else{
        return await ampController.saveRevertUsManualAlignment(
            dsManualAlignmentItem: dsManualAlignmentItem,
            deviceEui: ampItem?.deviceEui,
            context: context).then((value) async {
          if (value['body'].result != null) {
            clearMapValues();
            debugLogs("Revert Result : ${value['body'].result}");
            displayToastNotification(context,isDSAlignment,true);
            goBack();
            return true;
          } else {
            displayToastNotification(context,isDSAlignment,false);
            return false;
          }
        });
      }
    }  catch (e) {
      displayToastNotification(context,isDSAlignment,false);
      return false;
    } finally{
      homeController.update();
    }
  }
  displayToastNotification(
      context, bool isDsAlignment,bool isSuccess) {
    String msg = "";
    if (isDsAlignment)
      if(isSuccess)
        msg =  S.of(context).revertDsAlignmentCompleted;
      else
        msg =  S.of(context).revertDsAlignmentFailed;
    else
    if(isSuccess)
      msg =  S.of(context).revertUsAlignmentCompleted;
    else
      msg =  S.of(context).revertUsAlignmentFailed;

    if (isSuccess)
      msg.showSuccess(context);
    else
      msg.showError(context);
  }

}
