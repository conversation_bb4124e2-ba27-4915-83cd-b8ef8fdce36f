import 'package:flutter/scheduler.dart';
import 'package:quantumlink_node/app/cache/app_shared_preference.dart';
import 'package:quantumlink_node/app_import.dart';
import "package:universal_html/html.dart" as html;

final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
class HomePageHelper {
  late HomePageState state;
  late AuthController authController;
  String userProfileName = "";
  String versionNumber = "0.0.0";
  ApiStatus apiStatus = ApiStatus.initial;
  bool isHovered = false;
  final sideMenuController = SideMenuController();
  int pageIndex = 0;
  bool isDrawerOpen = true;
  Map<String, String> versions = {};
  Map<String,dynamic> connectedSsid = {};
  bool isConnectedWithInternet = false;
  HomePageHelper(this.state) {
    SchedulerBinding.instance.addPostFrameCallback(
      (timeStamp) async {
        authController = Get.put(AuthController());
        authController.goToSignInAfterSignOut();
        setUserInformation();
        await Future.delayed(Duration(seconds: 3));
        // ssidName();
      },
    );
  }

  // Future<void> ssidName() async {
  //   if (!AppConfig.shared.isQLCentral) {
  //     connectedSsid = await state.homeController.ssidReport(state.context);
  //     isConnectedWithInternet = connectedSsid.containsKey('internet') && (connectedSsid['internet'] == true);
  //     state.homeController.update();
  //   }
  // }

  Future<void> setUserInformation() async {
    await AppConfig.shared.loadJsonAsset();
    userProfileName = await authController.setUserName();
    state.homeController.update();
  }


  Future<String> getVersionNumber() async {
    if (versions.isEmpty) {
     // versions = await getBackendVersions(state.context);
    }
    try {
      versionNumber = await rootBundle.loadString(AppAssetsConstants.versionPath);
    } catch (e) {
      debugPrint('catch exception in getVersionNumber ---> ${e.toString()}');
    }
    return versionNumber;
  }

  Future<Map<String,String>> getBackendVersions(BuildContext context) async {
    DashboardRepository dashboardRepository = DashboardRepositoryImpl();
    if(versions.isEmpty){
      versions = await dashboardRepository.getBackendVersions(context);
    }
    return versions;
  }
  Future <void> hrefURL(String path) async {
    dynamic accessToken;
    if (await checkPrefKey(AppSharedPreference.accessToken)) {
    accessToken = await getPrefStringValue(AppSharedPreference.accessToken);
    }

    final String redirectUrl = "$path?tkn=$accessToken";
    html.window.location.href = redirectUrl;
  }

  void closeDrawer() {
    if (state.screenLayoutType != ScreenLayoutType.desktop) {
      scaffoldKey.currentState!.closeDrawer();
    }
  }

  // DOWNLOAD JSON FILE
  Future<void> downloadJsonFile(Map<String, dynamic> jsonData) async {
    try {
      final jsonString = jsonEncode(jsonData['body']);
      final bytes = utf8.encode(jsonString);
      final blob = html.Blob([bytes]);
      final url = html.Url.createObjectUrlFromBlob(blob);
      final anchor = html.AnchorElement(href: url)
        ..setAttribute('download', 'data.json')
        ..click();
      html.Url.revokeObjectUrl(url);
    } catch (e) {
      debugLogs('Error saving file: $e');
    }
  }
}

// LOGOUT DIALOG
logoutView(
  c,
  Function fun,
) {
  return showDialog(
    context: c,
    builder: (context) {
      return AlertDialog(
        surfaceTintColor: AppColorConstants.colorWhite,
        backgroundColor: AppColorConstants.colorWhite,
        insetPadding: EdgeInsets.zero,
        contentPadding: EdgeInsets.zero,
        titlePadding: EdgeInsets.zero,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
        title: getCustomAppBarWithClose(S.of(context).logout),
        content: StatefulBuilder(builder: (context, snapshot) {
          return Container(
              width: MediaQuery.of(context).size.width * 0.17, // Adjust the width as needed
              height: MediaQuery.of(context).size.height * 0.12,
              padding: const EdgeInsets.all(8.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AppText(
                    S.of(context).logoutConfirmation,
                    style: TextStyle(
                        fontWeight: getMediumFontWeight(),
                        fontSize: getSize(14),
                        fontFamily: AppAssetsConstants.openSans),
                  ),
                  SizedBox(height: getSize(10)),
                ],
              ));
        }),
        actions: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              AppButton(
                fontSize: 15,
                borderColor: Colors.grey.withOpacity(0.5),
                buttonHeight: 22,
                buttonColor: Colors.grey.withOpacity(0.5),
                buttonName: S.of(c).no,
                fontFamily: AppAssetsConstants.openSans,
                fontColor: AppColorConstants.colorBlackBlue,
                onPressed: () {
                  goBack();
                },
              ),
              const SizedBox(width: 16),
              AppButton(
                fontSize: 15,
                fontFamily: AppAssetsConstants.openSans,
                buttonHeight: 22,
                buttonName: S.of(c).yes,
                onPressed: () {
                  fun.call();
                },
              ),
            ],
          )
        ],
      );
    },
  );
}
