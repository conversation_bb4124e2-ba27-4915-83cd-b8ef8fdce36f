import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/controller/gw_setting_controller.dart';

class GateWayPage extends StatefulWidget {
  const GateWayPage({super.key});

  @override
  State<StatefulWidget> createState() => GateWayPageState();
}

class GateWayPageState extends State<GateWayPage> with TickerProviderStateMixin {

  late GWSettingController gwSettingController;
  late TabController tabController;
  List<DIGTabItem> gatewayTabList = [
    DIGTabItem(title: "GW Setting", isCurrentOpen: true, icon: AppAssetsConstants.settingIcon),
    DIGTabItem(title: "Diagnostics", isCurrentOpen: false, icon: AppAssetsConstants.diagnosticsIcon)
  ];
  List<bool> isHovered = List.generate(2, (index) => false);

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    tabController = TabController(
        initialIndex: 0,
        length: 2,
        vsync: this,
        animationDuration: Duration.zero
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<GWSettingController>(
      init: GWSettingController(),
      builder: (GWSettingController controller) {
        gwSettingController = controller;
        return getBody();
      },
    );
  }

  Widget getBody() {
    return getGatWayView();
  }

  Widget getGatWayView() {
    return ScreenLayoutTypeBuilder(
      builder: (context, screenType, constraints) {
        return SizedBox(
          height: (double.infinity),
          child: ClipRRect(
            borderRadius: const BorderRadius.all(Radius.circular(5)),
            child: Container(
              color: AppColorConstants.colorWhite,
              width: double.infinity,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: NestedScrollView(
                        headerSliverBuilder: (context, innerBoxIsScrolled) => [

                              SliverToBoxAdapter(
                                  child: Padding(
                                      padding: const EdgeInsets.symmetric(horizontal: 10),
                                      child: getPageTitleView(S.of(context).gwSetting))),
                              SliverToBoxAdapter(child: SizedBox(height: getSize(10))),
                              SliverToBoxAdapter(child: getTabsOnDiagnosticsHeader()),
                            ],
                        body: getTabsContent()),
                  )
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget getTabsContent() {
    return TabBarView(
      physics: const NeverScrollableScrollPhysics(),
      controller: tabController,
      children: List.generate(gatewayTabList.length, (index) {
        if (index == 0) {
          return  Container();
        }
        return  Container();
      }),
    );
  }

  Widget getTabsOnDiagnosticsHeader() {
    return TabBar(
      controller: tabController,
      dividerColor: AppColorConstants.colorWhite,
      labelPadding: EdgeInsets.zero,
      labelColor: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: getSize(10)),
      isScrollable: true,
      indicatorColor: AppColorConstants.colorWhite,
      tabAlignment: TabAlignment.start,
      // splashBorderRadius: BorderRadius.circular(12),
      onTap: (value) {
        for (var tabElement in gatewayTabList) {
          tabElement.isCurrentOpen = false;
        }
        gatewayTabList[value].isCurrentOpen = true;
        gwSettingController.update();
      },
      tabs: List.generate(gatewayTabList.length, (index) {
        DIGTabItem digTabItem = gatewayTabList[index];
        return MouseRegion(
          onEnter: (event) {
            isHovered[index] = true;
            gwSettingController.update();
          },
          onExit: (event) {
            isHovered[index] = false;
            gwSettingController.update();
          },
          child: Tab(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 5),
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: getSize(25)),
                height: getSize(42),
                alignment: Alignment.center,
                decoration: digTabItem.getDeco(isHovered[index]),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(5),
                      child: AppImageAsset(
                        height: 20,
                        width: 20,
                        image: digTabItem.icon ?? "",
                        color: isHovered[index] && !digTabItem.isCurrentOpen
                            ? AppColorConstants.colorBlackBlue
                            : digTabItem.isCurrentOpen
                                ? AppColorConstants.colorLightBlue
                                : AppColorConstants.colorH2,
                      ),
                    ),
                    AppText(
                      isSelectableText: false,
                      digTabItem.title,
                      style: TextStyle(
                        fontSize: getSize(16),
                        fontFamily: AppAssetsConstants.poppins,
                        fontWeight: FontWeight.w400,
                        color: isHovered[index] && !digTabItem.isCurrentOpen
                            ? AppColorConstants.colorBlackBlue
                            : digTabItem.isCurrentOpen
                                ? AppColorConstants.colorLightBlue
                                : AppColorConstants.colorH2,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      }),
    );
  }
}
