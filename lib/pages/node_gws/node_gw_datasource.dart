// ignore_for_file: deprecated_member_use

import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/model/node_gw_tab_iteam.dart';

class NodeGwDatasource extends DataTableSource {
  NodeGwDatasource.empty(this.context, this.gatewaysList ,this.onTap);

  NodeGwDatasource(this.context, this.gatewaysList ,this.onTap,[sortedByEUI = false]) {
    _hoveredIndices = List.generate(gatewaysList.length, (_) => false);
    if (sortedByEUI) {}
  }
  List<bool> _hoveredIndices = [];
  final BuildContext context;
  final List<GatewayItem> gatewaysList;
  final Function(GatewayItem) onTap;
  DataTableHelper dataTableHelper = DataTableHelper();
  void sort<T>(Comparable<T> Function(GatewayItem d) getField, bool ascending) {
    gatewaysList.sort((a, b) {
      final aValue = getField(a);
      final bValue = getField(b);
      return ascending ? Comparable.compare(aValue, bValue) : Comparable.compare(bValue, aValue);
    });
    notifyListeners();
  }

  @override
  DataRow2 getRow(int index, [GatewayItem ? gatewayItem]) {
    assert(index >= 0);
    if (index >= gatewaysList.length) throw 'index > _desserts.length';
    final dessert = gatewayItem ?? gatewaysList[index];
    String gwEui = (dessert.gwEui ?? "").toString();
    String name = (dessert.name ?? "").toString();
    String swVersion = dessert.swVersion ?? "";
    String hwVersion = (dessert.hwVersion?? "").toString();
    String userName = (dessert.userEmail?? "").toString();

    String status = (dessert.status?? "").toString();
    GWStatusType? detectedStatusType = getGWStatusType(status);
    String lastSeen = dessert.lastSeen != null ? getUtcTimeZone(dessert.lastSeen) : '';
    return DataRow2.byIndex(
      index: index,
      color: index.isEven
          ? MaterialStateProperty.all(AppColorConstants.colorWhite)
          : MaterialStateProperty.all(AppColorConstants.colorBackgroundDark),
      cells: [
        DataCell(onTap: ()=> onTap.call(dessert),
            MouseRegion(
          onEnter: (_) {
            _hoveredIndices[index] = true;
            notifyListeners();
          },
          onExit: (_) {
            _hoveredIndices[index] = false;
            notifyListeners();
          },
          child: AppText(
            gwEui ,
            maxLines: 1,
            isSelectableText: false,
            style: TextStyle(
                fontWeight: FontWeight.w600,
                fontFamily: AppAssetsConstants.openSans,
                color: AppColorConstants.colorLightBlue,
                fontSize: getSize(14),
                decoration:
                _hoveredIndices[index] ? TextDecoration.underline : TextDecoration.none,
                decorationThickness: 4,
                decorationColor: AppColorConstants.colorLightBlue),
          ),
        )),
        DataCell(Center(
          child: Container(
            alignment: Alignment.center,
            height: getSize(30),
            width: getSize(70),
            decoration: BoxDecoration(
                color: detectedStatusType == GWStatusType.online
                    ? AppColorConstants.colorGreen2
                    : detectedStatusType == GWStatusType.offline
                    ? AppColorConstants.colorH2.withOpacity(0.4)
                    : (detectedStatusType == GWStatusType.neverSeen)
                    ? AppColorConstants.colorOrange
                    : AppColorConstants.colorH2.withOpacity(0.4),
                borderRadius: BorderRadius.circular(getSize(18))),
            child: AppText(
              detectedStatusType == GWStatusType.online
                  ? S.of(context).online
                  : detectedStatusType == GWStatusType.offline
                  ? S.of(context).offline
                  : detectedStatusType == GWStatusType.neverSeen
                  ? S.of(context).neverSeen
                  : S.of(context).offline,
              textAlign: TextAlign.center,
              style: TextStyle(
                  color: (detectedStatusType == GWStatusType.offline ||
                      dessert.status == null)
                      ? AppColorConstants.colorH3
                      : AppColorConstants.colorWhite,
                  fontFamily: AppAssetsConstants.sourceSans,
                  fontSize: detectedStatusType == GWStatusType.neverSeen ?10 :13,
                  fontWeight: FontWeight.w600),
            ),
          ),
        )),
        DataCell(Center(
          child: AppText(hwVersion,
              style: dataTableHelper.dataRowTextStyle),
        )),
        DataCell(Center(
          child: AppText(swVersion,
              style: dataTableHelper.dataRowTextStyle),
        )),
        DataCell(Center(
          child: AppText(name,
              style: dataTableHelper.dataRowTextStyle),
        )),
        DataCell(Center(
          child: AppText(userName,
              style: dataTableHelper.dataRowTextStyle),
        )),
        DataCell(AppText(lastSeen,
            style: dataTableHelper.dataRowTextStyle)),
      ],
    );
  }

updateNotifyListeners(){
  notifyListeners();
}

  @override
  int get rowCount => gatewaysList.length;

  @override
  bool get isRowCountApproximate => false;

  @override
  int get selectedRowCount => 0;
}
