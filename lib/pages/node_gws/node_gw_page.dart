import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/model/node_gw_tab_iteam.dart';
import 'package:quantumlink_node/pages/node_gws/mobile_node_gw.dart';
import 'package:quantumlink_node/pages/node_gws/node_gw_detail/node_gw_details.dart';
import 'package:quantumlink_node/pages/node_gws/nodw_gw_page_helper.dart';

class NodeGWPage extends StatefulWidget {
  const NodeGWPage({super.key});

  @override
  State<StatefulWidget> createState() => NodeGWPageState();
}

class NodeGWPageState extends State<NodeGWPage> with TickerProviderStateMixin {
  NodeGWPageHelper? _helper;
  late NodeGwController controller;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    _helper ?? (_helper = NodeGWPageHelper(this));
    return GetBuilder<NodeGwController>(
      init: NodeGwController(),
      builder: (NodeGwController nodeGWController) {
        controller = nodeGWController;
        return getBody();
      },
    );
  }

  Widget getBody() {
    return getNodeGatWayView();
  }

  Widget getNodeGatWayView() {
    return ScreenLayoutTypeBuilder(
      builder: (context, screenType, constraints) {
        _helper!.screenLayoutType = screenType;
         MobileNodeGw().autoSelectTableType(_helper);
        return SizedBox(
          height: (double.infinity),
          child: ClipRRect(
            borderRadius: const BorderRadius.all(Radius.circular(5)),
            child: Container(
              color: AppColorConstants.colorWhite,
              width: double.infinity,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Scrollbar(
                      thumbVisibility: true,
                      child: NestedScrollView(
                          headerSliverBuilder: (context, innerBoxIsScrolled) => [
                                SliverToBoxAdapter(
                                    child: Padding(
                                        padding: const EdgeInsets.symmetric(horizontal: 10),
                                        child: getPageTitleView(S.of(context).nodeGW))),
                                SliverToBoxAdapter(child: SizedBox(height: getSize(10))),
                                SliverToBoxAdapter(child: getTabsOnDiagnosticsHeader()),
                              ],
                          body: getTabsContent()),
                    ),
                  )
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget getTabsContent() {
    return TabBarView(
      physics: const NeverScrollableScrollPhysics(),
      controller: _helper!.tabController,
      children: List.generate(_helper!.tabLIst.length, (index) {
        if (index == 0) {
          return getNodeGWContent();
        }
        return NodeGwDetailPage(_helper!.tabLIst[index].gatewayItem!);
      }),
    );
  }

  Widget getTabsOnDiagnosticsHeader() {
    return SelectionArea(
      child: TabBar(
        controller: _helper!.tabController,
        dividerColor: AppColorConstants.colorWhite,
        labelPadding: EdgeInsets.zero,
        labelColor: Colors.white,
        padding: EdgeInsets.symmetric(horizontal: getSize(10)),
        isScrollable: true,
        indicatorColor: AppColorConstants.colorWhite,
        tabAlignment: TabAlignment.start,
        // splashBorderRadius: BorderRadius.circular(12),
        onTap: (value) {
          for (var tabElement in _helper!.tabLIst) {
            tabElement.isCurrentOpen = false;
          }
          _helper!.tabLIst[value].isCurrentOpen = true;
          controller.update();
        },
        tabs: List.generate(_helper!.tabLIst.length, (index) {
          NodeGWTabItem nodeGWTabItem = _helper!.tabLIst[index];
          return MouseRegion(
            onEnter: (event) {
              _helper!.isHovered[index] = true;
              controller.update();
            },
            onExit: (event) {
              _helper!.isHovered[index] = false;
              controller.update();
            },
            child: Tab(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 5),
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: getSize(25)),
                  height: getSize(42),
                  alignment: Alignment.center,
                  decoration: nodeGWTabItem.getDeco(_helper!.isHovered[index]),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      AppText(
                        isSelectableText: false,
                        nodeGWTabItem.title,
                        style: TextStyle(
                          fontSize: getSize(16),
                          fontFamily: AppAssetsConstants.poppins,
                          fontWeight: FontWeight.w600,
                          color: _helper!.isHovered[index] && !nodeGWTabItem.isCurrentOpen
                              ? AppColorConstants.colorBlackBlue
                              : nodeGWTabItem.isCurrentOpen
                                  ? AppColorConstants.colorLightBlue
                                  : AppColorConstants.colorH2,
                        ),
                      ),
                      if (_helper!.tabLIst.length > 1 && index > 0) ...[
                        SizedBox(width: getSize(10)),
                        GestureDetector(
                            onTap: () async {
                              _helper!.removeTab(index);
                            },
                            child: CircleAvatar(
                                maxRadius: 10,
                                backgroundColor: nodeGWTabItem.isCurrentOpen
                                    ? AppColorConstants.colorLightBlue
                                    : AppColorConstants.colorH2.withOpacity(0.3),
                                child: Icon(Icons.close,
                                    size: getSize(16),
                                    color: nodeGWTabItem.isCurrentOpen
                                        ? AppColorConstants.colorWhite
                                        : AppColorConstants.colorH3))),
                      ]
                    ],
                  ),
                ),
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget getNodeGWContent() {
    if (_helper!.apiStatus != ApiStatus.success) {
      return ListView(padding: EdgeInsets.symmetric(horizontal: 15),
        physics: const ClampingScrollPhysics(),
        children: [
          ClipRRect(
            borderRadius:
                const BorderRadius.only(topRight: Radius.circular(8), topLeft: Radius.circular(8)),
            child: Container(
                alignment: Alignment.center,
                height: 300,
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.only(
                      topRight: Radius.circular(8), topLeft: Radius.circular(8)),
                  border: Border.all(color: AppColorConstants.colorH2, width: 0.8),
                ),
                width: double.infinity,
                child: const AppLoader()),
          ),
        ],
      );
    }
    Widget table = getNodeGWTableView();
    int itemsPerPage = _helper!.dataTableHelper.getCurrentPageDataLength(
        _helper!.nodeGwDatasource.gatewaysList, _helper!.currentPageIndex, perPageLimit: AppStringConstants.nodeGWPerPageLimit);
    _helper!.recordsInPage = (_helper!.nodeGwDatasource.gatewaysList.length > AppStringConstants.nodeGWPerPageLimit)
        ? itemsPerPage
        : _helper!.nodeGwDatasource.gatewaysList.length;
    return ListView(
      padding: EdgeInsets.symmetric(horizontal: 15),
      children: [
        ClipRRect(
          borderRadius:
              const BorderRadius.only(topRight: Radius.circular(8), topLeft: Radius.circular(8)),
          child: Container(
            decoration: _helper!.dataTableHelper.tableBorderDeco(),
            width: double.infinity,
            height: _helper!.isTableView ? (_helper!.nodeGwDatasource.gatewaysList.isNotEmpty)
                ? (_helper!.recordsInPage * _helper!.heightOfDataTableCell) +
                    (_helper!.recordsInPage * 0.1) +
                    210
                : (_helper!.recordsInPage * _helper!.heightOfDataTableCell) +
                    (_helper!.recordsInPage * 0.1) +
                    400 : null,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [getPageAppBar(),_helper!.isTableView ? table : MobileNodeGw().buildGatWayList(context,_helper!),
                AppPaginationWidget(
                  apiStatus: _helper!.apiStatus,
                  paginationHelper: _helper!.paginationHelper,
                  onLoadNext: () async {
                    await  _helper!.loadNextLogs(context);
                  },
                  onLoadPrevious: () async {
                    await  _helper!.loadPreviousLogs(context);
                    _helper!.update();
                  },
                  onGoToFirstPage: () {
                    _helper!.paginationHelper.setPage(0);
                    if(_helper!.isTableView) {
                      _helper!.paginatorController.goToFirstPage();
                    }
                    _helper!.update();
                  },
                  onGoToLastPage: () {
                    if(_helper!.isTableView) {
                      _helper!.paginatorController.goToLastPage();
                    }
                    _helper!.update();
                  },
                  itemsPerPage: AppStringConstants.nodeGWPerPageLimit,
                  onChanged: (value) {
                    if (_helper!.apiStatus != ApiStatus.loading) {
                      _helper!.currentPageIndex=0;
                      AppStringConstants.nodeGWPerPageLimit = int.parse(value);
                      _helper!.getGWList();
                      _helper!.update();
                    }
                  },
                )
              ],
            ),
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            buildLastSeenView(updateTime: _helper!.lastUpdateTime),
            AppRefresh(
              buttonColor: AppColorConstants.colorPrimary,
              loadingStatus: _helper!.apiStatus,
              onPressed: () => _helper!.getGWList(),
            )
          ],
        )
      ],
    );
  }

  Widget getPageAppBar() {
    return Container(
      decoration: BoxDecoration(
          border: Border.all(color: AppColorConstants.colorH2, width: 0.8),
          color: AppColorConstants.colorWhite,
          borderRadius: BorderRadius.only(
              topRight: Radius.circular(getSize(7)), topLeft: Radius.circular(getSize(7)))),
      alignment: Alignment.topCenter,
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      child: Column(
        children: [
         Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              selectStatusCheckBoxView(),
              if(_helper!.screenLayoutType != ScreenLayoutType.mobile) Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  searchTextFieldView(),
                  MobileNodeGw().selectTableTypeButtonView(_helper),
                ],
              )
            ],
          ),
          if(_helper!.screenLayoutType == ScreenLayoutType.mobile)
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Flexible(child: searchTextFieldView()),
                MobileNodeGw().selectTableTypeButtonView(_helper),
              ],
            ).paddingOnly(top: 5)
        ],
      ),
    );
  }
  Widget selectStatusCheckBoxView() {
    return Padding(
      padding: const EdgeInsets.only(left: 7.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Checkbox(
            splashRadius: 0,
            value: _helper!.isShowOnline,
            activeColor: AppColorConstants.colorGreen2,
            side: MaterialStateBorderSide.resolveWith(
                  (Set<MaterialState> states) {
                return BorderSide(
                    width: 2,
                    color: _helper!.isShowOnline
                        ? AppColorConstants.colorGreen2
                        : AppColorConstants.colorBlack);
              },
            ),
            onChanged: (bool? value) async {
              _helper!.isShowOnline = value!;
              if (value) {
                _helper!.defaultStatus = "online";
              } else {
                _helper!.defaultStatus = "offline";
              }
              await _helper!.getGWList();
              _helper!.paginatorController.goToFirstPage();
              _helper!.nodeGwDatasource.updateNotifyListeners();
            },
          ),
          FittedBox(
            fit: BoxFit.cover,
            // Ensure the text fits within the available space
            child: AppText(S.of(context).online,
                style: TextStyle(
                    fontFamily: AppAssetsConstants.openSans,
                    fontWeight: FontWeight.w500,
                    fontSize: getSize(16),
                    color: AppColorConstants.colorBlack)),
          ),
        ],
      ),
    );
  }

  Widget searchTextFieldView() {
    if(_helper!.gwIDFormWebHost.isNotEmpty){
      return Container(height: 25,);
    }
    return Container(
      height: 40,
      alignment: Alignment.centerRight,
      constraints: BoxConstraints(maxWidth: 500),
      decoration: BoxDecoration(
          color: AppColorConstants.colorWhite1, borderRadius: BorderRadius.circular(getSize(8))),
      child: AppTextFormField(
        onChanged: (value) {
          _helper!.filterData(value);
        },
        focusedBorderColor: AppColorConstants.colorPrimary,
        controller: _helper!.searchController,
        enabledBorderColor: AppColorConstants.colorWhite1,
        hintText: S.of(context).quickSearch,
        maxLines: 1,
        textInputType: TextInputType.emailAddress,
        validator: (value) {
          return null;
        },
        borderRadius: getSize(8),
        hintTextColor: AppColorConstants.colorDarkBlue,
        suffixIcon: const Padding(
          padding: EdgeInsets.all(12),
          child: AppImageAsset(image: AppAssetsConstants.searchIcon),
        ),
        hintFontSize: 17,
        fontFamily: AppAssetsConstants.openSans,
        fontWeight: FontWeight.w400,
      ),
    );
  }

  Widget getNodeGWTableView() {
    return Expanded(
      child: PaginatedDataTable2(
        rowsPerPage: AppStringConstants.nodeGWPerPageLimit,
        initialFirstRowIndex:
        _helper!.paginationHelper.currentPage * AppStringConstants.nodeGWPerPageLimit,
        columnSpacing: 2,
        controller: _helper!.paginatorController,
        headingCheckboxTheme:
            CheckboxThemeData(side: BorderSide(width: 2, color: AppColorConstants.colorWhite)),
        headingTextStyle: _helper!.dataTableHelper.headingTextStyle(),
        wrapInCard: false,
        datarowCheckboxTheme:
            const CheckboxThemeData(side: BorderSide(width: 2, color: Colors.grey)),
        border: _helper!.dataTableHelper.tableBorder(),
        renderEmptyRowsInTheEnd: false,
        headingRowColor: _helper!.dataTableHelper.headingRowColor(),
        source: _helper!.nodeGwDatasource,
        minWidth: 1300,
        columns: [
          DataColumn2(
            fixedWidth: 150,
            label: AppText(S.of(context).gwEUI),
          ),
          DataColumn2(
            fixedWidth: 80,
            label: Center(child: AppText(S.of(context).status)),
          ),
          DataColumn2(
            fixedWidth: 180,
            label: Center(child: AppText(S.of(context).hwVersion)),
          ),
          DataColumn2(
            fixedWidth: 200,
            label: Center(child: AppText(S.of(context).swVersion)),
          ),
          DataColumn2(
            fixedWidth: 150,
            label: Center(child: AppText(S.of(context).gwName)),
          ),
          DataColumn2(
            fixedWidth: 250,
            label: Center(child: AppText(S.of(context).currantUser)),
          ),
           DataColumn2(
            label: AppText(S.of(context).lastSeen),
          ),
        ],
        empty: _helper!.dataTableHelper.getEmptyTableContent(context),
        hidePaginator: true,
      ),
    );
  }
}
