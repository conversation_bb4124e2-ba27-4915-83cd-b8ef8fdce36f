import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/model/node_gw_tab_iteam.dart';
import 'package:quantumlink_node/pages/node_gws/nodw_gw_page_helper.dart';

class MobileNodeGw {
  int itemPerPage=10;
  Widget buildGatWayList(BuildContext context, NodeGWPageHelper helper) {
    if (helper.nodeGwDatasource.rowCount == 0) {
      return SizedBox(
        height: 350,
        child: helper.dataTableHelper.getEmptyTableContent(context),
      );
    }
    List<GatewayItem> fullList = helper.nodeGwDatasource.gatewaysList;
    List<GatewayItem> paginatedList = fullList
        .skip(helper.paginationHelper.currentPage *
        AppStringConstants.nodeGWPerPageLimit)
        .take(AppStringConstants.nodeGWPerPageLimit)
        .toList();
    return Container(
      decoration: helper.dataTableHelper.tableBorderDeco(),
      child: Column(
        children: [
          ListView.builder(
            shrinkWrap: true,
            itemCount: paginatedList.length,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              GatewayItem gatewayDeviceItem = paginatedList[index];
              DataRow dataRow = helper.nodeGwDatasource.getRow(index,gatewayDeviceItem);
              String gwName= gatewayDeviceItem.name ?? "" ;
              String swVersion= gatewayDeviceItem.swVersion ?? "" ;
              String hwVersion= gatewayDeviceItem.hwVersion ?? "" ;

              TextStyle textStyle = const TextStyle(fontSize: 14, fontWeight: FontWeight.w500);
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 3, horizontal: 5),
                child: CustomListTile(
                  onTap: () {
                    helper.nodeGwDatasource.onTap(gatewayDeviceItem);
                  },
                  index: index,
                  titleWidget: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            dataRow.cells[0].child,
                            AppText(
                              isSelectableText: false,
                              gwName,
                              style: textStyle,
                            ),
                            if (helper.screenLayoutType == ScreenLayoutType.mobile)
                              swAndHwVersionView(context, swVersion, hwVersion,true)
                          ],
                        ),
                      ),
                      SizedBox(width: getSize(10)),
                      if (helper.screenLayoutType != ScreenLayoutType.mobile)
                        swAndHwVersionView(context, swVersion, hwVersion,false) else dataRow.cells[1].child,
                    ],
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );

  }

  Widget swAndHwVersionView(BuildContext context, String swVersion, String hwVersion,bool isFlexible) {
    return Column(crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Row(crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AppText("${S
                .of(context)
                .hwVersion} :",
                style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w600)),
            if(isFlexible)
            Flexible(
              child: AppText(
                hwVersion,
                style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400),
              ),
            )else AppText(
              hwVersion,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400),
            )
          ],
        ),
        Row(
          children: [
            AppText("${S
                .of(context)
                .swVersion} :",
                style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w600)),
            if(isFlexible)
            Flexible(
              child: AppText(
                swVersion,
                style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400),
              )
            )else AppText(
              swVersion,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400),
            )
          ],
        ),
      ],
    );
  }

  Widget selectTableTypeButtonView(NodeGWPageHelper? helper) {
    return helper?.dataTableHelper.selectTableTypeButtonView(
      isTableType: helper.isTableView,
      onPressed: () {
        helper.isTableView = !helper.isTableView;
        helper.update();
      },
    );
  }

  void autoSelectTableType(NodeGWPageHelper? helper) {
    if (helper?.previousLayoutType != null) {
      ScreenLayoutType currentLayoutType = helper!.screenLayoutType;
      bool isMobile = currentLayoutType != ScreenLayoutType.desktop;
      if (helper.previousLayoutType != currentLayoutType) {
        helper.isTableView = !isMobile;
        helper.previousLayoutType = currentLayoutType;
      }
    }
  }

  Widget customPaginationView(NodeGWPageHelper? helper){
    if(helper!.isTableView){
      return SizedBox();
    }
    return  AppPaginationWidget(
      apiStatus: ApiStatus.success,
      paginationHelper: helper.paginationHelper,
      onLoadNext: () async {
        helper.paginationHelper.currentPage++;
        helper.update();
      },
      onLoadPrevious: () async {
        helper.paginationHelper.currentPage--;
        helper.update();
      },
      onGoToFirstPage: () {
        helper.paginationHelper.currentPage=0;
        helper.update();
      },
      onGoToLastPage: () {
        List<GatewayItem> fullList = helper.nodeGwDatasource.gatewaysList;
        int totalPages = (fullList.length / itemPerPage).ceil();
        helper.paginationHelper.currentPage=totalPages-1;
        helper.update();
      },
      onChanged: (value) {

      },
    );
  }
}
