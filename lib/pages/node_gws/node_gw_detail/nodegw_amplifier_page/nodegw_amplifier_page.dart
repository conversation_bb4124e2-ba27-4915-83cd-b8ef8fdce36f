
import 'package:quantumlink_node/app_import.dart';
import 'nodegw_amps_datasource.dart';

class NodeGwAmplifierPage extends StatefulWidget {
  final  String ? gwEuiId;
  const NodeGwAmplifierPage({super.key, required this.gwEuiId});

  @override
  State<NodeGwAmplifierPage> createState() => NodeGwAmplifierPageState();
}

class NodeGwAmplifierPageState extends State<NodeGwAmplifierPage> {
  late ScreenLayoutType screenLayoutType;
  NodeGwController nodeGwController = NodeGwController();
  DataTableHelper dataTableHelper = DataTableHelper();
  PaginatorController paginatorController = PaginatorController();
  ApiStatus apiStatus = ApiStatus.initial;
  int recordsInPage = 0; // For Set Fixed and Static Height Of Table
  int currentPageIndex = 0;
  final double heightOfDataTableCell = 48;
  ProvisioningDeviceList listAmpsDeviceItem =
  ProvisioningDeviceList.empty();
  late NodeGwAmpsDataSource ampsDeviceDataSource;
  PaginationHelper  paginationHelper = PaginationHelper();
  String ampsErrorMessage= "";


  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    getDeviceListData();

  }

  update(){
    nodeGwController.update();
  }

  Future<void> getDeviceListData() async {
    apiStatus = ApiStatus.loading;
    paginationHelper.initializePagination();
    update();
    try {
      listAmpsDeviceItem = await nodeGwController.getNodeGwAmpsDeviceList(
          context: context,
          perPageLimit: AppStringConstants.ampPrePageLimit,
          pageOffset: paginationHelper.pageOffset,
          gwEui: widget.gwEuiId ?? "");
      bool hasMoreData = listAmpsDeviceItem.result.length == AppStringConstants.ampPrePageLimit ||
          listAmpsDeviceItem.result.length > AppStringConstants.ampPrePageLimit;
      paginationHelper.updatePagination(listAmpsDeviceItem.result.length,
          hasMore: hasMoreData, pageLimit: AppStringConstants.ampPrePageLimit);
      if (mounted) {
        ampsDeviceDataSource = NodeGwAmpsDataSource(context, listAmpsDeviceItem.result);
      }
    } catch (e) {
      ampsErrorMessage = S.of(context).somethingWentWrong;
      ampsDeviceDataSource = NodeGwAmpsDataSource(context, []);
      apiStatus = ApiStatus.success;
      update();
    } finally {
      apiStatus = ApiStatus.success;
      update();
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<NodeGwController>(
      init: NodeGwController(),
      builder: (NodeGwController controller) {
        nodeGwController = controller;
        return getBody();
      },
    );
  }

  Widget getBody() {
    return getTableBoardView().paddingOnly(top: 15);
  }

  Widget getTableBoardView() {
    if (apiStatus == ApiStatus.loading) {
      return ListView(
        shrinkWrap: true,
        children: [
          Align(
              alignment: Alignment.center,
              child: Container(
                  height: 400,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: AppColorConstants.colorWhite,
                    borderRadius:   const BorderRadius.only(
                      topRight: Radius.circular(8),
                      topLeft: Radius.circular(8),
                    ),
                    border: Border.all(color: AppColorConstants.colorH2, width: 1),
                  ),
                  child: const AppLoader())),

        ],
      );
    }
    int itemsPerPage =
        dataTableHelper.getCurrentPageDataLength(listAmpsDeviceItem.result, currentPageIndex);
    recordsInPage =
        (listAmpsDeviceItem.result.length > 10) ? itemsPerPage : listAmpsDeviceItem.result.length;
    return Column(
      children: [
        ClipRRect(
          borderRadius:
              const BorderRadius.only(topRight: Radius.circular(8), topLeft: Radius.circular(8)),
          child: Container(
              width: double.infinity,
              decoration: dataTableHelper.tableBorderDeco(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Table
                  SizedBox(
                    height: (listAmpsDeviceItem.result.isNotEmpty)
                        ? (recordsInPage * heightOfDataTableCell) + (recordsInPage * 0.1) + 150
                        : (recordsInPage * heightOfDataTableCell) + (recordsInPage * 0.1) + 350,
                    child: getDataTableWithPageBoardView(),
                  ),
                  AppPaginationWidget(
                    apiStatus: apiStatus,
                    paginationHelper:paginationHelper,
                    onLoadNext: () async {
                      await  loadNextLogs(context);
                    },
                    onLoadPrevious: () async {
                      await  loadPreviousLogs(context);
                      nodeGwController.update();
                    },
                    onGoToFirstPage: () {
                      paginationHelper.setPage(0);
                      paginatorController.goToFirstPage();

                      nodeGwController.update();
                    },
                    onGoToLastPage: () {
                      paginatorController.goToLastPage();

                      nodeGwController.update();
                    },
                    itemsPerPage: AppStringConstants.ampPrePageLimit,
                    onChanged: (value) {
                      if (apiStatus != ApiStatus.loading) {
                        AppStringConstants.ampPrePageLimit = int.parse(value);
                        getDeviceListData();
                        nodeGwController.update();
                      }
                    },
                  ),
                  SizedBox(height: getSize(20)),
                  Container(
                    height: 1,
                    width: double.infinity,
                    color: AppColorConstants.colorBlack12,
                  ),
                ],
              )),
        ),
        Align(
            alignment: Alignment.centerRight,
            child:ampsErrorMessage.isEmpty
                ? const SizedBox(height: 37)
                : errorMessageView(errorMessage: ampsErrorMessage,padding: 5)),
      ],
    );
  }


  Widget getDataTableWithPageBoardView() {
    return PaginatedDataTable2(
      columnSpacing: 20,
      rowsPerPage: AppStringConstants.ampPrePageLimit,
      initialFirstRowIndex:
      paginationHelper.currentPage * AppStringConstants.ampPrePageLimit,
      headingCheckboxTheme: CheckboxThemeData(
          side: BorderSide(width: 2, color: AppColorConstants.colorWhite)),
      headingTextStyle:
      dataTableHelper.headingTextStyle(),
      wrapInCard: false,
      datarowCheckboxTheme: const CheckboxThemeData(
          side: BorderSide(width: 2, color: Colors.grey)),
      border: dataTableHelper.tableBorder(),
      renderEmptyRowsInTheEnd: false,
      headingRowColor:
      dataTableHelper.headingRowColor(),
      columns: _getDataColumns(),
      controller: paginatorController,
      source: ampsDeviceDataSource,
      onSelectAll:
      ampsDeviceDataSource.selectAll,
      minWidth: 1170,
      dataRowHeight: 51,
      // For progress indicator
      hidePaginator: true,
      empty:
      dataTableHelper.getEmptyTableContent(context),
    );
  }

  List<DataColumn> _getDataColumns() {
    return [
      DataColumn2(fixedWidth: 90, label: AppText(S.of(context).alarmStatus)),
      DataColumn2(fixedWidth: getSize(220),label: AppText(S.of(context).deviceEUI)),
      DataColumn2(fixedWidth: getSize(200), label: Center(child: AppText(S.of(context).type))),
      DataColumn2(fixedWidth: getSize(130), label: Center(child: AppText(S.of(context).status))),
      DataColumn2(fixedWidth: getSize(180), label: Center(child: AppText(S.of(context).assetID))),
      if(AppConfig.shared.isQLCentral) DataColumn2(fixedWidth: getSize(120), label: AppText(S.of(context).site)),
      DataColumn2( label: AppText(S.of(context).lastSeen)),
    ];
  }

  Future<void> loadPreviousLogs(BuildContext context) async {
    paginationHelper.setPage(paginationHelper.currentPage - 1);
    paginatorController.goToPreviousPage();
    nodeGwController.update();
  }

  Future<void> loadNextLogs(BuildContext context) async {
    if (paginationHelper.canGoToNextPage) {
      paginationHelper.setPage(paginationHelper.currentPage + 1);
      if (paginationHelper.currentPage >= paginationHelper.totalPages) {
        paginationHelper.pageOffset = listAmpsDeviceItem.result.length;
        await updateAmplifierPageData();
      } else {
        paginatorController.goToNextPage();
        nodeGwController.update();
      }
    }
  }

  updateAmplifierPageData() async {
    apiStatus = ApiStatus.loading;
    nodeGwController.update();
    ProvisioningDeviceList ampDeviceList = ProvisioningDeviceList.empty();
    ampDeviceList = await nodeGwController.getNodeGwAmpsDeviceList(
        context: context,
        perPageLimit: AppStringConstants.ampPrePageLimit,
        pageOffset: paginationHelper.pageOffset,
        gwEui:  widget.gwEuiId ?? "");
    if (ampDeviceList.result.isNotEmpty) {
      listAmpsDeviceItem.result.addAll(ampDeviceList.result);
      ampsDeviceDataSource = NodeGwAmpsDataSource(context, listAmpsDeviceItem.result);
      bool hasMoreData = ampDeviceList.result.length == AppStringConstants.ampPrePageLimit;
      paginationHelper.updatePagination(listAmpsDeviceItem.result.length, hasMore: hasMoreData,pageLimit: AppStringConstants.ampPrePageLimit);
    } else {
      paginationHelper.setPage(paginationHelper.currentPage - 1);
      paginationHelper.updatePagination(listAmpsDeviceItem.result.length, hasMore: false,pageLimit: AppStringConstants.ampPrePageLimit);
      Future.delayed(const Duration(milliseconds: 100)).then((value) {
          if (ampDeviceList.result.isNotEmpty) paginatorController.goToLastPage();
        ampsDeviceDataSource.notifyListeners();
      });
    }
    apiStatus = ApiStatus.success;
    nodeGwController.update();
  }
  
}
