import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/pages/node_gws/node_gw_detail/gw_diagnostics_socket/gw_diagnostics_socket.dart';
import 'package:quantumlink_node/pages/node_gws/node_gw_detail/gw_setting/gw_setting_page.dart';
import 'package:quantumlink_node/pages/node_gws/node_gw_detail/nodegw_amplifier_page/nodegw_amplifier_page.dart';

import '../../../app/cache/app_shared_preference.dart';

class NodeGwDetailPage extends StatefulWidget {
  final GatewayItem gatewayItem;
  const NodeGwDetailPage(this.gatewayItem, {super.key});

  @override
  State<NodeGwDetailPage> createState() => _NodeGwDetailPageState();
}

class _NodeGwDetailPageState extends State<NodeGwDetailPage> with TickerProviderStateMixin {
  int currentSubTab = 0;
  String previousDeviceEui = "";
  List<String> subTabList = [
    "Details",
    "Diagnostics",
    //"Topology"
  ];
  late ScreenLayoutType screenLayoutType;
  late TabController subTabController;
  late NodeGwController controller;
  String gwIDFormWebHost = "";

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    manageSubTab();
    getGWEui();
  }

  manageSubTab() {
    subTabController = TabController(
        initialIndex: currentSubTab, length: 2, vsync: this, animationDuration: Duration.zero);
  }

  getGWEui() async {
    if (AppConfig.shared.isOpenFromBLE) {
      gwIDFormWebHost = await getPrefStringValue(AppSharedPreference.gw) ?? '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<NodeGwController>(
      init: NodeGwController(),
      builder: (NodeGwController nodeGwController) {
        controller = nodeGwController;
        return ScreenLayoutTypeBuilder(builder: (context, screenType, constraints) {
          screenLayoutType = screenType;
          return MergeSemantics(
            child: ListView(
              shrinkWrap: true,
              children: [
                Divider(
                  height: getSize(1),
                  color: AppColorConstants.colorDotLine,
                ).paddingSymmetric(horizontal: 15),
                getTabsHeaderView(controller, screenType).paddingSymmetric(
                    horizontal: screenType == ScreenLayoutType.mobile ? getSize(0) : getSize(15)),
                Divider(
                  height: getSize(1),
                  color: AppColorConstants.colorDotLine,
                ).paddingOnly(left: 15,right: 15,bottom: 5),
                tabContainView()
              ],
            ),
          );
        });
      },
    );
  }

  Widget getTabsHeaderView(NodeGwController controller, ScreenLayoutType screenType) {
    return Row(
      children: [
        Flexible(
          child: TabBar(
              indicatorPadding: EdgeInsets.symmetric(horizontal: getSize(15)),
              controller: subTabController,
              dividerColor: AppColorConstants.colorWhite,
              labelPadding: EdgeInsets.only(left: getSize(5)),
              labelColor: Colors.white,
              padding: EdgeInsets.zero,
              isScrollable: true,
              indicatorColor: AppColorConstants.colorChartLine1,
              tabAlignment: TabAlignment.start,
              onTap: (value) {
                currentSubTab = value;
                controller.update();
              },
              tabs: List.generate(subTabList.length, (index) {
                String ampTabItem = subTabList[index];
                return Tab(
                  height: 40,
                  child: Padding(
                    padding:
                        EdgeInsets.only(left: getSize(30), right: getSize(30), top: getSize(8)),
                    child: commonTabContainView(ampTabItem, index),
                  ),
                );
              })),
        ),
      ],
    );
  }

  Widget commonTabContainView(String ampTabItem, int index) {
    return Row(
      mainAxisSize: MainAxisSize.min, // Prevents unnecessary expansion
      children: [
        AppText(
          isSelectableText: false,
          ampTabItem,
          style: TextStyle(
            fontFamily: AppAssetsConstants.openSans,
            fontWeight: currentSubTab == index ? getMediumBoldFontWeight() : getMediumFontWeight(),
            fontSize: 14,
            color: currentSubTab == index
                ? AppColorConstants.colorLightBlue
                : AppColorConstants.colorBlack,
          ),
        )
      ],
    );
  }

  Widget tabContainView() {
    final gwEui = gwIDFormWebHost.isNotEmpty
        ? gwIDFormWebHost
        : widget.gatewayItem.gwEui;
    switch (currentSubTab) {
      case 0:
        return GWSettingsPage(gwEuiId: gwEui);
      case 1:
        return GWDiagnosticsSocketPage(gwEuiId: gwEui);
      case 2:
        return NodeGwAmplifierPage(gwEuiId: gwEui);
      default:
        return const SizedBox();
    }
  }
}
