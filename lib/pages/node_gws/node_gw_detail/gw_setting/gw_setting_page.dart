import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/controller/gw_setting_controller.dart';
import 'package:quantumlink_node/pages/node_gws/node_gw_detail/gw_setting/gw_setting_page_helper.dart';



class GWSettingsPage extends StatefulWidget {
  final  String ? gwEuiId;
  const GWSettingsPage({this.gwEuiId,super.key});

  @override
  State<GWSettingsPage> createState() => GWSettingsPageState();
}

class GWSettingsPageState extends State<GWSettingsPage> {
  GWSettingsPageHelper? _pageHelper;
  late ScreenLayoutType screenLayoutType;
  late GWSettingController gwController;

  @override
  Widget build(BuildContext context) {
    _pageHelper ?? (_pageHelper = GWSettingsPageHelper(this));
    return GetBuilder<GWSettingController>(
      init: GWSettingController(),
      builder: (GWSettingController controller) {
        gwController = controller;
        return ScreenLayoutTypeBuilder(
          builder: (context, screenType, constraints) {
            screenLayoutType = screenType;
            return SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: getSize(15)),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (screenType == ScreenLayoutType.desktop)
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                buildGwDetailView(),
                                getUSRSSIDetailView(),
                              ],
                            ),
                          ),
                          Expanded(
                            child: Column(
                              children: [
                                ingressSwitchView().paddingOnly(left: 10,bottom: 10),
                                statsView().paddingOnly(left: 10),
                                gainRefreshButtonView(),
                                if (_pageHelper?.statsErrorMessage != null)
                                  Align(alignment:Alignment.centerRight ,child:errorMessageView(errorMessage: _pageHelper?.statsErrorMessage ?? ""),),
                              ],
                            ),
                          ),
                        ],
                      )
                    else ...[
                      buildGwDetailView(),
                      ingressSwitchView().paddingOnly(top: 10),
                      getUSRSSIDetailView(),
                      statsView().paddingOnly(top: 10),
                      gainRefreshButtonView(),
                      if (_pageHelper?.statsErrorMessage != null)
                      Align(alignment:Alignment.centerRight ,child:errorMessageView(errorMessage: _pageHelper?.statsErrorMessage ?? ""),),

                    ],

                    const SizedBox(height: 10),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
  Widget getPageAppBar() {
    return Container(
      decoration: BoxDecoration(
          border: Border.all(color: AppColorConstants.colorH2, width: 0.8),
          color: AppColorConstants.colorWhite,
          borderRadius: BorderRadius.only(
              topRight: Radius.circular(getSize(7)), topLeft: Radius.circular(getSize(7)))),
      alignment: Alignment.topCenter,
      padding: const EdgeInsets.symmetric(horizontal: 8,vertical:8),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AppText(
                    _pageHelper!.currentGatewayId.isNotEmpty
                        ? "${S.of(context).gwEUI} : ${_pageHelper?.currentGatewayId.replaceAll(":", "")}"
                        : "",
                    style: TextStyle(
                        fontFamily: AppAssetsConstants.openSans,
                        fontWeight: FontWeight.w700,
                        color: AppColorConstants.colorPrimary,
                        fontSize: getSize(15)),
                  ),
                  AppText(
                    _pageHelper?.gatewayItem.name != null
                        ? "${S.of(context).gwName} : ${_pageHelper?.gatewayItem.name ?? ""}"
                        : "",
                    style: TextStyle(
                        fontFamily: AppAssetsConstants.openSans,
                        fontWeight: FontWeight.w700,
                        color: AppColorConstants.colorPrimary,
                        fontSize: getSize(15)),
                  ),
                ],
              ),
              if (screenLayoutType != ScreenLayoutType.mobile)
                Flexible(
                  child: Column(crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      if(_pageHelper!.hwVersion.isNotEmpty) AppText(
                        "${S.of(context).hwVersion} : ${"${_pageHelper?.hwVersion}"}",
                        style: TextStyle(
                            fontFamily: AppAssetsConstants.openSans,
                            fontWeight: FontWeight.w500,
                            color: AppColorConstants.colorBlackBlue,
                            fontSize: getSize(14)),
                      ).paddingOnly(right: 10),
                      if (_pageHelper!.sWVersion.isNotEmpty)
                        AppText(
                          "${S.of(context).swVersion} : ${"${_pageHelper?.sWVersion}"}",
                          style: TextStyle(
                              fontFamily: AppAssetsConstants.openSans,
                              fontWeight: FontWeight.w500,
                              color: AppColorConstants.colorBlackBlue,
                              fontSize: getSize(14)),
                        ).paddingOnly(right: 10),
                    ],
                  ),
                )
            ],
          ),
          if (screenLayoutType == ScreenLayoutType.mobile) ... [
            const SizedBox(height: 5),
            if (_pageHelper!.hwVersion.isNotEmpty)
              AppText(
                "${S.of(context).hwVersion} : ${"${_pageHelper?.hwVersion}"}",
                style: TextStyle(
                    fontFamily: AppAssetsConstants.openSans,
                    fontWeight: FontWeight.w500,
                    color: AppColorConstants.colorBlackBlue,
                    fontSize: getSize(14)),
              ),
            if (_pageHelper!.sWVersion.isNotEmpty)
              AppText(
                "${S.of(context).swVersion} : ${"${_pageHelper?.sWVersion}"}",
                style: TextStyle(
                    fontFamily: AppAssetsConstants.openSans,
                    fontWeight: FontWeight.w500,
                    color: AppColorConstants.colorBlackBlue,
                    fontSize: getSize(14)),
              )
          ],
          if (_pageHelper?.gwDetailErrorMessage != null)
            CommonAPIErrorView(
              errorMessage: _pageHelper!.gwDetailErrorMessage ?? "",
              rightPadding: 5,
            )
        ],
      ),
    );
  }
  Widget ingressSwitchView() {
    return Column(
      children: [
        Stack(alignment: Alignment.center,
          children: [
            Container(
              decoration: borderViewDecoration,
              child: Column(
                children: [
                  commonIngressSwitchView(
                    onChanged: (value) {
                      _pageHelper?.updateGainValue(value);
                      _pageHelper?.indicatorValue = value;
                      gwController.update();
                    },
                    isActive: true,
                    onChangeEnd: (value) async {},
                    title: "",
                    ingressValue: _pageHelper!.indicatorValue,
                  ),
                  saveAndCancelButtonView(),
                ],
              ),
            ),
            if (_pageHelper?.apiStatus == ApiStatus.loading)
              const Center(child: SizedBox(height: 60, width: 60, child: AppLoader()))
          ],
        ),
        if (_pageHelper?.saveGainErrorMessage != null)
          CommonAPIErrorView(
              errorMessage: _pageHelper?.saveGainErrorMessage ?? "", rightPadding: 5)
      ],
    );
  }


  Widget saveAndCancelButtonView() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0,horizontal: 10),
      child: Row(mainAxisAlignment: MainAxisAlignment.end,
        children: [
          SizedBox(
            width: 100,
            child: AppButton(
                buttonHeight: 30,
                buttonName: S.of(context).cancel,
                onPressed: () {
                  _pageHelper!.cancelGWSettingValue();
                },
                fontSize: 15,
                fontColor: AppColorConstants.colorBlackBlue,
                fontFamily: AppAssetsConstants.poppins,
                buttonStyle: ElevatedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      side: BorderSide(
                          color: AppColorConstants.colorH2,width: 1.5),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    foregroundColor: AppColorConstants.colorWhite,
                    backgroundColor: AppColorConstants.colorWhite,
                    elevation: 4,
                    padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
                    shadowColor: AppColorConstants.colorGray.withOpacity(0.3))),
          ),
          SizedBox(width: getSize(10)),
          SizedBox(
            width: 100,
            child: AppButton(
              loadingStatus: _pageHelper!.saveStatus,
              buttonHeight: 30,
              buttonName: S.of(context).save,
              fontColor: _pageHelper!.hasUnsavedChanges ? AppColorConstants.colorWhite : AppColorConstants.colorBlackBlue,
              fontFamily: AppAssetsConstants.poppins,
              fontSize: 15,
              onPressed: (_pageHelper!.hasUnsavedChanges) ?() {
                if(_pageHelper!.currentGatewayId.isEmpty) return;
                _pageHelper!.saveGWSettingValue(_pageHelper!.indicatorValue);
              } :null,
              buttonStyle: ElevatedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                    side: BorderSide(color:_pageHelper!.hasUnsavedChanges ? AppColorConstants.colorLightBlue : AppColorConstants.colorH2,width: 1.5),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  foregroundColor: AppColorConstants.colorWhite,
                  backgroundColor: AppColorConstants.colorLightBlue,
                  elevation: 4,
                  padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
                  shadowColor: AppColorConstants.colorGray.withOpacity(0.3)),
            ),
          )
        ],
      ),
    );
  }

  Widget getUSRSSIDetailView() {
    final isLoading =
        _pageHelper!.rssiStatsListData.isEmpty && _pageHelper!.apiStatus == ApiStatus.loading;
    final isEmptyAndLoaded =
        _pageHelper!.rssiStatsListData.isEmpty && _pageHelper!.apiStatus != ApiStatus.initial;
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.only(top: getSize(10)),
          child: Container(
            decoration: BoxDecoration(
              color: AppColorConstants.colorWhite,
              border: Border.all(color: AppColorConstants.colorDotLine, width: 1.5),
              borderRadius: BorderRadius.circular(
                getSize(10),
              ),
            ),
            child: Column(
              children: [
                commonTitleView(title: S.of(context).usRssi),
                if (isLoading)
                  const SizedBox(height: 200, width: 60, child: AppLoader())
                else if (isEmptyAndLoaded)
                  _buildEmptyStatsView()
                else
                  ListView.builder(padding: const EdgeInsets.only(bottom: 10),
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: _pageHelper?.rssiStatsListData.length,
                      itemBuilder: (context, index) {
                        return commonSubTitleView(
                            subTitle: _pageHelper!.rssiStatsListData[index]!['device'].toString(),
                            value: _pageHelper!.rssiStatsListData[index]!['rssi'].toString());
                      }),
              ],
            ),
          ),
        ),
      ],
    );
  }
  Widget buildGwDetailView()  {
    return Column(
      children: [
        Container(padding: const EdgeInsets.only(bottom: 5),
          decoration: BoxDecoration(
            color: AppColorConstants.colorWhite,
            border: Border.all(color: AppColorConstants.colorDotLine, width: 1.5),
            borderRadius: BorderRadius.circular(
              getSize(10),
            ),
          ),
          child: Column(
            children: [
              commonTitleView(title: S.of(context).gwDetail),
              commonSubTitleView(
                  subTitle:S.of(context).gwEUI ,
                  value: _pageHelper?.currentGatewayId),
              commonSubTitleView(
                  subTitle:S.of(context).gwName ,
                  value: _pageHelper?.gatewayItem.name ?? ""),
              commonSubTitleView(
                  subTitle:S.of(context).currantUser ,
                  value: _pageHelper?.gatewayItem.userEmail ?? ""),
              commonSubTitleView(
                  subTitle:S.of(context).status ,
                  value: _pageHelper?.gatewayItem.status ?? ""),
              commonSubTitleView(
                  subTitle:S.of(context).swVersion ,
                  value: _pageHelper?.sWVersion),
              commonSubTitleView(
                  subTitle:S.of(context).hwVersion ,
                  value: _pageHelper?.hwVersion),
              commonSubTitleView(
                  subTitle:S.of(context).lastSeen ,
                  value: _pageHelper?.gatewayItem.lastSeen != null ? getUtcTimeZone(_pageHelper?.gatewayItem.lastSeen) : ''),
            ],
          ),
        ),
      ],
    );
  }

  Widget statsView() {
    final isLoading = _pageHelper?.statsListData.isEmpty == true &&
        _pageHelper?.apiStatus == ApiStatus.loading;
    final isEmptyAndLoaded = _pageHelper?.statsListData.isEmpty == true &&
        _pageHelper?.apiStatus != ApiStatus.initial;

    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
            color: AppColorConstants.colorWhite,
            border: Border.all(color: AppColorConstants.colorDotLine, width: 1.5),
            borderRadius: BorderRadius.circular(getSize(10)),
          ),
          child: Column(
            children: [
              commonTitleView(title: S.of(context).stats),
              if (isLoading)
                const SizedBox(height: 200, width: 60, child: AppLoader())
              else if (isEmptyAndLoaded)
                _buildEmptyStatsView()
              else
                _buildStatsListView(),
              const SizedBox(height: 8),
            ],
          ),
        ),
      ],
    );
  }


  Widget _buildStatsListView() {
    return Column(
      children: [
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _pageHelper?.statsListData.length ?? 0,
          itemBuilder: (context, index) {
            final entry = _pageHelper!.statsListData[index];
            final label = _pageHelper?.keyLabels[entry.key] ?? entry.key;
            return commonSubTitleView(
              subTitle: label.toString(),
              value: entry.value.toString(),
            );
          },
        ),
        const SizedBox(height: 12),
        Align(
          alignment: Alignment.bottomRight,
          child: Padding(
            padding: const EdgeInsets.only(right: 15),
            child: SizedBox(
              width: 100,
              child: AppButton(
                buttonName: S.of(context).reset,
                onPressed: () {
                  DialogUtils().confirmationDialog(
                    context,
                    "${S.of(context).reset} ${S.of(context).msgAskConfirmationTitle}",
                    S.of(context).msgAskResetPktFwd,
                    S.of(context).yes,
                    S.of(context).no,
                        () async {
                      goBack();
                      _pageHelper?.resetStatsValue();
                    },
                        () => goBack(),
                  );
                },
                fontSize: 15,
                fontColor: AppColorConstants.colorBlackBlue,
                fontFamily: AppAssetsConstants.poppins,
                buttonStyle: ElevatedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                    side: BorderSide(color: AppColorConstants.colorH2,width: 1.5),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  foregroundColor: AppColorConstants.colorWhite,
                  backgroundColor: AppColorConstants.colorWhite,
                  padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
                  shadowColor: AppColorConstants.colorGray,
                  elevation: 1,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyStatsView() {
    return Padding(
      padding: const EdgeInsets.all(30),
      child: Container(
        height: 55,
        width: double.infinity,
        color: AppColorConstants.colorWhite.withOpacity(0.8),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const AppImageAsset(
              image: AppAssetsConstants.emptyLogo,
              height: 30,
            ),
            AppText(
              S.of(context).noDataFound,
              style: TextStyle(
                color: AppColorConstants.colorH2,
                fontFamily: AppAssetsConstants.openSans,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget commonIngressSwitchView(
      {required double ingressValue,
      required String title,
      required ValueChanged<double>? onChanged,
      required bool isActive,
      required ValueChanged<double>? onChangeEnd}) {
    return Padding(
        padding: const EdgeInsets.symmetric(vertical: 30,horizontal: 8).copyWith(top: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 50),
            Padding(
              padding: EdgeInsets.only(left: 10, bottom: getSize(10)),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AppText(
                    "${S.of(context).nodeGWDSGain} ${_pageHelper!.dsGain.isNotEmpty ? "(${_pageHelper?.dsGain})" : " "}",
                    style: TextStyle(
                        fontFamily: AppAssetsConstants.openSans,
                        fontWeight: FontWeight.w600,color: AppColorConstants.colorPrimary,
                        fontSize: getSize(15)),
                  ),
                  Flexible(
                    child: Column(
                      children: [
                        Stack(
                          children: [
                            SliderTheme(
                              data: SliderTheme.of(context).copyWith(
                                trackHeight: 8.3,
                                thumbColor: AppColorConstants.colorPrimary,
                                overlayColor: AppColorConstants.colorH2.withOpacity(0.4),
                                overlayShape: const RoundSliderOverlayShape(overlayRadius: 12),
                                activeTrackColor: AppColorConstants.colorLow,

                                valueIndicatorColor:
                                    AppColorConstants.colorChartLine.withOpacity(0.6),
                                showValueIndicator: ShowValueIndicator.onlyForContinuous,thumbShape: CustomThumbShape()

                              ),
                              child: Slider(label:ingressValue.toInt().toString(),
                                inactiveColor: AppColorConstants.colorLimeGray,
                                value: ingressValue.toDouble(),
                                min: _pageHelper!.minValue,
                                max: _pageHelper!.maxValue,
                                onChanged: (double newValue) {
                                  if (isActive && _pageHelper!.apiStatus != ApiStatus.loading) {
                                    onChanged!(newValue);
                                  }
                                },
                              ),
                            ),
                          ],
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            commonGainTypeString('Low (${_pageHelper?.loraDsCfgData['low']?['min'] ?? ""})'),
                            commonGainTypeString('Medium (${_pageHelper?.mediumValue})').paddingOnly(left: 5),
                            commonGainTypeString('High (${_pageHelper?.loraDsCfgData['high']?['max'] ?? ""})'),
                          ],
                        )
                      ],
                    ).paddingSymmetric(horizontal: 10),
                  ),
                ],
              ),
            ),
          ],
        ));
  }

  Widget commonGainTypeString(String text){
    return   Column(
      children: [
        Container(
          height: getSize(5),
          width: getSize(1.5),
          color: AppColorConstants.colorH1,
        ),
         AppText(
          text,
          style: const TextStyle(fontFamily: AppAssetsConstants.openSans,fontWeight: FontWeight.w600,fontSize: 13),
        ),
      ],
    );
  }

  Widget commonTitleView(
      {required String title, double? width = double.infinity}) {
    return Container(
      alignment: Alignment.center,
      height: getSize(50),
      width: width,
      padding: EdgeInsets.only(top: getSize(13), left: getSize(20)),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(getSize(7)),
          topLeft: Radius.circular(getSize(7)),
        ),
      ),
      child: AppText(
        title,
        style: TextStyle(
            fontFamily: AppAssetsConstants.openSans,
            fontWeight: getMediumBoldFontWeight(),
            letterSpacing: 0.32,
            color: AppColorConstants.colorLow,
            fontSize: getSize(18)),
      ),
    );
  }

  Widget commonSubTitleView(
      {required String subTitle, required dynamic value , bool isFlex = false}) {
    return Padding(
        padding: EdgeInsets.only(
            left: getSize(25),
            right: getSize(20),
            top: getSize(4),
            bottom: getSize(10)),
        child: Row(
          children: [
            AppText(
              subTitle,
              style: TextStyle(
                  color: AppColorConstants.colorBlackBlue,
                  fontFamily: AppAssetsConstants.openSans,
                  fontWeight: FontWeight.w600,
                  fontSize: getSize(14)),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: DottedLine(color: AppColorConstants.colorDotLine),
              ),
            ),
            if(isFlex)Expanded(child: AppText(
              "$value",
              style: TextStyle(
                  fontFamily: AppAssetsConstants.openSans,
                  fontWeight: FontWeight.w500,
                  color: AppColorConstants.colorBlackBlue,
                  fontSize: getSize(14)),
            ))else
              AppText(
                "$value",
                style: TextStyle(
                    fontFamily: AppAssetsConstants.openSans,
                    fontWeight: FontWeight.w500,
                    color: AppColorConstants.colorBlackBlue,
                    fontSize: getSize(14)),
              ),
          ],
        ));
  }

  Widget gainRefreshButtonView() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5,horizontal: 5),
      child: Row(mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Flexible(child: buildLastSeenView(apiStatus: _pageHelper!.apiStatus, lastSeen: _pageHelper!.statsLastUpdateTime)),
          AppRefresh(
            loadingStatus: _pageHelper!.apiStatus,
            buttonColor: AppColorConstants.colorPrimary,
            onPressed:  (_pageHelper!.currentGatewayId.isEmpty) ? null : () async {
              if (_pageHelper!.apiStatus != ApiStatus.loading) {
                _pageHelper!.statsErrorMessage = null;
                _pageHelper!.apiStatus = ApiStatus.loading;
                _pageHelper!.update();
                 await _pageHelper!.getStats();
                _pageHelper!.apiStatus = ApiStatus.success;
                _pageHelper!.statsLastUpdateTime = DateTime.now();
                _pageHelper!.update();
              }
            },
          ),
        ],
      ),
    );
  }
  Widget buildLastSeenView({ApiStatus ? apiStatus , DateTime ? lastSeen}) {
    if (apiStatus == ApiStatus.loading) {
      return getTimeDurationView(
        refreshStatus: apiStatus,
        updateTime: lastSeen,
        onTapTime: DateTime.now(),
      );
    } else {
      if (lastSeen != null) {
        return  getLastSeenView(lastSeen);
      } else {
        return Container();
      }
    }
  }
  errorMessageView({required String errorMessage,double ?padding}) {
    return Padding(
      padding: EdgeInsets.all(padding ?? 8.0),
      child: CustomPaint(
        painter: DottedBorderPainter(
          borderColor: AppColorConstants.colorRedLight.withOpacity(0.8),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 8.0),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.error_outline,
                  color: AppColorConstants.colorRedLight, size: 15),
              const SizedBox(width: 5),
              Flexible(
                child: AppText(
                  errorMessage,
                  style: TextStyle(
                    color: AppColorConstants.colorDarkBlue,
                    fontSize: 12,
                    fontFamily: AppAssetsConstants.openSans,
                    fontWeight: getMediumFontWeight(),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  BoxDecoration borderViewDecoration = BoxDecoration(
    border: Border.all(color: AppColorConstants.colorChart, width: 1.8),
    borderRadius: const BorderRadius.all(Radius.circular(8)),
  );
}
