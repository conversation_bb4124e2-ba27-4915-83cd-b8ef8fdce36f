import 'package:http/http.dart';
import 'package:quantumlink_node/app/cache/app_shared_preference.dart';
import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/pages/node_gws/node_gw_detail/gw_setting/gw_setting_page.dart';
import 'package:universal_html/html.dart';

class GWSettingsPageHelper {
  late GWSettingsPageState state;
  ApiStatus apiStatus = ApiStatus.initial;
  ApiStatus saveStatus = ApiStatus.initial;
  double indicatorValue = 0;
  DateTime? statsLastUpdateTime;
  Timer? statsRefreshTimer;
  String? statsErrorMessage;
  String? gwDetailErrorMessage;
  String? saveGainErrorMessage;
  String? gwIDErrorMessage;
  String currentGatewayId ="";
  String dsGain="";
  double minValue = 0;
  double maxValue = 0;
  double mediumValue = 0;
  String hwVersion= "";
  String sWVersion= "";
  GatewayItem gatewayItem = GatewayItem.empty();
  Map<String, dynamic>  loraDsCfgData ={};
  Map<String, dynamic>  getDsGainData ={};
  List<Map<String, dynamic>?> rssiStatsListData = [];
  List<MapEntry<String, dynamic>> statsListData = [];
  Map<String, String> keyLabels = {
    'recv_pkts': 'US pkts received',
    'recv_crc_err_pkts': 'US CRC errors',
    'forw_pkts': 'US pkts sent',
    'ack_forw_pkts': 'US pkts acked',
    'dn_rx_pkts': 'DS pkts received',
    'dn_tx_pkts': 'DS pkts sent',
    'join_req_pkts': 'Join requests',
    'join_ack_pkts': 'Join accepts',
  };


  num currentGainValue=0;
  num lastSavedGainValue=0;
  bool hasUnsavedChanges=false;

  GWSettingsPageHelper(this.state) {
    Future.delayed(const Duration(milliseconds: 500)).then((value) async {
      getSettings();
      loadGatewayId();
      await gwSettingHealthCheck();
    });
  }

  Future<void> loadGatewayId() async {
    if (state.widget.gwEuiId != null) {
      currentGatewayId = state.widget.gwEuiId!;
    } else {
      currentGatewayId = await getPrefStringValue(AppSharedPreference.gw) ?? '';
    }
  }

  void update(){
   state.gwController.update();
  }


  void initializeStatsTimer() {
    statsLastUpdateTime = null;
    statsRefreshTimer?.cancel();
  }


  Future <void > gwSettingHealthCheck() async {
    apiStatus = ApiStatus.loading;
    update();
    try {
      final response = await state.gwController.gwSettingHealthCheck(context: state.context);
      if (response != null && response.isNotEmpty) {
        Map<String, dynamic> data = response;
        if(data['statusCode'] == 200){
          apiStatus = ApiStatus.success;
          update();
          if (currentGatewayId.isNotEmpty) {
            await Future.wait([getGetWayData(), getStats()]);
          }else{
            gwIDErrorMessage= S.of(state.context).gwNotFoundMessage;
          }
        }
        apiStatus = ApiStatus.success;
        update();
      }else{
        apiStatus = ApiStatus.failed;
        update();
      }
    }  catch (e) {
      debugLogs('catch exception in gwSettingHealthCheck ---> ${e}');
    }

  }

  Future<void> getGetWayData() async {
    statsErrorMessage = null;
    await getGatDetail();
    statsLastUpdateTime = DateTime.now();
    update();
  }


  Future<void> getGatDetail() async {
    gwDetailErrorMessage =null;
    final gwVersionResponse = await state.gwController.getGWDetail(context: state.context,gwID: currentGatewayId);
    if (gwVersionResponse != null) {
      if (gwVersionResponse is GatewayItem) {
        gatewayItem = gwVersionResponse;
        hwVersion = gatewayItem.hwVersion ?? "";
        sWVersion = gatewayItem.swVersion ?? "";
      } else {
        gwDetailErrorMessage = gwVersionResponse['detail'];
      }
    } else {
      gwDetailErrorMessage = S.of(state.context).somethingWentWrong;
      update();
    }
  }

  void initializeDSGainValue(String dsGain) {
    currentGainValue = double.tryParse(dsGain) ?? minValue;
    if (currentGainValue < minValue) {
      currentGainValue = minValue;
    } else if (currentGainValue > maxValue) {
      currentGainValue = maxValue;
    }
    indicatorValue = currentGainValue.toDouble();
    update();
  }

  saveGWSettingValue(double indicatorValue) async {
    if(apiStatus == ApiStatus.loading)return;
    if(saveStatus == ApiStatus.loading)return;
    saveGainErrorMessage = null;
    saveStatus = ApiStatus.loading;
    update();
    try {
      Map<String, dynamic> bodyData = {
        "gain": indicatorValue.toInt()};
      final response =
          await state.gwController.setDsGain(context: state.context, bodyData: bodyData,gwID: currentGatewayId);
      if (response != null && response.isNotEmpty) {
        saveStatus = ApiStatus.success;
        response['message'].toString().showSuccess(state.context);
        hasUnsavedChanges = false;
        await getStats().then((value) {
          statsLastUpdateTime = DateTime.now();});
      }else{
        saveGainErrorMessage = S.of(state.context).somethingWentWrong;
      }
    } catch (e) {
      saveGainErrorMessage = S.of(state.context).somethingWentWrong;
      statsLastUpdateTime = DateTime.now();
      debugLogs("catch exception in saveGWSettingValue ---> ${e}");
    } finally {
      saveStatus = ApiStatus.success;
      update();
    }
  }

  Future<void> getStats() async {
    initializeStatsTimer();
    statsErrorMessage = null;
    statsLastUpdateTime = null;
    apiStatus = ApiStatus.loading;
    update();

    final response = await state.gwController.getStats(context: state.context,gwID: currentGatewayId);

    if (response != null) {
      if(response is GatewayStatsResponse ){
        GatewayStatsResponse statsResponse = response ;
        final result = statsResponse.result;
        if(statsResponse.result != null) {
          statsListData = result?.stats?.entries.toList() ?? [];
          if (result?.rssi is List) {
            final List? rawList = result!.rssi;
            rssiStatsListData = rawList!.map((e) => Map<String, dynamic>.from(e)).toList();
          } else {
            rssiStatsListData = [];
          }

          dsGain = "${result?.gain ?? ""}";
          if (dsGain != currentGainValue.toString() && !hasUnsavedChanges) {
            currentGainValue = int.parse(dsGain);
            lastSavedGainValue = int.parse(dsGain);
          }
          if(!hasUnsavedChanges)initializeDSGainValue(dsGain);
        }
      }else{
        statsErrorMessage = response['detail']  ?? S.of(state.context).somethingWentWrong;
      }

    } else {
      statsListData = [];
      rssiStatsListData = [];
      statsErrorMessage = S.of(state.context).somethingWentWrong;
      apiStatus = ApiStatus.failed;
    }
    statsLastUpdateTime = DateTime.now();
    apiStatus = ApiStatus.success;
    update();
    statsRefreshTimer= Timer(const Duration(seconds:10), () {
      getStats();
    });
  }

  bool _hasValueChanged() {
    if (currentGainValue.toInt() != lastSavedGainValue.toInt()) {
      return true;
    }
    return false;
  }

  void updateGainValue(num newValue) {
     currentGainValue = newValue;
     hasUnsavedChanges = _hasValueChanged();
     update();
  }
 void resetStatsValue() async {
    if(apiStatus == ApiStatus.loading)return;
    statsErrorMessage = null;
    apiStatus = ApiStatus.loading;
    update();
    try {
      final response =
      await state.gwController.resetStats(context: state.context,gwID: currentGatewayId);

      if (response != null) {
        Map<String, dynamic> data = jsonDecode(response);
        data['message'].toString().showSuccess(state.context);
        await Future.wait([getStats()]);
      }
    } catch (e) {
      statsErrorMessage = S.of(state.context).somethingWentWrong;
      debugLogs("catch exception in saveGWSettingValue ---> ${e}");
    } finally {
      apiStatus = ApiStatus.success;
      update();
    }
  }


  void getSettings() async {
    final configUrl = "${window.location.origin}/assets/"+ AppAssetsConstants.configPath + '?t=${DateTime.now().millisecondsSinceEpoch}';
    debugLogs("configUrl -> ");
    debugLogs(configUrl);
    final response = await get(Uri.parse(configUrl));
    String configDataString = "";
    if (response.statusCode == 200) {
      configDataString = response.body;
      debugLogs("configDataString -> from api");
    } else {
      debugLogs("configDataString -> from local");
      configDataString = await rootBundle.loadString(AppAssetsConstants.configPath);
    }
    //String configDataString = await rootBundle.loadString(AppAssetsConstants.configPath);
    Map mapData = jsonDecode(configDataString);
    loraDsCfgData = mapData["lora_ds_cfg"] ?? {};
    minValue = loraDsCfgData['low']?['min'] ?? -9;
    maxValue = loraDsCfgData['high']?['max'] ?? 22;
    mediumValue = ((minValue + maxValue) / 2).ceilToDouble();
    indicatorValue= minValue;
    update();
  }

  void cancelGWSettingValue(){
    currentGainValue = lastSavedGainValue;
    hasUnsavedChanges = false;
    indicatorValue= currentGainValue.toDouble();
    update();
  }


  double getNearestStage(double value) {
    if (value <= 10) {
      return 0; // Low
    } else if (value <= 20) {
      return 15; // Mid
    } else {
      return 30; // High
    }
  }

}
