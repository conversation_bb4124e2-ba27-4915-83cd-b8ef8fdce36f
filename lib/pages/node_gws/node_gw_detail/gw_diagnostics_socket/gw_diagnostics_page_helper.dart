import 'package:quantumlink_node/app/cache/app_shared_preference.dart';
import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/pages/node_gws/node_gw_detail/gw_diagnostics_socket/gw_diagnostics_socket.dart';
import 'package:quantumlink_node/pages/node_gws/node_gw_detail/gw_diagnostics_socket/gw_diagnostics_socket_datasource.dart';
import 'package:rxdart/rxdart.dart';


class GWDiagnosticsPageHelper {
  late GWDiagnosticsSocketPageState state;

  ApiStatus apiStatus = ApiStatus.initial;
  late ScreenLayoutType screenLayoutType;
  final key = GlobalKey<PaginatedDataTableState>();
  final double heightOfDataTableCell = 48;
  int recordsInPage = 0; // For Set Fixed and Static Height Of Table
  int currentPageIndex = 0;
  int perPageLimit=10;
  PaginatorController pageController = PaginatorController();
  SocketHelper socketHelper = SocketHelper();
  BehaviorSubject<bool> isStreamLoading = BehaviorSubject();
  List<DiagnosticsDataModel> listDiagnosticsData = [];
  late GWDiagnosticsDataSource gwDiagnosticsDataSource;
  DataTableHelper dataTableHelper = DataTableHelper();
  double tableHeight = 0;
  AmplifierDeviceList amplifierDeviceList = AmplifierDeviceList.empty();
  AmplifierDeviceList searchAmplifierDeviceList = AmplifierDeviceList.empty();
  PaginationHelper paginationHelper = PaginationHelper();
  late HomeController homeController ;
  List<String> listDeviceEUI = [];
  RxList<String> selectedListDeviceEUI = <String>[].obs;
  bool isTimeout = false;
  DateTime ? lastUpdateTime;
  Timer? _timeoutTimer;
  String gwIDFormWebHost = "";
  bool isTableView = true;
  late ScreenLayoutType previousLayoutType = ScreenLayoutType.desktop;
  GWDiagnosticsPageHelper(this.state) {
    socketHelper.connectDiagnosticsSocket();
    Future.delayed(const Duration(milliseconds: 500)).then((value) async {
       homeController = Get.find<HomeController>();
       await getWayEuiIdList();
       startTimeoutWatcher();
    });
    debugLogs("Socket URL -> ");
    debugLogs(Uri.parse(
        AppConfig.shared.wsUrl + RestConstants.instance.wsDiagnostics));
  }

  void startTimeoutWatcher() {
    _timeoutTimer?.cancel();
    _timeoutTimer = Timer(const Duration(seconds: 10), () {
      isTimeout = true;
      state.gwController.update();
    });
  }

  getWayEuiIdList() async {
    selectedListDeviceEUI.clear();
    apiStatus = ApiStatus.loading;
    state.gwController.update();
    await loadGatewayId();
    lastUpdateTime= DateTime.now();
    apiStatus = ApiStatus.success;
    state.gwController.update();
  }

  Future<void> loadGatewayId() async {
    if (state.widget.gwEuiId != null) { // from list
      gwIDFormWebHost = state.widget.gwEuiId ?? "";
      if (gwIDFormWebHost.isNotEmpty) getSocketData([gwIDFormWebHost]);
    } else { // from side menu
      gwIDFormWebHost = await getPrefStringValue(AppSharedPreference.gw) ?? '';
      if (gwIDFormWebHost.isNotEmpty) getSocketData([gwIDFormWebHost]);
    }
  }

  selectDevEUI(String devEUI,bool isAdd) {
    if (isAdd) {
      if (!selectedListDeviceEUI.contains(devEUI)) {
        selectedListDeviceEUI.add(devEUI);
      }
    } else {
      selectedListDeviceEUI.remove(devEUI);
    }
    if (selectedListDeviceEUI.isEmpty) {
      if(gwIDFormWebHost.isNotEmpty) getSocketData([gwIDFormWebHost]);
    } else {
      getSocketData(selectedListDeviceEUI, isDeviceEuiSend: true);
    }
  }

  getSocketData(List<String> formattedEuiList , {bool isDeviceEuiSend=false}) {
    listDiagnosticsData.clear();
    socketHelper.sendDiagnosticsDeviceEUIList(isDeviceEui: isDeviceEuiSend,formattedEuiList, (message) {
      handleWebSocketMessage(message);
    });
  }

  String getListToString(List<AmplifierDeviceItem> listAmps) {
    List deviceEUIList = listAmps.map((device) => device.deviceEui).toList();
    return deviceEUIList.map((e) => '"$e"').toList().toString();
  }

  String getSearchString(List<String> listAmps) {
    return listAmps.map((e) => '"$e"').toList().toString();
  }

  handleWebSocketMessage(String message) {
    debugLogs("VLGW Socket Msg ->");
    final data = jsonDecode(message) as Map<String, dynamic>;
    debugLogs(jsonEncode(data));
    DiagnosticsDataModel diagnosticsDataItem =
        DiagnosticsDataModel.fromJson(data);
    bool isUnique =
        listDiagnosticsData.every((item) => item.id != diagnosticsDataItem.id);
    if (isUnique) {
      listDiagnosticsData.add(diagnosticsDataItem);
      listDiagnosticsData.sort((a, b) => a.time.compareTo(b.time));
      gwDiagnosticsDataSource = GWDiagnosticsDataSource(
        state.context, listDiagnosticsData.reversed.toList(), (dataMap) {
        jsonTreeDataHandling(dataMap);
      });
      handleTableHeightChange();
    }

  }

  jsonTreeDataHandling(DiagnosticsDataModel dataMap) {
    String newJsonData = jsonEncode(dataMap);
    Map<String, dynamic> jsonData = jsonDecode(newJsonData);
    homeController.updateJsonData(jsonData);
    scaffoldKey.currentState?.openEndDrawer();
  }

  handleTableHeightChange() {
    int itemsPerPage = dataTableHelper.getCurrentPageDataLength(
        listDiagnosticsData, currentPageIndex);
    int recordsInPage = (listDiagnosticsData.length > 10)
        ? itemsPerPage
        : listDiagnosticsData.length;
    double height = (listDiagnosticsData.isNotEmpty)
        ? (recordsInPage * heightOfDataTableCell) + (recordsInPage * 0.1) + 190
        : (recordsInPage * heightOfDataTableCell) + (recordsInPage * 0.1) + 300;

    tableHeight = height;
  }


}
