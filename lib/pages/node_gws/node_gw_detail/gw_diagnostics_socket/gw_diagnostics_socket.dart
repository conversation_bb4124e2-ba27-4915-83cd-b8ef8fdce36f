import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/controller/gw_setting_controller.dart';
import 'package:quantumlink_node/pages/node_gws/node_gw_detail/gw_diagnostics_socket/gw_diagnostics_page_helper.dart';
import 'mobile_gw_diagnostics_socket.dart';

  class GWDiagnosticsSocketPage extends StatefulWidget {
  final  String ? gwEuiId;
  const GWDiagnosticsSocketPage({this.gwEuiId,super.key});

  @override
  State<GWDiagnosticsSocketPage> createState() => GWDiagnosticsSocketPageState();
}

class GWDiagnosticsSocketPageState extends State<GWDiagnosticsSocketPage> {
  GWDiagnosticsPageHelper ? _helper;
  late GWSettingController gwController;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _helper!.socketHelper.diagnosticsOnDispose();
    super.dispose();
  }
 


  @override
  Widget build(BuildContext context) {
    _helper ?? (_helper = GWDiagnosticsPageHelper(this));
    return GetBuilder<GWSettingController>(
      init: GWSettingController(),
      builder: (GWSettingController controller) {
        gwController = controller;
        return Stack(
          children: [
            getBodyView(),
          ],
        );
      },
    );
  }

  getBodyView() {
    return ScreenLayoutTypeBuilder(
      builder: (context, screenType, constraints) {
        _helper!.screenLayoutType = screenType;
        MobileDiagnosticsSocket().autoSelectTableType(_helper);
        return getDiagnosticsBoardView();
      },
    );
  }

  getDiagnosticsBoardView() {
    return Container(
      color: AppColorConstants.colorWhite,
      width: double.infinity,
      child: ListView(
        shrinkWrap: true,
        physics: const ClampingScrollPhysics(),
        padding: const EdgeInsets.symmetric(horizontal: 10).copyWith(bottom: 20),
        children: [
          getPageAppBar(),
          diagnosticsBoardView(),
          SizedBox(
            height: 40,
            child: Wrap(
              crossAxisAlignment: WrapCrossAlignment.end,
              alignment: WrapAlignment.end,
              children: [
                if (_helper!.gwIDFormWebHost.isEmpty &&
                    _helper!.apiStatus == ApiStatus.success)
                  errorMessageView(errorMessage: S.of(context).gwNotFoundMessage),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const SizedBox(
                      width: 5,
                    ),
                    getTimeDurationView(
                      onTapTime: DateTime.now(),
                      refreshStatus: _helper!.apiStatus,
                      updateTime: _helper!.lastUpdateTime,
                    ),
                    AppRefresh(
                        onPressed: () {
                          _helper!.isTimeout = false;
                          _helper!.startTimeoutWatcher();
                          _helper!.getWayEuiIdList();
                        },
                        loadingStatus: _helper!.apiStatus),
                  ],
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  getPageAppBar() {
    return Container(
      margin:const EdgeInsets.symmetric(horizontal: 5),
      alignment: Alignment.centerLeft,
      decoration: BoxDecoration(borderRadius: BorderRadius.only(
          topRight: Radius.circular(getSize(7)), topLeft: Radius.circular(getSize(7))),border:   Border.all(
        color: AppColorConstants.colorH2,
      ),
          color: AppColorConstants.colorWhite),
      padding: EdgeInsets.only(left: getSize(18)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: getSize(8)),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                flex: 3,
                child: Obx(() {
                  return  SizedBox(
                          height: 40,
                          child: ListView.builder(
                              itemCount: _helper
                                  !.selectedListDeviceEUI.length,
                              scrollDirection: Axis.horizontal,
                              shrinkWrap: true,
                              itemBuilder: (context, index) {
                                return Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 4.0),
                                  child: Chip(
                                    backgroundColor: Colors.white,
                                    label: Text(_helper
                                        !.selectedListDeviceEUI[index]),
                                    deleteIcon: CircleAvatar(
                                        maxRadius: 10,
                                        backgroundColor:
                                            AppColorConstants.colorLightBlue,
                                        child: Icon(Icons.close,
                                            size: getSize(16),
                                            color:
                                                AppColorConstants.colorWhite)),
                                    onDeleted: () {
                                      if (!_helper!.isTableView) {
                                       _helper!.paginationHelper.currentPage = 0;
                                      } else {
                                       _helper!.pageController.goToFirstPage();
                                      }
                                     _helper!.selectDevEUI(
                                         _helper!.selectedListDeviceEUI[index],
                                          false);
                                    },
                                  ),
                                );
                              }),
                        );
                }),
              ),
              if (_helper!.screenLayoutType == ScreenLayoutType.desktop)
               SizedBox(width: MediaQuery.of(context).size.width*0.3,child:  searchAutoTextView())
            ],
          ),
          if (_helper!.screenLayoutType != ScreenLayoutType.desktop)
            Padding(padding: EdgeInsets.only(right: 8),child: searchAutoTextView(),),
          SizedBox(height: getSize(8)),
        ],
      ),
    );
  }

  searchAutoTextView() {
    TextEditingController textEditingController = TextEditingController();
    return Row(mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          height: 40,
          width: 50,
          child: StreamBuilder(
            stream:_helper!.isStreamLoading,
            builder: (context, snapshot) {
              if (snapshot.data != null && snapshot.data == true) {
                return const AppLoader();
              } else {
                return Container();
              }
            },
          ),
        ),
        Expanded(
            child: Container(
          height: 40,
          child: AppTextFormField(
            controller: textEditingController,
            onFieldSubmitted: (value) {
              if (value.isEmpty) return;
              if (!_helper!.isTableView) {
               _helper!.paginationHelper.currentPage = 0;
              } else {
               _helper!.pageController.goToFirstPage();
              }
             _helper!.selectDevEUI(value, true);
              textEditingController.clear();
            },
            hintText: S.of(context).searchByDeviceEUI,
            contentPadding: const EdgeInsets.symmetric(vertical: 0, horizontal: 20),
            focusedBorderColor: AppColorConstants.colorPrimary,
            enabledBorderColor: AppColorConstants.colorBlackBlue,
            maxLines: 1,
            textInputType: TextInputType.text,
            borderRadius: getSize(8),
            hintTextColor: AppColorConstants.colorDarkBlue,
            suffixIcon: const Padding(
              padding: EdgeInsets.all(10),
              child: AppImageAsset(image: AppAssetsConstants.searchIcon),
            ),
            hintFontSize: 17,
            fontFamily: AppAssetsConstants.openSans,
            fontWeight: FontWeight.w400,
          ),
        )),
        MobileDiagnosticsSocket().selectTableTypeButtonView(_helper),
      ],
    );
  }

  diagnosticsBoardView() {
    if ((_helper!.gwIDFormWebHost.isEmpty) &&
       _helper!.apiStatus == ApiStatus.success) {
      return Container(
        margin:const EdgeInsets.symmetric(horizontal: 5),
        alignment: Alignment.center,
        height: 400,
        decoration:_helper!.dataTableHelper.tableBorderDeco(),
        width: double.infinity,
        child:_helper!.dataTableHelper.getEmptyTableContent(context),
      );
    }
    return StreamBuilder<dynamic>(
      stream: _helper!.socketHelper.diagnosticsStreamView,
      builder: (context, snapshot) {
        if ((snapshot.connectionState == ConnectionState.waiting ||
           _helper!.apiStatus == ApiStatus.loading  )&& ! _helper!.isTimeout  ) {
          return Container(
              margin:const EdgeInsets.symmetric(horizontal: 5),
              alignment: Alignment.center,
              height: 400,
              decoration: BoxDecoration(
                border: Border.all(color: AppColorConstants.colorH2),
                color: AppColorConstants.colorWhite
              ),
              //decoration:diagnosticsPageHelper!.dataTableHelper.tableBorderDeco() ,
              width: double.infinity,
              child: const AppLoader());
        } else if (snapshot.connectionState == ConnectionState.active) {
          if (snapshot.hasData) {
           _helper!.isStreamLoading.add(true);
            //diagnosticsPageHelper!.handleWebSocketMessage(snapshot.data);
          } else {
           _helper!.isStreamLoading.add(false);
          }
        }else if (snapshot.connectionState == ConnectionState.waiting && _helper!.isTimeout) {
          return _buildTimeoutMessage();
        }
        return Container(
            margin:const EdgeInsets.symmetric(horizontal: 5),
            height: (!_helper!.isTableView) ? null :_helper!.tableHeight,
            width: double.infinity,
            decoration:_helper!.dataTableHelper.tableBorderDeco(),
          //  decoration:diagnosticsPageHelper!.dataTableHelper.tableBorderDeco(),
            child: getDiagnosticsDataTableView());
      },
    );
  }

  getDiagnosticsDataTableView() {
    if (!_helper!.isTableView) {
      return  MobileDiagnosticsSocket()
          .buildDiagnosticsList(context, _helper!);
    }
    return PaginatedDataTable2(
      isVerticalScrollBarVisible: false,
      columnSpacing: 8,
      showCheckboxColumn: false,
      headingTextStyle:
         _helper!.dataTableHelper.headingTextStyle(),
      wrapInCard: false,
     border:_helper!.dataTableHelper.tableBorder(),
      renderEmptyRowsInTheEnd: false,
      dividerThickness: 0.3,
      //ignore: deprecated_member_use
      headingRowColor:_helper!.dataTableHelper.headingRowColor(),
      columns: [
        DataColumn2(
          fixedWidth: 180,
          label: AppText(S.of(context).timestamp),
        ),
        DataColumn2(
          fixedWidth: 250,
          label: AppText(S.of(context).description),
        ),
        DataColumn2(
          fixedWidth: 150,
          label: AppText(S.of(context).devAddress),
        ),
        DataColumn2(
          fixedWidth: 170,
          label: AppText(S.of(context).devEUI),
        ),
        DataColumn2(
          fixedWidth: 200,
          label: AppText(S.of(context).gatWayId),
        ),
        DataColumn2(fixedWidth: 100,
          label: AppText(S.of(context).rssi+"\n(${S.of(context).dBm})",textAlign: TextAlign.center,),
        ),
        DataColumn2(fixedWidth: 100,
          label: AppText(S.of(context).fPort),
        ),
        DataColumn2(fixedWidth: 150,
          label: AppText(S.of(context).frequency),
        ),
        const DataColumn2(
          label: SizedBox(),
        ),
      ],
      controller:_helper!.pageController,
      source:_helper!.gwDiagnosticsDataSource,
      minWidth: 1350,
      dataRowHeight: 55,
      // For progress indicator
      hidePaginator: false,
      empty:
         _helper!.dataTableHelper.getEmptyTableContent(context),
    );
  }
  Widget _buildTimeoutMessage() {
    return Card(
      elevation: 10,
      margin: EdgeInsets.only(left: getSize(5), right: getSize(5),bottom: getSize(5)),
      child: Container(
        alignment: Alignment.center,
        height: 400,
        decoration: BoxDecoration(
            color:  AppColorConstants.colorWhite,
            border: Border.all(color: AppColorConstants.colorH2)
        ),
        width: double.infinity,
        child:  Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const AppImageAsset(
              image: AppAssetsConstants.emptyLogo,
              height: 50,
            ),
            AppText(
              S.of(context).socketTimeOutMessage,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 16, color: Colors.black87),
            ),
          ],
        ),
      ),
    );
  }
}
