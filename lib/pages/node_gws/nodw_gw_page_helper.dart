import 'package:quantumlink_node/app/cache/app_shared_preference.dart';
import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/model/node_gw_tab_iteam.dart';
import 'package:quantumlink_node/pages/node_gws/node_gw_datasource.dart';
import 'package:quantumlink_node/pages/node_gws/node_gw_page.dart';

class NodeGWPageHelper {
  late NodeGWPageState state;

  late NodeGwDatasource nodeGwDatasource;
  ApiStatus apiStatus = ApiStatus.initial;
  final double heightOfDataTableCell = 48;
  PaginatorController paginatorController = PaginatorController();
  int recordsInPage = 0;
  int currentPageIndex = 0;
  DataTableHelper dataTableHelper = DataTableHelper();
  PaginationHelper paginationHelper = PaginationHelper();
  late ScreenLayoutType previousLayoutType = ScreenLayoutType.desktop;
  late ScreenLayoutType screenLayoutType;
  bool isTableView = true;
  Gateways gateway = Gateways();
  late TabController tabController;
  List<GatewayItem> ?filteredGateway;
  GatewayItem gatewayItem = GatewayItem();
  String gwIDFormWebHost = "";
  TextEditingController searchController=TextEditingController();
  DateTime ?lastUpdateTime;
  List<NodeGWTabItem> tabLIst = [
    NodeGWTabItem(title: "Node Gateway", isCurrentOpen: true,),
  ];
  String defaultStatus="online";
  List<bool> isHovered = List.generate(1, (index) => false);
  bool isShowOnline = true;
  NodeGWPageHelper(this.state) {
    tabController = TabController(
        initialIndex: 0,
        length: 1,
        vsync: state,
        animationDuration: Duration.zero
    );
    Future.delayed(const Duration(milliseconds: 500)).then((value) async {
      getGWList();
      getCurrantPageIndex();
    });
  }
  update(){
    state.controller.update();
  }

  getGWList() async {
    paginationHelper.initializePagination();
    currentPageIndex=0;
    gwIDFormWebHost = await getPrefStringValue(AppSharedPreference.gw) ?? '';
    apiStatus = ApiStatus.loading;
    update();

    gateway = await state.controller
        .getNodeGateways(context: state.context,statusType: defaultStatus, pageOffset: paginationHelper.pageOffset, perPageLimit: AppStringConstants.nodeGWPerPageLimit);
    filteredGateway = gateway.items;
    nodeGwDatasource = dataSource(filteredGateway);
    bool hasMoreData = filteredGateway!.length == AppStringConstants.nodeGWPerPageLimit;
    paginationHelper.updatePagination(filteredGateway!.length, hasMore: hasMoreData,pageLimit: AppStringConstants.nodeGWPerPageLimit);
    lastUpdateTime = DateTime.now();
    apiStatus = ApiStatus.success;
    update();
  }
  //GET CURRANT PAGE INDEX
  getCurrantPageIndex() {
    paginationHelper.initializePagination();
    paginatorController.addListener(() {
      currentPageIndex =
          (paginatorController.currentRowIndex / AppStringConstants.nodeGWPerPageLimit).ceil();
      update();
    });
  }

  Future<void> loadPreviousLogs(BuildContext context) async {
    paginationHelper.setPage(paginationHelper.currentPage - 1);
    if(isTableView) {
      paginatorController.goToPreviousPage();
    }
    update();
  }

  Future<void> loadNextLogs(BuildContext context) async {
    if (paginationHelper.canGoToNextPage) {
      paginationHelper.setPage(paginationHelper.currentPage + 1);
      if (paginationHelper.currentPage >= paginationHelper.totalPages && searchController.text.isEmpty) {
        paginationHelper.pageOffset = filteredGateway!.length;
        await updateGWListPageData();
      } else {
        if(searchController.text.isNotEmpty){
          bool hasMoreData= paginationHelper.currentPage+1 < paginationHelper.totalPages;
          paginationHelper.updatePagination(filteredGateway!.length,
              hasMore: hasMoreData, pageLimit: AppStringConstants.nodeGWPerPageLimit);
        }
        if(isTableView) {
          paginatorController.goToNextPage();
        }
        update();
      }
    }
  }

  updateGWListPageData() async {
    apiStatus = ApiStatus.loading;
    update();
    List<GatewayItem> gatewayList=[];
    Gateways gateway = await state.controller
        .getNodeGateways(context: state.context,statusType: defaultStatus, pageOffset: paginationHelper.pageOffset, perPageLimit: AppStringConstants.nodeGWPerPageLimit);
    gatewayList= gateway.items ?? [];
    print("gatewayList--->${gatewayList.length}");
    if (gatewayList.isNotEmpty) {
      filteredGateway!.addAll(gatewayList);
      print("filteredGateway--->${filteredGateway!.length}");
      nodeGwDatasource = dataSource(filteredGateway);
      bool hasMoreData = gatewayList.length == AppStringConstants.nodeGWPerPageLimit;
      paginationHelper.updatePagination(filteredGateway!.length, hasMore: hasMoreData,pageLimit: AppStringConstants.nodeGWPerPageLimit);
    } else {
      paginationHelper.setPage(paginationHelper.currentPage - 1);
      paginationHelper.updatePagination(filteredGateway!.length, hasMore: false,pageLimit: AppStringConstants.nodeGWPerPageLimit);
      Future.delayed(const Duration(milliseconds: 100)).then((value) {
        if (isTableView) {
          if (gatewayList.isNotEmpty) paginatorController.goToLastPage();
        }
        nodeGwDatasource.updateNotifyListeners();
      });
    }
    lastUpdateTime = DateTime.now();
    apiStatus = ApiStatus.success;
    update();
  }


    void filterData(String query) {
      paginationHelper.currentPage=0;
      currentPageIndex=0;
      if(isTableView){
        paginatorController.goToFirstPage();
      }
      nodeGwDatasource.updateNotifyListeners();
      query = query.toLowerCase();
      if (query.isEmpty) {
        filteredGateway = gateway.items;
        nodeGwDatasource = dataSource(filteredGateway);
        nodeGwDatasource.updateNotifyListeners();
      } else {
        filteredGateway = gateway.items!.where((customer) {
          return
              customer.gwEui?.toLowerCase().contains(query) == true ||
              customer.name?.toLowerCase().contains(query) == true ||
              customer.siteId?.toLowerCase().contains(query) == true ||
              customer.userEmail?.toLowerCase().contains(query) == true ||
              customer.status?.toLowerCase().contains(query) == true ||
              customer.domain?.toLowerCase().contains(query) == true;
        }).toList();
        nodeGwDatasource = dataSource(filteredGateway);
        nodeGwDatasource.updateNotifyListeners();
      }
      bool hasMoreData = filteredGateway!.length > AppStringConstants.nodeGWPerPageLimit;
    paginationHelper.updatePagination(filteredGateway!.length,
        hasMore: hasMoreData, pageLimit: AppStringConstants.nodeGWPerPageLimit);
    update();
  }

  dataSource(filteredGateway){
    return NodeGwDatasource(state.context, filteredGateway ?? [], (value) {
      gatewayItem = value;
      addTabTableOnTap(value);
    });
  }
// TAB ON TAP
  addTabTableOnTap(GatewayItem value) {
    bool gwEuiExists = tabLIst.any((tab) => tab.title == value.gwEui);
    if (!gwEuiExists) {
      if (tabLIst.length <= 6) {
        addTab(value.gwEui, true,value);
      }else{
        S.of(state.context).maxTabMessage.showError(state.context);
      }
      update();
    } else {
      for (var element in tabLIst) {
        element.isCurrentOpen = false;
      }
      int euiIndex = tabLIst.indexWhere((element) => element.title == value.gwEui);
      tabLIst[euiIndex].isCurrentOpen = true;
      tabController.animateTo(
        euiIndex,
        duration: const Duration(milliseconds: 100),
      );
    }

    update();
  }
  addTab(t, o, GatewayItem gwItem) {
    for (var tabElement in tabLIst) {
      tabElement.isCurrentOpen = false;
    }
    tabLIst.add(NodeGWTabItem(title: t, isCurrentOpen: o,gatewayItem: gwItem));
    isHovered = List<bool>.filled(tabLIst.length, false);
    tabController = TabController(
        initialIndex: tabLIst.length - 1,
        length: tabLIst.length,
        vsync: state,
        animationDuration: Duration.zero
    );
  }
  removeTab(int index) {

    tabLIst.removeAt(index);
    for (var tabElement in tabLIst) {
      tabElement.isCurrentOpen = false;
    }
    tabController = TabController(
        initialIndex: tabLIst.length - 1,
        length: tabLIst.length,
        vsync: state,
        animationDuration: Duration.zero
    );
    tabLIst.first.isCurrentOpen = true;
    tabController.animateTo(0);
  update();
  }



}
