import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/model/setting_tab_item.dart';
import 'package:quantumlink_node/pages/settings/amp_provider/amp_provider_datasource.dart';
import 'package:http/src/response.dart' as http;

class SettingsPageHelper {
  late SettingsPageState state;

  SettingsPageHelper(this.state) {
    tabController = TabController(
        initialIndex: 0, length: tabLIst.length, vsync: state, animationDuration: Duration.zero);
    Future.delayed(const Duration(milliseconds: 500)).then((value) async {});
  }

  List<SettingTabItem> tabLIst = [
    SettingTabItem(
      title: AppStringConstants.amplifierProvider,
      isCurrentOpen: true,
    ),
  ];
  TextEditingController searchController = TextEditingController();
  ApiStatus apiStatus = ApiStatus.initial;
  ApiStatus addApiStatus = ApiStatus.initial;
  ApiStatus deleteApiStatus = ApiStatus.initial;
  late TabController tabController;
  final double heightOfDataTableCell = 48;
  PaginatorController paginatorController = PaginatorController();
  int recordsInPage = 0;
  int currentPageIndex = 0;
  DataTableHelper dataTableHelper = DataTableHelper();
  PaginationHelper paginationHelper = PaginationHelper();
  late ScreenLayoutType screenLayoutType;
  List<bool> isHovered = List.generate(1, (index) => false);
  DateTime lastUpdateTime = DateTime.now();
  List<AmplifierProviderItem>? ampProviderList = [];
  List<AmplifierProviderItem> searchAmpProviderList = [];
  String ampProviderErrorMessage = "";

  late AmpProviderDataSource ampProviderDataSource;

  updateState() {
    state.controller.update();
  }

  getAmpProviderList() async {
    ampProviderErrorMessage = '';
    apiStatus = ApiStatus.loading;
    ampProviderList = await state.controller.getAmpProviderList(context: state.context);
    if (ampProviderList != null) {
      ampProviderDataSource = dataSource(ampProviderList);
    } else {
      ampProviderDataSource = dataSource(ampProviderList);
      ampProviderErrorMessage = S.of(state.context).somethingWentWrong;
    }

    lastUpdateTime = DateTime.now();
    apiStatus = ApiStatus.success;
    updateState();
  }

  Future<dynamic> getVendorKeysList(
      {required BuildContext context, required String vendorCode}) async {
    return await state.controller.getVendorKeysList(context: context, vendorCode: vendorCode);
  }

  Future<void> addNewAmpProvider(AmplifierProviderItem ampProviderItem) async {
    addApiStatus = ApiStatus.loading;
    updateState();
    try {
      Map<String, dynamic>?  response = await state.controller
          .addNewAmpProvider(context: state.context, itemData: ampProviderItem);
      if (response != null) {
        String? message = response['detail'] ?? response['message'];
        if (message != null && message.isNotEmpty) {
           message.showError(state.context);
        } else {
          S.of(state.context).createdSuccessfully.showSuccess(state.context);
          getAmpProviderList();
        }
      }
    } catch (e) {
      debugLogs("Add New AmpProvider Error--->$e");
      S.of(state.context).somethingWentWrong.showError(state.context);
    } finally {
      addApiStatus = ApiStatus.success;
      updateState();
    }
  }

  Future<void> updateAmpProvider(AmplifierProviderItem ampProviderItem) async {
    addApiStatus = ApiStatus.loading;
    updateState();
    try {
      http.Response? response = await state.controller.updateAmpProvider(
          context: state.context,
          itemData: ampProviderItem,
          vendorCode: ampProviderItem.code ?? "");
      if (response != null) {
        final responseData = json.decode(response.body);
        if (response.statusCode == 200) {
          if (state.mounted) {
            AmplifierProviderItem amplifierProviderItem =
                AmplifierProviderItem.fromJson(responseData);
            "${amplifierProviderItem.name} update successfully".showSuccess(state.context);
            getAmpProviderList();
          }
        } else {
          if (state.mounted) {
            responseData['detail'].toString().showError(state.context);
          }
        }
      }
    } catch (e) {
      debugLogs("UpdateAmpProvider Error--->$e");
      S.of(state.context).somethingWentWrong.showError(state.context);
    } finally {
      addApiStatus = ApiStatus.success;
      updateState();
    }
  }

  void applyAmpProviderSearch(String value) {
    final lowerCaseValue = value.toLowerCase();

    paginatorController.goToFirstPage();

    searchAmpProviderList = ampProviderList!.where((element) {
      String name = element.name?.toLowerCase() ?? "";
      String appName = element.chirpstackAppName?.toLowerCase() ?? "";
      String code = element.code?.toLowerCase() ?? "";

      return name.contains(lowerCaseValue) ||
          appName.contains(lowerCaseValue) ||
          code.contains(lowerCaseValue);
    }).toList();

    ampProviderDataSource.notifyListeners();

    final isSearchEmpty = searchController.text.trim().isEmpty;
    final updatedList = isSearchEmpty ? ampProviderList : searchAmpProviderList;

    ampProviderDataSource = dataSource(updatedList);
    updateState();
  }

  AmpProviderDataSource dataSource(List<AmplifierProviderItem>? ampProviderList) {
    return AmpProviderDataSource(state.context, ampProviderList ?? [], (itemData) {
      bool isUpdate = true;
      addNewProviderModalView(state.context, isUpdate, ampProviderItem: itemData);
    }, (itemData) {
      deleteDialogView(state.context, () async {
        await deleteAmpProvider(itemData);
        goBack();
      }, itemData.name ?? "");
    }, (itemData) {
      openVendorKeysTab(itemData);
    });
  }

  void openVendorKeysTab(AmplifierProviderItem vendor) {
    // Add a new tab for vendor keys if not already open
    String tabTitle = vendor.code ?? "";
    if (!tabLIst.any((tab) => tab.title == tabTitle)) {
      tabLIst.add(SettingTabItem(title: tabTitle, isCurrentOpen: true));
      isHovered.add(false);
      tabController = TabController(initialIndex: tabLIst.length - 1, length: tabLIst.length, vsync: state, animationDuration: Duration.zero);
      for (var tab in tabLIst) { tab.isCurrentOpen = false; }
      tabLIst.last.isCurrentOpen = true;
      updateState();
    } else {
      int idx = tabLIst.indexWhere((tab) => tab.title == tabTitle);
      tabController.index = idx;
      for (var tab in tabLIst) { tab.isCurrentOpen = false; }
      tabLIst[idx].isCurrentOpen = true;
      updateState();
    }
  }

  void removeVendorKeysTab(int index){
    tabLIst.removeAt(index);
    isHovered.removeAt(index);
    tabController = TabController(
      initialIndex: 0,
      length: tabLIst.length,
      vsync: state,
      animationDuration: Duration.zero,
    );
    for (var tab in tabLIst) { tab.isCurrentOpen = false; }
    if (tabLIst.isNotEmpty) {
     tabLIst[tabController.index].isCurrentOpen = true;
    }
    updateState();
  }
  Future<void> deleteAmpProvider(AmplifierProviderItem ampProviderItem) async {
    deleteApiStatus =ApiStatus.loading;
    updateState();
    try {
      final result = await state.controller.deleteAmpProvider(
        context: state.context,
        vendorCode: ampProviderItem.code ?? '',
      );
      if (result != null) {
        AmplifierProviderItem amplifierProviderItem = AmplifierProviderItem.fromJson(result);
         "${amplifierProviderItem.name ?? ""} ${S.of(state.context).deleteSuccessfully}".showSuccess(state.context);
        getAmpProviderList();
      } else {
        S.of(state.context).somethingWentWrong.showError(state.context);
      }
    } catch (e) {
       debugLogs("Delete AmpProvider Error--->$e");
      S.of(state.context).somethingWentWrong.showError(state.context);
    }finally{
      deleteApiStatus =ApiStatus.success;
      updateState();
    }
  }

  addNewProviderModalView(c, bool isUpdate, {AmplifierProviderItem? ampProviderItem}) {
    {
      TextEditingController codeController = TextEditingController();
      TextEditingController nameController = TextEditingController();
      TextEditingController appNameController = TextEditingController();
      RegExp codeRegExp = RegExp(ValidationUtils.alphaNumericUnderscoreRegExp);
      final GlobalKey<FormState> deviceFormKey = GlobalKey<FormState>();

      return showDialog(
        context: c,
        builder: (context) {
          return GetBuilder<SettingController>(
            init: SettingController(),
            initState: (state) {
              if (ampProviderItem != null && isUpdate) {
                codeController.text = ampProviderItem.code ?? "";
                nameController.text = ampProviderItem.name ?? "";
                appNameController.text = ampProviderItem.chirpstackAppName ?? "";
              }
            },
            builder: (SettingController controller) {
              return AlertDialog(
                surfaceTintColor: AppColorConstants.colorWhite,
                backgroundColor: AppColorConstants.colorWhite,
                insetPadding: EdgeInsets.symmetric(horizontal: 8),
                contentPadding: EdgeInsets.zero,
                titlePadding: EdgeInsets.zero,
                actionsPadding: EdgeInsets.zero,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
                title: getCustomAppBarWithClose(
                    isUpdate ? S.of(context).updateAmpProvider : S.of(context).newAmpProvider),
                content: StatefulBuilder(builder: (context, snapshot) {
                  return Padding(
                    padding: EdgeInsets.only(bottom: MediaQuery.of(c).viewInsets.bottom),
                    //padding: const EdgeInsets.all(8.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Form(
                          key: deviceFormKey,
                          child: Container(
                            width: 350,
                            padding: const EdgeInsets.only(left: 16, right: 16, top: 16),
                            child: Column(
                              children: [
                                const SizedBox(height: 8),
                                AppTextFormField(
                                  readOnly: isUpdate,
                                  label: S.of(context).vendor,
                                  controller: codeController,
                                  onChanged: (value) {
                                    if(value == AppStringConstants.aoi){
                                      appNameController.text= AppStringConstants.quantumLinkApp;
                                      controller.update();
                                    }
                                  },
                                  maxLines: 1,
                                  textInputType: TextInputType.text,
                                  validator: (value) {
                                    if (value!.isEmpty) {
                                      return S.of(context).thisFieldIsRequired;
                                    } else if (!codeRegExp.hasMatch(value)) {
                                      return S.of(context).invalidCodeFormat;
                                    }
                                    return null;
                                  },
                                ),
                                const SizedBox(height: 8),
                                AppTextFormField(
                                  label: S.of(context).name,
                                  controller: nameController,
                                  maxLines: 1,
                                  textInputType: TextInputType.text,
                                  validator: (value) {
                                    if (value!.isEmpty) {
                                      return S.of(context).thisFieldIsRequired;
                                    }
                                    return null;
                                  },
                                ),
                                const SizedBox(height: 8),
                                AppTextFormField(
                                  readOnly: isUpdate,
                                  label: S.of(context).applicationName,
                                  controller: appNameController,
                                  maxLines: 1,
                                  textInputType: TextInputType.text,
                                  validator: (value) {
                                    if (value!.isEmpty) {
                                      return S.of(context).thisFieldIsRequired;
                                    }
                                    return null;
                                  },
                                ),
                                const SizedBox(height: 16),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 24),
                        getAppDivider(),
                      ],
                    ),
                  );
                }),
                actions: [
                  Container(
                    padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16, top: 8),
                    child: AppButton(
                      loadingStatus: addApiStatus,
                      fontSize: 14,
                      buttonWidth: 80,
                      buttonHeight: 35,
                      fontFamily: AppAssetsConstants.openSans,
                      buttonName: isUpdate ? S.of(context).update : S.of(context).addNewBtn,
                      onPressed: () async {
                        if (!deviceFormKey.currentState!.validate()) {
                          return;
                        }
                        AmplifierProviderItem ampProvideItem = AmplifierProviderItem(
                          code: codeController.text,
                          name: nameController.text,
                          chirpstackAppName: appNameController.text,
                        );
                        if (isUpdate) {
                          await updateAmpProvider(ampProvideItem);
                        } else {
                          await addNewAmpProvider(ampProvideItem);
                        }
                        goBack();
                      },
                    ),
                  ),
                ],
              );
            },
          );
        },
      );
    }
  }
  deleteDialogView(c, Function fun ,String vendorName) {
    return showDialog(
      context: c,
      builder: (context) {
        return GetBuilder<SettingController>(
            init: SettingController(),
          builder: (SettingController controller) {
            return AlertDialog(
              surfaceTintColor: AppColorConstants.colorWhite,
              backgroundColor: AppColorConstants.colorWhite,
              insetPadding: const EdgeInsets.symmetric(horizontal: 8.0),
              contentPadding: EdgeInsets.zero,
              titlePadding: EdgeInsets.zero,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
              title: getCustomAppBarWithClose(S.of(context).confirmDelete),
              content: StatefulBuilder(builder: (context, snapshot) {
                return Container(
                  width: MediaQuery.of(context).size.width *
                      0.1, // Adjust the width as needed
                  height: MediaQuery.of(context).size.height * 0.1,
                  padding: const EdgeInsets.all(8.0),
                  child:  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: AppText(
                        "${S.of(context).deleteConfirmationMessage} $vendorName ?",
                        style: TextStyle(
                          fontFamily: AppAssetsConstants.openSans,
                          fontSize: 14,
                          color:  AppColorConstants.colorBlackBlue,
                          fontWeight: FontWeight.w600,
                        )),
                  ),
                );
              }),
              actions: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    AppButton(
                      buttonHeight: 32,
                      borderColor: Colors.grey.withOpacity(0.5),
                      buttonColor: Colors.grey.withOpacity(0.5),
                      buttonName: S.of(context).no,
                      fontFamily: AppAssetsConstants.openSans,
                      onPressed: () {
                        goBack();
                      },
                    ),
                    const SizedBox(width: 16),
                    AppButton(
                      buttonWidth: 40,
                      buttonHeight: 32,
                      loadingStatus: deleteApiStatus,
                      fontFamily: AppAssetsConstants.openSans,
                      buttonName: S.of(context).yes,
                      onPressed: () {
                        fun.call();
                      },
                    ),
                  ],
                )
              ],
            );
          }
        );
      },
    );
  }
}
