// ignore_for_file: deprecated_member_use
import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/pages/settings/vendor_keys/vendor_keys_datasource.dart';

class VendorKeysPage extends StatefulWidget {
  final dynamic vendor;
  final SettingsPageHelper pageHelper;

  const VendorKeysPage({super.key, this.vendor, required this.pageHelper});

  @override
  State<VendorKeysPage> createState() => VendorKeysPageState();
}

class VendorKeysPageState extends State<VendorKeysPage> {
  bool isLoading = true;
  final double heightOfDataTableCell = 48;
  PaginatorController paginatorController = PaginatorController();
  DataTableHelper dataTableHelper = DataTableHelper();
  int recordsInPage = 0;
  int currentPageIndex = 0;
  TextEditingController searchController = TextEditingController();
  ApiStatus apiStatus = ApiStatus.initial;
  String errorMessage = "";
  late VendorKeysDataSource keysDataSource;
  late ScreenLayoutType screenLayoutType;
  List<VendorKeyItem>? vendorKeyList = [];
  List<VendorKeyItem>? searchVendorKeyList = [];
  DateTime lastUpdateTime = DateTime.now();
  ApiStatus deleteApiStatus = ApiStatus.initial;
  ApiStatus addApiStatus = ApiStatus.initial;
  @override
  void initState() {
    super.initState();
    fetchVendorKeys();
  }

  Future<void> fetchVendorKeys() async {
    errorMessage = '';
    apiStatus = ApiStatus.loading;
    vendorKeyList =
        await widget.pageHelper.getVendorKeysList(context: context, vendorCode: widget.vendor);
    if (vendorKeyList != null) {
      keysDataSource = dataSource(vendorKeyList);
    } else {
      keysDataSource = dataSource(vendorKeyList);
      errorMessage = S.of(context).somethingWentWrong;
    }

    lastUpdateTime = DateTime.now();
    apiStatus = ApiStatus.success;
    widget.pageHelper.updateState();
  }

  void applyKeysSearch(String value) {
    final lowerCaseValue = value.toLowerCase();
    paginatorController.goToFirstPage();
    searchVendorKeyList = vendorKeyList!.where((element) {
      String key = element.key?.toLowerCase() ?? "";
      String vendorCode = element.vendorCode?.toLowerCase() ?? "";
      return key.contains(lowerCaseValue) || vendorCode.contains(lowerCaseValue);
    }).toList();

    final isSearchEmpty = searchController.text.trim().isEmpty;
    final updatedList = isSearchEmpty ? vendorKeyList : searchVendorKeyList;
    keysDataSource = dataSource(updatedList);
    keysDataSource.notifyListeners();
    widget.pageHelper.updateState();
  }

  VendorKeysDataSource dataSource(List<VendorKeyItem>? keyList) {
    return VendorKeysDataSource(context, keyList ?? [], onDelete: (VendorKeyItem item) async {
      deleteDialogView(context, () async {
        await deleteVendorKey(item);
        goBack();
      }, item.key ?? "");

    });
  }

 Future<void> addNewVendorKey(VendorKeyItem item) async {
    addApiStatus = ApiStatus.loading;
    widget.pageHelper.updateState();
    try {
      Map<String, dynamic>?  response = await widget.pageHelper.state.controller
          .addNewVendorKey(context: context,keyItem: item);
      if (response != null) {
        String? message = response['detail'] ?? response['message'];
        if (message != null && message.isNotEmpty) {
          message.showError(context);
        } else {
          VendorKeyItem item =VendorKeyItem.fromJson(response);
          "${item.key} ${S.of(context).createdSuccessfully}".showSuccess(context);
          fetchVendorKeys();
        }
      }
    } catch (e) {
      debugLogs("Add New AmpProvider Error--->$e");
      S.of(context).somethingWentWrong.showError(context);
    } finally {
      addApiStatus = ApiStatus.success;
      widget.pageHelper.updateState();
    }
  }

  Future<void> deleteVendorKey(VendorKeyItem item) async {
    deleteApiStatus = ApiStatus.loading;
    widget.pageHelper.updateState();
    try {
      final result = await widget.pageHelper.state.controller
          .deleteVendorKey(context: context, keyId: item.id.toString());
      if (result != null) {
        Map<String, dynamic> response = result;
        String? message = response['message'] ?? response['detail'];
        if (message != null && message.isNotEmpty) {
          message.showSuccess(context);
          fetchVendorKeys();
        } else {
          S.of(context).somethingWentWrong.showError(context);
        }
      } else {
        S.of(context).somethingWentWrong.showError(context);
      }
    } catch (e) {
      debugLogs("Delete VendorKey Error--->$e");
      S.of(context).somethingWentWrong.showError(context);
    } finally {
      deleteApiStatus = ApiStatus.success;
      widget.pageHelper.updateState();
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SettingController>(
      init: SettingController(),
      builder: (SettingController settingController) {
        return Stack(
          children: [
            getBody(),
          ],
        );
      },
    );
  }

  Widget getBody() {
    return getVendorKeysBoardView();
  }

  Widget getVendorKeysBoardView() {
    return ScreenLayoutTypeBuilder(builder: (context, screenType, constraints) {
      screenLayoutType = screenType;
      return ClipRRect(
        borderRadius: const BorderRadius.all(Radius.circular(5)),
        child: Container(
          color: AppColorConstants.colorWhite,
          width: double.infinity,
          height: double.infinity,
          child: ListView(
            shrinkWrap: true,
            padding: EdgeInsets.only(left: getSize(15), right: getSize(20)),
            children: [
              getPageAppBar(),
              getTableBoardView(),
              SizedBox(height: getSize(20)),
            ],
          ),
        ),
      );
    });
  }

  Widget getPageAppBar() {
    return Container(
      decoration: BoxDecoration(
          border: Border.all(color: AppColorConstants.colorH2, width: 0.8),
          color: AppColorConstants.colorWhite,
          borderRadius: BorderRadius.only(
              topRight: Radius.circular(getSize(7)), topLeft: Radius.circular(getSize(7)))),
      alignment: Alignment.topCenter,
      padding: const EdgeInsets.all(10),
      child: Column(
        children: [
          Row(
            children: [
              const Spacer(),
              if (screenLayoutType == ScreenLayoutType.desktop) ...[searchTextFieldView()],
            ],
          ),
          if (screenLayoutType != ScreenLayoutType.desktop)
            Row(
              children: [
                searchTextFieldView(),
              ],
            )
        ],
      ),
    );
  }

  Widget getTableBoardView() {
    if (apiStatus == ApiStatus.loading) {
      return SizedBox(
          height: 400,
          child: Align(
              alignment: Alignment.center,
              child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(border: Border.all(color: AppColorConstants.colorH2)),
                  child: const AppLoader())));
    }
    int itemsPerPage =
        dataTableHelper.getCurrentPageDataLength(vendorKeyList ?? [], currentPageIndex);
    recordsInPage =
        (vendorKeyList != null && vendorKeyList!.isNotEmpty && vendorKeyList!.length > 10)
            ? itemsPerPage
            : vendorKeyList?.length ?? 0;
    return Column(
      children: [
        Container(
            width: double.infinity,
            decoration: BoxDecoration(border: Border.all(color: AppColorConstants.colorH2)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: (vendorKeyList != null && vendorKeyList!.isNotEmpty)
                      ? (recordsInPage * heightOfDataTableCell) + (recordsInPage * 0.1) + 150
                      : (recordsInPage * heightOfDataTableCell) + (recordsInPage * 0.1) + 350,
                  child: getDataTableWithPageBoardView(),
                ),
                SizedBox(height: getSize(20)),
                Container(
                  height: 1,
                  width: double.infinity,
                  color: AppColorConstants.colorBlack12,
                ),
                Container(
                  padding: const EdgeInsets.only(left: 16, top: 10, bottom: 10),
                  width: double.infinity,
                  decoration: BoxDecoration(
                      color: AppColorConstants.colorBackgroundDark,
                      borderRadius: const BorderRadius.only(
                          bottomRight: Radius.circular(5), bottomLeft: Radius.circular(5))),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      addButtonView(),
                      const SizedBox(width: 10),
                      addAutoButtonView()
                    ],
                  ),
                ),
              ],
            )),
        buildLastSeenView()
      ],
    );
  }
  Widget addAutoButtonView() {
    return AppButton(
      loadingStatus: addApiStatus,
      buttonHeight: 33,
      fontSize: 14.5,
      buttonRadius: 9,
      buttonName: S.of(context).addAutoBtn,
      onPressed: () {
        VendorKeyItem item = VendorKeyItem(
          key:  null,
          vendorCode: widget.vendor,
        );
        addNewVendorKey(item);
      },
      fontFamily: AppAssetsConstants.openSans,
    );
  }
  Widget addButtonView() {
    return AppButton(
      buttonHeight: 33,
      fontSize: 14.5,
      buttonRadius: 9,
      buttonName: S.of(context).addBtn,
      onPressed: () {
        addCustomKeyVendorView(context);
      },
      fontFamily: AppAssetsConstants.openSans,
    );
  }
  Widget buildLastSeenView() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        getTimeDurationView(
          onTapTime: DateTime.now(),
          refreshStatus: apiStatus,
          updateTime:lastUpdateTime,
        ),
        getRefreshButtonView(),
      ],
    );
  }
  Widget getRefreshButtonView() {
    return Padding(
      padding: const EdgeInsets.only(right: 10.0),
      child: AppRefresh(
          onPressed: () {
            if (apiStatus != ApiStatus.loading) {
               apiStatus = ApiStatus.success;
               widget.pageHelper.updateState();
               fetchVendorKeys();
            }
          },
          loadingStatus: apiStatus),
    );
  }

  Widget getDataTableWithPageBoardView() {
    return PaginatedDataTable2(
      headingCheckboxTheme:
          CheckboxThemeData(side: BorderSide(width: 2, color: AppColorConstants.colorWhite)),
      headingTextStyle: dataTableHelper.headingTextStyle(),
      wrapInCard: false,
      datarowCheckboxTheme: const CheckboxThemeData(side: BorderSide(width: 2, color: Colors.grey)),
      border: dataTableHelper.tableBorder(),
      renderEmptyRowsInTheEnd: false,
      headingRowColor: dataTableHelper.headingRowColor(),
      columns: _getDataColumns(),
      controller: paginatorController,
      source: keysDataSource,
      minWidth: 950,
      dataRowHeight: 51,
      hidePaginator: false,
      empty: dataTableHelper.getEmptyTableContent(context),
    );
  }

  List<DataColumn> _getDataColumns() {
    return [
      DataColumn2(
        fixedWidth: getSize(350),
        label: AppText(S.of(context).key),
      ),
      DataColumn2(
        fixedWidth: getSize(150),
        label: AppText(S.of(context).vendorCode),
      ),
      DataColumn2(
        fixedWidth: getSize(200),
        label: AppText(S.of(context).createdAt),
      ),
      DataColumn2(
        label: AppText(S.of(context).deleteBtn),
      ),
    ];
  }

  Widget searchTextFieldView() {
    return Flexible(
      child: Container(
        decoration: BoxDecoration(
            color: AppColorConstants.colorWhite1, borderRadius: BorderRadius.circular(getSize(8))),
        child: AppTextFormField(
          onChanged: (value) {
            applyKeysSearch(value);
          },
          focusedBorderColor: AppColorConstants.colorPrimary,
          controller: searchController,
          enabledBorderColor: AppColorConstants.colorWhite1,
          hintText: S.of(context).quickSearch,
          maxLines: 1,
          textInputType: TextInputType.emailAddress,
          validator: (value) {
            return null;
          },
          borderRadius: getSize(8),
          hintTextColor: AppColorConstants.colorDarkBlue,
          suffixIcon: const Padding(
            padding: EdgeInsets.all(12),
            child: AppImageAsset(image: AppAssetsConstants.searchIcon),
          ),
          hintFontSize: 17,
          fontFamily: AppAssetsConstants.openSans,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }
  deleteDialogView(c, Function fun ,String vendorCodeName) {
    return showDialog(
      context: c,
      builder: (context) {
        return GetBuilder<SettingController>(
            init: SettingController(),
            builder: (SettingController controller) {
              return AlertDialog(
                surfaceTintColor: AppColorConstants.colorWhite,
                backgroundColor: AppColorConstants.colorWhite,
                insetPadding: const EdgeInsets.symmetric(horizontal: 8.0),
                contentPadding: EdgeInsets.zero,
                titlePadding: EdgeInsets.zero,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
                title: getCustomAppBarWithClose(S.of(context).confirmDelete),
                content: StatefulBuilder(builder: (context, snapshot) {
                  return Container(
                    width: MediaQuery.of(context).size.width *
                        0.1, // Adjust the width as needed
                    height: MediaQuery.of(context).size.height * 0.12,
                    padding: const EdgeInsets.all(8.0),
                    child:  Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: AppText(
                          "${S.of(context).deleteConfirmationMessage} $vendorCodeName ?",
                          style: TextStyle(
                            fontFamily: AppAssetsConstants.openSans,
                            fontSize: 14,
                            color:  AppColorConstants.colorBlackBlue,
                            fontWeight: FontWeight.w600,
                          )),
                    ),
                  );
                }),
                actions: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      AppButton(
                        buttonHeight: 32,
                        borderColor: Colors.grey.withOpacity(0.5),
                        buttonColor: Colors.grey.withOpacity(0.5),
                        buttonName: S.of(context).no,
                        fontFamily: AppAssetsConstants.openSans,
                        onPressed: () {
                          goBack();
                        },
                      ),
                      const SizedBox(width: 16),
                      AppButton(
                        buttonWidth: 40,
                        buttonHeight: 32,
                        loadingStatus: deleteApiStatus,
                        fontFamily: AppAssetsConstants.openSans,
                        buttonName: S.of(context).yes,
                        onPressed: () {
                          fun.call();
                        },
                      ),
                    ],
                  )
                ],
              );
            }
        );
      },
    );
  }
  addCustomKeyVendorView(c) {
    {
      TextEditingController codeController = TextEditingController();
      TextEditingController keyController = TextEditingController();
      final GlobalKey<FormState> deviceFormKey = GlobalKey<FormState>();

      return showDialog(
        context: c,
        builder: (context) {
          return GetBuilder<SettingController>(
            init: SettingController(),
            initState: (state) {
                codeController.text = widget.vendor ?? "";
            },
            builder: (SettingController controller) {
              return AlertDialog(
                surfaceTintColor: AppColorConstants.colorWhite,
                backgroundColor: AppColorConstants.colorWhite,
                insetPadding: EdgeInsets.zero,
                contentPadding: EdgeInsets.zero,
                titlePadding: EdgeInsets.zero,
                actionsPadding: EdgeInsets.zero,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
                title: getCustomAppBarWithClose(S.of(context).newKeyVendor),
                content: StatefulBuilder(builder: (context, snapshot) {
                  return Padding(
                    padding: EdgeInsets.only(bottom: MediaQuery.of(c).viewInsets.bottom),
                    //padding: const EdgeInsets.all(8.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Form(
                          key: deviceFormKey,
                          child: Container(
                            padding: const EdgeInsets.only(left: 16, right: 16, top: 16),
                            child: Column(
                              children: [
                                const SizedBox(height: 8),
                                AppTextFormField(
                                  readOnly: true,
                                  label: S.of(context).code,
                                  controller: codeController,
                                  maxLines: 1,
                                  textInputType: TextInputType.text,
                                  validator: (value) {
                                    if (value!.isEmpty) {
                                      return S.of(context).thisFieldIsRequired;
                                    }
                                    return null;
                                  },
                                ),
                                const SizedBox(height: 8),
                                AppTextFormField(
                                  label: S.of(context).key,
                                  controller: keyController,
                                  maxLines: 1,
                                  textInputType: TextInputType.text,
                                  validator: (value) {
                                    if (value!.isEmpty) {
                                      return S.of(context).thisFieldIsRequired;
                                    }
                                    return null;
                                  },
                                ),
                                const SizedBox(height: 16),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 24),
                        getAppDivider(),
                      ],
                    ),
                  );
                }),
                actions: [
                  Container(
                    padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16, top: 8),
                    child: AppButton(
                      loadingStatus: addApiStatus,
                      fontSize: 14,
                      buttonWidth: 80,
                      buttonHeight: 35,
                      fontFamily: AppAssetsConstants.openSans,
                      buttonName: S.of(context).addNewBtn,
                      onPressed: () async {
                        if (!deviceFormKey.currentState!.validate()) {
                          return;
                        }
                        VendorKeyItem item = VendorKeyItem(
                          key: keyController.text,
                          vendorCode: widget.vendor,
                        );
                        await addNewVendorKey(item);
                        goBack();
                      },
                    ),
                  ),
                ],
              );
            },
          );
        },
      );
    }
  }
}
