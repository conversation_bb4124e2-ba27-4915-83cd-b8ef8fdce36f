import 'package:quantumlink_node/app_import.dart';

class VendorKeysDataSource extends DataTableSource {
  final BuildContext context;
  final List<VendorKeyItem> keysList;
  final Function(VendorKeyItem)? onDelete;

  VendorKeysDataSource(this.context, this.keysList, {this.onDelete});
  DataTableHelper dataTableHelper = DataTableHelper();
  @override
  DataRow2 getRow(int index, [Color? color]) {
    final keyItem = keysList[index];


    String keyName= keyItem.key ?? '';
    String vendorCode= keyItem.vendorCode ?? '';
    String createdAt= keyItem.createdAt ?? '';
    return DataRow2.byIndex(
      index: index,
      color: index.isEven
          ? MaterialStateProperty.all(AppColorConstants.colorWhite)
          : MaterialStateProperty.all(AppColorConstants.colorBackgroundDark),
      cells: [
        DataCell(AppText(keyName,style: dataTableHelper.dataRowTextStyle)),
        DataCell(AppText(vendorCode,style: dataTableHelper.dataRowTextStyle)),
        DataCell(AppText(createdAt,style: dataTableHelper.dataRowTextStyle)),
        DataCell(IconButton(
          icon:  Icon(Icons.delete,color: AppColorConstants.colorRed,),
          onPressed: onDelete != null ? () => onDelete!(keyItem) : null,
        )),
      ],
    );
  }

  @override
  int get rowCount => keysList.length;

  @override
  bool get isRowCountApproximate => false;

  @override
  int get selectedRowCount => 0;
} 