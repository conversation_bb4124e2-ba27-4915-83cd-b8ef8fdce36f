// ignore_for_file: deprecated_member_use
import 'package:quantumlink_node/app_import.dart';

class AmplifierProviderPage extends StatefulWidget {
  final SettingsPageHelper settingPageHelper;

  const AmplifierProviderPage({super.key, required this.settingPageHelper});

  @override
  State<AmplifierProviderPage> createState() => AmplifierProviderPageState();
}

class AmplifierProviderPageState extends State<AmplifierProviderPage> {
  SettingController controller = SettingController();
  late SettingsPageHelper _pageHelper;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _pageHelper = widget.settingPageHelper;
    _pageHelper.getAmpProviderList();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SettingController>(
      init: SettingController(),
      builder: (SettingController settingController) {
        controller = settingController;
        return Stack(
          children: [
            getBody(),
          ],
        );
      },
    );
  }

  Widget getBody() {
    return getAMPVendorBoardView();
  }

  Widget getPageAppBar() {
    return Container(
      decoration: BoxDecoration(
          border: Border.all(color: AppColorConstants.colorH2, width: 0.8),
          color: AppColorConstants.colorWhite,
          borderRadius: BorderRadius.only(
              topRight: Radius.circular(getSize(7)), topLeft: Radius.circular(getSize(7)))),
      alignment: Alignment.topCenter,
      padding: const EdgeInsets.all(10),
      child: Column(
        children: [
          Row(
            children: [
              const Spacer(),
              if (_pageHelper.screenLayoutType == ScreenLayoutType.desktop) ...[
                searchTextFieldView()
              ],
            ],
          ),
          if (_pageHelper.screenLayoutType != ScreenLayoutType.desktop)
            Row(
              children: [
                searchTextFieldView(),
              ],
            )
        ],
      ),
    );
  }

  Widget getAMPVendorBoardView() {
    return ScreenLayoutTypeBuilder(
      builder: (context, screenType, constraints) {
        _pageHelper.screenLayoutType = screenType;
        return ClipRRect(
          borderRadius: const BorderRadius.all(Radius.circular(5)),
          child: Container(
            color: AppColorConstants.colorWhite,
            width: double.infinity,
            height: double.infinity,
            child: ListView(
              shrinkWrap: true,
              padding: EdgeInsets.only(left: getSize(15), right: getSize(20)),
              children: [
                getPageAppBar(),
                getTableBoardView(),
                buildLastSeenView(),
                if (_pageHelper.ampProviderErrorMessage.isNotEmpty)
                  Align(
                    alignment: Alignment.centerRight,
                    child: errorMessageView(
                      errorMessage: _pageHelper.ampProviderErrorMessage,
                    ),
                  ),
                SizedBox(height: getSize(20)),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget getTableBoardView() {
    if (_pageHelper.apiStatus == ApiStatus.loading) {
      return SizedBox(
          height: 400,
          child: Align(
              alignment: Alignment.center,
              child: Container(
                  width: double.infinity,
                  decoration: _pageHelper.dataTableHelper.tableBorderDeco(),
                  child: const AppLoader())));
    }
    int itemsPerPage = _pageHelper.dataTableHelper
        .getCurrentPageDataLength(_pageHelper.ampProviderList ?? [], _pageHelper.currentPageIndex);
    _pageHelper.recordsInPage = (_pageHelper.ampProviderList != null &&
            _pageHelper.ampProviderList!.isNotEmpty &&
            _pageHelper.ampProviderList!.length > 10)
        ? itemsPerPage
        : _pageHelper.ampProviderList?.length ?? 0;
    return Column(
      children: [
        Container(
            width: double.infinity,
            decoration: _pageHelper.dataTableHelper.tableBorderDeco(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Table
                SizedBox(
                  height: (_pageHelper.ampProviderList != null &&
                          _pageHelper.ampProviderList!.isNotEmpty)
                      ? (_pageHelper.recordsInPage * _pageHelper.heightOfDataTableCell) +
                          (_pageHelper.recordsInPage * 0.1) +
                          150
                      : (_pageHelper.recordsInPage * _pageHelper.heightOfDataTableCell) +
                          (_pageHelper.recordsInPage * 0.1) +
                          350,
                  child: getDataTableWithPageBoardView(),
                ),
                SizedBox(height: getSize(20)),
                // Divider
                Container(
                  height: 1,
                  width: double.infinity,
                  color: AppColorConstants.colorBlack12,
                ),
                Container(
                  padding: const EdgeInsets.only(left: 16, top: 10, bottom: 10),
                  width: double.infinity,
                  decoration: BoxDecoration(
                      color: AppColorConstants.colorBackgroundDark,
                      borderRadius: const BorderRadius.only(
                          bottomRight: Radius.circular(5), bottomLeft: Radius.circular(5))),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Wrap(
                        direction: _pageHelper.screenLayoutType != ScreenLayoutType.mobile
                            ? Axis.horizontal
                            : Axis.vertical,
                        spacing: 16,
                        children: [
                          getAddButtonView(),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            )),
      ],
    );
  }

  Widget getRefreshButtonView() {
    return Padding(
      padding: const EdgeInsets.only(right: 10.0),
      child: AppRefresh(
          onPressed: () {
            if (_pageHelper.apiStatus != ApiStatus.loading) {
              _pageHelper.apiStatus = ApiStatus.loading;
              _pageHelper.updateState();
              _pageHelper.getAmpProviderList();
            }
          },
          loadingStatus: _pageHelper.apiStatus),
    );
  }

  Widget buildLastSeenView() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        getTimeDurationView(
          onTapTime: DateTime.now(),
          refreshStatus: _pageHelper.apiStatus,
          updateTime: _pageHelper.lastUpdateTime,
        ),
        getRefreshButtonView(),
      ],
    );
  }

  Widget getDataTableWithPageBoardView() {
    return PaginatedDataTable2(
      headingCheckboxTheme:
          CheckboxThemeData(side: BorderSide(width: 2, color: AppColorConstants.colorWhite)),
      headingTextStyle: _pageHelper.dataTableHelper.headingTextStyle(),
      wrapInCard: false,
      datarowCheckboxTheme: const CheckboxThemeData(side: BorderSide(width: 2, color: Colors.grey)),
      border: _pageHelper.dataTableHelper.tableBorder(),
      renderEmptyRowsInTheEnd: false,
      headingRowColor: _pageHelper.dataTableHelper.headingRowColor(),
      columns: _getDataColumns(),
      controller: _pageHelper.paginatorController,
      source: _pageHelper.ampProviderDataSource,
      minWidth: 950,
      dataRowHeight: 51,
      hidePaginator: false,
      empty: _pageHelper.dataTableHelper.getEmptyTableContent(context),
    );
  }

  List<DataColumn> _getDataColumns() {
    return [
      DataColumn2(
        fixedWidth: getSize(170),
        label: SelectableText(S.of(context).vendor),
      ),
      DataColumn2(
        fixedWidth: getSize(250),
        label: AppText(
          S.of(context).name,
          textAlign: TextAlign.center,
        ),
      ),
      DataColumn2(
        fixedWidth: getSize(250),
        label: AppText(
          S.of(context).applicationName,
          textAlign: TextAlign.center,
        ),
      ),
      DataColumn2(
       // fixedWidth: getSize(100),
        label:  AppText(
       S.of(context).edit,
          textAlign: TextAlign.center,
        ),
      ),
      // DataColumn2(
      //   label:  AppText(
      //    S.of(context).deleteBtn,
      //     textAlign: TextAlign.center,
      //   ),
      // ),
    ];
  }

  Widget getAddButtonView() {
    return AppButton(
      buttonHeight: 33,
      fontSize: 14.5,
      buttonRadius: 9,
      buttonName: S.of(context).addBtn,
      onPressed: () {
        _pageHelper.addNewProviderModalView(context, false);
      },
      fontFamily: AppAssetsConstants.openSans,
    );
  }

  Widget searchTextFieldView() {
    return Flexible(
      child: Container(
        decoration: BoxDecoration(
            color: AppColorConstants.colorWhite1, borderRadius: BorderRadius.circular(getSize(8))),
        child: AppTextFormField(
          onChanged: (value) {
            _pageHelper.applyAmpProviderSearch(value);
          },
          focusedBorderColor: AppColorConstants.colorPrimary,
          controller: _pageHelper.searchController,
          enabledBorderColor: AppColorConstants.colorWhite1,
          hintText: S.of(context).quickSearch,
          maxLines: 1,
          textInputType: TextInputType.emailAddress,
          validator: (value) {
            return null;
          },
          borderRadius: getSize(8),
          hintTextColor: AppColorConstants.colorDarkBlue,
          suffixIcon: const Padding(
            padding: EdgeInsets.all(12),
            child: AppImageAsset(image: AppAssetsConstants.searchIcon),
          ),
          hintFontSize: 17,
          fontFamily: AppAssetsConstants.openSans,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }
}
