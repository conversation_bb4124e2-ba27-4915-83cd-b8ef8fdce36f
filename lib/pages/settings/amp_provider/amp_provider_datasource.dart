// ignore_for_file: deprecated_member_use

import 'package:quantumlink_node/app_import.dart';


class AmpProviderDataSource extends DataTableSource {
  int selectedCount = 0;

  AmpProviderDataSource.empty(this.context, this.ampProviderList, this.onEdit, this.onDelete, this.onTap);

  AmpProviderDataSource(this.context, this.ampProviderList, this.onEdit, this.onDelete, this.onTap,
      [sortedByEUI = false,
      this.hasRowHeightOverrides = false,
      this.hasZebraStripes = false]) {
    if (sortedByEUI) {
      sort((d) => d.name!, true);
    }
  }

  final BuildContext context;
  final List<AmplifierProviderItem> ampProviderList;
  final Function(AmplifierProviderItem) onEdit;
  final Function(AmplifierProviderItem) onDelete;
  final Function(AmplifierProviderItem) onTap;
  // Override height values for certain rows
  bool hasRowHeightOverrides = false;

  // Color each Row by index's parity
  bool hasZebraStripes = false;

  void sort<T>(Comparable<T> Function(AmplifierProviderItem d) getField, bool ascending) {
    ampProviderList.sort((a, b) {
      final aValue = getField(a);
      final bValue = getField(b);
      return ascending ? Comparable.compare(aValue, bValue) : Comparable.compare(bValue, aValue);
    });
    notifyListeners();
  }
  DataTableHelper dataTableHelper = DataTableHelper();

  @override
  DataRow2 getRow(int index, [Color? color]) {
    assert(index >= 0);
    if (index >= ampProviderList.length) throw 'index > _desserts.length';
    final dessert = ampProviderList[index];

    String code= dessert.code ?? "";
    String vendorName = code;
    String name= dessert.name ?? "";
    String chirpstackAppName= dessert.chirpstackAppName ?? "";

    return DataRow2.byIndex(
      index: index,
      color:  index.isEven
          ? MaterialStateProperty.all(AppColorConstants.colorWhite)
          : MaterialStateProperty.all(AppColorConstants.colorBackgroundDark),
      cells: [
        DataCell(
            onTap: () => onTap.call(dessert),
            AppText(
          "${dessert.code ?? ""}",
          maxLines: 1,
          isSelectableText: false,
          style: TextStyle(
              fontWeight: FontWeight.w600,
              fontFamily: AppAssetsConstants.openSans,
              color: AppColorConstants.colorLightBlue,
              fontSize: getSize(14),
              decorationThickness: 4,
              decorationColor: AppColorConstants.colorLightBlue),
        )),
        DataCell(AppText(name,
            style: dataTableHelper.dataRowTextStyle)),
        DataCell(AppText(chirpstackAppName,
            style: dataTableHelper.dataRowTextStyle)),
        DataCell(IconButton(
          color: AppColorConstants.colorPrimary,
          icon: const Icon(Icons.edit),
          onPressed:  () {
            onEdit.call(dessert);
          },
        )),
        // DataCell( IconButton(
        //   color: AppColorConstants.colorRed,
        //   icon: const Icon(Icons.delete),
        //   onPressed: () {
        //     onDelete.call(dessert);
        //   },
        // ),)
      ],
    );
  }

  @override
  int get rowCount => ampProviderList.length;

  @override
  bool get isRowCountApproximate => false;

  @override
  int get selectedRowCount => selectedCount;



}
