import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/model/setting_tab_item.dart';
import 'package:quantumlink_node/pages/settings/amp_provider/amp_provider_page.dart';
import 'vendor_keys/vendor_keys_page.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => SettingsPageState();
}

class SettingsPageState extends State<SettingsPage> with TickerProviderStateMixin {
  SettingsPageHelper? _pageHelper;
  SettingController controller = SettingController();

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SettingController>(
      init: SettingController(),
      builder: (SettingController settingController) {
        _pageHelper ?? (_pageHelper = SettingsPageHelper(this));
        controller = settingController;
        return Stack(
          children: [
            getBody(),
          ],
        );
      },
    );
  }

  Widget getBody() {
    return getSettingView();
  }

  Widget getSettingView() {
    return ScreenLayoutTypeBuilder(
      builder: (context, screenType, constraints) {
        _pageHelper!.screenLayoutType = screenType;
        return SizedBox(
          height: (double.infinity),
          child: ClipRRect(
            borderRadius: const BorderRadius.all(Radius.circular(5)),
            child: Container(
              color: AppColorConstants.colorWhite,
              width: double.infinity,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: NestedScrollView(
                        headerSliverBuilder: (context, innerBoxIsScrolled) => [
                              SliverToBoxAdapter(
                                  child: Padding(
                                      padding: const EdgeInsets.symmetric(horizontal: 10),
                                      child: getPageTitleView(S.of(context).homeSettings))),
                              SliverToBoxAdapter(child: SizedBox(height: getSize(10))),
                              SliverToBoxAdapter(child: getTabsOnSettingHeader()),
                            ],
                        body: getTabsContent()),
                  )
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget getTabsOnSettingHeader() {
    return SelectionArea(
      child: TabBar(
        controller: _pageHelper!.tabController,
        dividerColor: AppColorConstants.colorWhite,
        labelPadding: EdgeInsets.zero,
        labelColor: Colors.white,
        padding: EdgeInsets.symmetric(horizontal: getSize(10)),
        isScrollable: true,
        indicatorColor: AppColorConstants.colorWhite,
        tabAlignment: TabAlignment.start,
        onTap: (value) {
          for (var tabElement in _pageHelper!.tabLIst) {
            tabElement.isCurrentOpen = false;
          }
          _pageHelper!.tabLIst[value].isCurrentOpen = true;
          _pageHelper!.updateState();
        },
        tabs: List.generate(_pageHelper!.tabLIst.length, (index) {
          SettingTabItem settingTabItem = _pageHelper!.tabLIst[index];
          return MouseRegion(
            onEnter: (event) {
              _pageHelper!.isHovered[index] = true;
              _pageHelper!.updateState();
            },
            onExit: (event) {
              _pageHelper!.isHovered[index] = false;
              _pageHelper!.updateState();
            },
            child: Tab(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 5),
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: getSize(25)),
                  height: getSize(42),
                  alignment: Alignment.center,
                  decoration: settingTabItem.getDeco(_pageHelper!.isHovered[index]),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      AppText(
                        isSelectableText: false,
                        settingTabItem.title,
                        style: TextStyle(
                          fontSize: getSize(16),
                          fontFamily: AppAssetsConstants.poppins,
                          fontWeight: FontWeight.w600,
                          color: _pageHelper!.isHovered[index] && !settingTabItem.isCurrentOpen
                              ? AppColorConstants.colorBlackBlue
                              : settingTabItem.isCurrentOpen
                                  ? AppColorConstants.colorLightBlue
                                  : AppColorConstants.colorH2,
                        ),
                      ),
                      if (_pageHelper!.tabLIst.length > 1 && index > 0) ...[
                        SizedBox(width: getSize(10)),
                        GestureDetector(
                            onTap: () async {
                             _pageHelper!.removeVendorKeysTab(index);
                            },
                            child: CircleAvatar(
                                maxRadius: 10,
                                backgroundColor: settingTabItem.isCurrentOpen
                                    ? AppColorConstants.colorLightBlue
                                    : AppColorConstants.colorH2.withOpacity(0.3),
                                child: Icon(Icons.close,
                                    size: getSize(16),
                                    color: settingTabItem.isCurrentOpen
                                        ? AppColorConstants.colorWhite
                                        : AppColorConstants.colorH3))),
                      ]
                    ],
                  ),
                ),
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget getTabsContent() {
    return TabBarView(
      physics: const NeverScrollableScrollPhysics(),
      controller: _pageHelper!.tabController,
      children: List.generate(_pageHelper!.tabLIst.length, (index) {
        final tab = _pageHelper!.tabLIst[index];
        if (index == 0) {
          return AmplifierProviderPage(settingPageHelper: _pageHelper!);
        }
        return VendorKeysPage(vendor: tab.title ,pageHelper: _pageHelper!);
      }),
    );
  }
}
