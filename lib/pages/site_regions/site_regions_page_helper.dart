import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/serialized/dashboard/dashboard.dart';

class SiteRegionsPageHelper {
  late SiteRegionsPageState state;

  ApiStatus apiStatus = ApiStatus.initial;
  late ScreenLayoutType screenLayoutType;
  final key = GlobalKey<PaginatedDataTableState>();
  final double heightOfDataTableCell = 48;
  int recordsInPage = 0; // For Set Fixed and Static Height Of Table
  int currentPageIndex = 0;
  DataTableHelper dataTableHelper = DataTableHelper();
  PaginatorController sitePaginatorController = PaginatorController();
  List<SiteDataModel> listSiteSensorData = [];
  late SiteRegionsDataSource siteRegionsDataSource;
  DateTime ?siteUpdateTime;
  DateTime? onTapTime;
  Duration ? differenceTime;
  bool isShowText = true;
  Timer? refreshTimer;
  DashboardModel dashboardModel = DashboardModel();
  SiteRegionsPageHelper(this.state) {
    apiStatus = ApiStatus.loading;
    Future.delayed(const Duration(milliseconds: 500)).then((value) async {
      getSiteRegionsData();
      getCurrantPageIndex();
    });
  }

  getSiteDashboardData() async {
    dashboardModel = await state.siteRegionsController.getDashboardDataList(state.context);
    state.siteRegionsController.update();
  }

  getSiteRegionsData() async {
    initializeTimer();
    state.siteRegionsController.update();
    await getSiteDashboardData();
    await state.siteRegionsController
        .getSiteRegionsDeviceList(context: state.context)
        .then(
      (value) {
        listSiteSensorData = value.cast<SiteDataModel>();
      },
    );

    if (state.mounted) {
      siteRegionsDataSource = SiteRegionsDataSource(state.context, listSiteSensorData, (value) {
        bool isUpdate = true;
        addNewSiteDeviceModalView(
            state.context, (SiteDataModel item) => addSiteDevice(item, isUpdate),
            item: value);
      }, (value) {
        deleteSiteDeviceModalView(state.context, () => deleteSiteDevice(value), item: value);
      });
    }
    siteUpdateTime = DateTime.now();
    getDifferenceTime();
    apiStatus = ApiStatus.success;
    state.siteRegionsController.update();
  }

  //GET CURRANT PAGE INDEX
  getCurrantPageIndex() {
    sitePaginatorController.addListener(() {
      currentPageIndex = (sitePaginatorController.currentRowIndex / 10).ceil();
      state.siteRegionsController.update();
    });
  }
  void initializeTimer() {
    differenceTime = null;
    onTapTime = DateTime.now();
    isShowText = true;
    refreshTimer?.cancel();
  }
  getDifferenceTime() {
    differenceTime = DateTime.now().difference(onTapTime!);
    refreshTimer= Timer(const Duration(seconds: 2), () {
      isShowText = false;
      state.siteRegionsController.update();
    });
  }

  addSiteDevice(SiteDataModel item, bool isUpdate) async {
    goBack();
    apiStatus = ApiStatus.loading;
    state.siteRegionsController.update();
    if (isUpdate) {
      Map<String, dynamic>? response = await state.siteRegionsController
          .updateSiteDevice(itemData: item, context: state.context);
      if (response != null && response.isNotEmpty) {
        if (state.mounted) {
          if (response['message'].toString() != "null") {
            response['message'].toString().showSuccess(state.context);
          } else {
            S.of(state.context).updateSiteSuccess.toString().showSuccess(state.context);
          }
          getSiteRegionsData();
        }
      } else {
        if (state.mounted) {
          S.of(state.context).updateSiteFailed.toString().showError(state.context);
        }
      }
    } else {
      Map<String, dynamic>? response =
          await state.siteRegionsController.addSiteDevice(itemData: item, context: state.context);
      if (response != null && response.isNotEmpty) {
        if (state.mounted) {
          if (response['message'].toString() != "null") {
            response['message'].toString().showSuccess(state.context);
          } else {
            S.of(state.context).addSiteSuccess.toString().showSuccess(state.context);
          }
          getSiteRegionsData();
        }
      } else {
        if (state.mounted) {
          S.of(state.context).addSiteFailed.toString().showError(state.context);
        }
      }
    }

    apiStatus = ApiStatus.success;
    state.siteRegionsController.update();
  }

  deleteSiteDevice(SiteDataModel value) async {
    apiStatus = ApiStatus.loading;
    state.siteRegionsController.update();
    Map<String, dynamic>? success =
        await state.siteRegionsController.deleteSiteDevice(siteId: value.id);
    if (success != null && success.isNotEmpty) {
      if (state.mounted) {
        "${value.name} ${S.of(state.context).deleteSiteSuccess}"
            .toString()
            .showSuccess(state.context);
      }
      getSiteRegionsData();
    } else {
      if (state.mounted) {
        success!['message'].toString().showError(state.context);
      }
    }

    apiStatus = ApiStatus.success;
    state.siteRegionsController.update();
  }

}
