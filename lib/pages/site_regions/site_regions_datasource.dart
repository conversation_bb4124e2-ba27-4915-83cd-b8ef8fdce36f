// ignore_for_file: deprecated_member_use

import 'package:quantumlink_node/app_import.dart';

class SiteRegionsDataSource extends DataTableSource {
  int _selectedCount = 0;
  SiteRegionsController siteRegionsController = Get.find<SiteRegionsController>();

  SiteRegionsDataSource.empty(this.context, this.list, this.onEdit, this.onDelete);

  SiteRegionsDataSource(this.context, this.list, this.onEdit, this.onDelete,
      [sortedByEUI = false,
      this.hasRowTaps = false,
      this.hasRowHeightOverrides = false,
      this.hasZebraStripes = false]) {
    if (sortedByEUI) {
      sort((d) => d.name, true);
    }
  }

  final BuildContext context;
  final List<SiteDataModel> list;
  final Function(SiteDataModel) onEdit;
  final Function(SiteDataModel) onDelete;

  // Add row tap handlers and show snackbar
  bool hasRowTaps = false;

  // Override height values for certain rows
  bool hasRowHeightOverrides = false;
  DataTableHelper dataTableHelper = DataTableHelper();

  // Color each Row by index's parity
  bool hasZebraStripes = false;

  void sort<T>(Comparable<T> Function(SiteDataModel d) getField, bool ascending) {
    list.sort((a, b) {
      final aValue = getField(a);
      final bValue = getField(b);
      return ascending ? Comparable.compare(aValue, bValue) : Comparable.compare(bValue, aValue);
    });
    notifyListeners();
  }

  @override
  DataRow2 getRow(int index, [Color? color]) {
    assert(index >= 0);
    if (index >= list.length) throw 'index > _desserts.length';
    final dessert = list[index];
    return DataRow2.byIndex(
      index: index,
      selected: dessert.selected ?? false,
      color: (index.isEven
          ? MaterialStateProperty.all(AppColorConstants.colorWhite)
          : MaterialStateProperty.all(AppColorConstants.colorBackgroundDark)),
      onSelectChanged: (value) {
        if (dessert.selected != value) {
          _selectedCount += value! ? 1 : -1;
          assert(_selectedCount >= 0);
          dessert.selected = value;
          notifyListeners();
        }
        siteRegionsController.update();
      },
      onTap: hasRowTaps ? () => showMessage('Tapped on row ${dessert.name}') : null,
      onDoubleTap: hasRowTaps ? () => showMessage('Double Tapped on row ${dessert.name}') : null,
      onLongPress: hasRowTaps ? () => showMessage('Long pressed on row ${dessert.name}') : null,
      onSecondaryTap: hasRowTaps ? () => showMessage('Right clicked on row ${dessert.name}') : null,
      onSecondaryTapDown:
          hasRowTaps ? (d) => showMessage('Right button down on row ${dessert.name}') : null,
      cells: [
        DataCell(AppText("${dessert.name}",
            style: dataTableHelper.dataRowTextStyle)),
        DataCell(AppText("${dessert.ampsCount}",
            style: dataTableHelper.dataRowTextStyle)),
        DataCell(AppText("${dessert.rpdCount}",
            style: dataTableHelper.dataRowTextStyle)),
        DataCell(AppText("${dessert.vlgwCount}",
            style: dataTableHelper.dataRowTextStyle)),
        DataCell(IconButton(
          onPressed: () => onEdit(dessert),
          icon: const Padding(
            padding: EdgeInsets.all(5),
            child: AppImageAsset(
              image: AppAssetsConstants.editIcon,
            ),
          ),
        )),
        DataCell(IconButton(
          onPressed: () => onDelete(dessert),
          icon:  Padding(
            padding: EdgeInsets.all(5),
            child: AppImageAsset(width: 25,image: AppAssetsConstants.deleteEnabledIcon,color: AppColorConstants.colorH2.withOpacity(0.8)),
          ),
        ))
      ],
    );
  }

  @override
  int get rowCount => list.length;

  @override
  bool get isRowCountApproximate => false;

  @override
  int get selectedRowCount => _selectedCount;

  void selectAll(bool? checked) {
    for (final dessert in list) {
      dessert.selected = checked ?? false;
    }
    _selectedCount = (checked ?? false) ? list.length : 0;
    siteRegionsController.update();
    notifyListeners();
  }
}
