import 'package:quantumlink_node/app_import.dart';

import 'site_regions_page_helper.dart';

class SiteRegionsPage extends StatefulWidget {
  const SiteRegionsPage({super.key});

  @override
  State<SiteRegionsPage> createState() => SiteRegionsPageState();
}

class SiteRegionsPageState extends State<SiteRegionsPage> {
  SiteRegionsPageHelper? sitePageHelper;
  late SiteRegionsController siteRegionsController;

  @override
  Widget build(BuildContext context) {
    sitePageHelper ?? (sitePageHelper = SiteRegionsPageHelper(this));
    return GetBuilder<SiteRegionsController>(
      init: SiteRegionsController(),
      builder: (SiteRegionsController controller) {
        siteRegionsController = controller;
        return Stack(
          children: [
            getBody(),
          ],
        );
      },
    );
  }

  Widget getBody() {
    return getBodyView();
  }

  Widget getBodyView() {
    return ScreenLayoutTypeBuilder(
      builder: (context, screenType, constraints) {
        sitePageHelper!.screenLayoutType = screenType;
        return getSiteRegionBoardView();
      },
    );
  }

  Widget getSiteRegionBoardView() {
    int itemsPerPage = sitePageHelper!.dataTableHelper.getCurrentPageDataLength(
        sitePageHelper!.listSiteSensorData, sitePageHelper!.currentPageIndex);
    sitePageHelper!.recordsInPage = (sitePageHelper!.listSiteSensorData.length > 10)
        ? itemsPerPage
        : sitePageHelper!.listSiteSensorData.length;
    return Container(
      padding: EdgeInsets.only(
          left: getSize(15), right: getSize(20)),
      child: ClipRRect(
        borderRadius: const BorderRadius.all(Radius.circular(5)),
        child: Container(
          color: AppColorConstants.colorWhite,
          width: double.infinity,
          child: ListView(
            children: [
              getPageTitleView(S.of(context).site),
              SizedBox(height: getSize(10)),
              if (sitePageHelper!.screenLayoutType == ScreenLayoutType.desktop)
                Row(
                  children: [
                    getCardView(
                        S.of(context).vLGW1,
                        AppAssetsConstants.monitorIcon,
                        sitePageHelper!.dashboardModel.vlgws ?? "",
                        sitePageHelper!.dashboardModel.vlgwsWithoutSite?? ""),
                    const SizedBox(
                      width: 20,
                    ),
                    getCardView(
                        S.of(context).rPD,
                        AppAssetsConstants.diagnosticsIcon,
                        sitePageHelper!.dashboardModel.rpds?? "",
                        sitePageHelper!.dashboardModel.rpdsWithoutSite?? ""),
                    const SizedBox(
                      width: 20,
                    ),
                    getCardView(
                        S.of(context).aMPS,
                        AppAssetsConstants.amplifiersIcon,
                        sitePageHelper!.dashboardModel.activeAmplifiers?? "",
                        sitePageHelper!.dashboardModel.ampsWithoutSite?? ""),
                  ],
                ),
              if (sitePageHelper!.screenLayoutType != ScreenLayoutType.desktop) ...[
                getCardView(
                    S.of(context).vLGW1,
                    AppAssetsConstants.monitorIcon,
                    sitePageHelper!.dashboardModel.vlgws?? "",
                    sitePageHelper!.dashboardModel.vlgwsWithoutSite?? ""),
                const SizedBox(
                  width: 20,
                ),
                getCardView(
                    S.of(context).rPD,
                    AppAssetsConstants.diagnosticsIcon,
                    sitePageHelper!.dashboardModel.rpds?? "",
                    sitePageHelper!.dashboardModel.rpdsWithoutSite?? ""),
                const SizedBox(
                  width: 20,
                ),
                getCardView(
                    S.of(context).aMPS,
                    AppAssetsConstants.amplifiersIcon,
                    sitePageHelper!.dashboardModel.activeAmplifiers?? "",
                    sitePageHelper!.dashboardModel.ampsWithoutSite?? ""),
              ],
              siteRegionsBoardView(),
              buildLastSeenView(),
              SizedBox(height: getSize(20)),
            ],
          ),
        ),
      ),
    );
  }
  Widget buildLastSeenView() {
    if (sitePageHelper!.isShowText) {
      return getTimeDurationView(
        refreshStatus: sitePageHelper!.apiStatus,
        updateTime: sitePageHelper!.siteUpdateTime,
        onTapTime: sitePageHelper!.onTapTime,
        difference: sitePageHelper!.differenceTime,
      );
    } else {
      if (sitePageHelper!.siteUpdateTime != null) {
        return getLastSeenView(sitePageHelper!.siteUpdateTime);
      } else {
        return Container();
      }
    }
  }

  Widget getCardView(String cardTitle, String cardIcon, dynamic totalValue, dynamic missingValue) {
    if (sitePageHelper!.screenLayoutType == ScreenLayoutType.desktop) {
      return Flexible(
        child: buildCardWidget(cardTitle, cardIcon,totalValue , missingValue),
      );
    }
    return buildCardWidget(cardTitle, cardIcon,totalValue , missingValue);
  }

  Widget buildCardWidget(String cardTitle, String cardIcon, dynamic totalValue, dynamic missingValue) {
    return Padding(
        padding:
            EdgeInsets.only(bottom: sitePageHelper!.screenLayoutType == ScreenLayoutType.desktop ? 40 : 10),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            border: Border.all(color: AppColorConstants.colorTableBroader, width: 1),
            borderRadius: BorderRadius.circular(getSize(8)),
          ),
          child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Row(
              children: [
                Container(
                    height: 60,
                    width: 60,
                    decoration: BoxDecoration(
                      color: AppColorConstants.colorAppbar,
                      borderRadius: BorderRadius.circular(getSize(8)),
                    ),
                    child: Padding(
                      padding: EdgeInsets.all(getSize(15)),
                      child: AppImageAsset(
                        image: cardIcon,
                      ),
                    )),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      cardSubTitleView(S.of(context).total, "${totalValue}"),
                      SizedBox(height: getSize(8)),
                      cardSubTitleView(S.of(context).missingSiteInfo, "${missingValue}"),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: getSize(10)),
            AppText(
              cardTitle,
              style: TextStyle(
                  color: AppColorConstants.colorTableBroader,
                  fontSize: getSize(40),
                  fontWeight: FontWeight.w800,
                  fontFamily: AppAssetsConstants.manrope),
            ),
            SizedBox(height: getSize(5)),
          ]),
        ));
  }

  Widget cardSubTitleView(String cardSubTitle, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: getSize(25)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Flexible(
            child: AppText(
              cardSubTitle,
              style: TextStyle(
                  color: AppColorConstants.colorPrimary,
                  fontSize: getSize(16),
                  fontWeight: getMediumBoldFontWeight(),
                  fontFamily: AppAssetsConstants.openSans),
            ),
          ),
          AppText(
            value,
            style: TextStyle(
                color: AppColorConstants.colorH3,
                fontSize: getSize(14),
                fontWeight: getMediumFontWeight(),
                fontFamily: AppAssetsConstants.openSans),
          ),
        ],
      ),
    );
  }

  Widget siteRegionsBoardView() {
    if (sitePageHelper!.apiStatus == ApiStatus.loading) {
      return  ClipRRect(
        borderRadius:
            const BorderRadius.only(topRight: Radius.circular(8), topLeft: Radius.circular(8)),
        child: Container(
            alignment: Alignment.center,
            height: 300,
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(8), topLeft: Radius.circular(8)),
              border: Border.all(color: AppColorConstants.colorH2, width: 0.8),
            ),
            width: double.infinity,
            child: const AppLoader()),
      );
    }
    return Column(
      children: [
        ClipRRect(
          borderRadius:
          const BorderRadius.only(topRight: Radius.circular(8), topLeft: Radius.circular(8)),
          child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(8), topLeft: Radius.circular(8)),
                border: Border(
                    bottom: BorderSide(color: AppColorConstants.colorH2, width: 0.8),
                    left: BorderSide(color: AppColorConstants.colorH2, width: 0.8),
                    right: BorderSide(color: AppColorConstants.colorH2, width: 0.8)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Table
                  SizedBox(
                    height: (sitePageHelper!.listSiteSensorData.isNotEmpty)
                        ? (sitePageHelper!.recordsInPage * sitePageHelper!.heightOfDataTableCell) +
                            (sitePageHelper!.recordsInPage * 0.1) +
                            150
                        : (sitePageHelper!.recordsInPage * sitePageHelper!.heightOfDataTableCell) +
                            (sitePageHelper!.recordsInPage * 0.1) +
                            300,
                    child: getSiteRegionsDataTableView(),
                  ),
                  SizedBox(height: getSize(20)),
                  // Divider
                  Container(
                    height: 1,
                    width: double.infinity,
                    color: AppColorConstants.colorBlack12,
                  ),
                  // Action Buttons
                  Container(
                    padding: const EdgeInsets.only(left: 16, top: 10, bottom: 10),
                    constraints: const BoxConstraints(minWidth: 170),
                    child: getAddButtonView(),
                  ),
                ],
              )),
        )
      ],
    );
  }

  Widget getSiteRegionsDataTableView() {
    return PaginatedDataTable2(
      showCheckboxColumn: false,
      headingTextStyle: sitePageHelper!.dataTableHelper.headingTextStyle(),
      wrapInCard: false,
      border: sitePageHelper!.dataTableHelper.tableBorder(),
      renderEmptyRowsInTheEnd: false,
      // ignore: deprecated_member_use
      headingRowColor: sitePageHelper!.dataTableHelper.headingRowColor(),
      columns: _getDataColumns(),
      controller: sitePageHelper!.sitePaginatorController,
      source: sitePageHelper!.siteRegionsDataSource,
      onSelectAll: sitePageHelper!.siteRegionsDataSource.selectAll,
      minWidth: 900,
      dataRowHeight: 51,
      // For progress indicator
      hidePaginator: false,
      empty: sitePageHelper!.dataTableHelper.getEmptyTableContent(context),
    );
  }

  List<DataColumn> _getDataColumns() {
    return [
      DataColumn2(fixedWidth: 200,
        label: SelectableText(S.of(context).name),
      ),
      DataColumn2(fixedWidth: 150,
        label: AppText(S.of(context).sitesAmplifiers),
      ),
      DataColumn2(fixedWidth: 150,
        label: AppText(S.of(context).rpd),
      ),
      DataColumn2(fixedWidth: 150,
        label: AppText(S.of(context).vLGW),
      ),
      DataColumn2(fixedWidth: 120,
        label: AppText(
          S.of(context).edit,
        ),
      ),
      DataColumn2(
        label: AppText(
          S.of(context).deleteBtn,
        ),
      )
    ];
  }

  Widget getAddButtonView() {
    return AppButton(
      // ignore: deprecated_member_use
      padding: MaterialStateProperty.all(EdgeInsets.zero),
      buttonHeight: 35,
      buttonRadius: 9,
      buttonWidth: 90,
      fontSize: 15,
      buttonName: S.of(context).createSite,
      onPressed: () {
        bool isUpdate = false;
        addNewSiteDeviceModalView(
            context, (SiteDataModel item) => sitePageHelper!.addSiteDevice(item, isUpdate));
      },
      fontFamily: AppAssetsConstants.openSans,
    );
  }
}

addNewSiteDeviceModalView(c, Function fun, {SiteDataModel? item}) {
  {
    TextEditingController namController = TextEditingController();
    final TextEditingController descriptionsController = TextEditingController();

    final GlobalKey<FormState> deviceFormKey = GlobalKey<FormState>();

    if (item != null) {
      namController.text = item.name ?? "";
      descriptionsController.text = item.description ?? "";
    }

    return showDialog(
      context: c,
      builder: (context) {
        return GetBuilder<ProvisionController>(
          init: ProvisionController(),
          builder: (ProvisionController controller) {
            return AlertDialog(
              surfaceTintColor: AppColorConstants.colorWhite,
              backgroundColor: AppColorConstants.colorWhite,
              insetPadding: EdgeInsets.zero,
              contentPadding: EdgeInsets.zero,
              titlePadding: EdgeInsets.zero,
              actionsPadding: EdgeInsets.zero,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
              title: getCustomAppBarWithClose(
                  item != null ? S.of(context).updateSiteDetails : S.of(context).addSites),
              content: StatefulBuilder(builder: (context, snapshot) {
                return Padding(
                  padding: EdgeInsets.only(bottom: MediaQuery.of(c).viewInsets.bottom),
                  //padding: const EdgeInsets.all(8.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Form(
                        key: deviceFormKey,
                        child: Container(
                          padding: const EdgeInsets.only(left: 16, right: 16, top: 16),
                          child: Column(
                            children: [
                              const SizedBox(height: 8),
                              AppTextFormField(
                                label: S.of(context).siteName,
                                controller: namController,
                                maxLines: 1,
                                textInputType: TextInputType.text,
                                validator: (value) {
                                  if (value!.isEmpty) {
                                    return S.of(context).thisFieldIsRequired;
                                  } else {
                                    return null;
                                  }
                                },
                              ),
                              const SizedBox(height: 16),
                              AppTextFormField(
                                label: S.of(context).description,
                                controller: descriptionsController,
                                maxLines: 1,
                                textInputType: TextInputType.text,
                                validator: (value) {
                                  if (value!.isEmpty) {
                                    return S.of(context).thisFieldIsRequired;
                                  } else {
                                    return null;
                                  }
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),
                      getAppDivider(),
                    ],
                  ),
                );
              }),
              actions: [
                Container(
                  padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16, top: 8),
                  child: AppButton(
                    fontSize: 14,
                    buttonWidth: 80,
                    buttonHeight: 35,
                    fontFamily: AppAssetsConstants.openSans,
                    buttonName:
                        (item == null) ? "+ ${S.of(context).addNewBtn}" : S.of(context).update,
                    onPressed: () {
                      if (!deviceFormKey.currentState!.validate()) {
                        return;
                      }
                      if (item == null) {
                        // SAVE
                        SiteDataModel item = SiteDataModel.empty();
                        item.name = namController.text.trim();
                        item.description = descriptionsController.text.trim();
                        fun.call(item);
                      } else {
                        // UPDATE
                        SiteDataModel updateItem = item;
                        updateItem.name = namController.text.trim();
                        updateItem.description = descriptionsController.text.trim();
                        fun.call(updateItem);
                      }
                    },
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }
}

deleteSiteDeviceModalView(c, Function fun, {required SiteDataModel item}) {
  return showDialog(
    context: c,
    builder: (context) {
      return AlertDialog(
        surfaceTintColor: AppColorConstants.colorWhite,
        backgroundColor: AppColorConstants.colorWhite,
        insetPadding: EdgeInsets.zero,
        contentPadding: EdgeInsets.zero,
        titlePadding: EdgeInsets.zero,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
        title: getCustomAppBarWithClose(S.of(context).deleteMessage),
        content: StatefulBuilder(builder: (context, snapshot) {
          return Container(
            width: MediaQuery.of(context).size.width * 0.2, // Adjust the width as needed
            height: MediaQuery.of(context).size.height * 0.18,
            padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Expanded(
                  child: ListTile(
                    title: AppText(
                      ' ${1}. ${item.name} ?',
                      style: const TextStyle(
                          fontFamily: AppAssetsConstants.openSans,
                          fontSize: 17,
                          fontWeight: FontWeight.w400),
                    ),
                  ),
                ),
                getAppDivider(),
              ],
            ),
          );
        }),
        actions: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              AppButton(
                fontSize: 14,
                buttonHeight: 35,
                fontFamily: AppAssetsConstants.openSans,
                buttonName: 'No',
                onPressed: () {
                  goBack();
                },
              ),
              const SizedBox(width: 16),
              AppButton(
                fontSize: 14,
                buttonHeight: 35,
                fontFamily: AppAssetsConstants.openSans,
                buttonName: 'Yes',
                onPressed: () {
                  goBack();
                  fun.call();
                },
              ),
            ],
          )
        ],
      );
    },
  );
}
