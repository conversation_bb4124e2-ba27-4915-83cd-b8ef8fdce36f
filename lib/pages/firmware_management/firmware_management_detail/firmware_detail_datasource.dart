// ignore_for_file: deprecated_member_use

import 'package:quantumlink_node/app_import.dart';

class FirmwareDetailDataSource extends DataTableSource {
  int selectedCount = 0;
  List<DevicesDetail> deviseList;
  late FirmwareController firmwareController;
  FirmwareManagementDetailPageState state;
  ApiStatus apiStatus = ApiStatus.initial;
  List<DevicesDetail> deviceSearchList = [];

  FirmwareDetailDataSource(this.state,{required this.deviseList, required this.firmwareController}) ;

  void quickSearch(String value) {
    if (value.isNotEmpty) {
      deviceSearchList = state.depDetailDeviseList.deviseList.where((element) {
        return element.deviceEui!.contains(value);
      }).toList();
      deviseList = deviceSearchList;
    } else {
      deviseList = state.depDetailDeviseList.deviseList;
    }
    firmwareController.update();
    notifyListeners();
  }



  @override
  DataRow? getRow(int index) {
    if (index >= deviseList.length) {
      return null;
    }
    return DataRow(
        cells: getCells(index), color: MaterialStateProperty.all(AppColorConstants.colorWhite));
  }

  List<DataCell> getCells(int index) {


    DevicesDetail dessert = deviseList[index] ;

    String devEui = dessert.deviceEui ?? "";
    String summaryStatus = dessert.summaryStatus ?? "";
    String currentVersion   = dessert.currentVersion ?? "";
    String upgradeFailRemarks = dessert.remarks ?? "";
    List <ProgressDot> progressDotsList = dessert.progressDots ?? [];

    bool inProgress = !(progressDotsList.every((dot) =>
        dot.status.toLowerCase() != AppStringConstants.pendingLowerCase &&
        dot.status.toLowerCase() != AppStringConstants.failed));

    String status = summaryStatus.toLowerCase();
    bool isCheckedVersion = !inProgress &&
        (status.contains(AppStringConstants.completeLowerCase) ||
            status.contains(AppStringConstants.successLowerCase) ||
            status.contains(AppStringConstants.successfulLowerCase) ||
            status.contains(AppStringConstants.completed));


    List<DataCell> cells = [
      DataCell(AppText(devEui,
          style: TextStyle(
              fontFamily: AppAssetsConstants.roboto,
              fontSize: getSize(14),
              fontWeight: getMediumFontWeight()))),
      DataCell(Center(child: AppText(currentVersion, style: TextStyle(
          color: isCheckedVersion ? AppColorConstants.colorSuccessGreen : AppColorConstants.colorBlack,
          fontFamily: AppAssetsConstants.roboto,
          fontSize: getSize(14),
          fontWeight: getMediumFontWeight())))),
      DataCell(Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: progressDotsList.map((dot) {
              return isDoneCircleWidget(dot.status ?? "", dot.tooltip ?? "");
            }).toList(),
          ).paddingOnly(left: 50,right: 10),
          Expanded(
            child: Align(
              alignment: Alignment.center,
              child: summaryStatus.toLowerCase().contains(AppStringConstants.fail) &&
                  upgradeFailRemarks.isNotEmpty
                  ? tooltipWidget(
                tooltipMessage: upgradeFailRemarks,
                text: summaryStatus,
              )
                  : AppText(
                summaryStatus,
                style: TextStyle(
                  color: state.getStatusFieldsColourCoding(
                    summaryStatus,
                    inProgress: inProgress,
                  ),
                  fontFamily: AppAssetsConstants.roboto,
                  fontSize: getSize(14),
                  fontWeight: getMediumFontWeight(),
                ),
              ),
            ),
          ),
        ],
      )),
      DataCell(Container())
    ];
    return cells;
  }

  upgradeDeployment(String devEui,String deploymentId) async {
    try {
      apiStatus = ApiStatus.loading;
      state.setState(() {});
      Map<String, dynamic> response = await firmwareController
          .upgradeDeployment(context: state.context, devEui: devEui, deploymentId: deploymentId);
      if (response.isNotEmpty) {
        if (response['success'] == true) {
          apiStatus = ApiStatus.success;
          state.setState(() {});
          response['message'].toString().showSuccess(state.context);
          state.getFirmwareDetailData();
        } else {
          apiStatus = ApiStatus.success;
          state.setState(() {});
          response['message'].toString().showError(state.context);
        }
      }
    } catch (e) {
      apiStatus = ApiStatus.success;
      state.setState(() {});
      S.of(state.context).socketExceptionMessage.showError(state.context);
    }
  }




  Widget isDoneCircleWidget(String status , String toolTipMessage) {
    switch (status.toLowerCase()) {
      case AppStringConstants.successLowerCase:
        return commonCircleWidget(AppColorConstants.colorGreenBright, toolTipMessage);
      case AppStringConstants.failed:
        return commonCircleWidget(AppColorConstants.colorRed, toolTipMessage);
      default:
        return commonCircleWidget(AppColorConstants.colorWhite, toolTipMessage);
    }
  }

  Widget commonCircleWidget(Color color, String text) {
    return Tooltip(verticalOffset: -40,
      message: text ,
      triggerMode: TooltipTriggerMode.tap,
      child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 2.5),
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: color,
            border: Border.all(
                color: AppColorConstants.colorBlack, width: 1),
          )),
    );
  }


  Widget tooltipWidget({String ?tooltipMessage , String ?text}) {
    return  Tooltip(
        verticalOffset: -40,
        textStyle: TextStyle(
          fontFamily: AppAssetsConstants.openSans,
          fontWeight: FontWeight.w500,
          fontSize: getSize(14),
          color: AppColorConstants.colorBlackBlue,
        ),
        message: "\u{26A0} $tooltipMessage",
        decoration: BoxDecoration(
          color: AppColorConstants.colorCriticalLite,
          border: Border.all(
              color:  AppColorConstants.colorRedAccent, width: 0.8),
          borderRadius: BorderRadius.circular(8),
        ),
        height: 30,child: AppText(text??"", style: TextStyle(
        color: text!.toLowerCase().contains(AppStringConstants.failLowerCase)
            ? AppColorConstants.colorRedDark
            : AppColorConstants.colorBlack,
        fontFamily: AppAssetsConstants.roboto,
        fontSize: getSize(14),
        fontWeight: getMediumFontWeight())) );
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => deviseList.length;

  @override
  int get selectedRowCount => 0;

  void selectAll(bool? checked) {
    // for (final dessert in deviceStatusList) {
    //   dessert.selected = checked ?? false;
    // }
    // selectedCount = (checked ?? false) ? firmDetailList.length : 0;
    notifyListeners();
  }
}


