// ignore_for_file: deprecated_member_use

import 'package:flutter/cupertino.dart';
import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/pages/firmware_management/firmware_management_detail/firmware_detail_datasource.dart';
import 'package:quantumlink_node/pages/firmware_management/firmware_management_detail/mobile_firmware_detail.dart';
import 'package:quantumlink_node/pages/firmware_management/mobile_firmware.dart';
import 'package:rxdart/rxdart.dart';

import '../../vlgw/vlgw_detail/vlgw_info/vlgw_info_view.dart';

class FirmwareManagementDetailPage extends StatefulWidget {
  final String deploymentID;
  final Deployment deployment;
  final FirmwarePageHelper firmwarePageHelper;
  const FirmwareManagementDetailPage({required this.deploymentID,super.key, required this.deployment,required this.firmwarePageHelper});

  @override
  State<FirmwareManagementDetailPage> createState() => FirmwareManagementDetailPageState();
}

class FirmwareManagementDetailPageState extends State<FirmwareManagementDetailPage> {
  PaginatorController paginatorController = PaginatorController();
  late FirmwareController firmwareController;
  ApiStatus apiStatus = ApiStatus.initial;
  ApiStatus detailUpgradeApiStatus = ApiStatus.initial;
  final double heightOfDataTableCell = 48;
  int recordsInPage = 0;
  int currentPageIndex = 0;
  late FirmwareDetailDataSource firmwareDetailDataSource;
  late ScreenLayoutType screenLayoutType;
  double constraintsWidth = 0;
  bool isShowCompleted = false;
  DeploymentDetail deploymentDetail = DeploymentDetail.empty();
  DeploymentDetailDeviseList depDetailDeviseList = DeploymentDetailDeviseList.empty();
  bool isMultiCast = false;
  TextEditingController searchController = TextEditingController();
  DataTableHelper dataTableHelper = DataTableHelper();
  PaginationHelper depDevicesPaginationHelper = PaginationHelper();
  int deploymentDevicesPageOffset = 0;
  DateTime? lastUpdateTime ;
  DateTime? onTapTime;
  Duration? differenceTime;
  bool isShowText = true;
  Timer? refreshTimer;
  Timer? detailRefreshTimer;
  bool isRefresh = false;
  int callCount = 0;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    firmwareController = Get.find<FirmwareController>();
    apiStatus = ApiStatus.loading;
    WidgetsBinding.instance.addPostFrameCallback((_){
      getFirmwareDetailData(); });
    getCurrantPageIndex();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<FirmwareController>(
      init: FirmwareController(),
      builder: (FirmwareController controller) {
        firmwareController = controller;
        return Stack(
          children: [
            getBodyView(),
          ],
        );
      },
    );
  }

  Future getFirmwareDetailData() async {
    initializeTimer();
    firmwareController.update();
    deploymentDetail = await firmwareController.getDeploymentDetail(
        context: context, deploymentId: widget.deploymentID);
    if (depDevicesPaginationHelper.currentPage == 0 && searchController.text.isEmpty) {
      deploymentDevicesPageOffset = 0;
      depDetailDeviseList = await firmwareController.getDeploymentDetailDeviseList(
          context: context,
          deploymentId: widget.deploymentID,
          pageOffset: deploymentDevicesPageOffset,
          perPageLimit: AppStringConstants.deploymentDevicesPerPageLimit);
      firmwareDetailDataSource = FirmwareDetailDataSource(
        this,
        deviseList: depDetailDeviseList.deviseList,
        firmwareController: firmwareController,
      );
      bool hasMoreData =
          depDetailDeviseList.deviseList.length == AppStringConstants.deploymentDevicesPerPageLimit;
      depDevicesPaginationHelper.updatePagination(depDetailDeviseList.deviseList.length,
          hasMore: hasMoreData, pageLimit: AppStringConstants.deploymentDevicesPerPageLimit);
    }
    start30SecTimer();
    getDifferenceTime();
    lastUpdateTime = DateTime.now();
    apiStatus = ApiStatus.success;
    isRefresh = false;
    firmwareController.update();
  }

  void start30SecTimer() {
    detailRefreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) async {
      await getFirmwareDetailData();
    });
  }
  getCurrantPageIndex() {
    paginatorController.addListener(() {
      currentPageIndex = (paginatorController.currentRowIndex / AppStringConstants.deploymentDevicesPerPageLimit).ceil();
      firmwareController.update();
    });
  }

  void initializeTimer() {
    isRefresh =true;
    detailRefreshTimer?.cancel();
    differenceTime = null;
    onTapTime = DateTime.now();
    isShowText = true;
    refreshTimer?.cancel();
  }

  getDifferenceTime() {
    differenceTime = DateTime.now().difference(onTapTime!);
    refreshTimer= Timer(const Duration(seconds: 2), () {
      isShowText = false;
      firmwareController.update();
    });
  }

  applyDeployment(String deploymentId) async {
    try {
      Map<String, dynamic> response = await firmwareController
          .applyDeployment(context: context, deploymentId: deploymentId);
      if (response.isNotEmpty) {
        if (response['success'] == true) {
          response['message'].toString().showSuccess(context);
          getFirmwareDetailData();
        } else {
          response['message'].toString().showError(context);
        }
      }
    } catch (e) {
      S.of(context).socketExceptionMessage.showError(context);
    }
  }

  Color getStatusFieldsColourCoding(String status,
      {bool inProgress = false}) {
    status = status.toLowerCase();
    if ((status.contains(AppStringConstants.ready) ||
        status.contains(AppStringConstants.progress) ||
        status.contains(AppStringConstants.waiting) ||
        status.contains(AppStringConstants.pendingLowerCase) ||
        status.contains(AppStringConstants.upgrading) ||
        status.contains(AppStringConstants.notStarted)) && inProgress) {
      return AppColorConstants.colorOrange;
    } else if((status.contains(AppStringConstants.completeLowerCase) ||
        status.contains(AppStringConstants.successLowerCase) ||
        status.contains(AppStringConstants.successfulLowerCase) ||
        status.contains(AppStringConstants.completed)) && !inProgress) {
      return AppColorConstants.colorSuccessGreen;
    } else if ((status.contains(AppStringConstants.failLowerCase) ||
        status.contains(AppStringConstants.error) ||
        status.contains(AppStringConstants.failed)) && inProgress) {
      return AppColorConstants.colorRedDark;
    } else {
      return AppColorConstants.colorBlackBlue;
    }
  }

  Widget getBodyView() {
    return ScreenLayoutTypeBuilder(
      builder: (context, screenType, constraints) {
        screenLayoutType = screenType;
        constraintsWidth=constraints.maxWidth;
        return getDetailView();
      },
    );
  }

  Widget getDetailView() {
    return SizedBox(
      width: double.infinity,
      child: Stack(
        children: [
          ListView(
            padding: const EdgeInsets.symmetric(horizontal: 15),
            physics: const ClampingScrollPhysics(),
            children: [
              deploymentInfoView(
                title: S.of(context).deploymentSummary,
                deployment: apiStatus != ApiStatus.success
                    ? Deployment.empty()
                    : deploymentDetail.deploymentDetailItem?.deployment ??
                        Deployment.empty(),
              ),
              const SizedBox(height: 20),
               getPageAppBar(),
              getFirmwareContent()
            ],
          ),
          apiStatus == ApiStatus.success &&
              firmwareDetailDataSource.apiStatus == ApiStatus.loading ?
          const Center(child: AppLoader()) : const SizedBox(),
        ],
      ),
    );
  }

  Widget deploymentInfoView({required String title,required Deployment deployment}) {
    return Container(
      height: apiStatus != ApiStatus.success ? getSize(250) : null,
      padding: EdgeInsets.all(getSize(20)).copyWith(top: 0,bottom: getSize(10)),
      decoration: infoViewDecoration,
      child: apiStatus != ApiStatus.success ? const Center(child: AppLoader())  :  Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 15,bottom: 8),
            child: Wrap(
              children: [
                AppText(
                  "$title : ",
                  style: TextStyle(
                      fontFamily: AppAssetsConstants.openSans,
                      fontWeight: getMediumBoldFontWeight(),
                      letterSpacing: 0.32,
                      color: AppColorConstants.colorLow,
                      fontSize: getSize(17)),
                ),
                AppText(
                  "${apiStatus != ApiStatus.success ? "" : deployment.id ?? ""}",
                  style: TextStyle(
                      color: AppColorConstants.colorLow,
                      fontFamily: AppAssetsConstants.openSans,
                      letterSpacing: 0.32,
                      fontWeight: getMediumBoldFontWeight(),
                      fontSize: getSize(17)),
                ),
              ],
            ),
          ),
          SizedBox(height: getSize(10)),
          (screenLayoutType != ScreenLayoutType.desktop) ? Column(
            children: [
              detailedInfoTable1(
                deployment: apiStatus != ApiStatus.success
                    ? Deployment.empty()
                    : deploymentDetail.deploymentDetailItem?.deployment ??
                    Deployment.empty(),

              ),
              SizedBox(height: getSize(20)),
              detailedInfoTable2(
                deployment: apiStatus != ApiStatus.success
                    ? Deployment.empty()
                    : deploymentDetail.deploymentDetailItem?.deployment ??
                    Deployment.empty(),
              )
            ],
          ) : Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: detailedInfoTable1(
                  deployment: apiStatus != ApiStatus.success
                      ? Deployment.empty()
                      : deploymentDetail.deploymentDetailItem?.deployment ??
                      Deployment.empty(),
                ),
              ),
              SizedBox(width: getSize(20)),
              Expanded(
                child: detailedInfoTable2(
                  deployment: apiStatus != ApiStatus.success
                      ? Deployment.empty()
                      : deploymentDetail.deploymentDetailItem?.deployment ??
                      Deployment.empty(),
                ),
              ),
            ],
          ),
          screenLayoutType != ScreenLayoutType.desktop  ? Column(children: [
            upgradeButtonView(deployment: deployment),
            SizedBox(height: getSize(5)),
            Row(mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                progressIndicatorView(deployment),
                if(screenLayoutType != ScreenLayoutType.mobile) Align(
                  alignment: Alignment.bottomRight,
                  child: remarkView(deployment: deployment),
                ),
              ],
            ),
            if(screenLayoutType == ScreenLayoutType.mobile)
              Align(
                alignment: Alignment.bottomLeft,
                child: remarkView(deployment: deployment),
              ),
          ],) : Stack(
            children: [
              Align(
                alignment: Alignment.bottomRight,
                child: Row(
                  children: [
                    progressIndicatorView(deployment),
                    const SizedBox(width: 10,),
                    upgradeButtonView(deployment: deployment),
                    Align(
                      alignment: Alignment.bottomLeft,
                      child: remarkView(deployment: deployment),
                    )
                  ],
                ),
              ),
            ],
          ),
          refreshButtonView(),
        ],
      ),
    );
  }

  Widget progressIndicatorView(Deployment deployment) {
    // if (deployment.summaryStatus == AppStringConstants.inProgress||
    //     deployment.summaryStatus == AppStringConstants.success) {
      int pctComplete = deployment.pctComplete ?? 0;
      String summaryStatus = deployment.summaryStatus ?? "";
      return Padding(
        padding: const EdgeInsets.only(top: 13, left: 4),
        child: Wrap(crossAxisAlignment: WrapCrossAlignment.center,
          direction: constraintsWidth < 400 ? Axis.vertical : Axis.horizontal,
          children: [
            AppText(
              "${S.of(context).status} : ",
              style: TextStyle(
                  color: AppColorConstants.colorBlackBlue,
                  fontSize: getSize(14),
                  fontFamily: AppAssetsConstants.openSans,
                  fontWeight: FontWeight.w600),
            ),
            CustomCardLinearPercentIndicator(
              width: screenLayoutType != ScreenLayoutType.desktop ? 200.0 : 250.0,
              lineHeight: 22,
              percent: pctComplete / 100,
              labelText: summaryStatus,
              progressColor: getProgressSummaryStatus(summaryStatus),
              backgroundColor: Colors.grey.shade300,
              labelStyle: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 13,
                color:AppColorConstants.colorBlack,
              ),
              barRadius: const Radius.circular(10),
              elevation: 5,
              borderRadius: const BorderRadius.all(Radius.circular(12)),
            ),
          ],
        ),
      );
   // }
    // return Container();
  }
  Color getProgressSummaryStatus(String? status) {
    switch (status) {
      case AppStringConstants.success:
        return AppColorConstants.colorGreenBright;
      case AppStringConstants.inProgress:
        return AppColorConstants.colorGreenBright;
      case AppStringConstants.readyForUpgrade:
        return AppColorConstants.colorOrange;
      case AppStringConstants.partialSuccess:
        return AppColorConstants.colorGreenBright;
      case AppStringConstants.fail:
        return AppColorConstants.colorRedLight.withOpacity(0.7);
      default:
        return AppColorConstants.colorRedLight
            .withOpacity(0.7); // or handle the default case accordingly
    }
  }
  Widget upgradeButtonView({required Deployment deployment}) {
    if (deployment.updateMode == AppStringConstants.manualLowerCase) {
      final bool isUpgradeable = apiStatus == ApiStatus.success &&
          deployment.pctComplete == 100 &&
          deployment.enableUpgrade &&
          !deployment.rebootSent;
      return Center(
        child: Padding(
          padding: const EdgeInsets.only(top: 15.0),
          child: SizedBox(
            width: 100,
            child: AppButton(
              buttonHeight: 31,
              loadingStatus: detailUpgradeApiStatus,
              buttonName: S.of(context).upgrade,
              fontColor: isUpgradeable ? AppColorConstants.colorWhite : AppColorConstants.colorGray,
              fontFamily: AppAssetsConstants.openSans,
              fontSize: 13,
              onPressed: isUpgradeable
                  ? () async {
                      detailUpgradeApiStatus = ApiStatus.loading;
                      firmwareController.update();
                      await applyDeployment(deployment.id);
                      detailUpgradeApiStatus = ApiStatus.success;
                      firmwareController.update();
                    }
                  : null,
              buttonStyle: ElevatedButton.styleFrom(
                shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.all(Radius.circular(8))),
                foregroundColor: AppColorConstants.colorWhite,
                backgroundColor: isUpgradeable ? AppColorConstants.colorPrimary : Colors.grey,
                padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
                shadowColor: AppColorConstants.colorGray.withOpacity(0.5),
              ),
            ),
          ),
        ),
      );
    }
    return Container();
  }

  Widget detailedInfoTable1({required Deployment deployment}) {
    return Container(
      padding: EdgeInsets.only(bottom: getSize(20),top: getSize(20)),
      decoration: BoxDecoration(
        color: AppColorConstants.colorWhite,
        border: Border.all(color: AppColorConstants.colorBlackBlue, width: 0.8),
        borderRadius: BorderRadius.circular(
          getSize(10),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          commonSubTitleView(
              subTitle: S.of(context).type, value: apiStatus != ApiStatus.success ? "" : deployment.firmwareFile?.productName ?? ""),
          commonSubTitleView(
              subTitle: S.of(context).fileName, value: apiStatus != ApiStatus.success ? "" : deployment.firmwareFile?.filename ?? "",isExpand: true),
          commonSubTitleView(
              subTitle: S.of(context).version, value: apiStatus != ApiStatus.success ? "" : deployment.firmwareFile?.version ?? ""),
        ],
      ),
    );
  }



  Widget detailedInfoTable2({required Deployment deployment}) {
    return Container(
      padding: EdgeInsets.only(bottom: getSize(20),top: getSize(20)),
      decoration: BoxDecoration(
        color: AppColorConstants.colorWhite,
        border: Border.all(color: AppColorConstants.colorBlackBlue, width: 0.8),
        borderRadius: BorderRadius.circular(
          getSize(10),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          commonSubTitleView(
              subTitle: S.of(context).created, value: apiStatus != ApiStatus.success ? "" : deployment.createdAt != null ? formatIsoDate("${deployment.createdAt ?? ""}") : ''),
          commonSubTitleView(
              subTitle: S.of(context).totalDeploymentTime, value: apiStatus != ApiStatus.success ? "" : deployment.durationSec != null
              ? formatToMinutesAndSeconds(deployment.durationSec)
              : ""),
          commonSubTitleView(
              subTitle: S.of(context).upgradeType,
              value: apiStatus != ApiStatus.success ? "" : capitalizeFirstLetter(deployment.updateMode ?? "")),

        ],
      ),
    );
  }
  String capitalizeFirstLetter(String value) {
    if (value.isEmpty) return value;
    return value[0].toUpperCase() + value.substring(1).toLowerCase();
  }

  remarkView({required Deployment deployment}) {
    if (deployment.summaryStatus == AppStringConstants.success ||
        deployment.summaryStatus == AppStringConstants.partialSuccess) {
      return Padding(
        padding: const EdgeInsets.only(top: 15,left: 5),
        child: Row(
         mainAxisSize:  MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            AppText(
              modifiedRemarkMessage(deployment.upgradedDeviceCount ?? 0,deployment.totalDeviceCount ?? 0),
              style: TextStyle(
                  textBaseline: TextBaseline.alphabetic,
                  color: deployment.summaryStatus == AppStringConstants.fail
                      ? AppColorConstants.colorRedDark
                      : AppColorConstants.colorBlackBlue,
                  fontSize: getSize(14),
                  fontFamily: AppAssetsConstants.openSans,
                  fontWeight: FontWeight.w600),
            ),
          ],
        ),
      );
    }
    return Container(height: 18,);
  }

  modifiedRemarkMessage(int upgradedDeviceCount, int totalDeviceCount) {
    if (upgradedDeviceCount == 1) {
      return "$upgradedDeviceCount of $totalDeviceCount ${S.of(context).deviceWasUpgraded}";
    } else if (upgradedDeviceCount > 1) {
      return "$upgradedDeviceCount of $totalDeviceCount ${S.of(context).devicesWereUpgraded}";
    }
    return "$upgradedDeviceCount of $totalDeviceCount ${S.of(context).devicesWereUpgraded}";
  }

  Widget refreshButtonView() {
    return Padding(
      padding: EdgeInsets.only(top: getSize(5), right: getSize(5)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Align(alignment: Alignment.centerRight, child: buildLastSeenView()),
          getRefreshButtonView(),
        ],
      ),
    );
  }

  Widget getRefreshButtonView() {
    return AppRefresh(
        onPressed: () {
          if (!isRefresh) {
            getFirmwareDetailData();
          }
        },
        loadingStatus: isRefresh ? ApiStatus.loading : ApiStatus.success);
  }

  Widget buildLastSeenView() {
    if (isShowText) {
      return getTimeDurationView(
        refreshStatus: isRefresh ? ApiStatus.loading : ApiStatus.success,
        updateTime: lastUpdateTime,
        onTapTime: onTapTime,
        difference: differenceTime,
      );
    } else {
      if (lastUpdateTime != null) {
        return getLastSeenView(lastUpdateTime);
      } else {
        return Container();
      }
    }
  }



  //getFirmTableView
  Widget getFirmwareContent() {
    if (apiStatus != ApiStatus.success) {
      return Container(
          decoration: BoxDecoration(border: Border.all(color: AppColorConstants.colorH2, width: 1)),
          height: 400,
          width: 300,
          child: const Align(alignment: Alignment.center, child: AppLoader()));
    }
    int itemsPerPage = dataTableHelper.getCurrentPageDataLength(
        firmwareDetailDataSource.deviseList, currentPageIndex,
        perPageLimit: AppStringConstants.deploymentDevicesPerPageLimit);
    int recordsInPage = firmwareDetailDataSource.deviseList.length >
            AppStringConstants.deploymentDevicesPerPageLimit
        ? itemsPerPage
        : firmwareDetailDataSource.deviseList.length;
    Widget table = getFirmTableView(firmwareDetailDataSource);
    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: Column(
        children: [
          Container(
            decoration: dataTableHelper.tableBorderDeco(),
            width: double.infinity,
            height: firmwareDetailDataSource.deviseList.isNotEmpty
                ? (recordsInPage * heightOfDataTableCell) + (recordsInPage * 0.1) + 250
                : (recordsInPage * heightOfDataTableCell) + (recordsInPage * 0.1) + 400,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                table,
                SizedBox(height: getSize(20)),
                // Divider
                Container(
                  height: 1,
                  width: double.infinity,
                  color: AppColorConstants.colorBlack12,
                ),
                // Action Buttons
                Container(
                  padding: const EdgeInsets.only(left: 16, top: 15, bottom: 15),
                  width: double.infinity,
                  decoration: BoxDecoration(
                      color: AppColorConstants.colorBackgroundDark,
                      borderRadius: const BorderRadius.only(
                          bottomRight: Radius.circular(5), bottomLeft: Radius.circular(5))),
                  child: customPaginationArrowView(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget getPageAppBar() {
    int totalDeviceCount = deploymentDetail.deploymentDetailItem?.deployment?.totalDeviceCount ?? 0;
    return Container(
      alignment: Alignment.centerLeft,
      decoration: BoxDecoration(
          border: Border.all(color: AppColorConstants.colorH2, width: 0.8),
          color: buildTableAppbarColor(),
          borderRadius: BorderRadius.only(
              topRight: Radius.circular(getSize(5)), topLeft: Radius.circular(getSize(5)))),
      padding: EdgeInsets.only(left: getSize(18), top: getSize(8)),
      child: Column(
        mainAxisAlignment:  MainAxisAlignment.end,
        children: [
          Row(
            children: [
              AppText(
                  "${deploymentDetail.deploymentDetailItem?.deployment?.totalDeviceCount ?? ""} ${(totalDeviceCount > 1) ? S.of(context).devices : S.of(context).device} ${S.of(context).status}",
                  style: TextStyle(
                      color: AppColorConstants.colorPrimary,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      fontFamily: AppAssetsConstants.openSans)),
              const Spacer(),
             if(screenLayoutType == ScreenLayoutType.desktop) Flexible(child: searchTextFieldView()),
              SizedBox(width: getSize(20)),
              MobileFirmwareDetail().selectTableTypeButtonView(widget.firmwarePageHelper),
            ],
          ),
          if (screenLayoutType != ScreenLayoutType.desktop)
            searchTextFieldView().paddingOnly(top: getSize(5), right: getSize(10)),
          SizedBox(height: getSize(5)),
        ],
      ),
    );
  }



  Widget searchTextFieldView() {
    return Container(
      height: 40,
      decoration: BoxDecoration(
          color: buildTextFiledColor(), borderRadius: BorderRadius.circular(getSize(8))),
      child: AppTextFormField(
        onChanged: (value) => firmwareDetailDataSource.quickSearch(value),
        focusedBorderColor: AppColorConstants.colorPrimary,
        controller: searchController,
        enabledBorderColor: AppColorConstants.colorWhite1,
        hintText: S.of(context).quickSearch,
        maxLines: 1,
        textInputType: TextInputType.emailAddress,
        validator: (value) {
          return null;
        },
        borderRadius: getSize(8),
        hintTextColor: AppColorConstants.colorDarkBlue,
        suffixIcon: const Padding(
          padding: EdgeInsets.all(12),
          child: AppImageAsset(image: AppAssetsConstants.searchIcon),
        ),
        hintFontSize: 17,
        fontFamily: AppAssetsConstants.openSans,
        fontWeight: FontWeight.w400,
      ),
    );
  }


  Widget getFirmTableView(DataTableSource dataSource) {
    if(!widget.firmwarePageHelper.isTableViewForFirmwareDetails){
      return MobileFirmwareDetail().buildFirmwareFileList(context, widget.firmwarePageHelper, firmwareDetailDataSource,depDevicesPaginationHelper);
    }
    return Expanded(
      child: PaginatedDataTable2(
        columnSpacing: 8,
        rowsPerPage: AppStringConstants.deploymentDevicesPerPageLimit,
        initialFirstRowIndex: depDevicesPaginationHelper.currentPage *
            AppStringConstants.deploymentDevicesPerPageLimit,
        controller: paginatorController,
        headingCheckboxTheme:
            CheckboxThemeData(side: BorderSide(width: 2, color: AppColorConstants.colorWhite)),
        headingTextStyle: dataTableHelper.headingTextStyle(),
        wrapInCard: false,
        datarowCheckboxTheme:
            const CheckboxThemeData(side: BorderSide(width: 2, color: Colors.grey)),
        border:dataTableHelper.tableBorder(),
        renderEmptyRowsInTheEnd: false,
        headingRowColor: dataTableHelper.headingRowColor(),
        source: dataSource,
        showCheckboxColumn: false,
        hidePaginator: true,
        minWidth: 700  ,
        columns: getDetailTableColumns(),
        empty: dataTableHelper.getEmptyTableContent(context),
      ),
    );
  }

  List<DataColumn> getDetailTableColumns() {
    return [
      DataColumn2(label: AppText(S.of(context).deviceEUI), fixedWidth: getSize(160)),
      DataColumn2(label: Center(child: AppText(S.of(context).currentVersion,textAlign: TextAlign.center)), fixedWidth: getSize(150)),
      DataColumn2(label: Center(child: AppText(S.of(context).status)), fixedWidth: getSize(300)),
      DataColumn2(label:SizedBox() ,size: ColumnSize.L),
      // DataColumn2(label: AppText(S.of(context).addedToMulticast), fixedWidth: getSize(100)),
      // DataColumn2(label: AppText(S.of(context).fragmentSessionSetup), size: ColumnSize.M),
    ];
  }
  customPaginationArrowView(){
    return  AppPaginationWidget(
      apiStatus:  apiStatus,
      paginationHelper: depDevicesPaginationHelper,
      onLoadNext: () async {
        await devicesLoadNextLogs(context);
        firmwareController.update();
      },
      onLoadPrevious: () async {
        await devicesLoadPreviousLogs(context);
        firmwareController.update();
      },
      onGoToFirstPage: () {
        depDevicesPaginationHelper.setPage(0);
        paginatorController.goToFirstPage();
        firmwareController.update();
      },
      onGoToLastPage: () {
        paginatorController.goToLastPage();
        firmwareController.update();
      },
      itemsPerPage: AppStringConstants.deploymentDevicesPerPageLimit,
      onChanged: (value) {
        AppStringConstants.deploymentDevicesPerPageLimit = int.parse(value);
        if (apiStatus != ApiStatus.loading) {
          depDevicesPaginationHelper.setPage(0);
            paginatorController.goToFirstPage();
          deploymentDevicesPageOffset = 0;
          getFirmwareDetailData();
        }
        firmwareController.update();
        firmwareDetailDataSource.notifyListeners();
      },
    );
  }
  Future<void> devicesLoadPreviousLogs(BuildContext context) async {
    if (depDevicesPaginationHelper.canGoToPreviousPage) {
      depDevicesPaginationHelper.setPage(depDevicesPaginationHelper.currentPage - 1);
      paginatorController.goToPreviousPage();
      firmwareController.update();
    }
  }

  Future<void> devicesLoadNextLogs(BuildContext context) async {
    if (depDevicesPaginationHelper.canGoToNextPage) {
      depDevicesPaginationHelper.setPage(depDevicesPaginationHelper.currentPage + 1);
      if (depDevicesPaginationHelper.currentPage >= depDevicesPaginationHelper.totalPages) {
        deploymentDevicesPageOffset = depDetailDeviseList.deviseList.length;
        await updateDeploymentDeviseDataOnNextPage();
      } else {
        paginatorController.goToNextPage();
        firmwareController.update();
      }
    }
  }

  updateDeploymentDeviseDataOnNextPage() async {
    apiStatus = ApiStatus.loading;
    firmwareController.update();
    DeploymentDetailDeviseList tempDepDeviseList = await firmwareController.getDeploymentDetailDeviseList(
        deploymentId: widget.deploymentID,
        context: context,
        pageOffset: deploymentDevicesPageOffset,
        perPageLimit: AppStringConstants.deploymentDevicesPerPageLimit);

    if (tempDepDeviseList.deviseList.isNotEmpty) {
      depDetailDeviseList.deviseList.addAll(tempDepDeviseList.deviseList);
      firmwareDetailDataSource = FirmwareDetailDataSource(
        this,
        deviseList: depDetailDeviseList.deviseList,
        firmwareController: firmwareController,
      );
      bool hasMoreData = tempDepDeviseList.deviseList.length == AppStringConstants.deploymentDevicesPerPageLimit;
      depDevicesPaginationHelper.updatePagination(depDetailDeviseList.deviseList.length,
          hasMore: hasMoreData, pageLimit: AppStringConstants.deploymentDevicesPerPageLimit);
      firmwareDetailDataSource.notifyListeners();
    } else {
      depDevicesPaginationHelper.setPage(depDevicesPaginationHelper.currentPage - 1);
      depDevicesPaginationHelper
          .updatePagination(depDetailDeviseList.deviseList.length, hasMore: false,pageLimit: AppStringConstants.deploymentPerPageLimit);
      Future.delayed(const Duration(milliseconds: 100)).then((value) {
        paginatorController.goToLastPage();
        firmwareDetailDataSource.notifyListeners();
      });
    }
    apiStatus = ApiStatus.success;
    firmwareController.update();
  }


  // Card Views
  Widget cardTitleView(String cardTitle) {
    return Align(
      alignment: Alignment.center,
      child: Padding(
        padding: EdgeInsets.only(left: getSize(20), right: getSize(10)),
        child: AppText(
          cardTitle,
          style: TextStyle(
              color: AppColorConstants.colorLightBlue,
              fontSize: getSize(20),
              fontWeight: getMediumBoldFontWeight(),
              fontFamily: AppAssetsConstants.openSans),
        ),
      ),
    );
  }

  Widget cardSubTitleView(String cardSubTitle, String value) {
    return Padding(
      padding: const EdgeInsets.only(right: 8, bottom: 8),
      child: Row(
        children: [
          AppText(
            cardSubTitle,
            style: TextStyle(
              overflow: TextOverflow.ellipsis,
              color: AppColorConstants.colorBlackBlue,
              fontSize: getSize(14),
              fontWeight: getMediumFontWeight(),
              fontFamily: AppAssetsConstants.openSans,
            ),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: DottedLine(color: AppColorConstants.colorDotLine),
            ),
          ),
          AppText(
            value,
            style: TextStyle(
              color: AppColorConstants.colorBlackBlue,
              fontSize: getSize(14),
              fontWeight: getMediumFontWeight(),
              fontFamily: AppAssetsConstants.openSans,
            ),
          ),
        ],
      ),
    );
  }

  Widget commonTitleView(
      {required String title, double? width = double.infinity}) {
    return Container(
      alignment: Alignment.center,
      height: getSize(50),
      width: width,
      padding: EdgeInsets.only(top: getSize(13), left: getSize(20)),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(getSize(7)),
          topLeft: Radius.circular(getSize(7)),
        ),
      ),
      child: AppText(
        title,
        style: TextStyle(
            fontFamily: AppAssetsConstants.openSans,
            fontWeight: getMediumBoldFontWeight(),
            letterSpacing: 0.32,
            color: AppColorConstants.colorLightBlue,
            fontSize: getSize(18)),
      ),
    );
  }

  Widget commonSubTitleView(
      {required String subTitle,
      required dynamic value,
      bool isPadding = false,
      Color? fontColor,
      Color? dotLineColor,
      bool isTooltip = false,
      String? tooltipMessage,
      bool isLoading = false , bool isExpand=false}) {
    Widget textWidget = AppText(
      "$value",
      style: TextStyle(
        fontFamily: AppAssetsConstants.openSans,
        fontWeight: FontWeight.w500,
        color: fontColor ?? AppColorConstants.colorBlackBlue,
        fontSize: getSize(14),
      ),
    );
    return Padding(
        padding: EdgeInsets.only(
            left: getSize(25),
            right: getSize(20),
            top: isPadding ? getSize(10) :getSize(4),
            bottom: getSize(10)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            AppText(
              subTitle,
              style: TextStyle(
                  color: fontColor ?? AppColorConstants.colorBlackBlue,
                  fontFamily: AppAssetsConstants.openSans,
                  fontWeight: FontWeight.w500,
                  fontSize: getSize(14)),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: DottedLine(color: dotLineColor ?? AppColorConstants.colorDotLine),
              ),
            ),
            if (isTooltip)
              Tooltip(
                verticalOffset: -40,
                textStyle: TextStyle(
                  fontFamily: AppAssetsConstants.openSans,
                  fontWeight: FontWeight.w500,
                  fontSize: getSize(14),
                  color: AppColorConstants.colorBlackBlue,
                ),
                message: "\u{26A0} $tooltipMessage",
                decoration: BoxDecoration(
                  color: AppColorConstants.colorCriticalLite,
                  border: Border.all(
                      color: AppColorConstants.colorRedAccent, width: 0.8),
                  borderRadius: BorderRadius.circular(8),
                ),
                height: 30,
                child: Row(
                  children: [
                    Icon(Icons.error_outline_rounded,color: AppColorConstants.colorRedAccent,size: 15),
                    const SizedBox(width: 2),
                    textWidget,
                  ],
                ),
              )
            else ...[
              if (screenLayoutType == ScreenLayoutType.mobile && isExpand)
                Flexible(child: Align(alignment: Alignment.centerRight,child: textWidget))
              else
                textWidget
            ],
            if (isLoading)
              Container(
                height: 22,
                width: 22,
                margin: const EdgeInsets.only(left: 3),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    const CircularProgressIndicator(
                      strokeWidth: 2.3,
                    ),
                    Icon(
                      subTitle == S.of(context).upgradeStatus ? Icons.upload : Icons.download,
                      color: AppColorConstants.colorPrimary,
                      size: 18,
                    )
                  ],
                ),
              ),
          ],
        ));
  }
}
