import 'package:quantumlink_node/app_import.dart';

import 'firmware_detail_datasource.dart';

class MobileFirmwareDetail {
  TextStyle textStyle = TextStyle(
      fontFamily: AppAssetsConstants.roboto,
      fontSize: getSize(14),
      fontWeight: getMediumFontWeight());
  Widget buildFirmwareFileList(BuildContext context, FirmwarePageHelper firmwarePageHelper,
      FirmwareDetailDataSource firmwareDetailDataSource,PaginationHelper devicePaginationHelper) {
    if (firmwareDetailDataSource.rowCount == 0) {
      return SizedBox(
        height: 120,
        child: firmwarePageHelper.dataTableHelper.getEmptyTableContent(context),
      );
    }
    List<DevicesDetail> fullList = firmwareDetailDataSource.deviseList;
    List<DevicesDetail> paginatedList = fullList
        .skip( devicePaginationHelper.currentPage*
        AppStringConstants.deploymentDevicesPerPageLimit)
        .take(AppStringConstants.deploymentDevicesPerPageLimit)
        .toList();
    return ListView.builder(
      shrinkWrap: true,
      itemCount: paginatedList.length,
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        DevicesDetail devicesDetail = paginatedList[index];

        String devEui = devicesDetail.deviceEui ?? "";

        String summaryStatus = devicesDetail.summaryStatus ?? "";
        String currentVersion   = devicesDetail.currentVersion ?? "";
        String upgradeFailRemarks = devicesDetail.remarks ?? "";
        List <ProgressDot> progressDotsList = devicesDetail.progressDots ?? [];
        bool inProgress = !(progressDotsList.every((dot) => dot.status.toLowerCase() == AppStringConstants.successLowerCase));
        String status = summaryStatus.toLowerCase();
        bool isCheckedVersion = !inProgress &&
            (status.contains(AppStringConstants.completeLowerCase) ||
                status.contains(AppStringConstants.successLowerCase) ||
                status.contains(AppStringConstants.successfulLowerCase) ||
                status.contains(AppStringConstants.completed));
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 3, horizontal: 5),
          child: CustomListTile(
            onTap: () {},
            index: index,
            titleWidget: Column(
              children: [
                Row(
                  children: [
                    AppText(devEui,style: textStyle,),
                    SizedBox(width: getSize(10)),
                    const Spacer(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: progressDotsList.map((dot) {
                        return firmwareDetailDataSource.isDoneCircleWidget(
                            dot.status ?? "", dot.tooltip ?? "");
                      }).toList(),
                    )
                  ],
                ),
                Row(
                  children: [
                    AppText(currentVersion,style: TextStyle(
                            color:  isCheckedVersion ? AppColorConstants.colorSuccessGreen : AppColorConstants.colorBlack,
                            fontFamily: AppAssetsConstants.roboto,
                        fontSize: getSize(14),
                        fontWeight: getMediumFontWeight())),
                    SizedBox(width: getSize(10)),
                 //   const Spacer(),
                    summaryStatus.toLowerCase().contains(AppStringConstants.fail) && upgradeFailRemarks.isNotEmpty
                        ? firmwareDetailDataSource.tooltipWidget(tooltipMessage: upgradeFailRemarks, text: summaryStatus)
                        : Flexible(child: Align(alignment: Alignment.centerRight,
                          child: AppText(summaryStatus,
                          style: TextStyle(
                              color: firmwareDetailDataSource.state.getStatusFieldsColourCoding(summaryStatus,inProgress: inProgress),
                              fontFamily: AppAssetsConstants.roboto,
                              fontSize: getSize(14),
                              fontWeight: getMediumFontWeight())),
                        ))
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget selectTableTypeButtonView(FirmwarePageHelper? firmwarePageHelper) {
    return firmwarePageHelper?.dataTableHelper.selectTableTypeButtonView(
      isTableType: firmwarePageHelper.isTableViewForFirmwareDetails,
      onPressed: () {
        firmwarePageHelper.isTableViewForFirmwareDetails = !firmwarePageHelper.isTableViewForFirmwareDetails;
        firmwarePageHelper.state.firmwareController.update();
      },
    );
  }
}
