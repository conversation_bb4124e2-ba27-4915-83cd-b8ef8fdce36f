import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/pages/firmware_management/mobile_firmware.dart';

class FirmwarePage extends StatefulWidget {
  const FirmwarePage({super.key});

  @override
  State<StatefulWidget> createState() => FirmwarePageState();
}

class FirmwarePageState extends State<FirmwarePage> with TickerProviderStateMixin {
  FirmwarePageHelper? firmwarePageHelper;
  FirmwareController firmwareController = FirmwareController();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    firmwarePageHelper ?? (firmwarePageHelper = FirmwarePageHelper(this));
    firmwarePageHelper!.initializeFirmTabsList();
    return GetBuilder<FirmwareController>(
      init: FirmwareController(),
      builder: (FirmwareController controller) {
        firmwareController = controller;
        return getBody();
      },
    );
  }

  Widget getBody() {
    return getFirmwareView();
  }

  Widget getFirmwareView() {
    return ScreenLayoutTypeBuilder(
      builder: (context, screenType, constraints) {
        firmwarePageHelper!.screenLayoutType = screenType;
        MobileFirmware().autoSelectTableType(firmwarePageHelper);
        return ClipRRect(
          borderRadius: const BorderRadius.all(Radius.circular(5)),
          child: Scrollbar(
            thumbVisibility: true,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: NestedScrollView(
                      headerSliverBuilder: (context, innerBoxIsScrolled) => [
                            SliverToBoxAdapter(
                                child: Padding(
                                    padding: const EdgeInsets.symmetric(horizontal: 10),
                                    child: getPageTitleView(S.of(context).firmwareDownload))),
                            SliverToBoxAdapter(child: SizedBox(height: getSize(10))),
                            SliverToBoxAdapter(child: getTabsOnFirmHeader()),
                          ],
                      body: getTabsContent()),
                )
              ],
            ),
          ),
        );
      },
    );
  }

  getTabsContent() {
    return TabBarView(
      physics: const NeverScrollableScrollPhysics(),
      controller: firmwarePageHelper!.tabController,
      children: List.generate(firmwarePageHelper!.firmListTabs.length, (index) {
        return (firmwarePageHelper!.firmListTabs[index].title == S.of(context).firmwareFiles)
            ? getFirmwareContent()
            : (firmwarePageHelper!.firmListTabs[index].title == S.of(context).deployments)
                ? DeploymentsPage(firmwarePageHelper: firmwarePageHelper!)
                : FirmwareManagementDetailPage(firmwarePageHelper: firmwarePageHelper!,deploymentID:firmwarePageHelper!.firmListTabs[index].title,deployment: firmwarePageHelper!.firmListTabs[index].deployment!,);
      }),
    );
  }

  getTabsOnFirmHeader() {
    return SelectionArea(
      child: TabBar(
          controller: firmwarePageHelper!.tabController,
          dividerColor: AppColorConstants.colorWhite,
          labelPadding: EdgeInsets.zero,
          labelColor: Colors.white,
          padding: EdgeInsets.symmetric(horizontal: getSize(15)),
          indicatorColor: AppColorConstants.colorWhite,
          isScrollable: true,
          // overlayColor: MaterialStateProperty.all(Colors.transparent),
          // indicator: BoxDecoration(
          //   color: AppColorConstants.colorLightBlue1.withOpacity(0.7),
          // ),
          tabAlignment: TabAlignment.start,
          onTap: (value) => firmwarePageHelper!.tabFirmHeaderOnTap(value),
          tabs: List.generate(firmwarePageHelper!.firmListTabs.length, (index) {
            FirmTabItem firmTabItem = firmwarePageHelper!.firmListTabs[index];
            return MouseRegion(
              onEnter: (event) {
                firmwarePageHelper!.isHovered[index] = true;
                firmwareController.update();
              },
              onExit: (event) {
                firmwarePageHelper!.isHovered[index] = false;
                firmwareController.update();
              },
              child: Tab(
                // height: getSize(40),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 5),
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: getSize(25)),
                    height: getSize(42),
                    alignment: Alignment.center,
                    decoration: firmTabItem.getDeco(firmwarePageHelper!.isHovered[index]),
                    child: Row(
                      children: [
                        AppText(
                          isSelectableText: false,
                          firmwarePageHelper!.firmListTabs[index].title ==
                                      S.of(context).firmwareFiles ||
                                  firmwarePageHelper!.firmListTabs[index].title ==
                                      S.of(context).deployments
                              ? firmTabItem.title
                              : "..${getLastNChars(firmTabItem.title, 5)}",
                          style: TextStyle(
                              fontSize: getSize(16),
                              fontFamily: AppAssetsConstants.poppins,
                              fontWeight: FontWeight.w600,
                              color:
                                  firmwarePageHelper!.isHovered[index] && !firmTabItem.isCurrentOpen
                                      ? AppColorConstants.colorBlackBlue
                                      : firmTabItem.isCurrentOpen
                                          ? AppColorConstants.colorLightBlue
                                          : AppColorConstants.colorH2),
                        ),
                        if ( firmwarePageHelper!.firmListTabs[index].title !=
                            S.of(context).firmwareFiles &&
                            firmwarePageHelper!.firmListTabs[index].title !=
                                S.of(context).deployments) ...[
                          SizedBox(width: getSize(10)),
                          GestureDetector(
                              onTap: () =>  firmwarePageHelper!.removeFMDetailTab(index),
                              child: CircleAvatar(
                                  maxRadius: 10,
                                  backgroundColor: firmTabItem.isCurrentOpen
                                      ? AppColorConstants.colorLightBlue
                                      : AppColorConstants.colorH2.withOpacity(0.3),
                                  child: Icon(Icons.close,
                                      size: getSize(16),
                                      color: firmTabItem.isCurrentOpen
                                          ? AppColorConstants.colorWhite
                                          : AppColorConstants.colorH3)))
                        ]
                      ],
                    ),
                  ),
                ),
              ),
            );
          })),
    );
  }

  Widget getPageAppBar() {
    return Container(
      alignment: Alignment.centerLeft,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
              topRight: Radius.circular(getSize(7)), topLeft: Radius.circular(getSize(7))),
          border: Border.all(
            color: AppColorConstants.colorH2,
          ),
          color: buildTableAppbarColor(),),
      padding: const EdgeInsets.all(10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [MobileFirmware().selectTableTypeButtonView(firmwarePageHelper)],
      ),
    );
  }

  Widget getFirmwareContent() {
    if (firmwarePageHelper!.apiStatus == ApiStatus.loading) {
      return ListView(
        padding: const EdgeInsets.symmetric(horizontal: 15),
        physics: const ClampingScrollPhysics(),
        children: [
          ClipRRect(
            borderRadius: const BorderRadius.only(topRight: Radius.circular(8), topLeft: Radius.circular(8)),
            child: Container(
              decoration: BoxDecoration(
                color: AppColorConstants.colorWhite,
                borderRadius: const BorderRadius.only(topRight: Radius.circular(8), topLeft: Radius.circular(8)),
                border: Border.all(color: AppColorConstants.colorH2, width: 1),
              ),
              height: 400,
              child: const Align(alignment: Alignment.center, child: AppLoader()),
            ),
          ),
        ],
      );
    }

    int itemsPerPage = firmwarePageHelper!.dataTableHelper.getCurrentPageDataLength(
        firmwarePageHelper!.firmDataSource.firmwareList, firmwarePageHelper!.fmCurrentPageIndex,perPageLimit:  AppStringConstants.firmwarePerPageLimit);
    firmwarePageHelper!.recordsInPage =  (firmwarePageHelper!.firmDataSource.firmwareList.length > AppStringConstants.firmwarePerPageLimit
            ? itemsPerPage
            : firmwarePageHelper!.firmDataSource.firmwareList.length);
    // Widget table = getFirmTableView(
    //     firmwarePageHelper!.firmDataSource);
    return ListView(
      padding: const EdgeInsets.only(left: 15,right: 15),
      physics: const ClampingScrollPhysics(),
      children: [
        getPageAppBar(),
        Container(
          decoration: firmwarePageHelper!.dataTableHelper.tableBorderDeco(),
          child: Column(
            children: [
              getFirmwareBoardView(firmwarePageHelper!.firmDataSource),
              Container(
                height: 1,
                width: double.infinity,
                color: AppColorConstants.colorBlack12,
              ),
              // Action Buttons
              Container(
                padding: const EdgeInsets.only(left: 16, top: 15),
                width: double.infinity,
                decoration: BoxDecoration(
                  color: AppColorConstants.colorBackgroundDark,
                  borderRadius: const BorderRadius.only(bottomRight: Radius.circular(5), bottomLeft: Radius.circular(5)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Wrap(
                          direction:
                          firmwarePageHelper?.screenLayoutType == ScreenLayoutType.mobile
                              ? Axis.vertical
                              : Axis.horizontal,
                          children: [
                            getUploadButtonView(),
                            SizedBox(width: getSize(5), height: getSize(8)),
                            getDeleteButtonView(),
                          ],
                        ),
                        // Add Refresh button
                        /*customPaginationArrowView(),
                            refreshButtonView(),*/
                        if (firmwarePageHelper!.screenLayoutType != ScreenLayoutType.mobile)
                          Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              customPaginationArrowView(),
                              refreshButtonView(),
                            ],
                          ),
                      ],
                    ),
                    if (firmwarePageHelper!.screenLayoutType == ScreenLayoutType.mobile)
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          customPaginationArrowView(),
                          refreshButtonView(),
                        ],
                      )
                  ],
                ),
              ),

              (firmwarePageHelper!.firmwareDatatableError != null)
                  ? CommonAPIErrorView(rightPadding: 2,
                  errorMessage:firmwarePageHelper!.firmwareDatatableError ?? "")
                  : SizedBox(height: getSize(40))
            ],
          ),
        ),
        const SizedBox(height: 20)

        // ClipRRect(
        //   borderRadius: const BorderRadius.only(topRight: Radius.circular(8), topLeft: Radius.circular(8)),
        //   child: Container(
        //     decoration: firmwarePageHelper!.dataTableHelper.tableBorderDeco(),
        //     child: Column(
        //       mainAxisSize: MainAxisSize.min,
        //       crossAxisAlignment: CrossAxisAlignment.start,
        //       children: [
        //         table,
                // Divider

      ],
    );
  }
 Widget getFirmwareBoardView(DataTableSource dataSource){
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(color: AppColorConstants.colorWhite),
      child: getFirmTableView(dataSource),
    );
 }


  Widget getFirmTableView(DataTableSource dataSource) {
    if (!firmwarePageHelper!.isTableView) {
      return MobileFirmware().buildFirmwareFileList(context, firmwarePageHelper!);
    }
    return SizedBox(
      width: double.infinity,
      height: (firmwarePageHelper!.firmDataSource.firmwareList.isNotEmpty)
          ? (firmwarePageHelper!.recordsInPage * firmwarePageHelper!.fmHeightOfDataTableCell) +
          (firmwarePageHelper!.recordsInPage * 0.1) + 220
          : (firmwarePageHelper!.recordsInPage * firmwarePageHelper!.fmHeightOfDataTableCell) +
          (firmwarePageHelper!.recordsInPage * 0.1) +
          400,
      child: PaginatedDataTable2(
        columnSpacing: 5,
        rowsPerPage: AppStringConstants.firmwarePerPageLimit,
        initialFirstRowIndex: firmwarePageHelper!.fmPaginationHelper.currentPage *
            AppStringConstants.firmwarePerPageLimit,
        controller: firmwarePageHelper!.fmPageController,
        headingTextStyle: firmwarePageHelper!.dataTableHelper.headingTextStyle(),
        wrapInCard: false,
        border: firmwarePageHelper!.dataTableHelper.tableBorder(),
        renderEmptyRowsInTheEnd: false,
        // ignore: deprecated_member_use
        headingRowColor: firmwarePageHelper!.dataTableHelper.headingRowColor(),
        source: dataSource,
        hidePaginator: true,
        showCheckboxColumn: true,
        onSelectAll: (value) => false,
        minWidth: 1360,
        columns: getCurrentFirmTableColumns(),
        empty:firmwarePageHelper!.dataTableHelper.getEmptyTableContent(context),
      ),
    );
  }

  List<DataColumn> getCurrentFirmTableColumns() {
    return [
      DataColumn2(label: Center(child: AppText(S.of(context).select)),fixedWidth: 60),
      DataColumn2(label: Center(child: AppText(S.of(context).createDeployment,textAlign: TextAlign.center)),fixedWidth: 130),
      DataColumn2(label: Center(child: AppText(S.of(context).fileName)),fixedWidth: 250),
      DataColumn2(label: Center(child: AppText(S.of(context).type)),fixedWidth: 220),
      DataColumn2(label: Center(child: AppText(S.of(context).version)),fixedWidth: 130),
      DataColumn2(label: Center(child: AppText(S.of(context).size)),fixedWidth: 130),
      DataColumn2(label: Center(child: AppText(S.of(context).uploadAt)),fixedWidth: 200),
      DataColumn2(label: AppText(S.of(context).associateDeployments,textAlign: TextAlign.center),fixedWidth: 130),
      DataColumn2(label: AppText(S.of(context).deleteBtn)),
    ];
  }


  Widget getUploadButtonView() {
    return AppButton(
      buttonHeight: 33,
      buttonWidth: 100,
      fontSize: 14.5,
      buttonRadius: 9,
      buttonName: S.of(context).uploadNew,
      onPressed: () {
        uploadFirmwareView(context, firmwareController,firmwarePageHelper!);
      },
      fontFamily: AppAssetsConstants.openSans,
    );
  }
  Widget getDeleteButtonView() {
    return AppButton(
      loadingStatus:firmwarePageHelper!.isDeleteAllLoading ? ApiStatus.loading : ApiStatus.success,
      buttonHeight: 33,
      buttonWidth: 120,
      fontSize: 14.5,
      buttonRadius: 9,
      fontColor:
      firmwarePageHelper!.firmDataSource.selectedRowCount >
          0
          ? AppColorConstants.colorWhite
          : AppColorConstants.colorH1Grey,
      borderColor:
      firmwarePageHelper!.firmDataSource.selectedRowCount >
          0
          ? AppColorConstants.colorRedAccent
          : AppColorConstants.colorH2.withOpacity(0.5),
      buttonName: S.of(context).deleteSelected,
      onPressed: () {
        List<FirmwareData> firmwareIds = firmwarePageHelper?.firmDataSource.firmwareList
            .where((element) => (element.selected ?? false == true))
            .toList() ?? [];
        if (firmwareIds.isNotEmpty) {
          List<String> firmwareIdsList = firmwareIds.map((e) => e.firmwareId.toString()).toList();
          deleteFirmwareIdModalView(context, firmwareIdsList);
        }
      },
      fontFamily: AppAssetsConstants.openSans,
      buttonColor: firmwarePageHelper!.firmDataSource.selectedRowCount >  0
          ? AppColorConstants.colorRed :AppColorConstants.colorWhite,
    );
  }

  deleteFirmwareIdModalView(c, List<String> list) {
    return showDialog(
      context: c,
      builder: (context) {
        return AlertDialog(
          surfaceTintColor: AppColorConstants.colorWhite,
          backgroundColor: AppColorConstants.colorWhite,
          insetPadding: const EdgeInsets.symmetric(horizontal: 8.0),
          contentPadding: EdgeInsets.zero,
          titlePadding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
          title: getCustomAppBarWithClose("${S.of(context).deleteBtn} ?"),
          content: StatefulBuilder(builder: (context, snapshot) {
            return Container(
              width: 450, // Adjust the width as needed
              height: MediaQuery.of(context).size.height * 0.3,
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Expanded(
                    child: ListView.builder(
                      shrinkWrap: true,
                      // Allow the ListView to shrink-wrap its contents
                      itemCount: list.length,
                      itemBuilder: (context, index) {
                      String firmwareId = list[index].toString();
                        return ListTile(
                          title: AppText(
                            ' ${index + 1}. $firmwareId ?',
                            style: const TextStyle(fontSize: 15,
                              fontFamily: AppAssetsConstants.openSans,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  getAppDivider(),
                ],
              ),
            );
          }),
          actions: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                AppButton(
                  borderColor: Colors.grey.withOpacity(0.5),
                  buttonHeight: 20,
                  buttonColor: Colors.grey.withOpacity(0.5),
                  buttonName: S.of(context).no,
                  fontFamily: AppAssetsConstants.openSans,
                  onPressed: () {
                    goBack();
                  },
                ),
                const SizedBox(width: 16),
                AppButton(
                  fontFamily: AppAssetsConstants.openSans,
                  buttonHeight: 20,
                  buttonName: S.of(context).yes,
                  onPressed: () {
                    goBack();
                    firmwarePageHelper!.deleteMultipleFirmware(list);
                  },
                ),
              ],
            )
          ],
        );
      },
    );
  }

  skipCreateDeploymentDeviceListDialogView(List<SkippedDevice> skippedDevices, int totalDevices) {
    return showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          surfaceTintColor: AppColorConstants.colorWhite,
          backgroundColor: AppColorConstants.colorWhite,
          insetPadding: const EdgeInsets.symmetric(horizontal: 8.0),
          contentPadding: EdgeInsets.zero,
          titlePadding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
          title:  Column(
            children: [
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 10),
                child: AppImageAsset(image: AppAssetsConstants.alertTriangleIcon, fit: BoxFit.fitWidth, width: 50),
              ),
              AppText(
                'These ${skippedDevices.length} of $totalDevices devices were not processed \ndue to the following reason:-',
                style:  TextStyle(
                  fontSize: 16,
                  fontFamily: AppAssetsConstants.openSans,
                  color: AppColorConstants.colorPrimary,
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 2,  // Ensure text doesn't exceed two lines
                textAlign: TextAlign.center, // Center align text if needed
              ),
              SizedBox(height: 10),
            ],
          ),
          content: StatefulBuilder(builder: (context, snapshot) {
            double height = skippedDevices.length > 2 ? 0.3 : 0.2;
            return Container(
              width: 450, // Adjust the width as needed
              height: MediaQuery.of(context).size.height * height,
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Expanded(
                    child: ListView.builder(
                      shrinkWrap: true,
                      // Allow the ListView to shrink-wrap its contents
                      itemCount: skippedDevices.length,
                      itemBuilder: (context, index) {
                        SkippedDevice skippedDevice = skippedDevices[index];
                        String deviceEui = skippedDevice.deviceEui.toString();
                        return ListTile(minVerticalPadding: 9,
                          title: RichText(
                            text: TextSpan(
                              style: const TextStyle(
                                fontSize: 15,
                                fontFamily: AppAssetsConstants.openSans,
                                fontWeight: FontWeight.w600,
                                color: Colors.black, // Ensure text is visible
                              ),
                              children: [
                                TextSpan(
                                  text: '${index + 1}.', // Device ID on the first line
                                ),
                                TextSpan(
                                  text: ' $deviceEui - ', // Device ID on the first line
                                ),
                                TextSpan(
                                  text: skippedDevice.message.toString(), // Message continues
                                  style: const TextStyle(fontWeight: FontWeight.w400,fontSize: 14),
                                ),
                              ],
                            ),
                          ),
                        );

                      },
                    ),
                  ),
                  getAppDivider(thickness:3),
                ],
              ),
            );
          }),
          actions: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                AppButton(fontSize: 14,
                  fontFamily: AppAssetsConstants.openSans,
                  buttonHeight: 25,
                  buttonName: S.of(context).close,
                  onPressed: () {
                    goBack();
                  },
                ),
              ],
            )
          ],
        );
      },
    );
  }

  deviceListDialog(c, FirmwareData ? firmware , Function callback) {
    ApiStatus apiStatus = ApiStatus.initial;
    List<FirmwareData> ? firmwareDataList = firmwarePageHelper!.firmwareFilesList.result.dataList;
    String ? selectedFirmwareFile;
    final ScrollController _scrollController = ScrollController();
    bool isUpdateMode =true;
    return showDialog(
      barrierDismissible: false,
      context: c,
      builder: (context) {
        return GetBuilder<FirmwareController>(
          init: FirmwareController(),
          initState: (state) async {
            if(firmware == null){
              WidgetsBinding.instance.addPostFrameCallback((_) async {
                await firmwarePageHelper!.getFirmwareFileList();
              });
            }else{
             selectedFirmwareFile = firmware?.filename;
            firmwarePageHelper!.searchController.clear();
            firmwarePageHelper!.selectedDevices = [];
            apiStatus = ApiStatus.loading;
            await firmwarePageHelper!.getAmplifierDeviceData(firmware?.firmwareId);
            apiStatus = ApiStatus.success;
            firmwareController.update();
            _scrollController.addListener(() async {
              if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent &&
                  !firmwarePageHelper!.isLoadingMore) {
                if (firmwarePageHelper!.hasMoreData && firmwarePageHelper!.searchFirmwareDevicesList.result.devices.isEmpty) {
                  firmwarePageHelper!.ampFWPageOffset += AppStringConstants.ampFWPrePageLimit;
                  await firmwarePageHelper!.fetchNextPage(firmware?.firmwareId);
                }
              }
            });
            }
          },
          builder: (FirmwareController controller) {
            return StatefulBuilder(
              builder: (context, snapshot) {
                return AlertDialog(
                  surfaceTintColor: AppColorConstants.colorWhite,
                  backgroundColor: AppColorConstants.colorWhite,
                  insetPadding: EdgeInsets.symmetric(horizontal: getSize(10)),
                  contentPadding: EdgeInsets.zero,
                  titlePadding: EdgeInsets.zero,
                  actionsPadding: EdgeInsets.zero,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
                  title: Container(
                    color: AppColorConstants.colorPrimaryLime,
                    child: Padding(
                      padding: const EdgeInsets.only(left: 20, top: 20, right: 20, bottom: 15),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const AppImageAsset(width: 40, image: AppAssetsConstants.addListIcon),
                              const SizedBox(width: 10),
                              Expanded(flex: 2,
                                child: CommonDropdownButton(
                                  iconColor: AppColorConstants.colorH3,
                                  selectedValue: selectedFirmwareFile,
                                  fontSize: getSize(14),
                                  menuHeight: 50,
                                    buttonHeight: selectedFirmwareFile != null &&
                                            selectedFirmwareFile!.length > 25 &&
                                            firmwarePageHelper!.screenLayoutType ==
                                                ScreenLayoutType.mobile
                                        ? getSize(55)
                                        : getSize(40),
                                    hintText: S.of(context).select,
                                    items: firmwareDataList.isNotEmpty
                                        ? firmwarePageHelper!.firmwareFilesList.result.dataList
                                            .map((e) => e.filename ?? "")
                                            .toList()
                                        : [] ,
                                  onChanged:  AppConfig.shared.isOpenFromBLE ? (value) async {
                                      selectedFirmwareFile = value;
                                      firmware = firmwareDataList.firstWhere(
                                        (e) => e.filename == value,
                                      );
                                      if (value != null) {
                                      firmwarePageHelper!.searchController.clear();
                                      firmwarePageHelper!.selectedDevices = [];
                                      apiStatus = ApiStatus.loading;
                                      snapshot(() {});
                                      await firmwarePageHelper!
                                          .getAmplifierDeviceData(firmware!.firmwareId);
                                      apiStatus = ApiStatus.success;
                                      snapshot(() {});
                                      _scrollController.addListener(() async {
                                        if (_scrollController.position.pixels ==
                                            _scrollController.position.maxScrollExtent &&
                                            !firmwarePageHelper!.isLoadingMore) {
                                          if (firmwarePageHelper!.hasMoreData &&
                                              firmwarePageHelper!.searchFirmwareDevicesList
                                                  .result.devices.isEmpty) {
                                            firmwarePageHelper!.ampFWPageOffset +=
                                                AppStringConstants.ampFWPrePageLimit;
                                            await firmwarePageHelper!
                                                .fetchNextPage(firmware?.firmwareId);
                                          }
                                        }
                                      });
                                    }
                                  }:null ,
                                ),
                              ),
                              SizedBox(
                                height: 35,
                                width: 30,
                                child: firmwarePageHelper!.apiStatus == ApiStatus.loading
                                    ? const AppLoader()
                                    : const SizedBox(),
                              ),
                              const SizedBox(width: 10),
                             if(firmwarePageHelper!.isTableForAddDialog) Expanded(
                                    child: _buildFirmwareTypeAndVersionText(firmware?.productName, firmware?.version),
                              ),
                            ],
                          ),
                          if(!firmwarePageHelper!.isTableForAddDialog) Padding(
                            padding: const EdgeInsets.only(left: 52),
                            child: _buildFirmwareTypeAndVersionText(firmware?.productName, firmware?.version),
                          ),
                        ],
                      )
                    ),
                  ),
                  content: Padding(
                    padding: EdgeInsets.zero,
                    child: SizedBox(
                      width: 750,
                      height: 400,
                      child: firmware == null ?  Align(alignment: Alignment.center, child:
                          AppText(S.of(c).noFirmwareSelected)) : apiStatus == ApiStatus.loading
                          ? const Align(alignment: Alignment.center, child: AppLoader())
                          : ListView(
                        controller: _scrollController,
                        padding: const EdgeInsets.only(left: 20, top: 20, right: 20),

                        shrinkWrap: true,
                              children: [
                                Row(
                                  children: [
                                    Expanded(child: Padding(
                                      padding: firmwarePageHelper!.isTableForAddDialog ? EdgeInsets.zero :  const EdgeInsets.only(left: 5),
                                      child: searchTextFieldView(snapshot),
                                    )),
                                    MobileFirmware().selectTableTypeButtonViewForAddDialog(firmwarePageHelper),
                                  ],
                                ),
                                const SizedBox(height: 10),
                                getTableBoardView(apiStatus)
                              ],
                            ),
                    ),
                  ),
                  actions: [
                    Container(width: double.infinity,
                      color: AppColorConstants.colorLimeGray,
                      margin: EdgeInsets.only(right: 4) ,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10)
                            .copyWith(bottom: 20),
                        child: Wrap(alignment: WrapAlignment.spaceBetween,
                          children: [
                            Wrap(crossAxisAlignment: WrapCrossAlignment.center,
                              children: [
                                Checkbox(
                                    value: isUpdateMode,
                                    onChanged: (value) {
                                      isUpdateMode = !isUpdateMode;
                                      snapshot(() {});
                                    }),
                                AppText(S.of(context).upgradeAutoText,style: TextStyle(
                                  fontFamily: AppAssetsConstants.openSans,
                                  fontSize: 14,
                                  color:  AppColorConstants.colorBlackBlue,
                                  fontWeight: FontWeight.w600,
                                )),
                              ],),

                            Row(
                              mainAxisSize:
                                  (firmwarePageHelper?.screenLayoutType == ScreenLayoutType.mobile)
                                      ? MainAxisSize.max
                                      : MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                AppButton(padding: MaterialStateProperty.all(EdgeInsets.all(0)),
                                    buttonName: S.of(context).cancel,
                                    buttonWidth: 70,
                                    buttonHeight: 30,
                                    margin: EdgeInsets.all(0),
                                    onPressed: () {
                                      goBack();
                                    },
                                    fontSize: 15,
                                    fontColor: AppColorConstants.colorWhite,
                                    fontFamily: AppAssetsConstants.poppins,
                                    buttonStyle: ElevatedButton.styleFrom(
                                        shape: RoundedRectangleBorder(
                                          side: BorderSide(color: AppColorConstants.colorBlackBlue),
                                          borderRadius: BorderRadius.circular(8),
                                        ),
                                        foregroundColor: AppColorConstants.colorWhite,
                                        backgroundColor: AppColorConstants.colorH2,
                                        padding:
                                            const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
                                        elevation: 20,
                                        shadowColor: AppColorConstants.colorGray.withOpacity(0.3))),
                                SizedBox(width: getSize(10)),
                                AppButton(
                                  buttonHeight: 30,
                                  buttonWidth: 70,
                                  loadingStatus: firmwarePageHelper!.createDeploymentStatus,
                                  buttonName: S.of(context).proceed,
                                  fontColor: firmwarePageHelper!.selectedDevices.isNotEmpty
                                      ? AppColorConstants.colorWhite
                                      : AppColorConstants.colorBlackBlue,
                                  fontFamily: AppAssetsConstants.poppins,
                                  fontSize: 15,
                                  onPressed: () async {
                                    if (firmwarePageHelper!.selectedDevices.isNotEmpty) {
                                      List<String> selectedDevices = firmwarePageHelper!.selectedDevices;
                                      await callback(selectedDevices,isUpdateMode,firmware);
                                    }
                                  },
                                  buttonStyle: ElevatedButton.styleFrom(
                                      shape: RoundedRectangleBorder(
                                        side: BorderSide(
                                            color: firmwarePageHelper!.selectedDevices.isNotEmpty
                                                ? AppColorConstants.colorPrimary
                                                : AppColorConstants.colorH2),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      foregroundColor: AppColorConstants.colorWhite,
                                      backgroundColor: firmwarePageHelper!.selectedDevices.isNotEmpty
                                          ? AppColorConstants.colorPrimary
                                          : AppColorConstants.colorWhite1,
                                      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
                                      elevation: 20,
                                      shadowColor: AppColorConstants.colorGray.withOpacity(0.3)),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                );
              },
            );
          },
        );
      },
    );
  }
  Widget _buildFirmwareTypeAndVersionText(String? title, String? version) {
    return AppText(
      "${title != null ? '$title - ' : ''}${version != null ? 'V $version' : ''}",
      maxLines: 1,
      style: TextStyle(
        color: AppColorConstants.colorAppbar,
        fontWeight: FontWeight.w500,
        fontSize: getSize(15),
        fontFamily: AppAssetsConstants.openSans,
      ),
    );
  }
  Widget getTableBoardView(ApiStatus apiStatus) {
    if (apiStatus == ApiStatus.loading) {
      return  SizedBox(
          height: 400,
          child: Align(
              alignment: Alignment.center,
              child: Container(
                  width: double.infinity,
                  decoration:
                  firmwarePageHelper!.dataTableHelper.tableBorderDeco(),
                  child: const AppLoader())));
    }
    int itemsPerPage = firmwarePageHelper!.dataTableHelper
        .getCurrentPageDataLength(
        firmwarePageHelper!.firmwareDevicesList.result.devices,
        0);
    int recordsInPage =
    (firmwarePageHelper!.firmwareDevicesList.result.devices.length > 10)
        ? itemsPerPage
        : firmwarePageHelper!.firmwareDevicesList.result.devices.length;
    if (!firmwarePageHelper!.isTableForAddDialog) {
      return MobileFirmware().buildCreateDeployment(context, firmwarePageHelper!);
    }
    return SizedBox(
        width: double.infinity,
        child: Column(
          children: [
            // Table
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 0),
              height: (firmwarePageHelper!.firmwareDevicesList.result.devices.isNotEmpty)
                  ? (recordsInPage * 48) + (recordsInPage * 0.1) + 75
                  : (recordsInPage * 50) + (recordsInPage * 0.1) + 340,
              child: getDataTableWithPageBoardView(),
            ),
            firmwarePageHelper!.isLoadingMore
                ? const SizedBox(
                    height: 50,
                    width: 50,
                    child: Center(child: AppLoader()),
                  )
                : const SizedBox()
          ],
        ));
  }

  Widget getDataTableWithPageBoardView() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: PaginatedDataTable2(
        columnSpacing: 0,
        headingCheckboxTheme: CheckboxThemeData(
            side: BorderSide(width: 2, color: AppColorConstants.colorWhite)),
        headingTextStyle:TextStyle(
            color: AppColorConstants.colorWhite,
            fontWeight: FontWeight.w600,
            fontFamily: AppAssetsConstants.openSans,
            fontSize: 14),
        wrapInCard: false,
        headingRowHeight: 50,
        datarowCheckboxTheme: const CheckboxThemeData(
            side: BorderSide(width: 2, color: Colors.grey)),
        border: firmwarePageHelper!.dataTableHelper.tableBorder(),
        renderEmptyRowsInTheEnd: false,
        headingRowColor:
        firmwarePageHelper!.dataTableHelper.headingRowColor(),
        columns: _getDataColumns(),
        source: firmwarePageHelper!.amplifierFirmwareDatasource,
        onSelectAll:
        firmwarePageHelper!.amplifierFirmwareDatasource.selectAll,
        minWidth: 700,
        dataRowHeight: 48,
        hidePaginator: true,
        empty: firmwarePageHelper!.dataTableHelper.getEmptyTableContent(context),
      ),
    );
  }

  List<DataColumn> _getDataColumns() {
    return [
      DataColumn2(
        fixedWidth: getSize(170),
        label: SelectableText(S.of(context).tableDeviceEUI),
      ),
      DataColumn2(
        fixedWidth: getSize(150),
        label: AppText(S.of(context).tableType, textAlign: TextAlign.center,),
      ),
      DataColumn2(
        fixedWidth: getSize(105),
        label: AppText(S.of(context).ampFwVersion, textAlign: TextAlign.start,),
      ),
      DataColumn2(
        fixedWidth: getSize(105),
        label: AppText(S.of(context).transponderFwVersion, textAlign: TextAlign.start,),
      ),
      DataColumn2(
        //fixedWidth: getSize(100),
        size:   ColumnSize.L,
        label: AppText(S.of(context).status, textAlign: TextAlign.center,),
      ),

    ];
  }


  Widget searchTextFieldView(StateSetter snapshot) {
    return Container(
      color:  AppColorConstants.colorWhite,
      height: 40,
      child: AppTextFormField(
        borderRadius: getSize(12),
        onChanged: (value) {
          firmwarePageHelper!.amplifierFirmwareDatasource.notifyListeners();
          firmwarePageHelper!.searchFirmwareDevicesList.result.devices =
              firmwarePageHelper!.firmwareDevicesList.result.devices.where((element) {
            final lowerCaseValue = value.toLowerCase();
            final typeValue = element.deviceType.toLowerCase() ?? '';
            return element.deviceEui.toLowerCase().contains(lowerCaseValue) ||
                typeValue.contains(lowerCaseValue);
          }).toList();
          firmwarePageHelper!.amplifierFirmwareDatasource = firmwarePageHelper!.dataSource(firmwarePageHelper!.searchController.text.isEmpty
              ? firmwarePageHelper!.firmwareDevicesList.result.devices
              : firmwarePageHelper!.searchFirmwareDevicesList.result.devices);
          snapshot(() {});
          firmwareController.update();
        },
        focusedBorderColor: AppColorConstants.colorPrimary,
        controller: firmwarePageHelper!.searchController,
        enabledBorderColor: AppColorConstants.colorH2,
        hintText: S.of(context).search,
        maxLines: 1,
        textInputType: TextInputType.text,
        validator: (value) {
          return null;
        },
        hintTextColor: AppColorConstants.colorDarkBlue,
        suffixIcon: Padding(
          padding: const EdgeInsets.all(10),
          child: firmwarePageHelper!.searchController.text.isEmpty
              ? const AppImageAsset(image: AppAssetsConstants.searchIcon)
              : InkWell(
                  onTap: () {
                    firmwarePageHelper!.searchController.clear();
                    firmwarePageHelper!.searchFirmwareDevicesList =
                        FirmwareDevices.empty();
                    firmwarePageHelper!.amplifierFirmwareDatasource = firmwarePageHelper!
                        .dataSource(firmwarePageHelper!.firmwareDevicesList.result.devices);
                    snapshot(() {});
                    firmwarePageHelper!.amplifierFirmwareDatasource.notifyListeners();
                  },
                  child: const Icon(Icons.clear)),
        ),
        hintFontSize: 14,
        fontFamily: AppAssetsConstants.openSans,
        fontWeight: FontWeight.w400,
      ),
    );
  }

  deleteDialogView(c, Function fun,String fileName) {
    return showDialog(
      context: c,
      builder: (context) {
        return AlertDialog(
          surfaceTintColor: AppColorConstants.colorWhite,
          backgroundColor: AppColorConstants.colorWhite,
          insetPadding: const EdgeInsets.symmetric(horizontal: 8.0),
          contentPadding: EdgeInsets.zero,
          titlePadding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
          title: getCustomAppBarWithClose("Delete ?"),
          content: StatefulBuilder(builder: (context, snapshot) {
            return Container(
              width: MediaQuery.of(context).size.width *
                  0.1, // Adjust the width as needed
              height: MediaQuery.of(context).size.height * 0.1,
              padding: const EdgeInsets.all(8.0),
              child:  Padding(
                padding: const EdgeInsets.all(8.0),
                child: AppText(
                    "Are you sure you want to delete $fileName ?",
                    style: TextStyle(
                      fontFamily: AppAssetsConstants.openSans,
                      fontSize: 14,
                      color:  AppColorConstants.colorBlackBlue,
                      fontWeight: FontWeight.w600,
                    )),
              ),
            );
          }),
          actions: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                AppButton(
                  borderColor: Colors.grey.withOpacity(0.5),
                  buttonHeight: 20,
                  buttonColor: Colors.grey.withOpacity(0.5),
                  buttonName: S.of(context).no,
                  fontFamily: AppAssetsConstants.openSans,
                  onPressed: () {
                    goBack();
                  },
                ),
                const SizedBox(width: 16),
                AppButton(
                  fontFamily: AppAssetsConstants.openSans,
                  buttonHeight: 20,
                  buttonName: S.of(context).yes,
                  onPressed: () {
                    goBack();
                    fun.call();
                  },
                ),
              ],
            )
          ],
        );
      },
    );
  }

  Widget customPaginationArrowView() {
    return AppPaginationWidget(
      apiStatus:  firmwarePageHelper!.apiStatus,
      paginationHelper: firmwarePageHelper!.fmPaginationHelper,
      onLoadNext: () async {
        await firmwarePageHelper!.loadNextLogs(context);
        firmwareController.update();
      },
      onLoadPrevious: () async {
        await firmwarePageHelper!.loadPreviousLogs(context);
        firmwareController.update();
      },
      onGoToFirstPage: () {
        firmwarePageHelper!.fmPaginationHelper.setPage(0);
        if(firmwarePageHelper!.isTableView) {
          firmwarePageHelper!.fmPageController.goToFirstPage();
        }
        firmwareController.update();
      },
      onGoToLastPage: () {
        if(firmwarePageHelper!.isTableView) {
          firmwarePageHelper!.fmPageController.goToLastPage();
        }
        firmwareController.update();
      },
      itemsPerPage: AppStringConstants.firmwarePerPageLimit,
      onChanged: (value) {
        AppStringConstants.firmwarePerPageLimit = int.parse(value);
        if (firmwarePageHelper!.apiStatus != ApiStatus.loading) {
          firmwarePageHelper!.fmPaginationHelper.setPage(0);
          if(firmwarePageHelper!.isTableView) {
            firmwarePageHelper!.fmPageController.goToFirstPage();
          }
          firmwarePageHelper!.firmWarePageOffset = 0;
          firmwarePageHelper!.getCurrentFirmwareData();
        }
        firmwareController.update();
        firmwarePageHelper!.firmDataSource.notifyListeners();

      },
    );
  }

  Widget refreshButtonView(){
    return Row(mainAxisSize: MainAxisSize.min,children: [
      Align(alignment: Alignment.centerRight, child: buildLastSeenView()),
      getRefreshButtonView(),
    ],);
  }

  Widget getRefreshButtonView() {
    return AppRefresh(
        onPressed: () {
          if (firmwarePageHelper!.apiStatus != ApiStatus.loading) {
            firmwarePageHelper!.getCurrentFirmwareData(isTable: false);
          }
        },
        loadingStatus: firmwarePageHelper!.apiStatus);
  }

  Widget buildLastSeenView() {
    if (firmwarePageHelper!.isShowText) {
      return getTimeDurationView(
        refreshStatus: firmwarePageHelper!.apiStatus,
        updateTime: firmwarePageHelper!.lastUpdateTime,
        onTapTime: firmwarePageHelper!.onTapTime,
        difference: firmwarePageHelper!.differenceTime,
      );
    } else {
      if (firmwarePageHelper!.lastUpdateTime != null) {
        return getLastSeenView(firmwarePageHelper!.lastUpdateTime);
      } else {
        return Container();
      }
    }
  }

}

uploadFirmwareView(
  c,
  FirmwareController firmwareController,
  FirmwarePageHelper firmwarePageHelper,
) {
  {
    //TypeDDItem typeDDItem = TypeDDItem.fromName(null);
   // List<dynamic> listTypeDDItem = [];
    String fileName = "";
    String fileUploadMessage = "";
    PlatformFile ? fileFormate;
    Future<void> uploadFiles(StateSetter snapshot) async {
      try {
        FilePickerResult? result = (await FilePicker.platform.pickFiles(
          type: FileType.custom,
          allowedExtensions: ['bin','gbl'],
        ));
        if (result != null && result.files.isNotEmpty) {
          fileFormate = result.files.first;
          fileName = fileFormate!.name;
          snapshot(() {});
        }
      } on PlatformException catch (e) {
        debugLogs('Unsupported operation$e');
      } catch (e) {
        debugLogs(e.toString());
      }
    }

    Future<void> uploadFirmwareFiles(BuildContext context) async {
      if (fileFormate != null) {
        firmwarePageHelper.uploadStatus = ApiStatus.loading;
        firmwareController.update();
        try {
          final response = await firmwareController.uploadFirmware(
              context: context, fileFormate: fileFormate!);
          if (response != null) {
            Map<String, dynamic> data = json.decode(response);
            if (data[AppStringConstants.successLowerCase] == true) {
              goBack();
              data[AppStringConstants.message]
                  .toString()
                  .showSuccess(context);
              await firmwarePageHelper.getCurrentFirmwareData();
            } else {
              fileUploadMessage =
                  data[AppStringConstants.message].toString();
            }
          } else {
            fileUploadMessage = S.of(context).somethingWentWrong;
          }
        } catch (e) {
          fileUploadMessage = S.of(context).somethingWentWrong;
        } finally {
          firmwarePageHelper.uploadStatus = ApiStatus.success;
          firmwareController.update();
        }
      }
    }



    return showDialog(
      barrierDismissible: false,
      context: c,
      builder: (context) {
        return GetBuilder<FirmwareController>(
          init: FirmwareController(),
          builder: (FirmwareController controller) {
            return StatefulBuilder(
              builder: (context, snapshot) {
                return AlertDialog(
                  surfaceTintColor: AppColorConstants.colorWhite,
                  backgroundColor: AppColorConstants.colorWhite,
                  insetPadding: const EdgeInsets.symmetric(horizontal: 8.0),
                  contentPadding: EdgeInsets.zero,
                  titlePadding: EdgeInsets.zero,
                  actionsPadding: EdgeInsets.zero,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
                  title: Padding(
                    padding: const EdgeInsets.only(left: 24, top: 24, right: 20, bottom: 5),
                    child: Row(
                      children: [
                        SizedBox(
                          width: getSize(2),
                          height: getSize(8),
                        ),
                        CircleAvatar(
                          maxRadius: 18,
                          backgroundColor: AppColorConstants.colorLightBlue,
                          child: Icon(
                            size: 18,
                            Icons.check,
                            color: AppColorConstants.colorWhite,
                          ),
                        ),
                        SizedBox(
                          width: getSize(18),
                        ),
                        AppText(
                          S.of(context).uploadFirmware,
                          maxLines: 1,
                          style: TextStyle(
                              color: AppColorConstants.colorAppbar,
                              fontWeight: FontWeight.w700,
                              fontSize: getSize(25),
                              fontFamily: AppAssetsConstants.openSans),
                        ),
                        const Spacer(),
                        IconButton(
                            onPressed: () {
                              goBack();
                            },
                            icon: const Icon(Icons.close)),
                      ],
                    ),
                  ),
                  content: Padding(
                    padding: EdgeInsets.only(
                        left: 13, right: 24, bottom: MediaQuery.of(c).viewInsets.bottom),
                    //padding: const EdgeInsets.all(8.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 500,
                          padding: const EdgeInsets.only(left: 16, right: 16, top: 16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              const SizedBox(height: 10),
                              Row(
                                children: [
                                  InkWell(
                                    onTap: () {
                                        uploadFiles(snapshot);
                                        snapshot(() {});
                                    },
                                    child: Container(
                                      alignment: Alignment.centerLeft,
                                      decoration: BoxDecoration(
                                        boxShadow:  [
                                          BoxShadow(
                                              color: AppColorConstants.colorGray,
                                              spreadRadius: 1,
                                              blurRadius: 1,
                                              offset: const Offset(0, 1))
                                        ],
                                          borderRadius: BorderRadius.circular(4),
                                          color: AppColorConstants.colorWhite,
                                          border: Border.all(
                                              color: AppColorConstants.colorGray, width: 1)),
                                      height: 30,
                                      width: 100,
                                      child: Padding(
                                        padding: const EdgeInsets.only(left: 8.0),
                                          child: AppText(S.of(context).chooseFile,
                                              maxLines: 1,
                                              isSelectableText: false,
                                              style: TextStyle(
                                                  fontFamily: AppAssetsConstants.openSans,
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.w500,
                                                  color: AppColorConstants.colorBlack))),
                                    ),
                                  ),
                                  const SizedBox(width: 5,),
                                  AppText(fileName.isEmpty ? S.of(context).noFileChosen : fileName,
                                      style: TextStyle(
                                          fontFamily: AppAssetsConstants.openSans,
                                          fontSize: fileName.isEmpty ? 13 : 14,
                                          fontWeight: fileName.isEmpty ? FontWeight.w400 : FontWeight.w500,
                                          color: AppColorConstants.colorBlack))
                                ],
                              ),
                              const SizedBox(height: 8),
                              if(fileUploadMessage.isNotEmpty) errorMessageView(errorMessage: fileUploadMessage,padding: 0) else const SizedBox(height: 25),
                            ],
                          ),
                        ),

                        const SizedBox(height: 10),
                      ],
                    ),
                  ),
                  actions: [
                    Container(
                      color: AppColorConstants.colorWhiteShade,
                      child: Row(
                        children: [
                          const Spacer(),
                          Container(
                            padding: const EdgeInsets.only(left: 16, bottom: 16, top: 10),
                            child: AppButton(
                              fontSize: 14,
                              buttonWidth: 50,
                              buttonHeight: 35,
                              borderWidth: 2,
                              fontFamily: AppAssetsConstants.notoSans,
                              buttonName: S.of(context).cancel,
                              fontColor: AppColorConstants.colorBlackBlue,
                              onPressed: () {
                                goBack();
                              },
                              borderColor: Colors.grey.withOpacity(0.5),
                              buttonColor: AppColorConstants.colorWhite1,
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16, top: 8),
                            child: AppButton(
                              loadingStatus: firmwarePageHelper.uploadStatus,
                              fontSize: 14,
                              buttonWidth: 50,
                              buttonHeight: 35,
                              borderWidth: 2,
                              fontColor:  fileFormate != null
                                  ? AppColorConstants.colorWhite
                                  : AppColorConstants.colorH2,
                              borderColor:  fileFormate != null
                                  ? AppColorConstants.colorLightBlue
                                  : Colors.grey.withOpacity(0.5),
                              buttonColor:  fileFormate != null
                                  ? AppColorConstants.colorLightBlue
                                  : AppColorConstants.colorWhite1,
                              fontFamily: AppAssetsConstants.notoSans,
                              buttonName: S.of(context).upload,
                              onPressed: (fileFormate != null)
                                  ? () async {
                                      await uploadFirmwareFiles(context);
                                    }
                                  : null,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                );
              },
            );
          },
        );
      },
    );
  }
}
