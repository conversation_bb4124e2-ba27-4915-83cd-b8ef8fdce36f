// ignore_for_file: deprecated_member_use

import 'package:quantumlink_node/app_import.dart';


class AmplifierFirmwareDatasource extends DataTableSource {
  int selectedCount = 0;
  FirmwareController firmwareController = Get.find<FirmwareController>();

  AmplifierFirmwareDatasource.empty(this.context, this.list, this.onSelectChanged) : super();
  Function onSelectChanged;
  AmplifierFirmwareDatasource(this.context, this.list,this.onSelectChanged,
      [sortedByEUI = false,
        this.hasRowHeightOverrides = false,
        this.hasZebraStripes = false]) {
    if (sortedByEUI) {
      sort((d) => d.deviceEui, true);
    }
    for (final item in list) {
      if (getDetectedStatusType(item.status) == DetectedStatusType.online) {
        item.selected = true;
      }
    }
    selectedCount = list.where((d) => getDetectedStatusType(d.status) == DetectedStatusType.online && d.selected == true).length;
    if(selectedCount != 0) onSelectChanged.call();
  }
  // Override height values for certain rows
  bool hasRowHeightOverrides = false;

  // Color each Row by index's parity
  bool hasZebraStripes = false;
  final BuildContext context;
  final List<DeviceItem> list;

  void sort<T>(Comparable<T> Function(DeviceItem d) getField, bool ascending) {
    list.sort((a, b) {
      final aValue = getField(a);
      final bValue = getField(b);
      return ascending ? Comparable.compare(aValue, bValue) : Comparable.compare(bValue, aValue);
    });
    notifyListeners();
  }
  DataTableHelper dataTableHelper = DataTableHelper();

  @override
  DataRow2 getRow(int index, [Color? color]) {
    assert(index >= 0);
    if (index >= list.length) throw 'index > _desserts.length';
    final dessert = list[index];
    DetectedStatusType? detectedStatusType = getDetectedStatusType(dessert.status);
    TextStyle  dataRowTextStyle = TextStyle(
      fontWeight: getMediumBoldFontWeight(),
      fontFamily: AppAssetsConstants.openSans,
      color: detectedStatusType == DetectedStatusType.online
          ? AppColorConstants.colorBlack
          : AppColorConstants.colorBlack.withOpacity(0.5),
      fontSize: getSize(13),
    );
    return DataRow2.byIndex(
      index: index,
      selected: dessert.selected ?? false,
      color:  detectedStatusType == DetectedStatusType.online
          ? MaterialStateProperty.all(AppColorConstants.colorWhite)
          : MaterialStateProperty.all(AppColorConstants.colorTableBroader),
      onSelectChanged: detectedStatusType == DetectedStatusType.online
          ? (value) {
              if (dessert.selected != value) {
                if (dessert.selected == true) {
                  dessert.selected = value;
                } else {
                  selectedCount += value! ? 1 : -1;
                  assert(selectedCount >= 0);
                  dessert.selected = value;
                }
              }
              onSelectChanged.call();
              notifyListeners();
              firmwareController.update();
            }
          : (value) => false,
      cells: [
        DataCell(AppText(dessert.deviceEui ?? "",
            style: dataRowTextStyle)),
        DataCell(AppText(dessert.deviceType ?? "",
            style: dataRowTextStyle)),
        DataCell(AppText(
          dessert.ampFwVersion ?? "",
          style: dataRowTextStyle,
        )),
        DataCell(AppText(
          dessert.xponderFwVersion ?? "",
          style: dataRowTextStyle,
        )),
        DataCell(Container(
          alignment: Alignment.center,
          height: getSize(30),
          width: getSize(70),
          decoration: BoxDecoration(
              color: detectedStatusType == DetectedStatusType.online
                  ? AppColorConstants.colorLightGreen
                  : detectedStatusType == DetectedStatusType.offline
                  ? AppColorConstants.colorH2.withOpacity(0.4)
                  : (detectedStatusType == DetectedStatusType.pending)
                  ? AppColorConstants.colorOrange
                  : AppColorConstants.colorH2.withOpacity(0.4),
              borderRadius: BorderRadius.circular(getSize(8))),
          child: AppText(
            detectedStatusType == DetectedStatusType.online
                ? S.of(context).online
                : detectedStatusType == DetectedStatusType.offline
                ? S.of(context).offline
                : detectedStatusType == DetectedStatusType.pending
                ? S.of(context).pending
                : S.of(context).offline,
            style: TextStyle(
                color: (detectedStatusType == DetectedStatusType.offline ||
                    dessert.status == null)
                    ? AppColorConstants.colorH3
                    : AppColorConstants.colorWhite,
                fontFamily: AppAssetsConstants.sourceSans,
                fontSize: 15,
                fontWeight: FontWeight.w500),
          ),
        )),

      ],
    );
  }

  @override
  int get rowCount => list.length;

  @override
  bool get isRowCountApproximate => false;

  @override
  int get selectedRowCount => selectedCount;

  void selectAll(bool? checked) {
    for (final dessert in list) {
      DetectedStatusType? detectedStatusType = getDetectedStatusType(dessert.status);
      if (detectedStatusType == DetectedStatusType.online) {
        if(dessert.selected){
          dessert.selected = false;
        }else{
          dessert.selected = true;
        }
      }else{
        dessert.selected = false;
      }
    }
    selectedCount = (checked ?? false) ? list.length : 0;
    onSelectChanged.call();
    firmwareController.update();
    notifyListeners();
  }
}