// ignore_for_file: deprecated_member_use

import 'package:quantumlink_node/app_import.dart';

class CurrentFirmwareDataSource extends DataTableSource {
  List<FirmwareData> firmwareList;
  final BuildContext context;
  FirmwarePageHelper firmwarePageHelper;

  CurrentFirmwareDataSource(
      {required this.context, required this.firmwareList, required this.firmwarePageHelper});

  setFirmware(final List<FirmwareData> firmwareList) {
    this.firmwareList = firmwareList;
    notifyListeners();
  }



  @override
  DataRow? getRow(int index) {
    if (index >= firmwareList.length) {
      return null;
    }
    return DataRow2.byIndex(index: index,
        cells: getCells(index),
        selected: firmwareList[index].selected ?? false,
        color: index.isEven
            ? MaterialStateProperty.all(AppColorConstants.colorWhite)
            : MaterialStateProperty.all(AppColorConstants.colorBackgroundDark));
  }

  List<DataCell> getCells(int index) {
    TextStyle textStyle = TextStyle(
        fontFamily: AppAssetsConstants.roboto,
        fontSize: getSize(14),
        fontWeight: getMediumFontWeight());
    final dessert = firmwareList[index];

    String fileName = dessert.filename ?? "";
    String productName = dessert.productName ?? "";
    String version = dessert.version ?? "";
    String fileSize = dessert.size.toString();
    String deviceCount = dessert.deployments.length.toString();
    bool isActive = dessert.deployments.isNotEmpty;
    bool isDeleted = dessert.deployments.isEmpty;
    bool isSigned = dessert.signed ?? false;

    String uploadDate =   dessert.uploadedAt != null ? getUtcTimeZone(dessert.uploadedAt) : '';
    List<DataCell> cells = [
      DataCell(Checkbox(
          shape: RoundedRectangleBorder(
              side: BorderSide(color: AppColorConstants.colorPrimary),
              borderRadius: BorderRadius.circular(3)),
          activeColor: AppColorConstants.colorPrimary,
          checkColor: AppColorConstants.colorWhite,
          value: firmwareList[index].selected ?? false,
          onChanged: (firmwareList[index].deployments.isEmpty)
              ? (value) {
                  if (firmwareList[index].selected != value) {
                     firmwareList[index].selected = value;
                    notifyListeners();
                  }
                }
              : null)),
      DataCell(
        Container(
          alignment: Alignment.center,
          child:SizedBox(
            height: 30,
            width: 100,
            child: InkWell(
              onTap: () {
                firmwarePageHelper.state.deviceListDialog(context, dessert,
                        (List<String> deviceList, bool isUpdateMode ,FirmwareData data) async {
                      CreateDeploymentItem createDeployment = CreateDeploymentItem(
                        deviceEuis: deviceList,
                        firmwareId: dessert.firmwareId,
                        fileType: dessert.type,
                        updateMode: isUpdateMode ? "auto" : "manual",
                      );
                      await firmwarePageHelper.createDeployment(createDeployment);
                    });
              } ,
              child:  AppImageAsset(image: AppAssetsConstants.addListIcon,color: AppColorConstants.colorLightBlue,),
            ),
          )
          ,
        ),
      ),
      DataCell(
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween, // Ensures the icon is at the end
          children: [
            Expanded(
              // Ensures the text wraps properly
              child: AppText(
                fileName,
                style: textStyle,
              ),
            ),
           if(isSigned) Padding(
              padding: const EdgeInsets.only(left: 3,top: 3),
              child: Icon(
                Icons.verified_user_rounded,
                color: AppColorConstants.colorGreen2,
                size: 16,
              ),
            ),
          ],
        ),
      ),
      DataCell(Center(
        child: AppText(productName,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              fontFamily: AppAssetsConstants.openSans,
              fontSize: getSize(14),
            )),
      )),
      DataCell(Center(child: AppText(version, style: textStyle))),
      DataCell(Center(child: AppText(fileSize, style: textStyle))),
      DataCell(Center(child: AppText(uploadDate, style: textStyle))),
      DataCell(
          onTap: deviceCount == "0"
              ? null
              : () {
                 firmwarePageHelper.associateDeviceCountOnTap(dessert);
                },
          Center(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (deviceCount != "0")
                    Icon(
                      Icons.error,
                      color: AppColorConstants.colorLightBlue,
                      size: 15,
                    ),  SizedBox(width: 4),
              AppText(deviceCount,
                  isSelectableText: false,
                  style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontFamily: AppAssetsConstants.openSans,
                      color: deviceCount == "0"
                          ? AppColorConstants.colorBlack
                          : AppColorConstants.colorLightBlue,
                      fontSize: getSize(14))),


            ],
          ))),
      DataCell(Container(
        child: isDeleted
            ? InkWell(
                onTap: ()  {
                  firmwarePageHelper.state.deleteDialogView(
                      context, () => firmwarePageHelper.deleteFirmware(dessert.firmwareId),fileName);
                },
                child: const AppImageAsset(width: 25,image: AppAssetsConstants.deleteEnabledIcon))
            :  AppImageAsset(width: 25,image: AppAssetsConstants.deleteEnabledIcon,color: AppColorConstants.colorH2.withOpacity(0.8)),
      )),
    ];
    return cells;
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => firmwareList.length;

  @override
  int get selectedRowCount => firmwareList.where((firmware) => firmware.selected ?? false).length;




}

