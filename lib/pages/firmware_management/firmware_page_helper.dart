import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/pages/firmware_management/amplifier_firmware_datasource.dart';
import 'package:rxdart/rxdart.dart';

class FirmwarePageHelper {
  late FirmwarePageState state;

  late CurrentFirmwareDataSource firmDataSource;
  late DeploymentsDataSource deploymentsDataSource;
  ApiStatus apiStatus = ApiStatus.initial;
  ApiStatus uploadStatus = ApiStatus.initial;
  ApiStatus deploymentStatus = ApiStatus.initial;
  ApiStatus createDeploymentStatus = ApiStatus.initial;
  final double fmHeightOfDataTableCell = 48;
  int recordsInPage = 0;
  int fmCurrentPageIndex = 0;
  final double depHeightOfDataTableCell = 48;
  int depCurrentPageIndex = 0;
  late TabController tabController;
  List<FirmTabItem> firmListTabs = [];
  DataTableHelper dataTableHelper = DataTableHelper();
  late List<bool> isHovered;
  late ScreenLayoutType screenLayoutType;
  bool isInitializedTab = false;
  TextEditingController searchController = TextEditingController();
  bool isSelectAll= false;
  bool isDeleteAllLoading= false;
  AmplifierController ampController = Get.put<AmplifierController>( AmplifierController());
  FirmwareFiles firmwareFilesList = FirmwareFiles.empty();
  DeploymentList deploymentList = DeploymentList.empty();
  List<AmplifierDeviceItem> amplifierDeviceList = [];
  PaginationHelper fmPaginationHelper = PaginationHelper();
  PaginatorController fmPageController = PaginatorController();
  int firmWarePageOffset=0;
  Deployment deploymentItem = Deployment.empty();

  PaginationHelper depPaginationHelper = PaginationHelper();
  PaginatorController depPageController = PaginatorController();
  int depPageOffset=0;
  int ampFWPageOffset = 0;
  bool hasMoreData = false;
  bool isLoadingMore = false;
  String ?firmwareDatatableError;
  String ?deploymentDatatableError;
  Timer? _deploymentTimer;
  DateTime? lastUpdateTime ;
  DateTime? onTapTime;
  Duration? differenceTime;
  bool isShowText = true;
  Timer? refreshTimer;
  DateTime? deploymentLastUpdateTime ;
  DateTime? deploymentOnTapTime;
  Duration? deploymentDifferenceTime;
  bool deploymentIsShowText = true;
  Timer? deploymentRefreshTimer;
  FirmwareDevices firmwareDevicesList = FirmwareDevices.empty();
  FirmwareDevices searchFirmwareDevicesList = FirmwareDevices.empty() ;
  late AmplifierFirmwareDatasource amplifierFirmwareDatasource;
  List<String> selectedDevices = [];
  List<String> firmwareDeploymentIdList = [];
  bool isTableView = true;
  bool isTableViewForDeployment = true;
  bool isTableViewForFirmwareDetails = true;
  bool isTableForAddDialog = true;
  late ScreenLayoutType previousLayoutType = ScreenLayoutType.desktop;

  Future<void> getCurrentFirmwareData({isUpdate=true,isTable=true}) async {
    firmwareDatatableError = null;
    if (isUpdate) {
      fmPaginationHelper.setPage(0);
      if(isTable) {
        fmPageController.goToFirstPage();
      }
      firmWarePageOffset = 0;
    }
    initializeTimer();
    apiStatus = ApiStatus.loading;
    state.firmwareController.update();
    firmwareFilesList = await state.firmwareController.getFirmwareFileList(
        context: state.context,
        pageOffset: fmPaginationHelper.pageOffset,
        perPageLimit: AppStringConstants.firmwarePerPageLimit);
    firmDataSource = CurrentFirmwareDataSource(
        context: state.context,
        firmwareList: firmwareFilesList.result.dataList,
        firmwarePageHelper: this);
    bool hasMoreData =
        firmwareFilesList.result.dataList.length == AppStringConstants.firmwarePerPageLimit;
    fmPaginationHelper.updatePagination(firmwareFilesList.result.dataList.length,
        hasMore: hasMoreData,pageLimit: AppStringConstants.firmwarePerPageLimit);
    if (firmwareFilesList.success != null && firmwareFilesList.success == false) {
      firmwareDatatableError = S.of(state.context).socketExceptionMessage;
    }
    getDifferenceTime();
    lastUpdateTime = DateTime.now();
    apiStatus = ApiStatus.success;
    state.firmwareController.update();
  }

  getFirmwareFileList() async {
    apiStatus = ApiStatus.loading;
    state.firmwareController.update();
    firmwareFilesList = await state.firmwareController.getFirmwareFileList(
        context: state.context,
        pageOffset: fmPaginationHelper.pageOffset,
        perPageLimit: AppStringConstants.firmwarePerPageLimit);
    apiStatus = ApiStatus.success;
    state.firmwareController.update();
  }

  void initializeTimer() {
    differenceTime = null;
    onTapTime = DateTime.now();
    isShowText = true;
    refreshTimer?.cancel();
  }



  getDifferenceTime() {
    differenceTime = DateTime.now().difference(onTapTime!);
    refreshTimer= Timer(const Duration(seconds: 2), () {
      isShowText = false;
      state.firmwareController.update();
    });
  }

  Future getDeploymentsFirmwareData({isUpdate=true}) async {
    deploymentDatatableError= null;
    _deploymentTimer?.cancel();
    deploymentInitializeTimer();
    if (isUpdate) {
      deploymentStatus = ApiStatus.loading;
      state.firmwareController.update();
    }
    if(deploymentList.deployments!.isEmpty){
      deploymentStatus = ApiStatus.loading;
      state.firmwareController.update();
    }
    await state.firmwareController
        .getDeploymentsList(
            context: state.context,
            pageOffset: depPaginationHelper.pageOffset,
            perPageLimit: AppStringConstants.deploymentPerPageLimit)
        .then((value) {
      if (value != null) {
        deploymentList = value;
      } else {
        deploymentDatatableError = S.of(state.context).socketExceptionMessage;
      }
    });
    if (firmwareDeploymentIdList.isNotEmpty) {
      deploymentList.deployments!.removeWhere((element) => !firmwareDeploymentIdList.contains(element.id));
      deploymentsDataSource = DeploymentsDataSource(
          deploymentsList: deploymentList.deployments!,
          firmwarePageHelper: this,
          context: state.context);
    } else {
      deploymentsDataSource = DeploymentsDataSource(
          deploymentsList: deploymentList.deployments!,
          firmwarePageHelper: this,
          context: state.context);
    }

    bool hasMoreData =
        deploymentList.deployments!.length == AppStringConstants.deploymentPerPageLimit;
    depPaginationHelper.updatePagination(deploymentList.deployments!.length,
        hasMore: hasMoreData,pageLimit: AppStringConstants.deploymentPerPageLimit);
    deploymentLastUpdateTime = DateTime.now();
    deploymentGetDifferenceTime();
    _deploymentTimer = Timer(const Duration(seconds: 30), () async {
      await getDeploymentsFirmwareData(isUpdate: false);
    });
    deploymentStatus = ApiStatus.success;
    state.firmwareController.update();
  }
  void deploymentInitializeTimer() {
    deploymentDifferenceTime = null;
    deploymentOnTapTime = DateTime.now();
    deploymentIsShowText = true;
    deploymentRefreshTimer?.cancel();
  }
  deploymentGetDifferenceTime() {
    deploymentDifferenceTime = DateTime.now().difference(deploymentOnTapTime!);
    deploymentRefreshTimer= Timer(const Duration(seconds: 2), () {
      deploymentIsShowText = false;
      state.firmwareController.update();
    });
  }
  void cancelTimer() {
    _deploymentTimer?.cancel();
    _deploymentTimer = null;
  }
  initializeFirmTabsList() {
    if (!isInitializedTab) {
      firmListTabs = [
        if (!AppConfig.shared.isOpenFromBLE) FirmTabItem(title: S.of(state.context).firmwareFiles, isCurrentOpen: true),
          FirmTabItem(
              title: S.of(state.context).deployments,
              isCurrentOpen: AppConfig.shared.isOpenFromBLE ? true : false),
        // FirmTabItem(title: S.of(state.context).selectTypeLE, isCurrentOpen: false),
      ];
      isHovered = List<bool>.filled(firmListTabs.length, false);
      isInitializedTab = true;
    }
  }

  FirmwarePageHelper(this.state) {
    tabController = TabController(
      initialIndex: 0,
      length: AppConfig.shared.isOpenFromBLE ? 1 : 2,
      vsync: state,
      animationDuration: Duration.zero
    );
    apiStatus = ApiStatus.loading;
    Future.delayed(const Duration(milliseconds: 100)).then((value) async {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        await getCurrentFirmwareData(isUpdate: false);
        getCurrantPageIndex();
      });
    });
  }

  getCurrantPageIndex() {
    fmPageController.addListener(() {
      fmCurrentPageIndex = (fmPageController.currentRowIndex / AppStringConstants.firmwarePerPageLimit).ceil();
      state.firmwareController.update();
    });
    depPageController.addListener(() {
      depCurrentPageIndex = (depPageController.currentRowIndex / AppStringConstants.deploymentPerPageLimit).ceil();
      state.firmwareController.update();
    });
  }
  // TAB ON TAP
  addTabTableOnTap(Deployment dataItem) async {
    cancelTimer();
    deploymentItem = dataItem;
    bool existsID = firmListTabs.any((tab) => tab.title == dataItem.id);
    if (!existsID) {
      if (firmListTabs.length <=6) {
        addDetailTab(dataItem.id, true,dataItem);
      }else{
        S.of(state.context).maxTabMessage.showError(state.context);
      }
      state.firmwareController.update();
    } else {
      for (var element in firmListTabs) {
        element.isCurrentOpen = false;
      }
      int euiIndex = firmListTabs.indexWhere((element) => element.title == dataItem.id);
      firmListTabs[euiIndex].isCurrentOpen = true;
      tabController.animateTo(
        euiIndex,
        duration: const Duration(milliseconds: 100),
      );
    }

    state.firmwareController.update();
  }

  // ADD TAB FUNCTION
  addDetailTab(t, o,deployment) {
    for (var tabElement in firmListTabs) {
      tabElement.isCurrentOpen = false;
    }
    firmListTabs.add(FirmTabItem(title: t, isCurrentOpen: o,deployment: deployment));
    isHovered = List<bool>.filled(firmListTabs.length, false);
    tabController = TabController(
        initialIndex: firmListTabs.length - 1,
        length: firmListTabs.length,
        vsync: state,
        animationDuration: Duration.zero
    );
  }

  // REMOVE TAB
  removeFMDetailTab(int index) {
    if (tabController.index != 1) {
      getDeploymentsFirmwareData();
    }
    firmListTabs.removeAt(index);
    for (var tabElement in firmListTabs) {
      tabElement.isCurrentOpen = false;
    }
    tabController = TabController(
        initialIndex: firmListTabs.length - 1,
        length: firmListTabs.length,
        vsync: state,
        animationDuration: Duration.zero
    );
    int deploymentsIndex = firmListTabs
        .indexWhere((tab) => tab.title == S.of(state.context).deployments);
    firmListTabs[deploymentsIndex].isCurrentOpen = true;
    tabController.animateTo(deploymentsIndex);
    state.firmwareController.update();
  }

  //REPLACE TAB
  replaceFMDetailTab() {
    firmwareDeploymentIdList.clear();
    firmListTabs[1] =
        FirmTabItem(isCurrentOpen: true, title:S.of(state.context).deployments);
    getDeploymentsFirmwareData();
    state.firmwareController.update();
  }
  String currantFilterDeploymentID = '';
  // Associate device count on tap
  associateDeviceCountOnTap(FirmwareData dessert) {
    firmwareDeploymentIdList = dessert.deployments.map((element) => element.id.toString()).toList();
    if (dessert.deployments.isNotEmpty) {
      deploymentStatus = ApiStatus.loading;
      for (var tabElement in firmListTabs) {
        tabElement.isCurrentOpen = false;
      }
      currantFilterDeploymentID = dessert.firmwareId;
      getDeploymentsFirmwareData(isUpdate: false);
      tabFirmHeaderOnTap(1);
      tabController.animateTo(1, duration: const Duration(milliseconds: 200));
    }
  }

  tabFirmHeaderOnTap(int value) {
    for (var tabElement in firmListTabs) {
      tabElement.isCurrentOpen = false;
    }
    if (firmListTabs[value].title == S.of(state.context).firmwareFiles) {
      cancelTimer();
   //   getCurrentFirmwareData(isUpdate: false);
    } else if (firmListTabs[value].title == S.of(state.context).deployments)  {
      getDeploymentsFirmwareData(isUpdate: false);
    }else{
      cancelTimer();
    }
    firmListTabs[value].isCurrentOpen = true;
    state.firmwareController.update();
  }
// Create Deployment Function's
  getAmplifierDeviceData(firmwareId) async {
    firmwareDevicesList = await state.firmwareController.getFirmwareDeviseList(
        context: state.context,
         firmwareId: firmwareId,
        perPageLimit: AppStringConstants.ampFWPrePageLimit,
        pageOffset: ampFWPageOffset);
    amplifierFirmwareDatasource = dataSource(firmwareDevicesList.result.devices);
    state.firmwareController.update();
  }

  dataSource(List<DeviceItem> result) {
    return AmplifierFirmwareDatasource(state.context, result, () {
      selectDevicesHandling();
    });
  }

  void selectDevicesHandling() {
    List<String> tempSelectedDevices = [];
    if (searchController.text.isNotEmpty) {
      tempSelectedDevices = searchFirmwareDevicesList.result.devices
          .where((element) => element.selected == true)
          .map((e) => e.deviceEui.toString())
          .toList();
    }
    tempSelectedDevices = firmwareDevicesList.result.devices
        .where((element) => element.selected == true)
        .map((e) => e.deviceEui.toString())
        .toList();
    for (var device in tempSelectedDevices) {
      if (!selectedDevices.contains(device)) {
        selectedDevices.add(device); // Add if not already in the list
      }
    }
    selectedDevices.removeWhere((device) => !tempSelectedDevices.contains(device));
  }

  Future<FirmwareDevices> fetchDevices(firmwareId) async {
    FirmwareDevices devicesList=FirmwareDevices.empty();
    devicesList = await state.firmwareController.getFirmwareDeviseList(
        context: state.context,
        firmwareId: firmwareId,
        perPageLimit: AppStringConstants.ampFWPrePageLimit,
        pageOffset: ampFWPageOffset);
    hasMoreData = devicesList.result.devices.length == AppStringConstants.ampFWPrePageLimit;
    return devicesList;
  }


  Future<void> fetchNextPage(firmwareId) async {
    isLoadingMore = true;
    state.firmwareController.update();
    await Future.delayed(const Duration(seconds: 1));
    FirmwareDevices nextPageData = await fetchDevices(firmwareId);
    if (nextPageData.result.devices.isNotEmpty) {
      firmwareDevicesList.result.devices.addAll(nextPageData.result.devices);
      amplifierFirmwareDatasource= dataSource(firmwareDevicesList.result.devices);
      if(isTableView) {
        amplifierFirmwareDatasource.notifyListeners();
      }
    }
    isLoadingMore = false;
    state.firmwareController.update();
  }

  Future<void> createDeployment(CreateDeploymentItem createDeployment) async {
    createDeploymentStatus=  ApiStatus.loading;
    state.firmwareController.update();
    try{
      CreateDeploymentFiles createDeploymentFiles = await state.firmwareController
          .createDeployment(context: state.context, createDeployment: createDeployment);
      if( createDeploymentFiles.success != null){
        if(createDeploymentFiles.success == true){
          Deployment dessert = createDeploymentFiles.result?.deployment ?? Deployment.empty();
          addTabTableOnTap(dessert);
          if (createDeploymentFiles.result?.skippedDevices.isEmpty == true) {
            goBack();
            createDeploymentFiles.message.toString().showSuccess(state.context);
          } else {
            goBack();
            state.skipCreateDeploymentDeviceListDialogView(
                createDeploymentFiles.result?.skippedDevices ?? [] ,createDeployment.deviceEuis.length);
          }
          depCurrentPageIndex=0;
          getCurrentFirmwareData();
        }else{
          goBack();
          createDeploymentFiles.message.toString().showError(state.context);
        }
      }else{
        goBack();
        S.of(state.context).somethingWentWrong.showError(state.context);
      }
    }catch(e){
      goBack();
      S.of(state.context).somethingWentWrong.showError(state.context);
    }finally{
      createDeploymentStatus=  ApiStatus.success;
      state.firmwareController.update();
    }
  }

  Future<void> deleteFirmware(String firmwareId) async {
    try {
      Map<String, dynamic> response =
          await state.firmwareController.deleteFirmware(firmwareId: firmwareId);
      if (response.isNotEmpty) {
        if (response['success'] == true) {
          response['message'].toString().showSuccess(state.context);
          getCurrentFirmwareData();
        } else {
          response['message'].toString().showError(state.context);
        }
      }
    } catch (e) {
      S.of(state.context).noDataFound.showError(state.context);
    }
  }

  Future<void> deleteMultipleFirmware(List<String> firmwareIds) async {
    isDeleteAllLoading = true;
    state.firmwareController.update();
    try {
      Map<String, dynamic> response =
      await state.firmwareController.deleteMultipleFirmwares(context: state.context,firmwareIds: firmwareIds);
      if (response.isNotEmpty) {
        if (response['success'] == true) {
          response['message'].toString().showSuccess(state.context);
          getCurrentFirmwareData();
        } else {
          response['message'].toString().showError(state.context);
        }
      }
    } catch (e) {
      S.of(state.context).noDataFound.showError(state.context);
    }finally{
      isDeleteAllLoading = false;
      state.firmwareController.update();
    }
  }

  Future<void> deleteMultipleDeployments(List<String> deploymentsIds) async {
    isDeleteAllLoading = true;
    state.firmwareController.update();
    try {
      Map<String, dynamic> response =
      await state.firmwareController.deleteMultipleDeployments(context: state.context,deploymentsIds: deploymentsIds);
      if (response.isNotEmpty) {
        final message = response['message'].toString();
        if (response['success'] == true) {
          message.showSuccess(state.context);
          getDeploymentsFirmwareData();
        } else {
          message.showError(state.context);
        }
      }
    } catch (e) {
      S.of(state.context).noDataFound.showError(state.context);
    }finally{
      isDeleteAllLoading = false;
      state.firmwareController.update();
    }
  }

  Future<void> deleteDeployment(String deploymentId) async {
    try {
      Map<String, dynamic> response =
      await state.firmwareController.deleteDeployment(deploymentId: deploymentId);
      if (response.isNotEmpty) {
        final message = response['message'].toString();
        if (response['success'] == true) {
          message.showSuccess(state.context);
          await getDeploymentsFirmwareData();
        } else {
          message.showError(state.context);
        }
      }
    } catch (e) {
      S.of(state.context).noDataFound.showError(state.context);
    }
  }

  //Firmware Custom Pagination Function

  Future<void> loadPreviousLogs(BuildContext context) async {
    if (fmPaginationHelper.canGoToPreviousPage) {
      fmPaginationHelper.setPage(fmPaginationHelper.currentPage - 1);
      if(isTableView) {
        fmPageController.goToPreviousPage();
      }
      state.firmwareController.update();
    }
  }

  Future<void> loadNextLogs(BuildContext context) async {
    if (fmPaginationHelper.canGoToNextPage) {
      fmPaginationHelper.setPage(fmPaginationHelper.currentPage + 1);
      if (fmPaginationHelper.currentPage >= fmPaginationHelper.totalPages) {
        firmWarePageOffset = firmwareFilesList.result.dataList.length ;
        await updateFirmwareDataOnNextPage();
      } else {
        if(isTableView) {
          fmPageController.goToNextPage();
        }
        state.firmwareController.update();

      }
    }
  }
  updateFirmwareDataOnNextPage() async {
    apiStatus = ApiStatus.loading;
    state.firmwareController.update();
    FirmwareFiles filesList = await state.firmwareController.getFirmwareFileList(
        context: state.context,
        pageOffset: firmWarePageOffset,
        perPageLimit: AppStringConstants.firmwarePerPageLimit);

    if (filesList.result.dataList.isNotEmpty) {
      firmwareFilesList.result.dataList.addAll(filesList.result.dataList);
      firmDataSource = CurrentFirmwareDataSource(
          context: state.context,
          firmwareList: firmwareFilesList.result.dataList,
          firmwarePageHelper: this);
      bool hasMoreData =
          filesList.result.dataList.length == AppStringConstants.firmwarePerPageLimit;
      fmPaginationHelper.updatePagination(firmwareFilesList.result.dataList.length,
          hasMore: hasMoreData, pageLimit: AppStringConstants.firmwarePerPageLimit);
        firmDataSource.notifyListeners();
    } else {
      fmPaginationHelper.setPage(fmPaginationHelper.currentPage - 1);
      fmPaginationHelper
          .updatePagination(firmwareFilesList.result.dataList.length, hasMore: false,pageLimit: AppStringConstants.firmwarePerPageLimit);
      Future.delayed(const Duration(milliseconds: 100)).then((value) {
        if(isTableView) {
          fmPageController.goToLastPage();
        }

          firmDataSource.notifyListeners();
      });
    }
     apiStatus = ApiStatus.success;
     state.firmwareController.update();
  }


  // Deployment Custom Pagination Function
  Future<void> deploymentLoadPreviousLogs(BuildContext context) async {
    if (depPaginationHelper.canGoToPreviousPage) {
      depPaginationHelper.setPage(depPaginationHelper.currentPage - 1);
      depPageController.goToPreviousPage();
      state.firmwareController.update();
    }
  }

  Future<void> deploymentLoadNextLogs(BuildContext context) async {
    if (depPaginationHelper.canGoToNextPage) {
      depPaginationHelper.setPage(depPaginationHelper.currentPage + 1);
      if (depPaginationHelper.currentPage >= depPaginationHelper.totalPages) {
        depPageOffset = deploymentList.deployments!.length ;
        await updateDeploymentDataOnNextPage();
      } else {
        depPageController.goToNextPage();
        state.firmwareController.update();

      }
    }
  }

  updateDeploymentDataOnNextPage() async {
    deploymentStatus = ApiStatus.loading;
    state.firmwareController.update();
    DeploymentList depList = await state.firmwareController.getDeploymentsList(
        context: state.context,
        pageOffset: depPageOffset,
        perPageLimit: AppStringConstants.deploymentPerPageLimit);

    if (depList.deployments!.isNotEmpty ) {
      deploymentList.deployments!.addAll(depList.deployments!);
      deploymentsDataSource = DeploymentsDataSource(
          deploymentsList: deploymentList.deployments!,
          firmwarePageHelper: this,
          context: state.context);
      bool hasMoreData =
          depList.deployments!.length == AppStringConstants.deploymentPerPageLimit;
      depPaginationHelper.updatePagination(deploymentList.deployments!.length,
          hasMore: hasMoreData, pageLimit: AppStringConstants.deploymentPerPageLimit);
      deploymentsDataSource.notifyListeners();
    } else {
      depPaginationHelper.setPage(depPaginationHelper.currentPage - 1);
      depPaginationHelper
          .updatePagination(deploymentList.deployments!.length, hasMore: false,pageLimit: AppStringConstants.deploymentPerPageLimit);
      Future.delayed(const Duration(milliseconds: 100)).then((value) {
        depPageController.goToLastPage();
        deploymentsDataSource.notifyListeners();
      });
    }
    deploymentStatus = ApiStatus.success;
    state.firmwareController.update();
  }

  applyDeployment(String deploymentId) async {
    try {
      Map<String, dynamic> response = await state.firmwareController
          .applyDeployment(context: state.context, deploymentId: deploymentId);

      if (response.isNotEmpty) {
        final message = response['message'].toString();
        if (response['success'] == true) {
           message.showSuccess(state.context);
          getDeploymentsFirmwareData();
        } else {
           message.showError(state.context);
        }
      }
    } catch (e) {
      S.of(state.context).socketExceptionMessage.showError(state.context);
    }
  }

  void dispose() {}
}
