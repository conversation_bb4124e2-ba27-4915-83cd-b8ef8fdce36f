import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/pages/firmware_management/deployments_page/mobile_deployments.dart';
import 'package:quantumlink_node/pages/firmware_management/firmware_page_helper.dart';

import '../../../app_import.dart';

class DeploymentsPage extends StatefulWidget {
  final FirmwarePageHelper firmwarePageHelper;
  const DeploymentsPage(  {super.key,required this.firmwarePageHelper});

  @override
  State<StatefulWidget> createState() => DeploymentsPageState();
}

class DeploymentsPageState extends State<DeploymentsPage> with TickerProviderStateMixin {
  FirmwarePageHelper? firmwarePageHelper;
  FirmwareController firmwareController = FirmwareController();


  @override
  void initState() {
    super.initState();
    firmwarePageHelper=widget.firmwarePageHelper;
    if(AppConfig.shared.isOpenFromBLE){
      firmwarePageHelper!.deploymentStatus = ApiStatus.loading;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        firmwarePageHelper!.getDeploymentsFirmwareData();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<FirmwareController>(
      init: FirmwareController(),
      builder: (FirmwareController controller) {
        firmwareController = controller;
        return getBody();
      },
    );
  }

  Widget getBody() {
    return getDeploymentView();
  }

  Widget getDeploymentView() {
    return ScreenLayoutTypeBuilder(
      builder: (context, screenType, constraints) {
        firmwarePageHelper!.screenLayoutType = screenType;
        MobileDeployments().autoSelectTableType(firmwarePageHelper);
        return SizedBox(
          width: double.infinity,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(child: getDeploymentContent())
            ],
          ),
        );
      },
    );
  }
  Widget getPageAppBar() {
    return Container(
      alignment: Alignment.centerLeft,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
              topRight: Radius.circular(getSize(7)), topLeft: Radius.circular(getSize(7))),
          border: Border.all(
            color: AppColorConstants.colorH2,
          ),
        color: buildTableAppbarColor()),
      padding: const EdgeInsets.all(10),
      child: Column(crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Row(
            mainAxisAlignment: firmwarePageHelper!.currantFilterDeploymentID.isNotEmpty
                ? MainAxisAlignment.spaceBetween
                : MainAxisAlignment.end,
            children: [
              if (firmwarePageHelper!.currantFilterDeploymentID.isNotEmpty)
                Flexible(
                  child: Card(
                    elevation: 3,
                    shape: RoundedRectangleBorder(
                      side: BorderSide(color: AppColorConstants.colorChart, width: 0.5),
                      borderRadius: const BorderRadius.all(
                          Radius.circular(18)), // Set a higher value for a circular shape
                    ),
                    child: Chip(
                      shape: RoundedRectangleBorder(
                        side: BorderSide(color: AppColorConstants.colorChart, width: 0.5),
                        borderRadius: const BorderRadius.all(
                            Radius.circular(18)), // Set a higher value for a circular shape
                      ),
                      backgroundColor: Colors.white,
                      label: AppText(
                        isSelectableText: false,
                        widget.firmwarePageHelper.currantFilterDeploymentID,
                        style: TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w600,
                            color: AppColorConstants.colorPrimary,
                            fontFamily: AppAssetsConstants.notoSans),
                      ),
                      deleteIcon: CircleAvatar(
                          maxRadius: 9,
                          backgroundColor: AppColorConstants.colorLightBlue,
                          child: Icon(Icons.close,
                              size: getSize(16), color: AppColorConstants.colorWhite)),
                      onDeleted: () {
                        firmwarePageHelper!.currantFilterDeploymentID = '';
                        firmwarePageHelper!.firmwareDeploymentIdList.clear();
                        firmwarePageHelper!.getDeploymentsFirmwareData();
                      },
                      deleteButtonTooltipMessage: S.of(context).clear,
                    ),
                  ),
                ),
              if (firmwarePageHelper!.screenLayoutType != ScreenLayoutType.mobile)
                MobileDeployments().selectTableTypeButtonView(firmwarePageHelper)
            ],
          ),
          if (firmwarePageHelper!.screenLayoutType == ScreenLayoutType.mobile)
            MobileDeployments().selectTableTypeButtonView(firmwarePageHelper)
        ],
      ),
    );
  }
  Widget getDeploymentContent() {
    if (firmwarePageHelper!.deploymentStatus == ApiStatus.loading) {
      return  ListView(
        physics: const ClampingScrollPhysics(),
        padding: const EdgeInsets.symmetric(horizontal: 15),
        children: [
          Container(
            height: 400,
            decoration: BoxDecoration(
              color: AppColorConstants.colorWhite,
              borderRadius: firmwarePageHelper!.currantFilterDeploymentID.isNotEmpty
                  ? BorderRadius.zero
                  : const BorderRadius.only(topRight: Radius.circular(8), topLeft: Radius.circular(8)),
              border: Border.all(color: AppColorConstants.colorH2, width: 1),
            ),
            child: const Align(alignment: Alignment.center, child: AppLoader()),
          ),
        ],
      );
    }
    return ListView(
      shrinkWrap: true,
      padding: const EdgeInsets.symmetric(horizontal: 15),
      physics: const ClampingScrollPhysics(),
      children: [
        getPageAppBar(),
        Container(
          decoration: firmwarePageHelper!.dataTableHelper.tableBorderDeco(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              getDeploymentBoardView(firmwarePageHelper!.deploymentsDataSource),
              SizedBox(height: getSize(20)),
              // Divider
              Container(
                height: 1,
                width: double.infinity,
                color: AppColorConstants.colorBlack12,
              ),
              // Action Buttons
              Container(
                padding: const EdgeInsets.only(left: 16, top: 15, bottom: 15),
                width: double.infinity,
                decoration: BoxDecoration(
                  color: AppColorConstants.colorBackgroundDark,
                  borderRadius: const BorderRadius.only(bottomRight: Radius.circular(5), bottomLeft: Radius.circular(5)),
                ),
                child:Column(crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row( mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Wrap(spacing: 8,runSpacing: 8,
                          children: [
                            if (AppConfig.shared.isOpenFromBLE) getCreateDeploymentButtonView(),
                            if(firmwarePageHelper!.screenLayoutType != ScreenLayoutType.mobile) getDeleteButtonView(),
                          ],
                        ),
                        if(firmwarePageHelper!.screenLayoutType != ScreenLayoutType.mobile) refreshButtonView(),
                      ],
                    ),
                    if (firmwarePageHelper!.screenLayoutType == ScreenLayoutType.mobile) ...[
                      const SizedBox(height: 8),
                      getDeleteButtonView(),
                      refreshButtonView()
                    ]
                  ],
                ),
              ),
            ],
          ),
        ),
        (firmwarePageHelper!.deploymentDatatableError != null)
            ? CommonAPIErrorView(rightPadding: 2,
            errorMessage:firmwarePageHelper!.deploymentDatatableError ?? "")
            : SizedBox(height: getSize(40))

      ],
    );
  }
Widget getDeploymentBoardView(DataTableSource dataSource){
    return  Container(
        width: double.infinity,
        decoration: BoxDecoration(color: AppColorConstants.colorWhite),
      child: getDeploymentTableView(dataSource),
    );
}
  Widget refreshButtonView() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Align(alignment: Alignment.centerRight, child: lastSeenView()),
        getRefreshButtonView(),
      ],
    );
  }
  Widget getDeleteButtonView() {
    return AppButton(
      loadingStatus:firmwarePageHelper!.isDeleteAllLoading ? ApiStatus.loading : ApiStatus.success,
      buttonHeight: 33,
      buttonWidth: 120,
      fontSize: 14.5,
      buttonRadius: 9,
      fontColor:
      firmwarePageHelper!.deploymentsDataSource.selectedRowCount >
          0
          ? AppColorConstants.colorWhite
          : AppColorConstants.colorH1Grey,
      borderColor:
      firmwarePageHelper!.deploymentsDataSource.selectedRowCount >
          0
          ? AppColorConstants.colorRedAccent
          : AppColorConstants.colorH2.withOpacity(0.5),
      buttonName: S.of(context).deleteSelected,
      onPressed: () {
        List<Deployment> deploymentIds = firmwarePageHelper?.deploymentsDataSource.deploymentsList
            .where((element) => (element.selected ?? false == true))
            .toList() ?? [];
        if (deploymentIds.isNotEmpty) {
          List<String> deploymentIdsList = deploymentIds.map((e) => e.id.toString()).toList();
          deleteDeploymentIdModalView(context, deploymentIdsList);
        }
      },
      fontFamily: AppAssetsConstants.openSans,
      buttonColor: firmwarePageHelper!.deploymentsDataSource.selectedRowCount > 0?AppColorConstants.
      colorRed : AppColorConstants.colorWhite,
    );
  }
  Widget getCreateDeploymentButtonView() {
    return AppButton(
      buttonHeight: 32,
      buttonWidth: 150,
      fontSize: 14.5,
      buttonRadius: 9,
      fontColor:
       AppColorConstants.colorWhite,

      borderColor:
    AppColorConstants.colorLightBlue,
      buttonName: S.of(context).createDeployment,
      onPressed: () {
        firmwarePageHelper!.state.deviceListDialog(context, null,
                (List<String> deviceList, bool isUpdateMode, FirmwareData data) async {
              CreateDeploymentItem createDeployment = CreateDeploymentItem(
                deviceEuis: deviceList,
                firmwareId: data.firmwareId,
                fileType: data.type,
                updateMode: isUpdateMode ? "auto" : "manual",
              );
              await firmwarePageHelper!.createDeployment(createDeployment);
            });
      },
      fontFamily: AppAssetsConstants.openSans,
      buttonColor: AppColorConstants.colorLightBlue,
    );
  }

  deleteDeploymentIdModalView(c, List<String> list) {
    return showDialog(
      context: c,
      builder: (context) {
        return AlertDialog(
          surfaceTintColor: AppColorConstants.colorWhite,
          backgroundColor: AppColorConstants.colorWhite,
          insetPadding: const EdgeInsets.symmetric(horizontal: 8.0),
          contentPadding: EdgeInsets.zero,
          titlePadding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
          title: getCustomAppBarWithClose("Delete ?"),
          content: StatefulBuilder(builder: (context, snapshot) {
            return Container(
              width: 450, // Adjust the width as needed
              height: MediaQuery.of(context).size.height * 0.3,
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Expanded(
                    child: ListView.builder(
                      shrinkWrap: true,
                      // Allow the ListView to shrink-wrap its contents
                      itemCount: list.length,
                      itemBuilder: (context, index) {
                        String deploymentId = list[index].toString();
                        return ListTile(
                          title: AppText(
                            ' ${index + 1}. $deploymentId ?',
                            style: const TextStyle(fontSize: 15,
                              fontFamily: AppAssetsConstants.openSans,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  getAppDivider(),
                ],
              ),
            );
          }),
          actions: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                AppButton(
                  borderColor: Colors.grey.withOpacity(0.5),
                  buttonHeight: 20,
                  buttonColor: Colors.grey.withOpacity(0.5),
                  buttonName: S.of(context).no,
                  fontFamily: AppAssetsConstants.openSans,
                  onPressed: () {
                    goBack();
                  },
                ),
                const SizedBox(width: 16),
                AppButton(
                  fontFamily: AppAssetsConstants.openSans,
                  buttonHeight: 20,
                  buttonName: S.of(context).yes,
                  onPressed: () {
                    goBack();
                    firmwarePageHelper!.deleteMultipleDeployments(list);
                  },
                ),
              ],
            )
          ],
        );
      },
    );
  }


  Widget lastSeenView() {
    if (firmwarePageHelper!.deploymentIsShowText) {
      return getTimeDurationView(
        refreshStatus: firmwarePageHelper!.deploymentStatus,
        updateTime: firmwarePageHelper!.deploymentLastUpdateTime,
        onTapTime: firmwarePageHelper!.deploymentOnTapTime,
        difference: firmwarePageHelper!.deploymentDifferenceTime,
      );
    } else {
      if (firmwarePageHelper!.deploymentLastUpdateTime != null) {
        return getLastSeenView(firmwarePageHelper!.deploymentLastUpdateTime);
      } else {
        return Container();
      }
    }
  }

  Widget getRefreshButtonView() {
    return AppRefresh(
        onPressed: () {
          if (firmwarePageHelper!.deploymentStatus != ApiStatus.loading) {
            firmwarePageHelper!.getDeploymentsFirmwareData(isUpdate: true);
          }
        },
        loadingStatus: firmwarePageHelper!.apiStatus);
  }

  Widget getDeploymentTableView(DataTableSource dataSource) {
  if  (!firmwarePageHelper!.isTableViewForDeployment) {
    return MobileDeployments().buildFirmwareFileList(context, firmwarePageHelper!);
  }
  int itemsPerPage = firmwarePageHelper!.dataTableHelper.getCurrentPageDataLength(
      firmwarePageHelper!.deploymentsDataSource.deploymentsList,
      firmwarePageHelper!.depCurrentPageIndex ,perPageLimit:  AppStringConstants.deploymentPerPageLimit);
  int recordsInPage = (firmwarePageHelper!.deploymentsDataSource.deploymentsList.length > AppStringConstants.deploymentPerPageLimit
      ? itemsPerPage
      : firmwarePageHelper!.deploymentsDataSource.deploymentsList.length);
    return SizedBox(
      height: (firmwarePageHelper!.deploymentsDataSource.deploymentsList.isNotEmpty)
          ? (recordsInPage * firmwarePageHelper!.depHeightOfDataTableCell) + (recordsInPage * 0.1) + 100
          : (recordsInPage * firmwarePageHelper!.depHeightOfDataTableCell) + (recordsInPage * 0.1) + 400,
      child: PaginatedDataTable2(
        columnSpacing: 10,
        rowsPerPage: AppStringConstants.deploymentPerPageLimit,
        initialFirstRowIndex: firmwarePageHelper!.depPaginationHelper.currentPage *
            AppStringConstants.deploymentPerPageLimit,
        controller: firmwarePageHelper!.depPageController,
        headingCheckboxTheme:
        CheckboxThemeData(side: BorderSide(width: 2, color: AppColorConstants.colorWhite)),
        headingTextStyle: firmwarePageHelper!.dataTableHelper.headingTextStyle(),
        wrapInCard: false,
        datarowCheckboxTheme:
        const CheckboxThemeData(side: BorderSide(width: 2, color: Colors.grey)),
        border: firmwarePageHelper!.dataTableHelper.tableBorder(),
        renderEmptyRowsInTheEnd: false,
        // ignore: deprecated_member_use
        headingRowColor: firmwarePageHelper!.dataTableHelper.headingRowColor(),
        source: dataSource,
        hidePaginator: true,
        minWidth: 1150,
        columns: getDeploymentsFirmTableColumns(),
        empty:firmwarePageHelper!.dataTableHelper.getEmptyTableContent(context),
        onSelectAll: firmwarePageHelper?.deploymentsDataSource.selectAll,
      ),
    );
  }


  List<DataColumn> getDeploymentsFirmTableColumns() {
    bool isTabletWidth = MediaQuery.of(context).size.width < 1025 ? true : false;
    return [
      DataColumn2(label: Center(child: AppText(S.of(context).select, style: const TextStyle(fontSize: 14))),fixedWidth: 50),
      DataColumn2(label: Center(child: AppText(S.of(context).id, style: const TextStyle(fontSize: 14))),fixedWidth:  90),
      DataColumn2(label: Center(child: AppText(S.of(context).fileName, style: const TextStyle(fontSize: 14))),fixedWidth: 230),
      DataColumn2(label: Center(child: AppText(S.of(context).type, style: const TextStyle(fontSize: 14))),fixedWidth: 150),

      DataColumn2(label: Center(child: AppText(S.of(context).version, style: const TextStyle(fontSize: 14))),fixedWidth: isTabletWidth ? 80 : 80),
      DataColumn2(label: Center(child: AppText(S.of(context).gwEUI, style: const TextStyle(fontSize: 14))),fixedWidth: 150),
      DataColumn2(label: Center(child: AppText(S.of(context).status, style: const TextStyle(fontSize: 14),)),fixedWidth:180),
      // DataColumn2(label: Center(child: AppText(S.of(context).summaryStatus, style: const TextStyle(fontSize: 14),)),fixedWidth: 150),
      DataColumn2(label: Center(child: AppText(S.of(context).deviceCount, style: const TextStyle(fontSize: 14),textAlign:TextAlign.center)),fixedWidth: isTabletWidth ? 60 : 70),
      //DataColumn2(label: Center(child: AppText(S.of(context).duration, style: const TextStyle(fontSize: 14))),fixedWidth: isTabletWidth ?  90 : 120),
      DataColumn2(label: AppText(S.of(context).createdOn, style: const TextStyle(fontSize: 14))),
      //DataColumn2(label: AppText(S.of(context).deleteBtn, style: const TextStyle(fontSize: 14))),
    ];
  }


  customPaginationArrowView(){
    return Container(height: 10);
    return  AppPaginationWidget(
      apiStatus:  firmwarePageHelper!.deploymentStatus,
      paginationHelper: firmwarePageHelper!.depPaginationHelper,
      onLoadNext: () async {
        await firmwarePageHelper!.deploymentLoadNextLogs(context);
      },
      onLoadPrevious: () async {
        await firmwarePageHelper!.deploymentLoadPreviousLogs(context);
        firmwareController.update();
      },
      onGoToFirstPage: () {
        firmwarePageHelper!.depPaginationHelper.setPage(0);
        firmwarePageHelper!.depPageController.goToFirstPage();
        firmwareController.update();
      },
      onGoToLastPage: () {
        firmwarePageHelper!.depPageController.goToLastPage();
        firmwareController.update();
      },
      itemsPerPage: AppStringConstants.deploymentPerPageLimit,
      onChanged: (value) {
        AppStringConstants.deploymentPerPageLimit = int.parse(value);
        if (firmwarePageHelper!.deploymentStatus != ApiStatus.loading) {
          firmwarePageHelper!.depPaginationHelper.setPage(0);
          firmwarePageHelper!.depPageController.goToFirstPage();
          firmwarePageHelper!.depPageOffset = 0;
          firmwarePageHelper!.getDeploymentsFirmwareData();
        }
        firmwareController.update();
        firmwarePageHelper!.deploymentsDataSource.notifyListeners();
      },
    );
  }

}
