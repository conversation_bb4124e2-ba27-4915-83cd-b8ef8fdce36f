import 'package:quantumlink_node/app_import.dart';

class MobileDeployments {
  TextStyle textStyle = TextStyle(
      fontFamily: AppAssetsConstants.roboto,
      fontSize: getSize(14),
      fontWeight: getMediumFontWeight());
  Widget buildFirmwareFileList(BuildContext context, FirmwarePageHelper firmwarePageHelper) {
    if (firmwarePageHelper.deploymentsDataSource.rowCount == 0) {
      return SizedBox(
        height: 350,
        child: firmwarePageHelper.dataTableHelper.getEmptyTableContent(context),
      );
    }
    List<Deployment> fullList = firmwarePageHelper.deploymentsDataSource.deploymentsList;

    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: fullList.length,
      itemBuilder: (context, index) {
        Deployment deployment = fullList[index];
        DataRow? dataRow = firmwarePageHelper.deploymentsDataSource.getRow(index);
        String id = deployment.id ?? "";
        String gwEui = deployment.gwEui ?? "-";
        String version = deployment.firmwareFile!.version ?? "";
        bool isReboot = deployment.rebootSent ?? false;
        String deviceCount = isReboot
            ? "${deployment.upgradedDeviceCount ?? 0}/${deployment.totalDeviceCount ?? 0}"
            : "${deployment.totalDeviceCount ?? 0}";
        String productName = deployment.firmwareFile?.productName ?? "";

        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 3, horizontal: 5),
          child: CustomListTile(
            onTap: () {},
            index: index,
            titleWidget: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                dataRow?.cells[0].child ?? Container(),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          InkWell(
                            onTap: () => firmwarePageHelper.addTabTableOnTap(deployment),
                            child: Center(
                              child: Row(
                                children: [
                                  ValueListenableBuilder<bool>(
                                    valueListenable: firmwarePageHelper
                                        .deploymentsDataSource.hoveredIndices[index],
                                    builder: (context, isHovered, child) {
                                      return MouseRegion(
                                        onEnter: (_) {
                                          firmwarePageHelper.deploymentsDataSource
                                              .hoveredIndices[index].value = true;
                                        },
                                        onExit: (_) {
                                          firmwarePageHelper.deploymentsDataSource
                                              .hoveredIndices[index].value = false;
                                        },
                                        child: AppText(
                                          "..${getLastNChars(id, 17)}",
                                          maxLines: 2,
                                          isSelectableText: false,
                                          style: TextStyle(
                                            fontWeight: FontWeight.w600,
                                            fontFamily: AppAssetsConstants.openSans,
                                            color: AppColorConstants.colorLightBlue,
                                            fontSize: getSize(14),
                                            decoration: isHovered
                                                ? TextDecoration.underline
                                                : TextDecoration.none,
                                            decorationThickness: 4,
                                            decorationColor: AppColorConstants.colorLightBlue,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                  SizedBox(width: getSize(5)),
                                  Icon(
                                    Icons.info,
                                    color: AppColorConstants.colorLightBlue,
                                    size: 18,
                                  )
                                ],
                              ),
                            ),
                          ),
                          SizedBox(
                            width: getSize(10),
                          ),
                          const Spacer(),
                          GestureDetector(
                              onTap: () {
                                _showDialog(
                                  context,
                                  id,
                                  dialogContent(
                                    context,
                                    type: productName,
                                    gwEUI: gwEui,
                                    version: version,
                                    deviceCount: deviceCount,
                                  ),
                                );
                              },
                              child: const Icon(Icons.info,size: 20,))
                        ],
                      ),
                      SizedBox(height: getSize(7)),
                      dataRow?.cells[2].child ?? Container(),
                      SizedBox(height: getSize(5)),
                      dataRow?.cells[6].child ?? Container(),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget selectTableTypeButtonView(FirmwarePageHelper? firmwarePageHelper) {
    return firmwarePageHelper?.dataTableHelper.selectTableTypeButtonView(
      isTableType: firmwarePageHelper.isTableViewForDeployment,
      onPressed: () {
        firmwarePageHelper.isTableViewForDeployment = !firmwarePageHelper.isTableViewForDeployment;
        firmwarePageHelper.state.firmwareController.update();
      },
    );
  }

  Color? getDeploymentSummaryUpgradeStatus(String? status) {
    switch (status) {
      case AppStringConstants.manualPending:
        return AppColorConstants.colorOrange;
      case AppStringConstants.manual:
        return AppColorConstants.colorOrange;
      case AppStringConstants.auto:
        return AppColorConstants.colorDotLine;
      case AppStringConstants.success:
        return AppColorConstants.colorSuccessGreen;
      case AppStringConstants.downloadError:
        return AppColorConstants.colorRedLight.withOpacity(0.7);
      case AppStringConstants.upgradeFail:
        return AppColorConstants.colorRedLight.withOpacity(0.7);
      default:
        return AppColorConstants.colorRedLight.withOpacity(0.7);
    }
  }

  void _showDialog(BuildContext context, String deviceEui, Widget contentView) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          surfaceTintColor: AppColorConstants.colorWhite,
          backgroundColor: AppColorConstants.colorWhite,
          insetPadding: EdgeInsets.symmetric(horizontal: getSize(10)),
          titlePadding: EdgeInsets.zero,
          actionsPadding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
          title: getCustomAppBarWithClose(deviceEui),
          content: contentView,
        );
      },
    );
  }
  Widget dialogContent(
      BuildContext context, {
        required String type,
        required String version,
        required String gwEUI,
        required String deviceCount,
      }) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(5.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          mainAxisSize: MainAxisSize.min,
          children: [
            dialogTitleAndValueView(context, S.of(context).type, type),
            SizedBox(height: getSize(7)),
            dialogTitleAndValueView(context, S.of(context).version, version),
            SizedBox(height: getSize(7)),
            dialogTitleAndValueView(context, S.of(context).gwEUI, gwEUI),
            SizedBox(height: getSize(7)),
            dialogTitleAndValueView(context, S.of(context).deviceCount, deviceCount),
            SizedBox(height: getSize(7)),
          ],
        ),
      ),
    );
  }

  Widget dialogTitleAndValueView(BuildContext context, String title, String value) {
    return Row(
      children: [
        AppText(
          title,
          style: TextStyle(
            fontWeight: FontWeight.w800,
            fontFamily: AppAssetsConstants.openSans,
            color: AppColorConstants.colorBlack,
            fontSize: getSize(14),
          ),
        ),
        SizedBox(
          width: getSize(10),
        ),
        const Spacer(),
        AppText(value, style: TextStyle(
          fontWeight: getMediumBoldFontWeight(),
          fontFamily: AppAssetsConstants.openSans,
          color: AppColorConstants.colorBlack,
          fontSize: getSize(14),
        ))
      ],
    );
  }
  void autoSelectTableType(FirmwarePageHelper? firmwarePageHelper) {
    if (firmwarePageHelper?.previousLayoutType != null) {
      ScreenLayoutType currentLayoutType = firmwarePageHelper!.screenLayoutType;
      bool isMobile = currentLayoutType != ScreenLayoutType.desktop;
      if (firmwarePageHelper.previousLayoutType != currentLayoutType) {
        firmwarePageHelper.isTableForAddDialog = !isMobile;
        firmwarePageHelper.isTableViewForDeployment = !isMobile;
        firmwarePageHelper.isTableViewForFirmwareDetails = !isMobile;
        firmwarePageHelper.previousLayoutType = currentLayoutType;
      }
    }
  }
}
