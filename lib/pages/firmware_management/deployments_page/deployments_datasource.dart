// ignore_for_file: deprecated_member_use
import 'package:quantumlink_node/app_import.dart';
class DeploymentsDataSource extends DataTableSource {
  int selectedCount = 0;
  List<Deployment> deploymentsList;
  FirmwarePageHelper firmwarePageHelper;
  final BuildContext context;
  List<ValueNotifier<bool>> hoveredIndices=[];

  DeploymentsDataSource(
      {required this.context, required this.deploymentsList, required this.firmwarePageHelper}){
    hoveredIndices = List.generate(deploymentsList.length, (_) =>  ValueNotifier(false));
  }

  setFirmware(List<Deployment> deploymentsList) {
    this.deploymentsList = deploymentsList;
    notifyListeners();
  }

  @override
  DataRow2? getRow(int index) {
    if (index >= deploymentsList.length) {
      return null;
    }

    return DataRow2.byIndex(
        index: index,
        selected: deploymentsList[index].selected ?? false,
        cells: getCells(index),
        color: index.isEven
            ? MaterialStateProperty.all(AppColorConstants.colorWhite)
            : MaterialStateProperty.all(AppColorConstants.colorBackgroundDark));
  }

  List<DataCell> getCells(int index) {
    TextStyle textStyle = TextStyle(
        fontFamily: AppAssetsConstants.roboto,
        fontSize: getSize(14),
        fontWeight: getMediumFontWeight());

    final dessert = deploymentsList[index];

    String id = dessert.id ?? "";
    String productName = dessert.firmwareFile?.productName ?? "";
    String createdAt =  dessert.createdAt != null ? formatIsoDate("${dessert.createdAt ?? ""}") : '';
    String fileName = dessert.firmwareFile!.filename ?? "";
    bool isReboot = dessert.rebootSent ?? false;
    String deviceCount = isReboot
        ? "${dessert.upgradedDeviceCount ?? 0}/${dessert.totalDeviceCount ?? 0}"
        : "${dessert.totalDeviceCount ?? 0}";
    String version = dessert.firmwareFile!.version ?? "";
    String summaryStatus = dessert.summaryStatus ?? "";
    String summaryUpgradeStatus = dessert.summaryUpgradeStatus ?? "";
    String gwEui = dessert.gwEui ?? "-";
    double pctComplete =dessert.pctComplete ??0.0;

    bool isReadyForUpgrade =pctComplete == 100 && dessert.enableUpgrade && !isReboot;
    bool isProgressFail= pctComplete == 0 && summaryStatus.contains(AppStringConstants.fail);

    List<DataCell> cells = [
      DataCell(Checkbox(
          shape: RoundedRectangleBorder(
              side: BorderSide(color: AppColorConstants.colorPrimary),
              borderRadius: BorderRadius.circular(3)),
          activeColor: AppColorConstants.colorPrimary,
          checkColor: AppColorConstants.colorWhite,
          value: dessert.selected ?? false,
          onChanged:(getSummaryStatusType(summaryStatus) == SummaryStatusType.success ||
              getSummaryStatusType(summaryStatus) == SummaryStatusType.failed ||
              getSummaryStatusType(summaryStatus) == SummaryStatusType.partialSuccess)
              ? (value) {
                  if (deploymentsList[index].selected != value) {
                    selectedCount += value! ? 1 : -1;
                    assert(selectedCount >= 0);
                    deploymentsList[index].selected = value;
                    notifyListeners();
                  }
                  firmwarePageHelper.state.firmwareController.update();
                }
              : null)),
      DataCell(InkWell(onTap: () =>  firmwarePageHelper.addTabTableOnTap(dessert),
        child: Center(
          child: Row(
            children: [
              ValueListenableBuilder<bool>(
                valueListenable: hoveredIndices[index],
                builder: (context, isHovered, child) {
                  return MouseRegion(
                    onEnter: (_) {
                      hoveredIndices[index].value = true;
                    },
                    onExit: (_) {
                      hoveredIndices[index].value = false;
                    },
                    child: AppText(
                      "..${getLastNChars(id, 5)}",
                      maxLines: 2,
                      isSelectableText: false,
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontFamily: AppAssetsConstants.openSans,
                        color: AppColorConstants.colorLightBlue,
                        fontSize: getSize(14),
                        decoration: isHovered ? TextDecoration.underline : TextDecoration.none,
                        decorationThickness: 4,
                        decorationColor: AppColorConstants.colorLightBlue,
                      ),
                    ),
                  );
                },
              ),
              SizedBox(width: getSize(5)),
              Icon(Icons.info, color: AppColorConstants.colorLightBlue,size: 18,)
            ],
          ),
        ),
      )),
      DataCell(AppText(fileName, style: textStyle)),
      DataCell(Padding(
        padding: const EdgeInsets.only(left: 10.0),
        child: AppText(productName, style: textStyle),
      )),
      DataCell(Center(child: AppText(version, style: textStyle))),
      DataCell(Center(child: AppText(gwEui, style: textStyle))),
      DataCell(
        Row(
          children: [
            Container(
              alignment: Alignment.center,
              child: progressIndicatorView(pctComplete,summaryStatus),
            ),
            Container(
              padding: const EdgeInsets.only(left: 8),
              alignment: Alignment.center,
              child: IgnorePointer(
                ignoring: (summaryUpgradeStatus == AppStringConstants.manualPending &&
                    isReadyForUpgrade)
                    ? false
                    : true,
                child: InkWell(
                  onTap: () {
                    DialogUtils().confirmationDialog(
                      context,
                      "${dessert.id}",
                      S.of(context).upgradeConfirmationMessage,
                      S.of(context).yes,
                      S.of(context).no,
                          () async {
                        goBack();
                        dessert.isUpgradeLoading = true;
                        notifyListeners();
                        await firmwarePageHelper.applyDeployment(dessert.id);
                        dessert.isUpgradeLoading = false;
                        notifyListeners();
                      },
                          () => goBack(),
                    );
                  },
                  child: dessert.isUpgradeLoading
                      ? const SizedBox(width: 32, child: AppLoader())
                      : AppImageAsset(
                          image: upgradeStatusImage(summaryStatus),
                          width: 24,
                        ),
                ),
              ),
            )
          ],
        ),
      ),
      // DataCell(Center(child: AppText(summaryStatus, style: textStyle,textAlign: TextAlign.center))),
      DataCell(Center(child: AppText(deviceCount, style: textStyle))),
     // DataCell(Center(child: AppText(duration, style: textStyle))),
      DataCell(AppText(createdAt, style: textStyle)),
      /*DataCell(
        Padding(padding: EdgeInsets.only(left: getSize(5)),
          child: (getSummaryStatusType(summaryStatus) == SummaryStatusType.success ||
                  getSummaryStatusType(summaryStatus) == SummaryStatusType.failed ||
                  getSummaryStatusType(summaryStatus) == SummaryStatusType.partialSuccess)
              ? InkWell(
                  onTap: () {
                    firmwarePageHelper.state.deleteDialogView(
                      context,
                      () => firmwarePageHelper.deleteDeployment(dessert.id),
                      fileName,
                    );
                  },
                  child: const AppImageAsset(width: 25,image: AppAssetsConstants.deleteEnabledIcon))
              :  AppImageAsset(width: 25,image: AppAssetsConstants.deleteEnabledIcon,color: AppColorConstants.colorH2.withOpacity(0.8)),
        ),
      ),*/
    ];
    return cells;
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => deploymentsList.length;

  @override
  int get selectedRowCount => selectedCount;

  void selectAll(bool? checked) {
    for (final dessert in deploymentsList) {
      dessert.selected = checked ?? false;
    }
    selectedCount = (checked ?? false) ? deploymentsList.length : 0;
    firmwarePageHelper.state.firmwareController.update();
    notifyListeners();
  }

  Color? getDeploymentSummaryUpgradeStatus(String? status) {
    switch (status) {
      case AppStringConstants.manualPending:
        return AppColorConstants.colorOrange;
      case AppStringConstants.manual:
        return AppColorConstants.colorOrange;
      case AppStringConstants.auto:
        return AppColorConstants.colorDotLine;
      case AppStringConstants.success:
        return AppColorConstants.colorSuccessGreen;
      case AppStringConstants.downloadError:
        return AppColorConstants.colorRedLight.withOpacity(0.7);
      case AppStringConstants.upgradeFail:
        return AppColorConstants.colorRedLight.withOpacity(0.7);
      default:
        return AppColorConstants.colorRedLight
            .withOpacity(0.7); // or handle the default case accordingly
    }
  }
  Color getProgressSummaryStatus(String? status) {
    switch (status) {
      case AppStringConstants.success:
        return AppColorConstants.colorGreenBright;
      case AppStringConstants.inProgress:
        return AppColorConstants.colorGreenBright;
      case AppStringConstants.readyForUpgrade:
        return AppColorConstants.colorOrange;
      case AppStringConstants.partialSuccess:
        return AppColorConstants.colorGreenBright;
      case AppStringConstants.fail:
        return AppColorConstants.colorRedLight.withOpacity(0.7);
      default:
        return AppColorConstants.colorRedLight
            .withOpacity(0.7); // or handle the default case accordingly
    }
  }

  String upgradeStatusImage(String? status){
    switch (status) {
      case AppStringConstants.readyForUpgrade:
        return AppAssetsConstants.playIcon;
      case AppStringConstants.partialSuccess:
        return AppAssetsConstants.errorIcon;
      case AppStringConstants.success:
        return AppAssetsConstants.successIcon;
      default:
        return ""; // or handle the default case accordingly
    }
  }

  Widget progressIndicatorView(double pctComplete , String summaryStatus) {
    return CustomCardLinearPercentIndicator(
      width: 120,
      lineHeight: 22,
      percent: pctComplete / 100,
      labelText: summaryStatus,
      progressColor: getProgressSummaryStatus(summaryStatus),
      backgroundColor: Colors.grey.shade300,
      labelStyle: TextStyle(
        fontWeight: FontWeight.bold,
        fontSize: 12,
        color:AppColorConstants.colorBlack,
      ),
      barRadius: const Radius.circular(10),
      elevation: 5,
      borderRadius: const BorderRadius.all(Radius.circular(12)),
    );
  }
}