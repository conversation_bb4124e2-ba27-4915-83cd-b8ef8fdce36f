import 'package:quantumlink_node/app_import.dart';

class MobileFirmware {
  DataTableHelper dataTableHelper = DataTableHelper();

  Widget buildFirmwareFileList(BuildContext context, FirmwarePageHelper firmwarePageHelper) {

    List<FirmwareData> fullList = firmwarePageHelper.firmDataSource.firmwareList;
    List<FirmwareData> paginatedList = fullList
        .skip(firmwarePageHelper.fmPaginationHelper.currentPage *
            AppStringConstants.firmwarePerPageLimit)
        .take(AppStringConstants.firmwarePerPageLimit)
        .toList();

    if (firmwarePageHelper.firmDataSource.rowCount == 0 || (paginatedList.isEmpty)) {
      return SizedBox(
        height: 350,
        child: firmwarePageHelper.dataTableHelper.getEmptyTableContent(context),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      itemCount: paginatedList.length,
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        FirmwareData firmwareData = paginatedList[index];
        String fileName = firmwareData.filename ?? "";
        String productName = firmwareData.productName ?? "";
        String version = firmwareData.version ?? "";
        String fileSize = firmwareData.size.toString();
        bool isDeleted = firmwareData.deployments.isEmpty;
        bool isSigned = firmwareData.signed ?? false;
        String deviceCount = firmwareData.deployments.length.toString();
        String uploadDate =
            firmwareData.uploadedAt != null ? getUtcTimeZone(firmwareData.uploadedAt) : '';

        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 3, horizontal: 5),
          child: CustomListTile(
            onTap: () {
              firmwarePageHelper.deploymentsDataSource.getRow(index)?.onTap!();
            },
            index: index,
            titleWidget: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Checkbox(
                    shape: RoundedRectangleBorder(
                        side: BorderSide(color: AppColorConstants.colorPrimary),
                        borderRadius: BorderRadius.circular(3)),
                    activeColor: AppColorConstants.colorPrimary,
                    checkColor: AppColorConstants.colorWhite,
                    value: firmwareData.selected ?? false,
                    onChanged: (firmwareData.deployments.isEmpty)
                        ? (value) {
                            if (firmwareData.selected != value) {
                              firmwareData.selected = value;
                              firmwarePageHelper.state.firmwareController.update();
                            }
                          }
                        : null),
                SizedBox(width: getSize(10)),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween, // Ensures the icon is at the end
                        children: [
                          Expanded(
                            // Ensures the text wraps properly
                            child: AppText(
                              fileName,
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                fontFamily: AppAssetsConstants.openSans,
                                fontSize: getSize(14),
                            ),
                          ),
                          ),
                          if(isSigned) Padding(
                            padding: const EdgeInsets.only(left: 3,top: 3),
                            child: Icon(
                              Icons.verified_user_rounded,
                              color: AppColorConstants.colorGreen2,
                              size: 16,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: getSize(3)),
                      AppText(productName,
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontFamily: AppAssetsConstants.openSans,
                            fontSize: getSize(14),
                          )),
                      SizedBox(height: getSize(3)),
                      AppText(version,
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontFamily: AppAssetsConstants.openSans,
                            fontSize: getSize(14),
                          )),
                      SizedBox(height: getSize(3)),
                    ],
                  ),
                ),
                Column(
                  children: [
                    Container(
                      height: 30,
                      alignment: AlignmentDirectional.centerStart,
                      child: InkWell(
                        onTap: () {
                          firmwarePageHelper.state.deviceListDialog(context, firmwareData,
                              (List<String> deviceList, bool isUpdateMode ,FirmwareData data) async {
                            CreateDeploymentItem createDeployment = CreateDeploymentItem(
                              deviceEuis: deviceList,
                              firmwareId: firmwareData.firmwareId,
                              fileType: firmwareData.type,
                              updateMode: isUpdateMode ? "auto" : "manual",
                            );
                            await firmwarePageHelper.createDeployment(createDeployment);
                          });
                        },
                        child:  Padding(
                          padding: EdgeInsets.only(left: 3.0),
                          child: AppImageAsset(
                            image: AppAssetsConstants.addListIcon,
                            color: AppColorConstants.colorLightBlue,
                            height: 25,
                            width: 25,
                          ),
                        ),
                      ),
                    ),
                    isDeleted
                        ? InkWell(
                            onTap: () {
                              firmwarePageHelper.state.deleteDialogView(
                                  context,
                                  () => firmwarePageHelper.deleteFirmware(firmwareData.firmwareId),
                                  fileName);
                            },
                            child: const AppImageAsset(
                                width: 25, height: 25, image: AppAssetsConstants.deleteEnabledIcon))
                        : AppImageAsset(
                            width: 25,
                            height: 25,
                            image: AppAssetsConstants.deleteEnabledIcon,
                            color: AppColorConstants.colorH2.withOpacity(0.8)),
                    GestureDetector(
                        onTap: () {
                          _showDialog(
                            context,
                            fileName,
                            dialogContent(
                              context,
                              productName: productName,
                              associateDeployments: deviceCount,
                              version: version,
                              size: fileSize,
                              uploadDate: uploadDate,
                            ),
                          );
                        },
                        child: const Icon(Icons.info))
                  ],
                )
              ],
            ),
          ),
        );
      },
    );
  }

  Widget buildCreateDeployment(BuildContext context, FirmwarePageHelper firmwarePageHelper) {
    if (firmwarePageHelper.amplifierFirmwareDatasource.rowCount == 0) {
      return SizedBox(
        height: 350,
        child: firmwarePageHelper.dataTableHelper.getEmptyTableContent(context),
      );
    }
    List<DeviceItem> fullList = firmwarePageHelper.amplifierFirmwareDatasource.list;

    return Column(
      children: [
        Expanded(
          child: NotificationListener<ScrollNotification>(
            onNotification: (ScrollNotification scrollInfo) {
              firmwarePageHelper.handleDeviseListPaginationScroll(scrollInfo);
              return false;
            },
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              scrollDirection: Axis.vertical,
              physics: const ClampingScrollPhysics(),
              itemCount: fullList.length,
              itemBuilder: (context, index) {
                DeviceItem deviceItem =
            fullList[index]; // amplifierPageHelper.amplifierDataSource.list[index];
        DataRow dataRow = firmwarePageHelper.amplifierFirmwareDatasource.getRow(index);
        DetectedStatusType? detectedStatusType = getDetectedStatusType(deviceItem.status);
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 3, horizontal: 5),
          child: CustomListTile(
            onTap: () {},
            index: index,
            titleWidget: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    SizedBox(
                      height: 15,
                      width: 15,
                      child: Checkbox(
                        shape: RoundedRectangleBorder(
                            side: BorderSide(color: AppColorConstants.colorPrimary),
                            borderRadius: BorderRadius.circular(3)),
                        activeColor: AppColorConstants.colorPrimary,
                        checkColor: AppColorConstants.colorWhite,
                        value: deviceItem.selected ?? false,
                        onChanged: detectedStatusType == DetectedStatusType.online
                            ? (value) {
                          if (deviceItem.selected != value) {
                            if (deviceItem.selected == true) {
                              deviceItem.selected = value;
                            } else {
                              firmwarePageHelper.amplifierFirmwareDatasource.selectedCount +=
                              value! ? 1 : -1;
                              assert(firmwarePageHelper.amplifierFirmwareDatasource.selectedCount >=
                                  0);
                              deviceItem.selected = value;
                            }
                          }
                          firmwarePageHelper.amplifierFirmwareDatasource.onSelectChanged.call();
                          firmwarePageHelper.state.firmwareController.update();
                        }
                            : null,
                      ),
                    ),
                    const Spacer(),
                    dataRow.cells[4].child,
                  ],
                ),
                if(deviceItem.deviceEui.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 7.0),
                    child: Row(
                      children: [
                        AppText(
                          S.of(context).deviceEUI,
                          textAlign: TextAlign.start,
                          style: TextStyle(
                            fontWeight: FontWeight.w800,
                            fontFamily: AppAssetsConstants.openSans,
                            color: AppColorConstants.colorBlack,
                            fontSize: getSize(14),
                          ),
                        ),
                        const Spacer(),
                        AppText(deviceItem.deviceEui,
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontFamily: AppAssetsConstants.openSans,
                              fontSize: getSize(14),
                            )),
                      ],
                    ),
                  ),
                if(deviceItem.deviceType.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 7.0),
                    child: Row(
                      children: [
                        AppText(
                          S.of(context).type,
                          textAlign: TextAlign.start,
                          style: TextStyle(
                            fontWeight: FontWeight.w800,
                            fontFamily: AppAssetsConstants.openSans,
                            color: AppColorConstants.colorBlack,
                            fontSize: getSize(14),
                          ),
                        ),
                        Expanded(
                          child: AppText(deviceItem.deviceType,textAlign: TextAlign.right,
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                fontFamily: AppAssetsConstants.openSans,
                                fontSize: getSize(14),
                              )),
                        ),
                      ],
                    ),
                  ),
                if (deviceItem.ampFwVersion.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 7.0),
                    child: Row(
                      children: [
                        AppText(
                          S.of(context).ampFwVersion,
                          textAlign: TextAlign.start,
                          style: TextStyle(
                            fontWeight: FontWeight.w800,
                            fontFamily: AppAssetsConstants.openSans,
                            color: AppColorConstants.colorBlack,
                            fontSize: getSize(14),
                          ),
                        ),
                        const Spacer(),
                        AppText(deviceItem.ampFwVersion,
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontFamily: AppAssetsConstants.openSans,
                              fontSize: getSize(14),
                            )),

                      ],
                    ),
                  ),
                if (deviceItem.xponderFwVersion.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 7.0),
                    child: Row(
                      children: [
                        AppText(
                          S.of(context).transponderFwVersion,
                          textAlign: TextAlign.start,
                          style: TextStyle(
                            fontWeight: FontWeight.w800,
                            fontFamily: AppAssetsConstants.openSans,
                            color: AppColorConstants.colorBlack,
                            fontSize: getSize(14),
                          ),
                        ),
                        const Spacer(),
                        AppText(deviceItem.xponderFwVersion,
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontFamily: AppAssetsConstants.openSans,
                              fontSize: getSize(14),
                            )),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        );
      },
            ),
          ),
        ),
        if (firmwarePageHelper.isLoadingMore)
          Container(
            height: 50,
            width: double.infinity,
            padding: const EdgeInsets.all(8),
            child: const Center(child: AppLoader()),
          ),
      ],
    );
  }

  Widget selectTableTypeButtonView(FirmwarePageHelper? firmwarePageHelper) {
    return firmwarePageHelper?.dataTableHelper.selectTableTypeButtonView(
      isTableType: firmwarePageHelper.isTableView,
      onPressed: () {
        firmwarePageHelper.isTableView = !firmwarePageHelper.isTableView;
        firmwarePageHelper.state.firmwareController.update();
      },
    );
  }

  Widget selectTableTypeButtonViewForAddDialog(FirmwarePageHelper? firmwarePageHelper) {
    return firmwarePageHelper?.dataTableHelper.selectTableTypeButtonView(
      isTableType: firmwarePageHelper.isTableForAddDialog,
      onPressed: () {
        firmwarePageHelper.isTableForAddDialog = !firmwarePageHelper.isTableForAddDialog;
        firmwarePageHelper.state.firmwareController.update();
      },
    );
  }

  void autoSelectTableType(FirmwarePageHelper? firmwarePageHelper) {
    if (firmwarePageHelper?.previousLayoutType != null) {
      ScreenLayoutType currentLayoutType = firmwarePageHelper!.screenLayoutType;
      bool isMobile = currentLayoutType != ScreenLayoutType.desktop;
      if (firmwarePageHelper.previousLayoutType != currentLayoutType) {
        firmwarePageHelper.isTableView = !isMobile;
        firmwarePageHelper.isTableForAddDialog = !isMobile;
        firmwarePageHelper.isTableViewForDeployment = !isMobile;
        firmwarePageHelper.isTableViewForFirmwareDetails = !isMobile;
        firmwarePageHelper.previousLayoutType = currentLayoutType;
      }
    }
  }

  Widget dialogContent(
    BuildContext context, {
    required String productName,
    required String version,
    required String size,
    required String uploadDate,
    required String associateDeployments,
  }) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(5.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          mainAxisSize: MainAxisSize.min,
          children: [
            dialogTitleAndValueView(context, S.of(context).type, productName),
            SizedBox(height: getSize(7)),
            dialogTitleAndValueView(context, S.of(context).version, version),
            SizedBox(height: getSize(7)),
            dialogTitleAndValueView(context, S.of(context).size, size),
            SizedBox(height: getSize(7)),
            dialogTitleAndValueView(context, S.of(context).uploadDate, uploadDate),
            SizedBox(height: getSize(7)),
            SizedBox(height: getSize(7)),
            dialogTitleAndValueView(
                context, S.of(context).associateDeployments, associateDeployments),
          ],
        ),
      ),
    );
  }

  Widget dialogTitleAndValueView(BuildContext context, String title, String value) {
    return Row(
      children: [
        AppText(
          title,
          style: TextStyle(
            fontWeight: FontWeight.w800,
            fontFamily: AppAssetsConstants.openSans,
            color: AppColorConstants.colorBlack,
            fontSize: getSize(14),
          ),
        ),
        SizedBox(
          width: getSize(10),
        ),
        const Spacer(),
        AppText(value, style: dataTableHelper.dataRowTextStyle)
      ],
    );
  }

  void _showDialog(BuildContext context, String deviceEui, Widget contentView) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          surfaceTintColor: AppColorConstants.colorWhite,
          backgroundColor: AppColorConstants.colorWhite,
          insetPadding: EdgeInsets.symmetric(horizontal: getSize(10)),
          titlePadding: EdgeInsets.zero,
          actionsPadding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
          title: getCustomAppBarWithClose(deviceEui),
          content: contentView,
        );
      },
    );
  }
}
