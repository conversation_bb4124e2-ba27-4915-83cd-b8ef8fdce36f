import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/controller/ss_id_report_controller.dart';
import 'package:quantumlink_node/pages/ss_id_report/ss_id_report_page_helper.dart';

class SsIdReportPage extends StatefulWidget {
  final Key key;

  const SsIdReportPage({required this.key}) : super(key: key);

  @override
  State<SsIdReportPage> createState() => SsIdReportPageState();
}

class SsIdReportPageState extends State<SsIdReportPage> {
  SSIdReportPageHelper? ssIdReportPageHelper;
  late ScreenLayoutType screenLayoutType;
  late SSIdReportController ssIdReportController;

  @override
  Widget build(BuildContext context) {
    ssIdReportPageHelper ?? (ssIdReportPageHelper = SSIdReportPageHelper(this));
    buildContext = context;
    return GetBuilder(
      init: SSIdReportController(),
      builder: (SSIdReportController controller) {
        ssIdReportController = controller;
        return ScreenLayoutTypeBuilder(
          builder: (context, screenType, constraints) {
            screenLayoutType = screenType;
            return AnnotatedRegion<SystemUiOverlayStyle>(
              value: getAppStatusBar(),
              child: Scaffold(
                appBar: AppBar(
                  leading: Container(),
                  toolbarHeight: getSize(80),
                  title: SizedBox(height: getSize(60), child: AppImageAsset(image: AppAssetsConstants.appBar)),
                  backgroundColor: AppColorConstants.colorAppbar,
                ),
                body: Stack(
                  alignment: Alignment.center,
                  children: [
                    Container(
                      color: AppColorConstants.colorPrimary,
                      width: double.infinity,
                      height: double.infinity,
                      child: const AppImageAsset(
                        image: AppAssetsConstants.loginBackground,
                        fit: BoxFit.fitHeight,
                      ),
                    ),
                    getBody(),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget getBody() {
    return SingleChildScrollView(
      child: Column(
        children: [
          const SizedBox(height: 10),
          if(ssIdReportPageHelper?.apiStatus != ApiStatus.loading) ... [
            if (ssIdReportPageHelper?.connectedSsid != null && (ssIdReportPageHelper?.connectedSsid.isNotEmpty ?? false)) ...[
              Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (ssIdReportPageHelper?.isSSIDConnected.isFalse ?? true) ...[
                        Container(
                          height: getSize(16),
                          width: getSize(16),
                          decoration: BoxDecoration(
                            border: Border.all(color: AppColorConstants.colorWhite),
                            color: ssIdReportPageHelper?.connectedSsid['internet'] == true
                                ? AppColorConstants.lightGreen
                                : AppColorConstants.lightRed,
                            shape: BoxShape.circle,
                          ),
                        ),
                        SizedBox(width: getSize(10)),
                      ],
                      if (ssIdReportPageHelper?.connectedSsid.containsKey('connected_ssid') == true && (ssIdReportPageHelper?.connectedSsid['connected_ssid'].isNotEmpty ?? true)) ...[
                        AppText(
                          (ssIdReportPageHelper?.isSSIDConnected.isTrue ?? false) ? S.of(context).connectedWith : S.of(context).notConnectedWith,
                          style: TextStyle(
                            color: AppColorConstants.colorWhite,
                            fontSize: getSize(14),
                            fontWeight: FontWeight.w300,
                            fontFamily: AppAssetsConstants.openSans,
                          ),
                        ),
                      ] else ... [
                        AppText(
                          S.of(context).notConnectedWith,
                          style: TextStyle(
                            color: AppColorConstants.colorWhite,
                            fontSize: getSize(14),
                            fontWeight: FontWeight.w300,
                            fontFamily: AppAssetsConstants.openSans,
                          ),
                        ),
                      ],
                    ],
                  ),
                  if (ssIdReportPageHelper?.isSSIDConnected.isTrue ?? false) ...[
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          height: getSize(16),
                          width: getSize(16),
                          decoration: BoxDecoration(
                            border: Border.all(color: AppColorConstants.colorWhite),
                            color: ssIdReportPageHelper?.connectedSsid['internet'] == true
                                ? AppColorConstants.lightGreen
                                : AppColorConstants.lightRed,
                            shape: BoxShape.circle,
                          ),
                        ),
                        SizedBox(width: getSize(10)),
                        Padding(
                          padding: const EdgeInsets.only(top: 4),
                          child: AppText(
                            (ssIdReportPageHelper?.connectedSsid.containsKey('connected_ssid') == true && (ssIdReportPageHelper?.connectedSsid['connected_ssid'].isEmpty ?? true)) ? S.of(context).connectedWithEthernet : ssIdReportPageHelper?.connectedSsid['connected_ssid'],
                            style: TextStyle(color: AppColorConstants.colorWhite, fontWeight: FontWeight.w500, fontSize: 18),
                          ),
                        ),
                      ],
                    ),
                  ],
                  Padding(
                    padding: const EdgeInsets.only(top: 4,bottom: 8),
                    child: AppText(
                      ssIdReportPageHelper?.connectedSsid['ip'] ?? "",
                      style: TextStyle(color: AppColorConstants.colorWhite, fontWeight: FontWeight.w500, fontSize: 14),
                    ),
                  ),
                ],
              ),
            ],
          ],
          Container(
            margin: EdgeInsets.symmetric(horizontal: getSize(16)),
            padding: EdgeInsets.all(getSize(20)),
            decoration: BoxDecoration(color: AppColorConstants.colorWhite, borderRadius: BorderRadius.circular(getSize(8))),
            constraints: BoxConstraints(maxWidth: getSize(500), maxHeight: getSize(450)),
            child: Column(
              children: [
                getStepperView(),
                ssIdReportPageHelper?.selectedSSID != null ? getDataIDBody() : getIDBody(),
              ],
            )
          ),
          const SizedBox(height: 10),
        ],
      ),
    );
  }

  Widget getStepperView(){
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        InkWell(
          onTap: () {
            ssIdReportPageHelper?.selectedSSID = null;
            ssIdReportPageHelper?.passwordController.clear();
            ssIdReportPageHelper?.responseData = null;
            ssIdReportPageHelper?.responseString = null;
            ssIdReportController.update();
          },
          child: Container(
            alignment: Alignment.center,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColorConstants.colorPrimary.withOpacity(0.8),
              shape: BoxShape.circle, // Makes the container circular
            ),
            child: AppText(
              "1",
              isSelectableText: false,
              style: TextStyle(color: AppColorConstants.colorWhite, fontSize: getSize(16)),
            ),
          ),
        ),
        Container(
          margin: EdgeInsets.only(left: getSize(5), right: getSize(5)),
          height: 2,
          width: getSize(200),
          color: ssIdReportPageHelper?.selectedSSID != null ? AppColorConstants.colorPrimary.withOpacity(0.8) : AppColorConstants.colorChartBackGround.withOpacity(0.3),
        ),
        Container(
          alignment: Alignment.center,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: ssIdReportPageHelper?.selectedSSID != null ? AppColorConstants.colorPrimary.withOpacity(0.8) : AppColorConstants.colorChartBackGround.withOpacity(0.3),
            shape: BoxShape.circle, // Makes the container circular
          ),
          child: AppText(
            "2",
            isSelectableText: false,
            style: TextStyle(color: AppColorConstants.colorWhite, fontSize: getSize(16)),
          ),
        ),
      ],
    );
  }

  Widget getIDBody() {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Stack(
            children: [
              Center(
                child: AppText(
                  S.of(context).selectWifi,
                  style: TextStyle(fontSize: getSize(22), fontWeight: FontWeight.bold),
                ),
              ),
              Positioned(
                right: 0,
                child: IconButton(
                  onPressed: () => ssIdReportPageHelper?.checkRefresh(),
                  icon: Icon(
                    Icons.refresh,
                    color: AppColorConstants.colorPrimary,
                    size: getSize(24),
                  ),
                ),
              ),
            ],
          ),
          searchTextFieldView(),
          Expanded(
            child: ((ssIdReportPageHelper?.availableNetworksList?.isEmpty ?? true) && ssIdReportPageHelper?.apiStatus == ApiStatus.loading)
                ? AppLoader()
                : (ssIdReportPageHelper?.availableNetworksList == null && ssIdReportPageHelper?.apiStatus == ApiStatus.failed)
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.error_outline, color: AppColorConstants.colorRedAccent, size: 40),
                            SizedBox(height: getSize(10)),
                            AppText(
                              (ssIdReportPageHelper?.networksError != null)
                                  ? (ssIdReportPageHelper?.networksError ?? '')
                                  : S.of(context).connectionTimeOut,
                              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500, color: AppColorConstants.colorRedAccent),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      )
                    : (ssIdReportPageHelper?.filterNetworksList?.isNotEmpty ?? false)
                        ? RawScrollbar(
                            controller: ssIdReportPageHelper?.scrollController,
                            thumbVisibility: true,
                            thickness: 8,
                            minThumbLength: 80,
                            radius: Radius.circular(12),
                            thumbColor: AppColorConstants.colorAppbar,
                            child: ListView.separated(
                              controller: ssIdReportPageHelper?.scrollController,
                              shrinkWrap: true,
                              itemCount: ssIdReportPageHelper?.filterNetworksList?.length ?? 0,
                              itemBuilder: (context, index) {
                                final String ssid = ssIdReportPageHelper!.filterNetworksList?[index] ?? '';
                                bool isSelected = ssIdReportPageHelper?.selectedSSID == ssid;
                                return Container(
                                  padding: EdgeInsets.symmetric(horizontal: getSize(15), vertical: getSize(14)),
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    color: AppColorConstants.colorChartBackGround.withOpacity(0.3),
                                    borderRadius: BorderRadius.all(Radius.circular(8)),
                                    border: Border.all(color: isSelected ? AppColorConstants.colorPrimary : Colors.transparent),
                                  ),
                                  child: InkWell(
                                    onTap: () => ssIdReportPageHelper?.manageSelectedSSID(ssid, index),
                                    child: Row(
                                      children: [
                                        Expanded(
                                          child: AppText(
                                            ssid,
                                            isSelectableText: false,
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w500,
                                              color: AppColorConstants.colorBlack,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ),
                                        Spacer(),
                                        Container(
                                          padding: EdgeInsets.symmetric(horizontal: getSize(10), vertical: getSize(1)),
                                          margin: EdgeInsets.only(right: getSize(10)),
                                          alignment: Alignment.center,
                                          child: AppText(
                                            S.of(context).connect,
                                            isSelectableText: false,
                                            style: TextStyle(color: AppColorConstants.colorWhite),
                                          ),
                                          decoration: BoxDecoration(
                                            color: AppColorConstants.colorPrimary,
                                            borderRadius: BorderRadius.all(Radius.circular(5)),
                                          ),
                                        ),
                                        Icon(Icons.lock, color: AppColorConstants.colorBlack, size: 18),
                                        SizedBox(width: getSize(10)),
                                        Icon(Icons.wifi, color: AppColorConstants.colorBlack, size: 18),
                                      ],
                                    ),
                                  ),
                                );
                              },
                              separatorBuilder: (context, index) => SizedBox(height: getSize(10)),
                            ),
                          )
                        : Center(
                            child: AppText(
                              S.of(context).noWifiFound,
                              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500, color: AppColorConstants.colorPrimary),
                            ),
                          ),
          ),
          Padding(
            padding: EdgeInsets.only(top: getSize(10)),
            child: SizedBox(
              height: getSize(30),
              width: getSize(100),
              child: AppButton(
                loadingStatus: ssIdReportPageHelper?.cancelApiStatus ?? ApiStatus.initial,
                onPressed: () async => ssIdReportPageHelper?.validateTokenBeforeNavigation(),
                buttonName: S.of(context).cancel,
                buttonColor: AppColorConstants.colorWhite,
                fontColor: AppColorConstants.colorPrimary,
                fontFamily: AppAssetsConstants.openSans,
                buttonWidth: getSize(80),
                borderColor: AppColorConstants.colorPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget getDataIDBody() {
    return Expanded(
      child: Column(
        children: [
          Container(
            child: AppText(
              "${S.of(context).connectTo}${S.of(context).ssid}",
              style: TextStyle(fontSize: getSize(22), fontWeight: FontWeight.bold),
            ),
          ),
          SizedBox(height: getSize(30)),
          Align(
            alignment: Alignment.centerLeft,
            child: Container(
              width: getSize(100),
              child: AppText(
                "${S.of(context).wifiSSID} : ",
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
            ),
          ),
          SizedBox(height: getSize(10)),
          Container(
            decoration: BoxDecoration(
              color: AppColorConstants.colorLimeBlue.withOpacity(0.3),
              borderRadius: BorderRadius.all(Radius.circular(8)),
            ),
            child: AppTextFormField(
              controller: ssIdReportPageHelper!.ssIdController,
              maxLines: 1,
              contentPadding: EdgeInsets.symmetric(vertical: getSize(12), horizontal: getSize(10)),
              textInputType: TextInputType.text,
              readOnly: true,
              borderRadius: 8,
              suffixIcon: InkWell(onTap: () {
                ssIdReportPageHelper?.changeView();
                ssIdReportPageHelper?.passwordController.clear();
                ssIdReportPageHelper?.responseData = null;
                ssIdReportPageHelper?.responseString = null;
              }, child: Icon(Icons.edit, color: AppColorConstants.colorPrimary)),
            ),
          ),
          SizedBox(height: getSize(25)),
          Align(
            alignment: Alignment.centerLeft,
            child: Container(
              width: getSize(100),
              child: AppText(
                "${S.of(context).passWord} : ",
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
            ),
          ),
          SizedBox(height: getSize(10)),
          Container(
            child: AppTextFormField(inputFormatters: [
              FilteringTextInputFormatter.deny(RegExp(r'\s')), // Prevents multiple spaces
            ],
              controller: ssIdReportPageHelper!.passwordController,
              maxLines: 1,
              textInputType: TextInputType.text,
              focusedBorderColor: AppColorConstants.colorPrimary,
              enabledBorderColor: AppColorConstants.colorH2.withOpacity(0.5),
              borderRadius: 8,
              hintText: S.of(context).enterPassword,
              hintTextColor: AppColorConstants.colorH2.withOpacity(0.5),
              hintFontSize: 14,
              errorText: ssIdReportPageHelper?.responseData?.statusCode == 400 ? ssIdReportPageHelper?.responseString : null,
              prefixIcon: Icon(Icons.lock, color: AppColorConstants.colorPrimary),
              suffixIcon: InkWell(
                onTap: () => ssIdReportPageHelper?.passwordVisible(),
                child: Icon((ssIdReportPageHelper?.isPasswordVisible ?? false) ? Icons.visibility : Icons.visibility_off, color: AppColorConstants.colorPrimary),
              ),
              obscureText: ssIdReportPageHelper?.isPasswordVisible ?? false,
              onFieldSubmitted: (value) => ssIdReportPageHelper?.connectToSSID(context),
            ),
          ),
          SizedBox(height: getSize(35)),
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                if (ssIdReportPageHelper?.apiStatus == ApiStatus.failed)
                  Expanded(
                    child: AppText(
                      ssIdReportPageHelper?.responseString ?? "",
                      textAlign: TextAlign.start,
                      style: TextStyle(color: AppColorConstants.colorRedAccent, fontSize: 14, fontWeight: FontWeight.w600),
                    ),
                  ),
                if (ssIdReportPageHelper?.responseData?.statusCode == 200) ... [
                  Expanded(
                    child: AppText(
                      ssIdReportPageHelper?.isSSIDConnected.value == false
                          ? S.of(context).connectionTimeOut
                          : ssIdReportPageHelper?.responseString ?? '',
                      maxLines: 2,
                      style: TextStyle(
                        color: ssIdReportPageHelper?.isSSIDConnected.value == false
                            ? AppColorConstants.colorRedAccent
                            : AppColorConstants.colorGreen,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
                SizedBox(
                  height: getSize(30),
                  width: getSize(100),
                  child: AppButton(
                    loadingStatus: ssIdReportPageHelper?.cancelApiStatus ?? ApiStatus.initial,
                    onPressed: () => ssIdReportPageHelper?.validateTokenBeforeNavigation(),
                    buttonName: S.of(context).cancel,
                    buttonColor: AppColorConstants.colorWhite,
                    fontColor: AppColorConstants.colorPrimary,
                    fontFamily: AppAssetsConstants.openSans,
                    buttonWidth: getSize(80),
                    borderColor: AppColorConstants.colorPrimary,
                  ),
                ),
                SizedBox(width: getSize(10)),
                SizedBox(
                  height: getSize(30),
                  width: getSize(100),
                  child: AppButton(
                    loadingStatus: ssIdReportPageHelper?.apiStatus ?? ApiStatus.initial,
                    onPressed: () async => await ssIdReportPageHelper?.connectToSSID(context),
                    buttonName: S.of(context).save,
                    buttonColor: AppColorConstants.colorPrimary,
                    fontFamily:   AppAssetsConstants.openSans,
                    borderColor: AppColorConstants.colorPrimary,
                    buttonWidth: getSize(80),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget searchTextFieldView() {
    return Container(
      margin: EdgeInsets.symmetric(vertical: getSize(10)),
      height: 40,
      decoration: BoxDecoration(
          color: AppColorConstants.colorWhite1, borderRadius: BorderRadius.circular(getSize(8))),
      child: AppTextFormField(
        onChanged: (value) => ssIdReportPageHelper?.filterNetworks(value),
        focusedBorderColor: AppColorConstants.colorPrimary,
        controller: ssIdReportPageHelper!.searchController,
        enabledBorderColor: AppColorConstants.colorWhite1,
        hintText: S.of(context).searchWifi,
        maxLines: 1,
        textInputType: TextInputType.emailAddress,
        validator: (value) {
          return null;
        },
        borderRadius: getSize(8),
        hintTextColor: AppColorConstants.colorDarkBlue,
        suffixIcon: const Padding(
          padding: EdgeInsets.all(12),
          child: AppImageAsset(image: AppAssetsConstants.searchIcon),
        ),
        hintFontSize: 17,
        fontFamily: AppAssetsConstants.openSans,
        fontWeight: FontWeight.w400,
      ),
    );
  }
}