import 'package:flutter/scheduler.dart';
import 'package:quantumlink_node/app/cache/app_shared_preference.dart';
import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/pages/ss_id_report/ss_id_report_page.dart';

class SSIdReportPageHelper {
  late SsIdReportPageState state;
  List<String>? availableNetworksList;
  String? networksError;
  Response? networksData;
  List<String>? filterNetworksList;
  TextEditingController ssIdController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  TextEditingController searchController = TextEditingController();
  ApiStatus apiStatus = ApiStatus.initial;
  ApiStatus cancelApiStatus = ApiStatus.initial;
  String? selectedSSID;
  bool isPasswordVisible = true;
  Response? responseData;
  String? responseString;
  Map<String,dynamic> connectedSsid = {};
  RxBool isSSIDConnected = false.obs;
  final ScrollController scrollController = ScrollController();

  SSIdReportPageHelper(this.state) {
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
      getDiscoveredWifi();
      checkWifiConnectivity();
    });
  }

  void updateState() => state.ssIdReportController.update();

  void updateSelectedSSID(String ssid) {
    selectedSSID = ssid;
    ssIdController.text = ssid;
    updateState();
  }

  void changeView() {
    selectedSSID = null;
    passwordController.clear();
    updateState();
  }

  void passwordVisible() {
    isPasswordVisible = !isPasswordVisible;
    updateState();
  }

  Future<void> getDiscoveredWifi() async {
    networksData = null;
    availableNetworksList = null;
    filterNetworksList = null;
    apiStatus = ApiStatus.loading;
    updateState();
    // networksData = await state.ssIdReportController.getDiscoveredNodeWifi(state.context) ?? null;
    if (networksData != null) {
      Map<String, dynamic> jsonResponse = json.decode(networksData?.body);
      if (jsonResponse.containsKey('available_networks')) {
        availableNetworksList = List.from(jsonResponse['available_networks'] ?? []);
        availableNetworksList = availableNetworksList!
            .where((network) => !network.contains("QLNODE") && network.trim().isNotEmpty)
            .toSet() // Removes duplicates
            .toList();
        filterNetworksList = availableNetworksList;
        apiStatus = ApiStatus.success;
      } else if (jsonResponse.containsKey('error')) {
        networksError = jsonResponse['error'];
        apiStatus = ApiStatus.failed;
      }
    } else {
      apiStatus = ApiStatus.failed;
    }

    updateState();
  }

  Future<void> connectToSSID(BuildContext context) async {
    if(apiStatus == ApiStatus.loading) return;
    apiStatus = ApiStatus.loading;
    updateState();
    // responseData = await state.ssIdReportController.connectToSSID(selectedSSID ?? "", passwordController.text, state.context);

    if (responseData == null) {
      apiStatus = ApiStatus.failed;
      responseString = S.of(context).somethingWentWrong;
      updateState();
      return;
    }
    Map<String, dynamic> jsonResponse = json.decode(responseData?.body);
    if (responseData?.statusCode == 400) {
      responseString = jsonResponse['detail'];
    } else if (responseData?.statusCode == 200) {
      responseString = jsonResponse['message'];
      /// TODO : Just added to confirm how the sign in page will open
      await Future.delayed(const Duration(seconds: 3));
      await checkWifiConnectivity();
      if (isSSIDConnected.value == true) {
        goToSignInPage();
      }
    }
    apiStatus = ApiStatus.success;
    updateState();
  }

  void manageSelectedSSID(String ssid, int index) {
    updateSelectedSSID(ssid);
  }

  void filterNetworks(String query) {
    if (query.isEmpty) {
      filterNetworksList = availableNetworksList;
    } else {
      filterNetworksList = availableNetworksList?.where((ssid) => ssid.toLowerCase().contains(query.toLowerCase())).toList();
    }
    updateState();
  }

  Future<void> checkWifiConnectivity() async {
    // connectedSsid = await state.ssIdReportController.ssidReport(state.context);
    isSSIDConnected = (connectedSsid.containsKey('internet') && (connectedSsid['internet'] == true)).obs;
    updateState();
  }

  Future<void> validateTokenBeforeNavigation() async {
    if(cancelApiStatus == ApiStatus.loading) return;
    if (selectedSSID == null) {
      cancelApiStatus = ApiStatus.loading;
      updateState();
      dynamic accessToken = await getPrefStringValue(AppSharedPreference.accessToken);
      if (accessToken != null) {
        router.go(RouteHelper.routeDashboard);
      } else {
        router.go(RouteHelper.routeSignInPage);
      }
    } else {
      selectedSSID = null;
      updateState();
    }
  }

  Future<void> checkRefresh() async {
    await getDiscoveredWifi();
    await checkWifiConnectivity();
  }
}
