import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/serialized/dashboard/dashboard.dart';
import 'package:http/src/response.dart' as http;

class DashboardPageHelper {
  late DashboardPageState state;

  ApiStatus apiStatus = ApiStatus.initial;
  late ScreenLayoutType screenLayoutType;
  final key = GlobalKey<PaginatedDataTableState>();
  DashboardModel dashboardModel = DashboardModel.empty();
  //NodeBatteryInfoItem nodeBatteryInfo = NodeBatteryInfoItem.empty();
  //NodeIdInfoItem nodeIdInfo = NodeIdInfoItem.empty();
  String keysCount = "" ;
  bool countUpdated = false;
  String versionNumber = "";
  bool  ?isHealthy;
  DateTime ?lastUpdateTime ;

  DashboardPageHelper(this.state) {
    Future.delayed(const Duration(milliseconds: 500)).then((value) async {
      apiStatus = ApiStatus.loading;
      state.dashboardController.update();
      if (AppConfig.shared.isQLCentral) {
        getDashboardData();
      }else{
        getNodeDashboardData();
      }
    });
  }

  getDashboardData() async {
    await Future.wait([getVersionNumber(), getKeysCount(), getDashboardDataList(), getServices()]);
    apiStatus = ApiStatus.success;
    lastUpdateTime = DateTime.now();
    state.dashboardController.update();

    Future.delayed(const Duration(seconds: 5), () {
      getDashboardData();
    });
  }


  Future getNodeDashboardData() async {
    await Future.wait([getKeysCount(), getDashboardDataList()]);
    state.dashboardController.update();
    //nodeIdInfo = await state.dashboardController.getNodeIdInfo(state.context);
    //state.dashboardController.update();
    //nodeBatteryInfo = await state.dashboardController.getNodeBatteryInfo(state.context);
    state.dashboardController.update();
    apiStatus = ApiStatus.success;
    lastUpdateTime = DateTime.now();
    state.dashboardController.update();

    Future.delayed(const Duration(seconds: 5), () {
      getNodeDashboardData();
    });
  }
  Future getDashboardDataList() async {
    dashboardModel = await state.dashboardController.getDashboardDataList(state.context);
    state.dashboardController.update();
  }
  Future<void> getKeysCount()async {
    http.Response? response = await state.dashboardController.getKeyCountsFromJoinServer(state.context);
    if (response != null) {
      final Map<String, dynamic> jsonData = jsonDecode(response.body);
      if (jsonData['count'] != null) {
        String count = jsonData['count'].toString();
        keysCount = count;
        countUpdated = true;
      }
    }
    state.dashboardController.update();
  }

  Future<void> getVersionNumber() async {
    try {
      versionNumber = await rootBundle.loadString(AppAssetsConstants.versionPath);
    } catch (e) {
      debugPrint('catch exception in getVersionNumber ---> ${e.toString()}');
    }
    state.dashboardController.update();
  }

  Future getServices() async {
    await Future.forEach(MockData.listApiStatus, (ApiStatusItem item) async {
         item.healthResponse = {};
        if(item.apiUrl.isEmpty){
          item.apiStatus = ApiStatus.failed;
          return;
        }
        dynamic statusData = await state.dashboardController.checkHealth(
          state.context,
          otherBaseUrl: item.apiUrl,
        );
        if (statusData != null) {
          String? statusDetail = statusData["detail"] as String?;
          if ((statusDetail?.isEmpty ?? true)) {
            item.healthResponse = statusData;
          } else {
            item.apiStatus = ApiStatus.failed;
          }
        } else {
          item.apiStatus = ApiStatus.failed;
        }

    });
    isHealthy =isAllApiStatusSuccessAndHealthy();
    state.dashboardController.update();

  }
  bool isAllApiStatusSuccessAndHealthy() {
    return MockData.listApiStatus.every((element) => element.healthResponse["status"]  == "Healthy");
  }
  String extractFWVersion(String input) {
    List<String> parts = input.split('-');
    return parts.isNotEmpty ? parts[0] : '';
  }

  Color batteryLevelColors(dynamic batteryStatus, {bool isBorder = false, bool isCharging = false}) {
    if(isCharging){
      return AppColorConstants.colorLightGreen;
    }
    int batteryLevel = extractPercentage(batteryStatus.toString());
    if (batteryLevel > 0 && batteryLevel < 25) {
      return AppColorConstants.colorRedDark;
    } else if (batteryLevel >= 25 && batteryLevel < 50) {
      return AppColorConstants.colorOrangeLight;
    } else if (batteryLevel >= 50 &&batteryLevel < 75) {
      return AppColorConstants.colorYellow200;
    } else if (batteryLevel >= 75 &&batteryLevel <= 100) {
      return AppColorConstants.colorLightGreen;
    } else {
      return isBorder ? AppColorConstants.colorGray : AppColorConstants.colorTableBroader;
    }
  }

  int extractPercentage(String input) {
    List<String> parts = input.split('%');
    String percentagePart = parts.first;
    num v =  int.tryParse(percentagePart) ?? 0; // Defaults to 0 if parsing fails
    v = v.abs();
    if (v < 0 || v > 100) {
      v = 0;
    }
    return v.toInt();
  }


  Timer? timer;

 /* void startTimer(int duration) {
    timer?.cancel();
    timer = Timer.periodic(Duration(seconds: duration), (timer) {
      if (AppConfig.shared.isQLCentral) {
        getDashboardData();
      } else {
        getNodeDashboardData();
      }
    });
  }*/

  void stopTimer() {
    timer?.cancel();
    timer = null;
  }

}
