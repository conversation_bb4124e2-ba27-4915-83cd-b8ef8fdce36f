import 'package:flutter/widgets.dart';
import 'package:lottie/lottie.dart';
import 'package:quantumlink_node/app_import.dart';
import 'package:quantumlink_node/controller/dashboard_controller.dart';
import 'dashboard_page_helper.dart';

class DashboardPage extends StatefulWidget {
  const DashboardPage({super.key});

  @override
  State<DashboardPage> createState() => DashboardPageState();
}

class DashboardPageState extends State<DashboardPage> {
  DashboardPageHelper? dashboardPageHelper;
  late DashboardController dashboardController;
  late ScreenLayoutType screenLayoutType;
  double constraintsWidth = 0.0;
  int batteryLevel = 100;

  @override
  void initState() {
    dashboardPageHelper ?? (dashboardPageHelper = DashboardPageHelper(this));
      //dashboardPageHelper!.startTimer(5);
    super.initState();
  }

  @override
  void dispose() {
    dashboardPageHelper!.stopTimer();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    
    return GetBuilder<DashboardController>(
      init: DashboardController(),
      builder: (DashboardController controller) {
        dashboardController = controller;
        return Stack(
          children: [
            getBodyView(),
          ],
        );
      },
    );
  }

  Widget getBodyView() {
    return ScreenLayoutTypeBuilder(
      builder: (context, screenType, constraints) {
        screenLayoutType = screenType;
        constraintsWidth = constraints.maxWidth;
        return Stack(children: [
            getDashboardView(),
            if (dashboardPageHelper!.apiStatus == ApiStatus.loading)
               Padding(
                padding:  EdgeInsets.only(right:(constraintsWidth > 1350) ? 150 : 0),
                child:const AppLoader(),
              )
          ],);
      },
    );
  }

  Widget getDashboardView() {
    return ClipRRect(
      borderRadius: const BorderRadius.all(Radius.circular(5)),
      child: Container(
        color: AppColorConstants.colorWhite,
        width: double.infinity,
        child: ListView(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          children: [
            getPageTitleView(S.of(context).dashboard),
            SizedBox(height: getSize(10)),
            if (AppConfig.shared.isQLCentral)
              buildQLCentralDashboardView()
            else
              buildQLNodeDashboardView(),
            Padding(
              padding:  EdgeInsets.only(right:(constraintsWidth > 1350) ? 150 : 0),
              child: getLastSeenView(dashboardPageHelper!.lastUpdateTime),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildQLCentralDashboardView() {
    return Column(
      children: [
        if (screenLayoutType == ScreenLayoutType.desktop)...[
          Row(
            children: [
              getQLNodeCardView(
                  cardTitle: S.of(context).amplifiers,
                  cardIcon: AppAssetsConstants.amplifiersIcon,
                  cardSubTitle1: S.of(context).amplifiersWithAlarms,
                  cardSubTitle2: S.of(context).nodeGWDongleConnection,
                  cardSubValue1: "${dashboardPageHelper!.dashboardModel.amplifierWithAlarms ?? ""}",
                  cardSubValue2: "${dashboardPageHelper!.dashboardModel.nodegwDongleConnection ?? ""}"),
              getQLNodeCardView(
                  cardTitle: S.of(context).joinServer,
                  cardIcon: AppAssetsConstants.joinServerIcon,
                  cardSubTitle1: S.of(context).url,
                  cardSubTitle2: S.of(context).keysSynced,
                  cardSubValue1: AppConfig.shared.joinServerUrl,
                  cardSubValue2: "${dashboardPageHelper?.keysCount ?? "n/a"}"),

              if (constraintsWidth> 1350)
                const SizedBox(
                  width: 150,
                )
            ],
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
            getQLCardView(
                cardTitle: S.of(context).siteRegions,
                cardIcon: AppAssetsConstants.stateRegionIcon,
                cardSubTitle1: S.of(context).site,
                cardSubTitle2: S.of(context).vLGW1,
                cardSubTitle3: S.of(context).rPD,
                cardSubTitle4: S.of(context).activeAmplifiers,
                cardSubValue1: "${dashboardPageHelper!.dashboardModel.sites ?? ""}",
                cardSubValue2: "${dashboardPageHelper!.dashboardModel.vlgws ?? ""}",
                cardSubValue3: "${dashboardPageHelper!.dashboardModel.rpds ?? ""}",
                cardSubValue4: "${dashboardPageHelper!.dashboardModel.activeAmplifiers ?? ""}"),

            getQLDetailView(),
              if (constraintsWidth> 1350)
              const SizedBox(
                width: 150,
              )
          ],)

        ],
        if (screenLayoutType != ScreenLayoutType.desktop) ...[
          getQLNodeCardView(
              cardTitle: S.of(context).amplifiers,
              cardIcon: AppAssetsConstants.amplifiersIcon,
              cardSubTitle1: S.of(context).amplifiersWithAlarms,
              cardSubTitle2: S.of(context).nodeGWDongleConnection,
              cardSubValue1: "${dashboardPageHelper!.dashboardModel.amplifierWithAlarms ?? ""}",
              cardSubValue2: "${dashboardPageHelper!.dashboardModel.nodegwDongleConnection ?? ""}"),
          const SizedBox(
            width: 20,
          ),
          getQLCardView(
              cardTitle: S.of(context).siteRegions,
              cardIcon: AppAssetsConstants.stateRegionIcon,
              cardSubTitle1: S.of(context).site,
              cardSubTitle2: S.of(context).vLGW1,
              cardSubTitle3: S.of(context).rPD,
              cardSubTitle4: S.of(context).amplifiers,
              cardSubValue1: "${dashboardPageHelper!.dashboardModel.sites ?? ""}",
              cardSubValue2: "${dashboardPageHelper!.dashboardModel.vlgws ?? ""}",
              cardSubValue3: "${dashboardPageHelper!.dashboardModel.rpds ?? ""}",
              cardSubValue4: "${dashboardPageHelper!.dashboardModel.activeAmplifiers ?? ""}"),
          const SizedBox(
            width: 20,
          ),
          getQLNodeCardView(
              cardTitle: S.of(context).joinServer,
              cardIcon: AppAssetsConstants.joinServerIcon,
              cardSubTitle1: S.of(context).url,
              cardSubTitle2: S.of(context).keysSynced,
              cardSubValue1: AppConfig.shared.joinServerUrl,
              cardSubValue2: "${dashboardPageHelper?.keysCount ?? "n/a"}"),
          const SizedBox(
            width: 20,
          ),
          getQLDetailView()
        ],
      ],
    );
  }

  Widget getQLDetailView() {
    if (screenLayoutType == ScreenLayoutType.desktop) {
      return Expanded(child: _buildCardWidget(S.of(context).details,
          detailWidget(),
          AppAssetsConstants.fileTextIcon));
    }
    return _buildCardWidget(S.of(context).details,
        detailWidget(),
        AppAssetsConstants.fileTextIcon);
  }

  List<Widget> detailWidget(){
    return [
      SizedBox(
        height: getSize(10),
      ),
      cardSubTitleView(S
          .of(context)
          .version, dashboardPageHelper!.versionNumber),
      SizedBox(
        height: getSize(10),
      ),
      Row(
        children: [
          AppText(
            S.of(context).serviceStatus,
            style: TextStyle(
              overflow: TextOverflow.ellipsis,
              color: AppColorConstants.colorH3,
              fontSize: getSize(13.4),
              fontWeight: getMediumBoldFontWeight(),
              fontFamily: AppAssetsConstants.manrope,
            ),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: DottedLine(color: AppColorConstants.colorDotLine),
            ),
          ),
          if (dashboardPageHelper?.isHealthy != null)
            dashboardPageHelper?.isHealthy == true
                ? AppText(S.of(context).healthy,
              style: TextStyle(
                color: AppColorConstants.colorH3,
                fontSize: getSize(14),
                fontWeight: getMediumFontWeight(),
                fontFamily: AppAssetsConstants.manrope,
              ),
            )
                : Icon(
              Icons.error_outline,
              color: AppColorConstants.colorYellow,
            ),
        ],
      ),
      SizedBox(
        height: getSize(10),
      ),
    ];
  }

  Widget buildQLNodeDashboardView() {
    return Column(
      children: [
        if (screenLayoutType == ScreenLayoutType.desktop) ...[
          Row(
            children: [
              getQLNodeCardView(
                  cardTitle: S.of(context).joinServer,
                  cardIcon: AppAssetsConstants.joinServerIcon,
                  cardSubTitle1: S.of(context).url,
                  cardSubTitle2: S.of(context).keysSynced,
                  cardSubValue1: AppConfig.shared.joinServerUrl,
                  cardSubValue2: "${dashboardPageHelper?.keysCount ?? "n/a"}"),
              getQLNodeCardView(
                cardTitle: S.of(context).amplifiers,
                cardIcon: AppAssetsConstants.amplifiersIcon,
                cardSubTitle1: S.of(context).activeAmplifiers,
                cardSubTitle2: S.of(context).amplifiersWithAlarms,
                cardSubValue1: "${dashboardPageHelper!.dashboardModel.activeAmplifiers ?? ""}",
                cardSubValue2: "${dashboardPageHelper!.dashboardModel.amplifierWithAlarms ?? ""}",
              ),
              if (constraintsWidth> 1350)
                const SizedBox(
                  width: 150,
                )
            ],
          ),
         /* Row(
            children: [
              Flexible(
                child: _buildDetailCardWidget(
                  S.of(context).details,
                  [
                    cardSubTitleView(S.of(context).gwEUI, dashboardPageHelper!.nodeIdInfo.gwEui ?? ""),
                    cardSubTitleView(S.of(context).model, dashboardPageHelper!.nodeIdInfo.model ?? ""),
                    cardSubTitleView(
                        S.of(context).fWVersion,
                        dashboardPageHelper!.nodeIdInfo.fwVersion != null
                            ? dashboardPageHelper!
                                .extractFWVersion(dashboardPageHelper!.nodeIdInfo.fwVersion)
                            : "" // Default value if null
                        ),
                    cardSubTitleView(S.of(context).bluetoothID, dashboardPageHelper!.nodeIdInfo.bluetoothId ?? ""),
                    cardSubTitleView(S.of(context).wifiSSID, dashboardPageHelper!.nodeIdInfo.wifiSsid ?? ""),
                  ],
                ),
              ),
              Flexible(child: _buildDeviceHealthWidget()),
              if (constraintsWidth> 1350)
                const SizedBox(
                  width: 150,
                )
            ],
          )*/
        ] else ...[
          getQLNodeCardView(
              cardTitle: S.of(context).joinServer,
              cardIcon: AppAssetsConstants.joinServerIcon,
              cardSubTitle1: S.of(context).url,
              cardSubTitle2: S.of(context).keysSynced,
              cardSubValue1: AppConfig.shared.joinServerUrl,
              cardSubValue2: "${dashboardPageHelper?.keysCount ?? "n/a"}"),
          getQLNodeCardView(
            cardTitle: S.of(context).amplifiers,
            cardIcon: AppAssetsConstants.amplifiersIcon,
            cardSubTitle1: S.of(context).activeAmplifiers,
            cardSubTitle2: S.of(context).amplifiersWithAlarms,
            cardSubValue1: "${dashboardPageHelper!.dashboardModel.activeAmplifiers ?? ""}",
            cardSubValue2: "${dashboardPageHelper!.dashboardModel.amplifierWithAlarms ?? ""}",
          ),
          /*_buildDetailCardWidget(
            S.of(context).details,
            [
              cardSubTitleView(S.of(context).gwEUI, dashboardPageHelper!.nodeIdInfo.gwEui ?? ""),
              cardSubTitleView(S.of(context).model, dashboardPageHelper!.nodeIdInfo.model ?? ""),
              cardSubTitleView(S.of(context).fWVersion,dashboardPageHelper!.nodeIdInfo.fwVersion ?? ""),
              cardSubTitleView(S.of(context).bluetoothID, dashboardPageHelper!.nodeIdInfo.bluetoothId ?? ""),
              cardSubTitleView(S.of(context).wifiSSID, dashboardPageHelper!.nodeIdInfo.wifiSsid ?? ""),
            ],
          ),*/
          //_buildDeviceHealthWidget()
        ],
      ],
    );
  }

  Widget getQLCardView(
      {required String cardTitle,
      required String cardSubTitle1,
      required String cardSubTitle2,
      required String cardSubTitle3,
      required String cardSubTitle4,
      required String cardSubValue1,
      required String cardSubValue2,
      required String cardSubValue3,
      required String cardSubValue4,
      required String cardIcon}) {
    if (screenLayoutType == ScreenLayoutType.desktop) {
      return Flexible(
        child: _buildCardWidget(
            cardTitle,
            [
              SizedBox(
                height: getSize(10),
              ),
              cardSubTitleView(cardSubTitle1, cardSubValue1),
              cardSubTitleView(cardSubTitle2, cardSubValue2),
              cardSubTitleView(cardSubTitle3, cardSubValue3),
              cardSubTitleView(cardSubTitle4, cardSubValue4),
            ],
            cardIcon),
      );
    }
    return _buildCardWidget(
      cardTitle,
      [
        SizedBox(
          height: getSize(10),
        ),
        cardSubTitleView(cardSubTitle1, cardSubValue1),
        cardSubTitleView(cardSubTitle2, cardSubValue2),
        cardSubTitleView(cardSubTitle3, cardSubValue3),
        cardSubTitleView(cardSubTitle4, cardSubValue4),
      ],
      cardIcon,
    );
  }

  Widget getQLNodeCardView(
      {required String cardTitle,
      required String cardSubTitle1,
      required String cardSubTitle2,
      required String cardSubValue1,
      required String cardSubValue2,
      required String cardIcon}) {
    if (screenLayoutType == ScreenLayoutType.desktop) {
      return Flexible(
        child: _buildCardWidget(
            cardTitle,
            [
              SizedBox(
                height: getSize(10),
              ),
              cardSubTitleView(cardSubTitle1, cardSubValue1),
              cardSubTitleView(cardSubTitle2, cardSubValue2),
            ],
            cardIcon),
      );
    }
    return _buildCardWidget(
      cardTitle,
      [
        SizedBox(
          height: getSize(10),
        ),
        cardSubTitleView(cardSubTitle1, cardSubValue1),
        cardSubTitleView(cardSubTitle2, cardSubValue2),
      ],
      cardIcon,
    );
  }

  Widget _buildCardWidget(String cardTitle, List<Widget> columns, String cardIcon) {
    return Padding(
        padding: const EdgeInsets.only(
            bottom: 20, right: 10, left: 10),
        child: Container(
          padding: const EdgeInsets.only(left: 40, right: 40, top: 40, bottom: 50),
          decoration: BoxDecoration(
            border: Border.all(color: AppColorConstants.colorTableBroader, width: 1),
            borderRadius: BorderRadius.circular(getSize(8)),
          ),
          child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Row(
              children: [
                Container(
                    height: 60,
                    width: 60,
                    decoration: BoxDecoration(
                      color: AppColorConstants.colorAppbar,
                      borderRadius: BorderRadius.circular(getSize(8)),
                    ),
                    child: Padding(
                      padding: EdgeInsets.all(getSize(15)),
                      child: AppImageAsset(
                        image: cardIcon,
                      ),
                    )),
                Expanded(
                  child: cardTitleView(cardTitle),
                ),
              ],
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: columns,
            ),
          ]),
        ));
  }

  Widget _buildDetailCardWidget(String cardTitle, List<Widget> columns) {
    return Padding(
        padding: EdgeInsets.only(
            bottom: screenLayoutType == ScreenLayoutType.desktop ? 40 : 20, right: 10, left: 10),
        child: Container(
          padding: const EdgeInsets.only(left: 40,right: 40,top: 40,bottom: 50),
          decoration: BoxDecoration(
            border: Border.all(color: AppColorConstants.colorTableBroader, width: 1),
            borderRadius: BorderRadius.circular(getSize(8)),
          ),
          child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Padding(
              padding: const EdgeInsets.only(bottom: 20),
              child: AppText(
                cardTitle,
                style: TextStyle(
                    color: AppColorConstants.colorAppbar,
                    fontSize: getSize(24),
                    fontWeight: getMediumBoldFontWeight(),
                    fontFamily: AppAssetsConstants.openSans),
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: columns,
            ),
          ]),
        ));
  }

  /*Widget _buildDeviceHealthWidget() {
    return Padding(
        padding: EdgeInsets.only(
            bottom: screenLayoutType == ScreenLayoutType.desktop ? 40 : 20, right: 10, left: 10),
        child: Container(
          padding:const EdgeInsets.only(left: 40,right: 40,top: 40,bottom: 55),
          decoration: BoxDecoration(
            border: Border.all(color: AppColorConstants.colorTableBroader, width: 1),
            borderRadius: BorderRadius.circular(getSize(8)),
          ),
          child: Column(crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(bottom: 20 ),
                child: AppText(
                  S.of(context).batteryAndCharging,
                  style: TextStyle(
                      color: AppColorConstants.colorAppbar,
                      fontSize: getSize(24),
                      fontWeight: getMediumBoldFontWeight(),
                      fontFamily: AppAssetsConstants.openSans),
                ),
              ),
              Wrap(
                  // crossAxisAlignment: CrossAxisAlignment.center,
                  // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(getSize(8)),
                      ),
                      child: batteryStatusView(),
                    ),
                    SizedBox(height: 25,),
                    AppText(
                            dashboardPageHelper!.nodeBatteryInfo.batteryStatus != null
                                ? "${dashboardPageHelper!.nodeBatteryInfo.batteryStatus}"
                                : "",
                            style: TextStyle(
                                fontFamily: AppAssetsConstants.openSans,
                                color: AppColorConstants.colorH2,
                                fontSize: getSize(35),
                                fontWeight: getBoldFontWeight()),
                          ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        cardSubTitleView(
                            S.of(context).temperatureTitle,
                            dashboardPageHelper!.nodeBatteryInfo.btDegC != null
                                ? "${dashboardPageHelper!.nodeBatteryInfo.btDegC!.toStringAsFixed(2)}°"
                                : ""),
                        cardSubTitleView(S.of(context).powerSource,
                            "${dashboardPageHelper!.nodeBatteryInfo.powerSource ?? ""}"),
                      ],
                    ),
                  ]),
            ],
          ),
        ));
  }*/

  /*Widget batteryStatusView() {
    String batteryStatus = dashboardPageHelper!.nodeBatteryInfo.batteryStatus ?? "0% Remaining";
    String powerSource = dashboardPageHelper!.nodeBatteryInfo.powerSource ?? "";
    bool isCharging = powerSource.toLowerCase() == "usb";
    return Stack(
      children: [
        CustomPaint(
          size: const Size(130, 61),
          painter: LinearProgressBorderPainter(
            borderWidth:5.0,
            borderColor: dashboardPageHelper!.batteryLevelColors(batteryStatus,isBorder: true, isCharging: isCharging), // Border color
            radius: 9.0,
          )
        ),
        LinearPercentIndicator(
          //restartAnimation: isCharging,
          animationDuration: 2000,
          barRadius: const Radius.circular(9),
          width: 130.0,
          backgroundColor: AppColorConstants.colorTableBroader,
          animation: false,
          center: isCharging
              ? AppImageAsset(
                  color: AppColorConstants.colorWhite,
                  height: 40,
                  image: AppAssetsConstants.chargingIcon,
                )
              : Container(),
          padding: EdgeInsets.zero,
          trailing: Container(
            decoration: BoxDecoration(
                borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(9), bottomRight: Radius.circular(9)),
                color: dashboardPageHelper!.batteryLevelColors(batteryStatus,isBorder: true, isCharging: isCharging)),
            width: 9,
            height: 30,
          ),
          lineHeight: 60,
          percent: dashboardPageHelper!.extractPercentage(batteryStatus).toDouble() / 100,
          progressColor: dashboardPageHelper!.batteryLevelColors(batteryStatus, isCharging: isCharging),
        ),
      ],
    );
  }*/

  Widget cardTitleView(
    String cardTitle,
  ) {
    return Padding(
      padding: EdgeInsets.only(left: getSize(25), right: getSize(10)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          (screenLayoutType == ScreenLayoutType.desktop)
              ? Flexible(
                  child: AppText(
                    cardTitle,
                    style: TextStyle(
                        color: AppColorConstants.colorAppbar,
                        fontSize: getSize(24),
                        fontWeight: getMediumBoldFontWeight(),
                        fontFamily: AppAssetsConstants.openSans),
                  ),
                )
              : AppText(
                  cardTitle,
                  style: TextStyle(
                      color: AppColorConstants.colorAppbar,
                      fontSize: getSize(24),
                      fontWeight: getMediumBoldFontWeight(),
                      fontFamily: AppAssetsConstants.openSans),
                ),
        ],
      ),
    );
  }

  Widget cardSubTitleView(String cardSubTitle, String value) {
    return Padding(
      padding: const EdgeInsets.only(right: 8, top: 10, bottom: 10),
      child: Row(
        children: [
          AppText(
            cardSubTitle,
            style: TextStyle(
              overflow: TextOverflow.ellipsis,
              color: AppColorConstants.colorH3,
              fontSize: getSize(13.4),
              fontWeight: getMediumBoldFontWeight(),
              fontFamily: AppAssetsConstants.manrope,
            ),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: DottedLine(color: AppColorConstants.colorDotLine),
            ),
          ),
          AppText(
            value,
            style: TextStyle(
              color: AppColorConstants.colorH3,
              fontSize: getSize(14),
              fontWeight: getMediumFontWeight(),
              fontFamily: AppAssetsConstants.manrope,
            ),
          ),
        ],
      ),
    );
  }

  Widget getTagButtonView() {
    return AppButton(
      buttonHeight: 33,
      buttonWidth: 45,
      fontSize: 14.5,
      buttonName: S.of(context).tag,
      onPressed: () {
        // bool isUpdate= false;
      },
      buttonColor: AppColorConstants.colorLightBlue,
      fontFamily: AppAssetsConstants.openSans,
    );
  }
}
class LinearProgressBorderPainter extends CustomPainter {
  final double borderWidth;
  final Color borderColor;
  final double radius;

  LinearProgressBorderPainter({
    required this.borderWidth,
    required this.borderColor,
    required this.radius,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderWidth;

    final RRect rRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(0, 0, size.width, size.height),
      Radius.circular(radius),
    );

    canvas.drawRRect(rRect, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}