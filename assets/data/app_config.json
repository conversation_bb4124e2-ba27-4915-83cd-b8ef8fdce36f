{"ql_api_gw_port": "", "ql_api_gw_prefix": "svc/api_gw", "ql_node_mgr_host": "", "ql_node_mgr_port": "5555", "ql_join_server_port": "", "ql_updater": "7777", "ql_ble_svc": "", "ql_api_gw_host": "ql-central.dev.ao-inc.com", "ql_audit_log_host": "audit-log.dev.ao-inc.com", "ql_fuota_server_port": "", "ql_fuota_server_host": "ql-node-tls.dev.ao-inc.com", "ql_fuota_server_prefix": "svc/fuota", "ql_join_server_host": "**************", "ql_join_server_prefix": "svc/join", "ql_map_prefix": "svc/map", "ql_audit_log_prefix": "svc/audit", "show_deployments_since_days": "3", "licensing_service": {"enable": true, "domain": ["ao-inc.com"], "provider": "Microsoft", "tenant": "87174931-84a0-420f-9fbd-7cdb1a64b443", "clientId": "cafe8512-d5ab-460f-8409-2836c87f87cb"}, "ql_gw_setting_port": "8001", "fuota_search_page_limit": "100", "lora_ds_cfg": {"low": {"min": -9, "max": 0, "set": -5}, "medium": {"min": 1, "max": 10, "set": 5}, "high": {"min": 11, "max": 22, "set": 21}}, "inventory": true, "map_url": "https://a.tile.openstreetmap.fr/osmfr/{z}/{x}/{y}.png", "attribution": ", tiles courtesy of OSM France", "external_path": {"alarm": "https://alarm-interface.dev.ao-inc.com/alarm-module/alarm/dashboard", "telemetry": "https://alarm-interface.dev.ao-inc.com/alarm-module/telemetry/dashboard"}}