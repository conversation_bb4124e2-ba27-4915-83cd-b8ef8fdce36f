<svg width="52" height="60" viewBox="0 0 52 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_31_4)">
<path d="M26 4C37.598 4 47 13.402 47 25C47 33.8926 41.4724 41.4941 33.666 44.5566L27.1963 53.4004C27.0614 53.5855 26.882 53.7365 26.6738 53.8408C26.4656 53.9451 26.2346 54 26 54C25.7654 54 25.5344 53.9451 25.3262 53.8408C25.118 53.7365 24.9386 53.5855 24.8037 53.4004L18.334 44.5566C10.5276 41.4941 5 33.8926 5 25C5 13.402 14.402 4 26 4ZM26 8C16.6112 8 9 15.6112 9 25C9 34.3888 16.6112 42 26 42C35.3888 42 43 34.3888 43 25C43 15.6112 35.3888 8 26 8Z" fill="white"/>
</g>
<defs>
<filter id="filter0_d_31_4" x="0" y="0" width="52" height="60" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="2.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_31_4"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_31_4" result="shape"/>
</filter>
</defs>
</svg>
